"use client"

import React, { createContext, useContext, useState, useEffect } from 'react'
import { useUser as useClerkUser, useClerk, useOrganization } from '@clerk/nextjs'
import { toast } from 'sonner'

export interface UserProfile {
  id: string
  name: string
  email: string
  role: string
  department: string
  phone?: string
  avatar?: string
  organizationId?: string
  organizationName?: string
  organizationRole?: string
}

interface UserContextType {
  user: UserProfile | null
  isLoading: boolean
  error: string | null
  setUser: (user: UserProfile | null) => void
  logout: () => Promise<void>
  refreshUserProfile: () => Promise<void>
  isSignedIn: boolean
  organization: {
    id: string
    name: string
    slug: string
    imageUrl: string
    role: string
  } | null
  isOrgLoading: boolean
}

const UserContext = createContext<UserContextType | undefined>(undefined)

export function UserProvider({ children }: { children: React.ReactNode }) {
  const { user: clerkUser, isLoaded: isClerkLoaded, isSignedIn } = useClerkUser()
  const { organization: clerkOrg, isLoaded: isOrgLoaded } = useOrganization()
  const { signOut } = useClerk()
  const [user, setUser] = useState<UserProfile | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const [organization, setOrganization] = useState<{
    id: string
    name: string
    slug: string
    imageUrl: string
    role: string
  } | null>(null)

  // Update organization state when Clerk org data changes
  useEffect(() => {
    if (isOrgLoaded && clerkOrg) {
      setOrganization({
        id: clerkOrg.id,
        name: clerkOrg.name || '',
        slug: clerkOrg.slug || '',
        imageUrl: clerkOrg.imageUrl || '',
        role: clerkOrg.membershipRole || '',
      })
    } else if (isOrgLoaded) {
      setOrganization(null)
    }
  }, [isOrgLoaded, clerkOrg])

  // Sync with Clerk user when it changes
  useEffect(() => {
    if (isClerkLoaded) {
      if (isSignedIn && clerkUser) {
        // Fetch user profile from our database
        refreshUserProfile()
      } else {
        // Clear user if not signed in
        setUser(null)
        setIsLoading(false)
      }
    }
  }, [isClerkLoaded, isSignedIn, clerkUser?.id, isOrgLoaded, clerkOrg?.id])

  const logout = async () => {
    try {
      await signOut()
      setUser(null)
    } catch (err) {
      console.error('Error signing out:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
      toast.error('Error signing out')
    }
  }

  const refreshUserProfile = async () => {
    if (!isSignedIn || !clerkUser) {
      setIsLoading(false)
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      // First try to get from local database
      const response = await fetch('/api/user/profile')

      if (!response.ok) {
        throw new Error('Failed to fetch user profile')
      }

      const data = await response.json()
      
      // Add organization data to the user profile
      const userWithOrg = {
        ...data.user,
        organizationId: organization?.id,
        organizationName: organization?.name,
        organizationRole: organization?.role,
      }
      
      setUser(userWithOrg)
    } catch (err) {
      console.error('Error refreshing user profile:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')

      // Fallback: Create a basic profile from Clerk user
      if (clerkUser) {
        const fallbackUser = {
          id: clerkUser.id,
          name: `${clerkUser.firstName || ''} ${clerkUser.lastName || ''}`.trim() || 'User',
          email: clerkUser.primaryEmailAddress?.emailAddress || '',
          role: 'User', // Default role
          department: 'General', // Default department
          avatar: clerkUser.imageUrl,
          organizationId: organization?.id,
          organizationName: organization?.name,
          organizationRole: organization?.role,
        }
        
        setUser(fallbackUser)
        
        // Try to create this user in our database
        try {
          await fetch('/api/user/profile/sync', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ 
              user: fallbackUser,
              organization: organization
            }),
          })
        } catch (syncError) {
          console.error('Failed to sync user profile:', syncError)
        }
      }
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <UserContext.Provider value={{
      user,
      isLoading,
      error,
      setUser,
      logout,
      refreshUserProfile,
      isSignedIn: isSignedIn || false,
      organization,
      isOrgLoading: !isOrgLoaded
    }}>
      {children}
    </UserContext.Provider>
  )
}

export function useUser() {
  const context = useContext(UserContext)
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider')
  }
  return context
}
