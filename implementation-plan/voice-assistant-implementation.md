# Voice Assistant Implementation Specification

## Prerequisites
- Node.js ≥ 18.x
- Next.js 15.x
- React 19.x
- TypeScript 5.x
- Elevenlabs API credentials
  - Agent ID: `ksY7sNnsYBDREsNnEXsa`
  - API Key: `***************************************************`

## Dependency Installation

```bash
# Install ElevenLabs SDK
npm install @11labs/react@latest
```

## Implementation Sequence

### 1. Environment Configuration

Create `.env.local` file in project root:

```
ELEVEN_LABS_API_KEY=***************************************************
ELEVEN_LABS_AGENT_ID=ksY7sNnsYBDREsNnEXsa
```

### 2. Type Definitions

Create `/types/voice-assistant.d.ts`:

```typescript
// Define TypeScript types for voice assistant state machine
type AssistantState = 'idle' | 'connecting' | 'listening' | 'processing' | 'speaking' | 'error';

interface ConversationMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

interface VoiceAssistantContextType {
  state: AssistantState;
  messages: ConversationMessage[];
  currentMessage: string;
  startListening: () => Promise<void>;
  stopListening: () => Promise<void>;
  toggleListening: () => Promise<void>;
  clearConversation: () => void;
}
```

### 3. VoiceAssistantProvider Implementation

Create `/components/voice-assistant/voice-assistant-provider.tsx`:

```typescript
'use client'

import { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react'
import { useConversation, type Role } from '@11labs/react'
import { useToast } from '@/components/ui/toast-context'

const VoiceAssistantContext = createContext<VoiceAssistantContextType | undefined>(undefined)

export function VoiceAssistantProvider({ children }: { children: ReactNode }) {
  const [state, setState] = useState<AssistantState>('idle')
  const [messages, setMessages] = useState<ConversationMessage[]>([])
  const [currentMessage, setCurrentMessage] = useState('')
  const { toast } = useToast()
  
  const conversation = useConversation({
    onError: (error: string) => {
      toast({
        title: 'Voice Assistant Error',
        description: error,
        variant: 'destructive',
      })
      setState('error')
    },
    onConnect: () => {
      setState('listening')
    },
    onMessage: (props: { message: string; source: Role }) => {
      const { message, source } = props
      
      if (source === 'ai') {
        setCurrentMessage(message)
        setState('speaking')
      } else {
        addMessage({
          id: Date.now().toString(),
          role: 'user',
          content: message,
          timestamp: new Date()
        })
      }
      
      if (source === 'ai' && !message) {
        setState('listening')
      }
    },
  })
  
  useEffect(() => {
    if (conversation.isSpeaking) {
      setState('speaking')
    } else if (state === 'speaking') {
      setState('listening')
    }
  }, [conversation.isSpeaking, state])
  
  const addMessage = useCallback((message: ConversationMessage) => {
    setMessages(prev => [...prev, message])
  }, [])
  
  const initializeConversation = useCallback(async () => {
    setState('connecting')
    
    try {
      await navigator.mediaDevices.getUserMedia({ audio: true })
      
      const response = await fetch('/api/eleven-labs/init', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      })
      
      const data = await response.json()
      
      if (data.error) throw new Error(data.error)
      
      await conversation.startSession({ signedUrl: data.signedUrl })
    } catch (error) {
      console.error('Failed to initialize conversation:', error)
      toast({
        title: 'Connection Error',
        description: 'Failed to initialize voice assistant',
        variant: 'destructive',
      })
      setState('error')
    }
  }, [conversation, toast])
  
  const startListening = useCallback(async () => {
    if (state === 'idle' || state === 'error') {
      await initializeConversation()
    } else if (state === 'speaking') {
      conversation.stopSpeaking()
      setState('listening')
    }
  }, [state, initializeConversation, conversation])
  
  const stopListening = useCallback(async () => {
    if (state !== 'idle' && state !== 'error') {
      await conversation.endSession()
      setState('idle')
    }
  }, [state, conversation])
  
  const toggleListening = useCallback(async () => {
    if (state === 'idle' || state === 'error') {
      await startListening()
    } else {
      await stopListening()
    }
  }, [state, startListening, stopListening])
  
  const clearConversation = useCallback(() => {
    setMessages([])
  }, [])
  
  useEffect(() => {
    return () => {
      conversation.endSession().catch(console.error)
    }
  }, [conversation])
  
  return (
    <VoiceAssistantContext.Provider
      value={{
        state,
        messages,
        currentMessage,
        startListening,
        stopListening,
        toggleListening,
        clearConversation,
      }}
    >
      {children}
    </VoiceAssistantContext.Provider>
  )
}

export function useVoiceAssistant() {
  const context = useContext(VoiceAssistantContext)
  
  if (context === undefined) {
    throw new Error('useVoiceAssistant must be used within a VoiceAssistantProvider')
  }
  
  return context
}
```

### 4. API Route Implementation

Create `/app/api/eleven-labs/init/route.ts`:

```typescript
import { NextResponse } from 'next/server'

export async function POST(request: Request) {
  const elevenlabsApiKey = process.env.ELEVEN_LABS_API_KEY
  const agentId = process.env.ELEVEN_LABS_AGENT_ID
  
  if (!elevenlabsApiKey || !agentId) {
    return NextResponse.json(
      { error: 'ElevenLabs credentials not properly configured' },
      { status: 500 }
    )
  }
  
  try {
    const apiUrl = new URL('https://api.elevenlabs.io/v1/convai/conversation/get_signed_url')
    apiUrl.searchParams.set('agent_id', agentId)
    
    const response = await fetch(apiUrl.toString(), {
      headers: { 'xi-api-key': elevenlabsApiKey },
    })
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.detail || response.statusText)
    }
    
    const data = await response.json()
    
    return NextResponse.json({ signedUrl: data.signed_url })
  } catch (error: any) {
    console.error('Error initializing ElevenLabs conversation:', error)
    
    return NextResponse.json(
      { error: `Failed to initialize conversation: ${error.message}` },
      { status: 500 }
    )
  }
}
```

### 5. Typing Effect Hook

Create `/components/voice-assistant/use-typing-effect.ts`:

```typescript
import { useState, useEffect, useRef, useCallback } from 'react'

export function useTypingEffect(text: string, typingSpeed: number = 30) {
  const [displayText, setDisplayText] = useState('')
  const textRef = useRef(text)
  const indexRef = useRef(0)
  
  const resetTyping = useCallback(() => {
    textRef.current = text
    indexRef.current = 0
    setDisplayText('')
  }, [text])
  
  useEffect(() => {
    // Reset when text changes
    resetTyping()
    
    if (!text) return
    
    const interval = setInterval(() => {
      if (indexRef.current < textRef.current.length) {
        setDisplayText(prev => prev + textRef.current.charAt(indexRef.current))
        indexRef.current += 1
      } else {
        clearInterval(interval)
      }
    }, typingSpeed)
    
    return () => clearInterval(interval)
  }, [text, typingSpeed, resetTyping])
  
  return displayText
}
```

### 6. Enhanced LogoButton Component

Update `/components/ui/logo-button.tsx`:

```typescript
"use client"

import { motion } from 'framer-motion'
import { useTheme } from "next-themes"
import { useVoiceAssistant } from "../voice-assistant/voice-assistant-provider"

interface LogoButtonProps {
  onClick?: () => void
  size?: number
  className?: string
}

export function LogoButton({ onClick, size = 36, className = "" }: LogoButtonProps) {
  const { theme } = useTheme()
  const isDarkTheme = theme === "dark"
  const { state, toggleListening } = useVoiceAssistant()
  
  const isActive = state !== 'idle' && state !== 'error'
  
  const handleClick = () => {
    if (onClick) {
      onClick()
    } else {
      toggleListening()
    }
  }
  
  const getAnimationProps = () => {
    switch (state) {
      case 'listening':
        return {
          scale: [1, 1.1, 1],
          opacity: [0.7, 1, 0.7],
          backgroundColor: ['rgba(164, 211, 33, 0.2)', 'rgba(164, 211, 33, 0.4)', 'rgba(164, 211, 33, 0.2)'],
        }
      case 'processing':
        return {
          scale: [1, 1.05, 1],
          opacity: [0.6, 0.8, 0.6],
          backgroundColor: ['rgba(139, 92, 246, 0.2)', 'rgba(139, 92, 246, 0.3)', 'rgba(139, 92, 246, 0.2)'],
        }
      case 'speaking':
        return {
          scale: [1, 1.2, 0.9, 1.1, 1],
          opacity: [0.6, 1, 0.8, 0.9, 0.6],
          backgroundColor: ['rgba(164, 211, 33, 0.2)', 'rgba(164, 211, 33, 0.5)', 'rgba(164, 211, 33, 0.3)', 'rgba(164, 211, 33, 0.4)', 'rgba(164, 211, 33, 0.2)'],
        }
      default:
        return {
          scale: [1, 1.05, 1],
          opacity: [0.5, 0.7, 0.5],
          backgroundColor: ['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.2)', 'rgba(255, 255, 255, 0.1)'],
        }
    }
  }
  
  const animationProps = getAnimationProps()
  
  return (
    <button
      onClick={handleClick}
      className={`relative flex items-center justify-center shadow-sm ${className}`}
      style={{ width: size, height: size }}
      aria-label={isActive ? 'Stop voice assistant' : 'Start voice assistant'}
    >
      {isActive && (
        <motion.div
          className="absolute inset-0 rounded-full"
          animate={animationProps}
          transition={{
            duration: state === 'speaking' ? 0.8 : 1.5,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      )}
      
      {/* SVG icon and animations */}
      <svg
        width={size}
        height={size}
        viewBox="0 0 1024 1024"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="relative z-10 transition-opacity duration-300"
      >
        <g clipPath="url(#clip0_logo)">
          <path
            d="M507.748 413.784L568.657 283.359C571.714 277.036 576.441 273.728 582.935 272.908C584.341 272.755 586.555 272.676 588.352 272.676H680.794L620.265 402.312C617.232 408.647 611.894 412.671 605.425 413.46C604.012 413.594 602.037 413.784 600.239 413.784H507.748Z"
            fill="#A4D321"
          />
          {/* Additional SVG paths */}
        </g>
        <defs>
          <clipPath id="clip0_logo">
            <rect width="1024" height="1024" rx="20" fill="white" />
          </clipPath>
        </defs>
      </svg>
      
      {state === 'listening' && (
        <motion.div 
          className="absolute -inset-3"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        >
          <svg className="w-full h-full" viewBox="0 0 100 100">
            <motion.circle
              cx="50"
              cy="50"
              r="48"
              fill="none"
              strokeWidth="2"
              stroke="#A4D321"
              initial={{ pathLength: 0, rotate: -90 }}
              animate={{ pathLength: 1, rotate: 270 }}
              transition={{
                duration: 10,
                ease: 'linear',
                repeat: Infinity,
              }}
              strokeLinecap="round"
            />
          </svg>
        </motion.div>
      )}
    </button>
  )
}
```

### 7. Application Integration

Update `/app/layout.tsx`:

```typescript
import type React from "react"
import "@/app/globals.css"
import { Inter } from "next/font/google"
import { ThemeProvider } from "@/components/theme-provider"
import { ToastProvider } from "@/components/ui/toast-context"
import { VoiceAssistantProvider } from "@/components/voice-assistant/voice-assistant-provider"
import type { Metadata } from "next"
import Script from "next/script"

const inter = Inter({ subsets: ["latin"] })

// Metadata definition

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* Head content */}
      </head>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="dark" disableSystemTheme>
          <ToastProvider>
            <VoiceAssistantProvider>
              <main className="min-h-screen bg-background">{children}</main>
            </VoiceAssistantProvider>
          </ToastProvider>
        </ThemeProvider>
        <Script
          id="register-service-worker"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js').then(
                    function(registration) {
                      console.log('Service Worker registration successful with scope: ', registration.scope);
                    },
                    function(err) {
                      console.log('Service Worker registration failed: ', err);
                    }
                  );
                });
              }
            `,
          }}
        />
      </body>
    </html>
  )
}
```

## Implementation Plan

1. Dependency Installation
   ```bash
   npm install @11labs/react@latest
   ```

2. Create the necessary files:
   - .env.local
   - /types/voice-assistant.d.ts
   - /components/voice-assistant/voice-assistant-provider.tsx
   - /app/api/eleven-labs/init/route.ts
   - /components/voice-assistant/use-typing-effect.ts

3. Update existing files:
   - /components/ui/logo-button.tsx
   - /app/layout.tsx

4. Remove AssistantDrawerVaul component:
   - Remove references to the component from MobileAIInterface

5. Testing:
   - Verify authentication with ElevenLabs
   - Test voice recognition
   - Test text-to-speech
   - Verify animated feedback
