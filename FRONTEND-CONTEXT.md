# Front-End Context

This document provides a comprehensive inventory of all front-end components, the Tailwind CSS configuration, and design tokens (CSS variables) used across the application. It can serve as a reference when migrating or integrating this design system into another codebase.

---

## 1. Component Structure

### 1.1 Screens (components/screens)

- auth-screen.tsx
- chat-screen.tsx
- contacts-screen.tsx
- corrective-actions-screen.tsx
- dashboard-screen.tsx
- forgot-password-screen.tsx
- help-screen.tsx
- home-screen.tsx
- history-screen.tsx
- inspection-dashboard.tsx
- inspection-form-screen.tsx
- inspection-report-screen.tsx
- knowledge-base-screen.tsx
- login-screen.tsx
- more-menu-screen.tsx
- new-chat-screen.tsx
- profile-screen.tsx
- reports-screen.tsx
- schedule-screen.tsx
- settings-screen.tsx
- signup-screen.tsx
- tasks-screen.tsx
- team-screen.tsx

### 1.2 UI Components (components/ui)

- accordion.tsx
- alert-dialog.tsx
- alert.tsx
- app-header.tsx
- aspect-ratio.tsx
- assistant-drawer-vaul.tsx
- assistant-drawer.tsx
- avatar.tsx
- badge.tsx
- breadcrumb.tsx
- button.tsx
- calendar.tsx
- card.tsx
- carousel.tsx
- chart.tsx
- checkbox.tsx
- collapsible.tsx
- command.tsx
- context-menu.tsx
- dialog.tsx
- drawer.tsx
- dropdown-menu.tsx
- form.tsx
- header-drawer.tsx
- home-indicator.tsx
- hover-card.tsx
- icons.tsx
- input-otp.tsx
- input.tsx
- label.tsx
- loading.tsx
- logo-button.tsx
- menubar.tsx
- navigation-bar.tsx
- navigation-menu.tsx
- openai-assistant-drawer.tsx
- pagination.tsx
- popover.tsx
- progress.tsx
- radio-group.tsx
- resizable.tsx
- screen-layout.tsx
- scroll-area.tsx
- select.tsx
- separator.tsx
- sheet.tsx
- sidebar.tsx
- skeleton.tsx
- slider.tsx
- sonner.tsx
- status-bar.tsx
- swipe-detector.tsx
- swipeable-container.tsx
- switch.tsx
- table.tsx
- tabs.tsx
- textarea.tsx
- toast-context.tsx
- toast.tsx
- toaster.tsx
- toggle-group.tsx
- toggle.tsx
- tooltip.tsx
- use-mobile.tsx
- use-toast.ts

### 1.3 Assistant Components (components/assistant)

- Message.tsx
- TextAnimation.tsx
- assistant-chat.tsx
- useTypingEffect.ts

### 1.4 Chat Components (components/chat)

- chat-window.tsx

### 1.5 Voice Assistant Components (components/voice-assistant)

- use-typing-effect.ts
- voice-assistant-provider.tsx

### 1.6 Root-Level Components (components/*.tsx)

- Auth08.tsx
- Message.tsx
- TextAnimation.tsx
- client-providers.tsx
- db-test.tsx
- error-boundary.tsx
- install-prompt.tsx
- mobile-ai-interface.tsx
- mobile-chat.tsx
- notifications-panel.tsx
- test-askara.tsx
- theme-provider.tsx
- theme-toggle.tsx
- user-profile.tsx

---

## 2. Tailwind CSS Configuration (tailwind.config.js)

```js
/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/!(node_modules)/*.{ts,tsx,js,jsx}",
    "./src/**/*.{ts,tsx}",
    "./*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: { "2xl": "1400px" },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: { DEFAULT: "hsl(var(--primary))", foreground: "hsl(var(--primary-foreground))" },
        secondary: { DEFAULT: "hsl(var(--secondary))", foreground: "hsl(var(--secondary-foreground))" },
        destructive: { DEFAULT: "hsl(var(--destructive))", foreground: "hsl(var(--destructive-foreground))" },
        muted: { DEFAULT: "hsl(var(--muted))", foreground: "hsl(var(--muted-foreground))" },
        accent: { DEFAULT: "hsl(var(--accent))", foreground: "hsl(var(--accent-foreground))" },
        popover: { DEFAULT: "hsl(var(--popover))", foreground: "hsl(var(--popover-foreground))" },
        card: { DEFAULT: "hsl(var(--card))", foreground: "hsl(var(--card-foreground))" },
      },
      borderRadius: { lg: "var(--radius)", md: "calc(var(--radius) - 2px)", sm: "calc(var(--radius) - 4px)" },
      keyframes: {
        "accordion-down": { from: { height: 0 }, to: { height: "var(--radix-accordion-content-height)" } },
        "accordion-up": { from: { height: "var(--radix-accordion-content-height)" }, to: { height: 0 } },
      },
      animation: { "accordion-down": "accordion-down 0.2s ease-out", "accordion-up": "accordion-up 0.2s ease-out" },
    },
  },
  plugins: [require("tailwindcss-animate")],
};
```

---

## 3. Design Tokens (CSS Custom Properties)

### 3.1 Light Theme (`:root`)

```css
:root {
  --background: 0 0% 100%;
  --foreground: 0 0% 3.9%;
  --card: 0 0% 100%;
  --card-foreground: 0 0% 3.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 3.9%;
  --primary: 0 0% 9%;
  --primary-foreground: 0 0% 98%;
  --secondary: 0 0% 96.1%;
  --secondary-foreground: 0 0% 9%;
  --muted: 0 0% 96.1%;
  --muted-foreground: 0 0% 45.1%;
  --accent: 0 0% 96.1%;
  --accent-foreground: 0 0% 9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;
  --border: 0 0% 89.8%;
  --input: 0 0% 89.8%;
  --ring: 0 0% 3.9%;
  --chart-1: 12 76% 61%;
  --chart-2: 173 58% 39%;
  --chart-3: 197 37% 24%;
  --chart-4: 43 74% 66%;
  --chart-5: 27 87% 67%;
  --radius: 0.5rem;
  --sidebar-background: 0 0% 98%;
  --sidebar-foreground: 240 5.3% 26.1%;
  --sidebar-primary: 240 5.9% 10%;
  --sidebar-primary-foreground: 0 0% 98%;
  --sidebar-accent: 240 4.8% 95.9%;
  --sidebar-accent-foreground: 240 5.9% 10%;
  --sidebar-border: 220 13% 91%;
  --sidebar-ring: 217.2 91.2% 59.8%;
}
```

### 3.2 Dark Theme (`.dark`)

```css
.dark {
  --background: 0 0% 3.9%;
  --foreground: 0 0% 98%;
  --card: 0 0% 3.9%;
  --card-foreground: 0 0% 98%;
  --popover: 0 0% 3.9%;
  --popover-foreground: 0 0% 98%;
  --primary: 0 0% 98%;
  --primary-foreground: 0 0% 9%;
  --secondary: 0 0% 14.9%;
  --secondary-foreground: 0 0% 98%;
  --muted: 0 0% 14.9%;
  --muted-foreground: 0 0% 63.9%;
  --accent: 0 0% 14.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --border: 0 0% 14.9%;
  --input: 0 0% 14.9%;
  --ring: 0 0% 83.1%;
  --chart-1: 220 70% 50%;
  --chart-2: 160 60% 45%;
  --chart-3: 30 80% 55%;
  --chart-4: 280 65% 60%;
  --chart-5: 340 75% 55%;
  --sidebar-background: 240 5.9% 10%;
  --sidebar-foreground: 240 4.8% 95.9%;
  --sidebar-primary: 224.3 76.3% 48%;
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 240 3.7% 15.9%;
  --sidebar-accent-foreground: 240 4.8% 95.9%;
  --sidebar-border: 240 3.7% 15.9%;
  --sidebar-ring: 217.2 91.2% 59.8%;
}
```

---

*End of Front-End Context*