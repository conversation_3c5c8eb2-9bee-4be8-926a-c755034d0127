{"DATABASE_URL": {"description": "Neon PostgreSQL connection string", "value": "YOUR_VALUE_HERE"}, "NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY": {"description": "Clerk publishable key (client-side)", "value": "YOUR_VALUE_HERE"}, "CLERK_SECRET_KEY": {"description": "Clerk secret key (server-side)", "value": "YOUR_VALUE_HERE"}, "CLERK_WEBHOOK_SECRET": {"description": "Secret for verifying Clerk webhooks", "value": "YOUR_VALUE_HERE"}, "NEXT_PUBLIC_CLERK_SIGN_IN_URL": {"description": "URL for the sign-in page", "value": "/sign-in"}, "NEXT_PUBLIC_CLERK_SIGN_UP_URL": {"description": "URL for the sign-up page", "value": "/sign-up"}, "NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL": {"description": "URL to redirect to after sign-in", "value": "/"}, "NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL": {"description": "URL to redirect to after sign-up", "value": "/"}, "MCP_PORT": {"description": "Port for MCP server", "value": "3005"}, "NODE_ENV": {"description": "Node environment (development/production)", "value": "production"}}