name: CI Pipeline

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x] # Or your project's target Node.js version

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}

    - name: Set up pnpm
      uses: pnpm/action-setup@v4
      with:
        version: 10 # Specify your desired pnpm version (e.g., 10)
        run_install: false # We will run install manually later

    - name: Get pnpm store directory
      id: pnpm-cache
      shell: bash
      run: |
        echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_OUTPUT
    - name: Cache pnpm store
      uses: actions/cache@v4
      with:
        path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
        key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
        restore-keys: |
          ${{ runner.os }}-pnpm-store-

    - name: Install dependencies
      run: pnpm install --frozen-lockfile

    - name: Lint and Test
      run: pnpm ci

    - name: Run TypeScript Check
      run: pnpm tsc --noEmit

    - name: Run Tests
      run: pnpm test

    - name: Build Next.js Application
      run: pnpm build

    - name: Run Database Migrations
      env:
        DATABASE_URL: ${{ secrets.DATABASE_URL }} # Use secret for connection string
      run: pnpm db:migrate

    - name: Run Database Seed
      env:
        DATABASE_URL: ${{ secrets.DATABASE_URL }} # Use secret for connection string
      run: pnpm db:seed

    # Optional: Upload build artifacts
    - name: Upload Next.js Build Artifacts
      uses: actions/upload-artifact@v4
      with:
        name: nextjs-build-${{ github.run_id }} # Name of the artifact zip file
        path: .next/ # Directory to upload
        retention-days: 7 # Optional: How long to keep the artifact (default is 90)