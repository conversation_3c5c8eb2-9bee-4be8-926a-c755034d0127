/**
 * Retrieves an environment variable and throws an error if it's missing or empty.
 * @param varName The name of the environment variable to retrieve.
 * @returns The value of the environment variable.
 * @throws {Error} If the environment variable is not set or is empty.
 */
export function assertEnv(varName: string): string {
  const value = process.env[varName];

  if (value === undefined || value === null || value === '') {
    throw new Error(`Missing or empty required environment variable: ${varName}`);
  }

  return value;
}