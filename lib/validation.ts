export type ValidationRule = {
  validate: (value: string) => boolean
  message: string
}

export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export const validatePassword = (password: string): boolean => {
  return password.length >= 8
}

export const validateRequired = (value: string): boolean => {
  return value.trim() !== ""
}

export const validatePhone = (phone: string): boolean => {
  // Basic phone validation - can be enhanced for specific formats
  const phoneRegex = /^\+?[0-9\s\-$$$$]{8,}$/
  return phoneRegex.test(phone)
}

export const validateUrl = (url: string): boolean => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

export const validateField = (
  value: string,
  rules: ValidationRule[],
): { isValid: boolean; errorMessage: string | null } => {
  for (const rule of rules) {
    if (!rule.validate(value)) {
      return { isValid: false, errorMessage: rule.message }
    }
  }
  return { isValid: true, errorMessage: null }
}

export const emailRules: ValidationRule[] = [
  {
    validate: validateRequired,
    message: "Email is required",
  },
  {
    validate: validateEmail,
    message: "Please enter a valid email address",
  },
]

export const passwordRules: ValidationRule[] = [
  {
    validate: validateRequired,
    message: "Password is required",
  },
  {
    validate: validatePassword,
    message: "Password must be at least 8 characters",
  },
]

export const phoneRules: ValidationRule[] = [
  {
    validate: validateRequired,
    message: "Phone number is required",
  },
  {
    validate: validatePhone,
    message: "Please enter a valid phone number",
  },
]
