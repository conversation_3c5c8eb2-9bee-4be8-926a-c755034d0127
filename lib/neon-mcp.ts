// Legacy compatibility wrapper for old "@/lib/neon-mcp" import.
// The actual database singleton now lives in "@/app/db" (neon-http driver).
export { db } from '@/app/db';
export * from '@/app/db/schema';

/* Deprecated implementation retained for reference
import { createClient } from '@neondatabase/mcp';
import { drizzle } from 'drizzle-orm/neon-mcp';
import * as schema from '@/app/db/schema';
import fs from 'fs';
import path from 'path';

// Load MCP configuration from mcp.json
const mcpConfigPath = path.join(process.cwd(), 'mcp.json');
let mcpConfig;

try {
  const configFile = fs.readFileSync(mcpConfigPath, 'utf8');
  mcpConfig = JSON.parse(configFile);
  console.log('Loaded MCP configuration from mcp.json');
} catch (error) {
  console.error('Error loading MCP configuration:', error);
  throw new Error('Failed to load MCP configuration');
}

// Create a Neon MCP client
const client = createClient({
  connectionString: mcpConfig.connectionString || process.env.DATABASE_URL,
  mcpServerUrl: mcpConfig.mcpServers['github.com/neondatabase/mcp-server-neon'].url,
});

// Create a Drizzle client
export const db = drizzle(client, { schema });

// Export schema for use in other files
export * from '@/app/db/schema';

// Export the client for direct SQL queries
export { client };

// Function to test the connection
export async function testConnection() {
  try {
    const result = await client.query('SELECT 1 as test');
    if (result && result.rows && result.rows[0] && result.rows[0].test === 1) {
      console.log('✅ Database connection successful');
      return true;
    } else {
      console.error('❌ Database connection failed: Unexpected response');
      return false;
    }
  } catch (error) {
    console.error('❌ Database connection error:', error);
    return false;
  }
}
*/
