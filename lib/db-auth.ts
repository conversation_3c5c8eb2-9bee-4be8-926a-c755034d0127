import { db } from '@/lib/neon-mcp'; // Import Drizzle client with Neon MCP
import { users } from '@/app/db/schema'; // Import users table schema (value and type)
import { eq } from 'drizzle-orm'; // Import Drizzle eq function

// Use the inferred type from the Drizzle schema
export type User = typeof users.$inferSelect;

/**
 * Find a user by email in the database
 */
export const findUserByEmail = async (email: string): Promise<User | null> => {
  try {
    // Use Drizzle query builder to find the user
    const user = await db.query.users.findFirst({
      where: eq(users.email, email),
    });

    return user ?? null; // Return user if found, otherwise null
  } catch (error) {
    console.error('Error finding user by email:', error);
    return null;
  }
};

/**
 * Get all users from the database
 */
export const getAllUsers = async (): Promise<User[]> => {
  try {
    // Use Drizzle query builder to get all users
    const allUsers = await db.select().from(users);
    return allUsers;
  } catch (error) {
    console.error('Error getting all users:', error);
    return [];
  }
};

/**
 * Add a temporary password to a user for testing purposes
 */
export const addPasswordToUser = async (email: string, password: string): Promise<boolean> => {
  try {
    // Find the user first
    const user = await findUserByEmail(email);

    if (!user) {
      console.error(`User with email ${email} not found`);
      return false;
    }

    // Update the user's password
    await db.update(users)
      .set({ password: password })
      .where(eq(users.email, email));

    // Check if the update was successful
    // Note: In PostgreSQL, the result doesn't have rowCount directly
    // We'll consider it successful if no error was thrown
    console.log(`Password updated for user ${email}`);
    return true;
  } catch (error) {
    console.error('Error adding password to user:', error);
    return false;
  }
};

/**
 * Verify a user's credentials
 */
export const verifyCredentials = async (email: string, password: string): Promise<User | null> => {
  try {
    const user = await findUserByEmail(email);

    if (!user) {
      // User not found by email
      console.log(`User with email ${email} not found`);
      return null;
    }

    // Check if the user has a password
    if (!user.password) {
      console.log(`User ${email} has no password set`);
      return null;
    }

    // In a real application, you would use a secure password comparison (e.g., bcrypt.compare)
    // For this demo, we're comparing plain text passwords
    if (user.password !== password) {
      console.log(`Invalid password for user ${email}`);
      return null;
    }

    // Update last login timestamp
    await db.update(users)
      .set({ last_login: new Date() })
      .where(eq(users.id, user.id));

    console.log(`User ${email} authenticated successfully`);

    // In a real application, we would remove the password before returning the user
    // For this demo, we'll just return the user as is
    return user;
  } catch (error) {
    console.error('Error verifying credentials:', error);
    return null;
  }
};
