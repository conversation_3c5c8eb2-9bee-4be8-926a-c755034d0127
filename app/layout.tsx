import type React from "react"
import "@/app/globals.css"
import { Inter } from "next/font/google"
import type { Metada<PERSON> } from "next"
import Script from "next/script"
import { Toaster } from "@/components/ui/sonner"
import { ClientProviders } from "@/components/client-providers"
import { ClerkProvider } from '@clerk/nextjs'
import { dark } from '@clerk/themes'

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "AskARA - Property Service Assistant",
  description: "Your AI-powered property service assistant",
  manifest: "/manifest.webmanifest",
  appleWebApp: {
    capable: true,
    statusBarStyle: "black-translucent",
    title: "AskARA",
  },
  generator: 'v0.dev'
}

export const viewport = {
  themeColor: "#A4D321",
  width: "device-width",
  initialScale: 1,
  maximumScale: 5,
  minimumScale: 1,
  userScalable: true,
  viewportFit: "cover"
}

const clerkAppearance = {
  baseTheme: dark,
  variables: {
    colorPrimary: '#A4D321',
    colorTextOnPrimaryBackground: '#000',
    colorBackground: '#121212',
    colorInputBackground: '#2A2A2A',
    colorTextSecondary: '#E0E0E0',
    colorInputText: '#FFFFFF',
  },
  elements: {
    formButtonPrimary: 'bg-[#A4D321] hover:bg-[#BFE550] text-black',
    card: 'bg-black',
    navbar: 'bg-black',
    headerTitle: 'text-white',
    headerSubtitle: 'text-gray-400',
    organizationSwitcherTrigger: 'bg-zinc-800 text-white hover:bg-zinc-700',
  }
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <ClerkProvider
          appearance={clerkAppearance}
          publishableKey={process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY}
          // Organization options
          organizationOptions={{
            defaultRole: "member",
            afterCreateOrganizationUrl: "/",
            afterLeaveOrganizationUrl: "/",
            afterSelectOrganizationUrl: "/",
          }}
        >
          <ClientProviders>
            {children}
            <Toaster />
          </ClientProviders>
        </ClerkProvider>
      </body>
    </html>
  )
}
