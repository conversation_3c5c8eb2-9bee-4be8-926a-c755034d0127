"use server"

// Server Actions for creating and deleting the current user's message
// These follow the Clerk + Neon + Drizzle demo style but adapt to our richer schema.
// A single message per user constraint is NOT enforced – the caller can decide.

import { auth } from "@clerk/nextjs/server";
import { db } from "@/app/db";
import { messages } from "@/app/db/schema";
import { eq } from "drizzle-orm";
import crypto from "crypto";

export async function createUserMessage(formData: FormData) {
  const { userId } = auth();
  if (!userId) throw new Error("User not found");

  const message = formData.get("message") as string | null;
  if (!message) throw new Error("Message is required");

  await db.insert(messages).values({
    id: crypto.randomUUID(),
    sender_id: userId,
    content_transcript: message,
    role: "user",
    status: "saved",
    type: "text",
    created_at: new Date(),
  });
}

export async function deleteUserMessage() {
  const { userId } = auth();
  if (!userId) throw new Error("User not found");

  // Delete all messages by this user (demo behaviour)
  await db.delete(messages).where(eq(messages.sender_id, userId));
}
