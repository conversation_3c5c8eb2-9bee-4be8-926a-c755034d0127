import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { organizationService } from '@/app/services/organization-service';

export const dynamic = 'force-dynamic'; // Force dynamic rendering

/**
 * GET /api/organizations
 * Get organizations for the current user
 */
export async function GET(_req: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const organizations = await organizationService.getOrganizationProperties();
    return NextResponse.json({ organizations });
  } catch (error) {
    console.error('Error getting organizations:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
