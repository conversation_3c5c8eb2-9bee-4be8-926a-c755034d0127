import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { ConvexHttpClient } from 'convex/browser';
import { api } from '@/convex/_generated/api';

const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

export const dynamic = 'force-dynamic'; // Force dynamic rendering

/**
 * GET /api/organizations
 * Get organizations for the current user
 */
export async function GET(_req: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get current organization from Convex
    const organization = await convex.query(api.organizations.getCurrentOrganization, {});
    
    if (!organization) {
      return NextResponse.json({ organizations: [] });
    }

    return NextResponse.json({ organizations: [organization] });
  } catch (error) {
    console.error('Error getting organizations:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
