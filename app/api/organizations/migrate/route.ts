import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { organizationService } from '@/app/services/organization-service';

/**
 * POST /api/organizations/migrate
 * Migrate unassigned properties and contracts to the current organization
 * This endpoint is for admin use during the transition to multi-tenant
 */
export async function POST(req: NextRequest) {
  try {
    const { userId, orgId, orgRole } = await auth();

    if (!userId || !orgId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only admins can perform migration
    if (orgRole !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const data = await req.json();
    const { migrateProperties, migrateContracts } = data;

    let propertiesResult = false;
    let contractsResult = false;

    if (migrateProperties) {
      propertiesResult = await organizationService.migratePropertiesToOrganization(orgId);
    }

    if (migrateContracts) {
      contractsResult = await organizationService.migrateContractsToOrganization(orgId);
    }

    return NextResponse.json({
      success: true,
      results: {
        propertiesResult,
        contractsResult,
      },
    });
  } catch (error) {
    console.error('Error migrating data:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
