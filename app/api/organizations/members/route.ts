import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { organizationService } from '@/app/services/organization-service';

/**
 * GET /api/organizations/members
 * Get members of the current organization
 */
export async function GET(_req: NextRequest) {
  try {
    const { userId, orgId, orgRole } = await auth();

    if (!userId || !orgId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const members = await organizationService.getOrganizationMembers(orgId);
    return NextResponse.json({ members });
  } catch (error) {
    console.error('Error getting organization members:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST /api/organizations/members
 * Add a new member to the current organization
 */
export async function POST(req: NextRequest) {
  try {
    const { userId, orgId, orgRole } = await auth();

    if (!userId || !orgId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only admins can add members
    if (orgRole !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const data = await req.json();
    const { memberId, role } = data;

    if (!memberId) {
      return NextResponse.json({ error: 'Member ID is required' }, { status: 400 });
    }

    const membershipId = await organizationService.addOrganizationMember(
      orgId,
      memberId,
      role || 'member'
    );

    return NextResponse.json({ membershipId });
  } catch (error) {
    console.error('Error adding organization member:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
