import { NextResponse } from 'next/server';
import crypto from 'crypto';

export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';

export async function GET(_request: Request) {
  try {
    // Get environment variables
    const streamApiKey = process.env.STREAM_API_KEY;
    const openAiApiKey = process.env.OPENAI_API_KEY;

    // Check if required environment variables are set
    if (!streamApiKey || !openAiApiKey) {
      return NextResponse.json(
        { error: "Missing required environment variables" },
        { status: 500 }
      );
    }

    // Generate a shorter UUID for callId (first 12 chars)
    const callId = crypto.randomUUID().replace(/-/g, '').substring(0, 12);
    // Generate a shorter UUID for userId (first 8 chars with prefix)
    const userId = `user-${crypto.randomUUID().replace(/-/g, '').substring(0, 8)}`;
    const callType = "default";

    // In a real implementation, you would generate a token here
    // For now, we'll just return the necessary information
    return NextResponse.json({
      apiKey: streamApiKey,
      callType,
      callId,
      userId,
      openAiApiKey
    });
  } catch (error) {
    console.error("Error generating credentials:", error);
    return NextResponse.json(
      { error: "Failed to generate credentials" },
      { status: 500 }
    );
  }
}
