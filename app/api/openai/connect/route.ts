import { NextResponse } from 'next/server';

export const runtime = 'edge';
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { callType, callId } = body;

    // In a real implementation, you would connect to the OpenAI agent here
    // For now, we'll just return a success response
    return NextResponse.json({ 
      ok: true,
      message: "OpenAI agent connected successfully",
      callId,
      callType
    });
  } catch (error) {
    console.error("Error connecting to OpenAI agent:", error);
    return NextResponse.json(
      { error: "Failed to connect to OpenAI agent" },
      { status: 500 }
    );
  }
}
