import { NextResponse } from 'next/server'

export async function POST() {
  const elevenlabsApiKey = process.env.ELEVEN_LABS_API_KEY
  const agentId = process.env.ELEVEN_LABS_AGENT_ID
  
  if (!elevenlabsApiKey || !agentId) {
    return NextResponse.json(
      { error: 'ElevenLabs credentials not properly configured' },
      { status: 500 }
    )
  }
  
  try {
    const apiUrl = new URL('https://api.elevenlabs.io/v1/convai/conversation/get_signed_url')
    apiUrl.searchParams.set('agent_id', agentId)
    
    const response = await fetch(apiUrl.toString(), {
      headers: { 
        'xi-api-key': elevenlabsApiKey,
        'Content-Type': 'application/json'
      },
    })
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.detail || response.statusText)
    }
    
    const data = await response.json()
    
    return NextResponse.json({ signedUrl: data.signed_url })
  } catch (error: unknown) { // Type error as unknown
    console.error('Error initializing ElevenLabs conversation:', error)
    const errorMessage = error instanceof Error ? error.message : String(error);
    return NextResponse.json(
      { error: `Failed to initialize conversation: ${errorMessage}` },
      { status: 500 }
    )
  }
}
