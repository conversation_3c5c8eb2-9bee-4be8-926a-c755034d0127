import { NextResponse } from 'next/server';
import { db } from '@/app/db';
import { users } from '@/app/db/schema';

export async function GET() {
  try {
    // Test the database connection by counting users
    const userCount = await db.select({ count: { value: users.id } })
      .from(users)
      .then(result => result[0]?.count?.value || 0);
    
    return NextResponse.json({ 
      status: 'success', 
      message: 'Database connection successful',
      userCount
    });
  } catch (error) {
    console.error('Database connection error:', error);
    return NextResponse.json({ 
      status: 'error', 
      message: 'Database connection failed',
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
