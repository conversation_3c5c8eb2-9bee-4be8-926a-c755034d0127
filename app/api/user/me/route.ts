export const dynamic = 'force-dynamic';
import { NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'; // Added Clerk import
import { db } from '@/app/db'; // Import Drizzle client
import { eq } from 'drizzle-orm'; // Import eq operator
import { users } from '@/app/db/schema'; // Import users table schema

// Removed Neon initialization

export async function GET(_request: Request) {
  try {
    const { userId } = await auth(); // Use Clerk auth

    if (!userId) {
      // Use NextResponse for consistency
      return new Response("Unauthorized", { status: 401 });
    }
    
    // Query the database for the user using Drizzle ORM
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
      columns: { // Explicitly select columns to match original query
        id: true,
        name: true,
        role: true,
        department: true,
        email: true,
        avatar: true,
      }
    });

    if (!user) { // Check if user is null or undefined
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }
    // No need for users[0] as find<PERSON>irst returns a single object or null
    
    return NextResponse.json(user)
  } catch (error) {
    console.error('Error fetching current user:', error)
    return NextResponse.json(
      { error: 'Failed to fetch user profile' },
      { status: 500 }
    )
  }
}
