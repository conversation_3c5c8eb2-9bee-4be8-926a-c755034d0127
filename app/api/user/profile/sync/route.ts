import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/app/db';
import { users } from '@/app/db/schema';
import { eq, and } from 'drizzle-orm';
import { v4 as uuidv4 } from 'uuid';
export async function POST(req: NextRequest) {
  try {
    // Get authenticated user ID
    const { userId, orgId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const data = await req.json();
    const { user, organization } = data;

    if (!user) {
      return NextResponse.json({ error: 'Invalid request' }, { status: 400 });
    }

    // Check if user exists in database
    const existingUser = await db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (existingUser) {
      // Update existing user
      const updateUserData = {
        name: user.name || existingUser.name,
        email: user.email || existingUser.email,
        // role: existingUser.role, // Don't overwrite existing role
        // department: existingUser.department, // Don't overwrite existing department
        avatar: user.avatar, // Map from input (might be null/undefined)
        last_login: new Date(),
      } as Partial<typeof users.$inferInsert>;
      // Remove undefined keys to avoid setting null unintentionally
      Object.keys(updateUserData).forEach(key => updateUserData[key as keyof typeof updateUserData] === undefined && delete updateUserData[key as keyof typeof updateUserData]);

      if (Object.keys(updateUserData).length > 0) { // Only update if there are changes
        const result = await db.update(users)
          .set(updateUserData)
          .where(eq(users.id, userId))
          .returning();
        return NextResponse.json({ success: true, user: result[0] });
      } else {
        return NextResponse.json({ success: true, user: existingUser }); // No changes needed
      }

    } else {
      // Create new user
      const newUserData = {
        id: userId,
        name: user.name || 'User',
        email: user.email || '',
        role: 'User',
        department: 'General',
        password: 'clerk-auth', // Not used with Clerk but required by schema
        avatar: user.avatar || null,
        created_at: new Date(),
        last_login: new Date(),
      };
      const result = await db.insert(users)
        .values(newUserData)
        .returning();

      return NextResponse.json({ success: true, user: result[0] });
    }
  } catch (error) {
    console.error('Error syncing user profile:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
