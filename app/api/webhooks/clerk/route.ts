import { NextRequest, NextResponse } from 'next/server';
import { WebhookEvent } from '@clerk/nextjs/server';
import { Webhook } from 'svix';
import { headers } from 'next/headers';
import { db } from '@/app/db';
import { users } from '@/app/db/schema';
import { eq, and } from 'drizzle-orm';
import { v4 as uuidv4 } from 'uuid';
export async function POST(req: NextRequest) {
  // Get the headers
  const headerPayload = headers();
  const svix_id = headerPayload.get('svix-id');
  const svix_timestamp = headerPayload.get('svix-timestamp');
  const svix_signature = headerPayload.get('svix-signature');

  // If there are no headers, error out
  if (!svix_id || !svix_timestamp || !svix_signature) {
    console.error('Error: Missing svix headers', { svix_id, svix_timestamp, svix_signature });
    return new NextResponse('Error: Missing svix headers', { status: 400 });
  }

  // Get the body
  const payload = await req.json();
  const body = JSON.stringify(payload);

  // Create a new Svix instance with your webhook secret
  const webhookSecret = process.env.CLERK_WEBHOOK_SECRET;
  
  if (!webhookSecret) {
    console.error('Error: CLERK_WEBHOOK_SECRET is not defined');
    return new NextResponse('Error: CLERK_WEBHOOK_SECRET is not defined', { status: 500 });
  }

  const wh = new Webhook(webhookSecret);

  let evt: WebhookEvent;

  // Verify the webhook
  try {
    evt = wh.verify(body, {
      'svix-id': svix_id,
      'svix-timestamp': svix_timestamp,
      'svix-signature': svix_signature,
    }) as WebhookEvent;
  } catch (err) {
    console.error('Error verifying webhook:', err);
    return new NextResponse('Error verifying webhook', { status: 400 });
  }

  // Handle the webhook
  const eventType = evt.type;
  console.log(`Processing webhook event: ${eventType}`);
  
  // Handle USER events
  if (eventType === 'user.created') {
    // A new user was created in Clerk
    const { id, email_addresses, first_name, last_name, image_url, phone_numbers } = evt.data;
    
    try {
      // Check if user already exists (should not, but just in case)
      const existingUser = await db.query.users.findFirst({
        where: eq(users.id, id),
      });
      
      if (existingUser) {
        console.log(`User ${id} already exists, updating instead of creating`);
        
        const updateUserData = {
          name: `${first_name || ''} ${last_name || ''}`.trim(),
          email: email_addresses[0]?.email_address || existingUser.email,
          avatar: image_url || existingUser.avatar,
          phone: phone_numbers?.[0]?.phone_number || existingUser.phone,
          last_login: new Date(),
        };
        // Remove undefined keys
        Object.keys(updateUserData).forEach(key => updateUserData[key as keyof typeof updateUserData] === undefined && delete updateUserData[key as keyof typeof updateUserData]);
        
        await db.update(users)
          .set(updateUserData)
          .where(eq(users.id, id));
      } else {
        // Create the user in our database
        const newUserData = {
          id,
          name: `${first_name || ''} ${last_name || ''}`.trim(),
          email: email_addresses[0]?.email_address || '',
          role: 'User',
          department: 'General',
          password: 'clerk-auth',
          avatar: image_url || null,
          phone: phone_numbers?.[0]?.phone_number || null,
          created_at: new Date(),
          last_login: new Date(),
        };
        await db.insert(users).values(newUserData);
      }
      
      console.log(`User processed successfully: ${id}`);
    } catch (error) {
      console.error('Error processing user:', error);
      return new NextResponse('Error processing user', { status: 500 });
    }
  } else if (eventType === 'user.updated') {
    // A user was updated in Clerk
    const { id, email_addresses, first_name, last_name, image_url, phone_numbers } = evt.data;
    
    try {
      // First check if user exists
      const existingUser = await db.query.users.findFirst({
        where: eq(users.id, id),
      });
      
      if (existingUser) {
        // Update the user in our database
        const updateUserData = {
          name: `${first_name || ''} ${last_name || ''}`.trim(),
          email: email_addresses[0]?.email_address || existingUser.email,
          avatar: image_url || existingUser.avatar,
          phone: phone_numbers?.[0]?.phone_number || existingUser.phone,
          last_login: new Date(),
        };
        // Remove undefined keys
        Object.keys(updateUserData).forEach(key => updateUserData[key as keyof typeof updateUserData] === undefined && delete updateUserData[key as keyof typeof updateUserData]);
        
        await db.update(users)
          .set(updateUserData)
          .where(eq(users.id, id));
      } else {
        // User doesn't exist in our database, create them
        const newUserData = {
          id,
          name: `${first_name || ''} ${last_name || ''}`.trim(),
          email: email_addresses[0]?.email_address || '',
          role: 'User',
          department: 'General',
          password: 'clerk-auth',
          avatar: image_url || null,
          phone: phone_numbers?.[0]?.phone_number || null,
          created_at: new Date(),
          last_login: new Date(),
        };
        await db.insert(users).values(newUserData);
      }
      
      console.log(`User updated: ${id}`);
    } catch (error) {
      console.error('Error updating user:', error);
      return new NextResponse('Error updating user', { status: 500 });
    }
  } else if (eventType === 'user.deleted') {
    // A user was deleted in Clerk
    const { id } = evt.data;
    
    try {
      // Delete the user from our database
      await db.delete(users).where(eq(users.id, id));
      
      console.log(`User deleted: ${id}`);
    } catch (error) {
      console.error('Error deleting user:', error);
      return new NextResponse('Error deleting user', { status: 500 });
    }
  } else if (eventType === 'session.created') {
    // A new session was created - update last login time
    const { user_id } = evt.data;
    
    if (user_id) {
      try {
        await db.update(users)
          .set({
            last_login: new Date(),
          } as Partial<typeof users.$inferInsert>)
          .where(eq(users.id, user_id));
        
        console.log(`User login updated: ${user_id}`);
      } catch (error) {
        console.error('Error updating user login time:', error);
        // Don't return an error for this - it's not critical
      }
    }
  } 

  return NextResponse.json({ success: true });
}
