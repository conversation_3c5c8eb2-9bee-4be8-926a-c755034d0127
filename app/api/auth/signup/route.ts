import { NextResponse } from 'next/server';
import { addUser, findUserByEmail } from '@/lib/db-signup';

export async function POST(request: Request) {
  try {
    const { email, password, name, role, department, phone } = await request.json();

    if (!email || !password) {
      return NextResponse.json({ error: 'Email and password are required' }, { status: 400 });
    }

    // Basic email format validation (can be improved)
    if (!/\S+@\S+\.\S+/.test(email)) {
       return NextResponse.json({ error: 'Invalid email format' }, { status: 400 });
    }

    // Basic password length check (can be improved)
    if (password.length < 6) {
        return NextResponse.json({ error: 'Password must be at least 6 characters long' }, { status: 400 });
    }

    console.log(`Signup attempt for email: ${email}`); // Log attempt

    const existingUser = await findUserByEmail(email);
    if (existingUser) {
      console.log(`Signup failed: User already exists - ${email}`);
      return NextResponse.json({ error: 'User already exists' }, { status: 409 }); // 409 Conflict
    }

    const newUser = await addUser({
      email,
      password,
      name: name || email.split('@')[0], // Use part of email as name if not provided
      role: role || 'User',
      department: department || 'General',
      phone: phone || null
    });

    if (!newUser) {
        // This case should technically not be reached if findUserByEmail works correctly,
        // but added for robustness.
        console.error(`Signup failed: Could not add user - ${email}`);
        return NextResponse.json({ error: 'Failed to create user' }, { status: 500 });
    }

    console.log(`Signup successful for user: ${newUser.email}, ID: ${newUser.id}`);

    // Return only necessary info, exclude password
    const { password: _password, ...userWithoutPassword } = newUser;
    return NextResponse.json(userWithoutPassword, { status: 201 }); // 201 Created

  } catch (error) {
    console.error('Signup error:', error);
    // Check if the error is due to invalid JSON parsing
    if (error instanceof SyntaxError) {
        return NextResponse.json({ error: 'Invalid request body' }, { status: 400 });
    }
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}