export const dynamic = 'force-dynamic';
import { NextRequest, NextResponse } from 'next/server';
import { authService, apiService } from '@/app/services';

export async function GET(req: NextRequest): Promise<NextResponse> {
  return apiService.handleAuthenticatedRequest(req, async () => {
    const user = await authService.getCurrentUser();
    
    if (!user) {
      throw new Error('User not found');
    }
    
    // Remove password from response
    const { password: _password, ...userWithoutPassword } = user;
    
    return { user: userWithoutPassword };
  });
}
