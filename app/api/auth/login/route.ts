import { NextRequest, NextResponse } from 'next/server';
import { authService, apiService } from '@/app/services';

interface LoginBody {
  email?: string;
  password?: string;
}

export async function POST(req: NextRequest): Promise<NextResponse> {
  return apiService.handlePublicRequest(req, async (data: unknown) => {
    const { email, password } = data as LoginBody;

    if (!email || !password) {
      throw new Error('Email and password are required');
    }

    console.log(`Login attempt for email: ${email}`);

    const result = await authService.login(email, password);

    if (!result) {
      console.log(`Login failed: Invalid credentials for email - ${email}`);
      throw new Error('Invalid email or password');
    }

    console.log(`Login successful for user: ${result.user.email}, ID: ${result.user.id}`);

    // Set auth token cookie
    authService.setAuthCookie(result.token);

    // Remove password from response
    const { password: _password, ...userWithoutPassword } = result.user;
    return { message: 'Login successful', user: userWithoutPassword };
  });
}