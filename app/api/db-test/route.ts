import { NextResponse } from 'next/server';
import { db } from '@/app/db';
import { users } from '@/app/db/schema';

export async function GET() {
  try {
    // Query the database for users
    const allUsers = await db.select({
      id: users.id,
      name: users.name,
      email: users.email,
      role: users.role,
      department: users.department,
      created_at: users.created_at,
    }).from(users).limit(10);
    
    // Return the users as JSON
    return NextResponse.json({
      success: true,
      message: 'Database connection successful',
      userCount: allUsers.length,
      users: allUsers,
    });
  } catch (error) {
    console.error('Error connecting to database:', error);
    
    // Return an error response
    return NextResponse.json({
      success: false,
      message: 'Database connection failed',
      error: error instanceof Error ? error.message : String(error),
    }, { status: 500 });
  }
}
