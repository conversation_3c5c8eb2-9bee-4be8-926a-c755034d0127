import { NextRequest, NextResponse } from 'next/server';
import { userRepository } from '@/app/repositories';

export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Get query parameters
    const { searchParams } = new URL(req.url);
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');
    const search = searchParams.get('search');
    const role = searchParams.get('role');
    const department = searchParams.get('department');
    
    let users;
    
    if (search) {
      // Search users by name, email, or department
      users = await userRepository.search(search, { limit, offset });
    } else if (role) {
      // Get users by role
      users = await userRepository.findByRole(role, { limit, offset });
    } else if (department) {
      // Get users by department
      users = await userRepository.findByDepartment(department, { limit, offset });
    } else {
      // Get all users
      users = await userRepository.getAll({ limit, offset });
    }
    
    // Remove passwords from response
    const usersWithoutPasswords = users.map(user => {
      const { password: _password, ...userWithoutPassword } = user;
      return userWithoutPassword;
    });
    
    return NextResponse.json({ users: usersWithoutPasswords });
  } catch (error) {
    console.error('Error getting users:', error);
    
    if (error instanceof Error) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }
    
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    const data = await req.json();
    const { name, email, password, role, department, phone } = data;
    
    if (!name || !email || !password) {
      return NextResponse.json({ error: 'Name, email, and password are required' }, { status: 400 });
    }
    
    // Check if user already exists
    const existingUser = await userRepository.findByEmail(email);
    
    if (existingUser) {
      return NextResponse.json({ error: 'User with this email already exists' }, { status: 400 });
    }
    
    // Create user
    const user = await userRepository.create({
      name,
      email,
      password,
      role: role || 'User',
      department: department || 'General',
      phone: phone || null,
      avatar: null,
      preferences: null,
      created_at: new Date(),
      last_login: null,
    });
    
    // Remove password from response
    const { password: _password, ...userWithoutPassword } = user;
    
    return NextResponse.json({ user: userWithoutPassword }, { status: 201 });
  } catch (error) {
    console.error('Error creating user:', error);
    
    if (error instanceof Error) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }
    
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
