import { NextRequest, NextResponse } from 'next/server';
import { authService } from './auth-service';

/**
 * API service for handling API requests
 */
export class ApiService {
  /**
   * Handle an authenticated API request
   */
  async handleAuthenticatedRequest<T>(
    req: NextRequest,
    handler: (userId: string, data: unknown) => Promise<T> // Change data type to unknown
  ): Promise<NextResponse> {
    try {
      // Check if user is authenticated
      const user = await authService.getCurrentUser();
      
      if (!user) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }
      
      // Parse request body
      let data = {};
      
      if (req.method !== 'GET') {
        try {
          data = await req.json();
        } catch (_error) { // Prefix unused variable
          // If request body is empty or invalid JSON, use empty object
        }
      }
      
      // Call handler with user ID and data
      const result = await handler(user.id, data);
      
      return NextResponse.json(result);
    } catch (error: unknown) { // Type error as unknown
      console.error('Error handling authenticated request:', error);
      
      if (error instanceof Error) {
        return NextResponse.json({ error: error.message }, { status: 400 });
      }
      
      return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
    }
  }

  /**
   * Handle a role-based API request
   */
  async handleRoleBasedRequest<T>(
    req: NextRequest,
    role: string,
    handler: (userId: string, data: unknown) => Promise<T> // Change data type to unknown
  ): Promise<NextResponse> {
    try {
      // Check if user is authenticated
      const user = await authService.getCurrentUser();
      
      if (!user) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }
      
      // Check if user has the required role
      if (user.role !== role) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }
      
      // Parse request body
      let data = {};
      
      if (req.method !== 'GET') {
        try {
          data = await req.json();
        } catch (_error) { // Prefix unused variable
          // If request body is empty or invalid JSON, use empty object
        }
      }
      
      // Call handler with user ID and data
      const result = await handler(user.id, data);
      
      return NextResponse.json(result);
    } catch (error: unknown) { // Type error as unknown
      console.error('Error handling role-based request:', error);
      
      if (error instanceof Error) {
        return NextResponse.json({ error: error.message }, { status: 400 });
      }
      
      return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
    }
  }

  /**
   * Handle a public API request
   */
  async handlePublicRequest<T>(
    req: NextRequest,
    handler: (data: unknown) => Promise<T> // Change data type to unknown
  ): Promise<NextResponse> {
    try {
      // Parse request body
      let data = {};
      
      if (req.method !== 'GET') {
        try {
          data = await req.json();
        } catch (_error) { // Prefix unused variable
          // If request body is empty or invalid JSON, use empty object
        }
      }
      
      // Call handler with data
      const result = await handler(data);
      
      return NextResponse.json(result);
    } catch (error: unknown) { // Type error as unknown
      console.error('Error handling public request:', error);
      
      if (error instanceof Error) {
        return NextResponse.json({ error: error.message }, { status: 400 });
      }
      
      return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
    }
  }
}

// Export a singleton instance
export const apiService = new ApiService();
