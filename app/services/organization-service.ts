import { auth } from '@clerk/nextjs/server';
import { db } from '@/app/db';
import { eq, and, isNull } from 'drizzle-orm';
import { properties, contract_specifications } from '@/app/db/schema';
import { v4 as uuidv4 } from 'uuid';

/**
 * Service for organization-related operations
 */
export class OrganizationService {

  /**
   * Get properties for the current organization
   */
  async getOrganizationProperties() {
    try {
      const { orgId } = await auth();
      
      if (!orgId) {
        return [];
      }

      return await db.query.properties.findMany({
        where: eq(properties.organization_id, orgId),
      });
    } catch (error) {
      console.error('Error getting organization properties:', error);
      return [];
    }
  }

  /**
   * Get contracts for the current organization
   */
  async getOrganizationContracts() {
    try {
      const { orgId } = await auth();
      
      if (!orgId) {
        return [];
      }

      return await db.query.contract_specifications.findMany({
        where: eq(contract_specifications.organization_id, orgId),
      });
    } catch (error) {
      console.error('Error getting organization contracts:', error);
      return [];
    }
  }

  /**
   * Update all properties with no organization to belong to the specified organization
   * This is a helper method for migrating existing data
   */
  async migratePropertiesToOrganization(orgId: string) {
    try {
      await db.update(properties)
        .set({ organization_id: orgId } as Partial<typeof properties.$inferInsert>)
        .where(isNull(properties.organization_id));
      
      return true;
    } catch (error) {
      console.error('Error migrating properties:', error);
      throw error;
    }
  }

  /**
   * Update all contracts with no organization to belong to the specified organization
   * This is a helper method for migrating existing data
   */
  async migrateContractsToOrganization(orgId: string) {
    try {
      await db.update(contract_specifications)
        .set({ organization_id: orgId } as Partial<typeof contract_specifications.$inferInsert>)
        .where(isNull(contract_specifications.organization_id));
      
      return true;
    } catch (error) {
      console.error('Error migrating contracts:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const organizationService = new OrganizationService();