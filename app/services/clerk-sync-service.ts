import { auth, clerkClient } from '@clerk/nextjs/server';
import { db } from '@/app/db';
import { users } from '@/app/db/schema';
import type { User } from '@/app/db/schema';
import { eq } from 'drizzle-orm';

/**
 * Service to sync Clerk user data with our database
 */
export class ClerkSyncService {
  /**
   * Sync the current Clerk user with our database
   */
  async syncCurrentUser() {
    try {
      const { userId } = await auth();

      if (!userId) {
        return null;
      }

      // Get the user from Clerk
      const clerkUser = await clerkClient.users.getUser(userId);

      // Check if the user exists in our database
      const existingUser = await db.query.users.findFirst({
        where: eq(users.id, userId),
      });

      if (existingUser) {
        // Update the user
        const updateData: Partial<User> = {
          name: `${clerkUser.firstName || ''} ${clerkUser.lastName || ''}`.trim(),
          email: clerkUser.emailAddresses[0]?.emailAddress || '',
          role: existingUser.role || 'User',
          department: existingUser.department || 'General',
          avatar: clerkUser.imageUrl || null,
          phone: clerkUser.phoneNumbers[0]?.phoneNumber || '',
          last_login: new Date(),
        };
        
        return await db.update(users)
          .set(updateData)
          .where(eq(users.id, userId))
          .returning();
      } else {
        // Create the user
        const newUser: User = {
          id: userId,
          name: `${clerkUser.firstName || ''} ${clerkUser.lastName || ''}`.trim(),
          email: clerkUser.emailAddresses[0]?.emailAddress || '',
          role: 'User',
          department: 'General',
          password: 'clerk-auth', // Not used with Clerk but required by schema
          avatar: clerkUser.imageUrl || null,
          phone: clerkUser.phoneNumbers[0]?.phoneNumber || '',
          preferences: {},
          created_at: new Date(),
          last_login: new Date(),
        };
        
        return await db.insert(users)
          .values(newUser)
          .returning();
      }
    } catch (error) {
      console.error('Error syncing Clerk user:', error);
      return null;
    }
  }
}

// Export a singleton instance
export const clerkSyncService = new ClerkSyncService();
