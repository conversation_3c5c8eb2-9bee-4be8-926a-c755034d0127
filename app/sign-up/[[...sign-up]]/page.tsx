import { SignUp } from "@clerk/nextjs";

export default function Page() {
  return (
    <div className="flex items-center justify-center min-h-screen bg-black p-4">
      <div className="w-full max-w-md">
        <SignUp 
          appearance={{
            elements: {
              formButtonPrimary: 'bg-gradient-to-r from-[#3D4D61] to-[#A4D321] hover:from-[#A4D321] hover:to-[#3D4D61] text-sm normal-case',
              card: 'bg-zinc-900 border-2 border-zinc-800/50 rounded-3xl shadow-[0_24px_48px_-12px] shadow-black/30',
              headerTitle: 'text-white',
              headerSubtitle: 'text-zinc-400',
              formFieldInput: 'h-12 bg-zinc-800/50 border-zinc-700/50 text-white',
              footerActionLink: 'text-[#A4D321] hover:text-[#8fb81d]',
              socialButtonsIconButton: 'bg-zinc-800/50 hover:bg-zinc-700/50 border-zinc-700/50 hover:border-zinc-600/50 text-white',
              socialButtonsBlockButton: 'bg-zinc-800/50 hover:bg-zinc-700/50 border-zinc-700/50 hover:border-zinc-600/50 text-white',
            }
          }}
        />
      </div>
    </div>
  );
}