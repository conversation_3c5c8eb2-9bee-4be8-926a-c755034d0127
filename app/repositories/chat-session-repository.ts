import { db } from '@/app/db';
import { chat_sessions, chat_participants, messages } from '@/app/db/schema';
import { eq, and, or, like, desc } from 'drizzle-orm'; // Removed unused 'asc'
import crypto from 'crypto';

// Define types for the repository
export type ChatSession = typeof chat_sessions.$inferSelect;
export type NewChatSession = typeof chat_sessions.$inferInsert;
export type ChatParticipant = typeof chat_participants.$inferSelect;
export type NewChatParticipant = typeof chat_participants.$inferInsert;

/**
 * Repository for chat session-related database operations
 */
export class ChatSessionRepository {
  /**
   * Find a chat session by ID
   */
  async findById(id: string): Promise<ChatSession | null> {
    try {
      const result = await db.query.chat_sessions.findFirst({
        where: eq(chat_sessions.id, id),
      });
      return result || null;
    } catch (error) {
      console.error('Error finding chat session by ID:', error);
      throw error;
    }
  }

  /**
   * Get all chat sessions
   */
  async getAll(options?: { limit?: number; offset?: number }): Promise<ChatSession[]> {
    try {
      const { limit = 100, offset = 0 } = options || {};
      
      const result = await db.select()
        .from(chat_sessions)
        .orderBy(desc(chat_sessions.date))
        .limit(limit)
        .offset(offset);
      
      return result;
    } catch (error) {
      console.error('Error getting all chat sessions:', error);
      throw error;
    }
  }

  /**
   * Create a new chat session
   */
  async create(sessionData: Omit<NewChatSession, 'id' | 'date'>): Promise<ChatSession> {
    try {
      const newSession: NewChatSession = {
        ...sessionData,
        id: crypto.randomUUID(),
        date: new Date(),
      };
      
      const result = await db.insert(chat_sessions).values(newSession).returning();
      
      if (!result.length) {
        throw new Error('Failed to create chat session');
      }
      
      return result[0];
    } catch (error) {
      console.error('Error creating chat session:', error);
      throw error;
    }
  }

  /**
   * Update a chat session
   */
  async update(id: string, sessionData: Partial<Omit<NewChatSession, 'id' | 'date'>>): Promise<ChatSession | null> {
    try {
      const result = await db.update(chat_sessions)
        .set(sessionData)
        .where(eq(chat_sessions.id, id))
        .returning();
      
      if (!result.length) {
        return null;
      }
      
      return result[0];
    } catch (error) {
      console.error('Error updating chat session:', error);
      throw error;
    }
  }

  /**
   * Delete a chat session
   */
  async delete(id: string): Promise<boolean> {
    try {
      // First delete all messages in the session
      await db.delete(messages)
        .where(eq(messages.session_id, id));
      
      // Then delete all participants
      await db.delete(chat_participants)
        .where(eq(chat_participants.session_id, id));
      
      // Finally delete the session itself
      const result = await db.delete(chat_sessions)
        .where(eq(chat_sessions.id, id))
        .returning();
      
      return result.length > 0;
    } catch (error) {
      console.error('Error deleting chat session:', error);
      throw error;
    }
  }

  /**
   * Toggle starred status
   */
  async toggleStarred(id: string): Promise<ChatSession | null> {
    try {
      // First get the current session to check its starred status
      const session = await this.findById(id);
      
      if (!session) {
        return null;
      }
      
      // Toggle the is_starred status
      const result = await db.update(chat_sessions)
        .set({
          is_starred: !session.is_starred,
        })
        .where(eq(chat_sessions.id, id))
        .returning();
      
      if (!result.length) {
        return null;
      }
      
      return result[0];
    } catch (error) {
      console.error('Error toggling chat session starred status:', error);
      throw error;
    }
  }

  /**
   * Get chat sessions by creator
   */
  async getByCreator(creatorId: string, options?: { limit?: number; offset?: number }): Promise<ChatSession[]> {
    try {
      const { limit = 100, offset = 0 } = options || {};
      
      const result = await db.select()
        .from(chat_sessions)
        .where(eq(chat_sessions.created_by, creatorId))
        .orderBy(desc(chat_sessions.date))
        .limit(limit)
        .offset(offset);
      
      return result;
    } catch (error) {
      console.error('Error getting chat sessions by creator:', error);
      throw error;
    }
  }

  /**
   * Get starred chat sessions
   */
  async getStarred(options?: { limit?: number; offset?: number }): Promise<ChatSession[]> {
    try {
      const { limit = 100, offset = 0 } = options || {};
      
      const result = await db.select()
        .from(chat_sessions)
        .where(eq(chat_sessions.is_starred, true))
        .orderBy(desc(chat_sessions.date))
        .limit(limit)
        .offset(offset);
      
      return result;
    } catch (error) {
      console.error('Error getting starred chat sessions:', error);
      throw error;
    }
  }

  /**
   * Search chat sessions by title
   */
  async search(query: string, options?: { limit?: number; offset?: number }): Promise<ChatSession[]> {
    try {
      const { limit = 100, offset = 0 } = options || {};
      const searchTerm = `%${query}%`;
      
      const result = await db.select()
        .from(chat_sessions)
        .where(
          or(
            like(chat_sessions.title, searchTerm),
            like(chat_sessions.preview, searchTerm)
          )
        )
        .orderBy(desc(chat_sessions.date))
        .limit(limit)
        .offset(offset);
      
      return result;
    } catch (error) {
      console.error('Error searching chat sessions:', error);
      throw error;
    }
  }

  /**
   * Add a participant to a chat session
   */
  async addParticipant(sessionId: string, userId: string): Promise<ChatParticipant> {
    try {
      // Check if the participant already exists
      const existingParticipant = await db.select()
        .from(chat_participants)
        .where(
          and(
            eq(chat_participants.session_id, sessionId),
            eq(chat_participants.user_id, userId)
          )
        )
        .limit(1);
      
      if (existingParticipant.length > 0) {
        return existingParticipant[0];
      }
      
      // Add the new participant
      const newParticipant: NewChatParticipant = {
        session_id: sessionId,
        user_id: userId,
        joined_at: new Date(),
        last_read_at: null,
      };
      
      const result = await db.insert(chat_participants)
        .values(newParticipant)
        .returning();
      
      if (!result.length) {
        throw new Error('Failed to add participant to chat session');
      }
      
      return result[0];
    } catch (error) {
      console.error('Error adding participant to chat session:', error);
      throw error;
    }
  }

  /**
   * Remove a participant from a chat session
   */
  async removeParticipant(sessionId: string, userId: string): Promise<boolean> {
    try {
      const result = await db.delete(chat_participants)
        .where(
          and(
            eq(chat_participants.session_id, sessionId),
            eq(chat_participants.user_id, userId)
          )
        )
        .returning();
      
      return result.length > 0;
    } catch (error) {
      console.error('Error removing participant from chat session:', error);
      throw error;
    }
  }

  /**
   * Get participants of a chat session
   */
  async getParticipants(sessionId: string): Promise<ChatParticipant[]> {
    try {
      const result = await db.select()
        .from(chat_participants)
        .where(eq(chat_participants.session_id, sessionId));
      
      return result;
    } catch (error) {
      console.error('Error getting chat session participants:', error);
      throw error;
    }
  }

  /**
   * Update participant's last read timestamp
   */
  async updateLastRead(sessionId: string, userId: string): Promise<boolean> {
    try {
      const result = await db.update(chat_participants)
        .set({
          last_read_at: new Date(),
        })
        .where(
          and(
            eq(chat_participants.session_id, sessionId),
            eq(chat_participants.user_id, userId)
          )
        )
        .returning();
      
      return result.length > 0;
    } catch (error) {
      console.error('Error updating participant last read timestamp:', error);
      throw error;
    }
  }

  /**
   * Get chat sessions for a participant
   */
  async getByParticipant(userId: string, options?: { limit?: number; offset?: number }): Promise<ChatSession[]> {
    try {
      const { limit = 100, offset = 0 } = options || {};
      
      // Get session IDs where the user is a participant
      const participantSessions = await db.select({ session_id: chat_participants.session_id })
        .from(chat_participants)
        .where(eq(chat_participants.user_id, userId));
      
      const sessionIds = participantSessions.map(p => p.session_id);
      
      if (sessionIds.length === 0) {
        return [];
      }
      
      // Get the actual sessions
      const result = await db.select()
        .from(chat_sessions)
        .where(
          sessionIds.map(id => eq(chat_sessions.id, id)).reduce((acc, curr) => or(acc, curr))
        )
        .orderBy(desc(chat_sessions.date))
        .limit(limit)
        .offset(offset);
      
      return result;
    } catch (error) {
      console.error('Error getting chat sessions by participant:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const chatSessionRepository = new ChatSessionRepository();
