import { ConvexHttpClient } from 'convex/browser';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';

const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

// Define types based on Convex schema
export interface ChatSession {
  _id: Id<"chatSessions">;
  title: string;
  preview?: string;
  isStarred: boolean;
  createdById: Id<"users">;
  participantIds: Id<"users">[];
  organizationId: Id<"organizations">;
  _creationTime: number;
}

export interface NewChatSession {
  title: string;
  preview?: string;
  participantIds?: Id<"users">[];
}

/**
 * Repository for chat session-related database operations
 * Wraps Convex queries and mutations with repository pattern
 */
export class ChatSessionRepository {
  /**
   * Find a chat session by ID
   */
  async findById(id: Id<"chatSessions">): Promise<ChatSession | null> {
    try {
      const result = await convex.query(api.chat.getChatSession, { sessionId: id });
      return result;
    } catch (error) {
      console.error('Error finding chat session by ID:', error);
      throw error;
    }
  }

  /**
   * Get all chat sessions for current user
   */
  async getAll(options?: { isStarred?: boolean }): Promise<ChatSession[]> {
    try {
      const result = await convex.query(api.chat.listChatSessions, {
        isStarred: options?.isStarred
      });
      return result;
    } catch (error) {
      console.error('Error getting all chat sessions:', error);
      throw error;
    }
  }

  /**
   * Create a new chat session
   */
  async create(sessionData: NewChatSession): Promise<Id<"chatSessions">> {
    try {
      const result = await convex.mutation(api.chat.createChatSession, sessionData);
      return result;
    } catch (error) {
      console.error('Error creating chat session:', error);
      throw error;
    }
  }

  /**
   * Toggle starred status of a chat session
   */
  async toggleStarred(id: Id<"chatSessions">, isStarred: boolean): Promise<Id<"chatSessions">> {
    try {
      const result = await convex.mutation(api.chat.toggleStarChatSession, {
        sessionId: id,
        isStarred
      });
      return result;
    } catch (error) {
      console.error('Error toggling chat session starred status:', error);
      throw error;
    }
  }

  /**
   * Delete a chat session
   */
  async delete(id: Id<"chatSessions">): Promise<Id<"chatSessions">> {
    try {
      const result = await convex.mutation(api.chat.deleteChatSession, { sessionId: id });
      return result;
    } catch (error) {
      console.error('Error deleting chat session:', error);
      throw error;
    }
  }

  /**
   * Get starred chat sessions
   */
  async getStarred(): Promise<ChatSession[]> {
    try {
      const result = await convex.query(api.chat.listChatSessions, { isStarred: true });
      return result;
    } catch (error) {
      console.error('Error getting starred chat sessions:', error);
      throw error;
    }
  }

  /**
   * Send a message to a chat session
   */
  async sendMessage(sessionId: Id<"chatSessions">, content: string, contentType: string = "text", metadata?: any): Promise<Id<"messages">> {
    try {
      const result = await convex.mutation(api.chat.sendMessage, {
        sessionId,
        content,
        contentType,
        metadata
      });
      return result;
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  /**
   * Get messages for a chat session
   */
  async getMessages(sessionId: Id<"chatSessions">, limit?: number): Promise<any[]> {
    try {
      const result = await convex.query(api.chat.getMessages, {
        sessionId,
        limit
      });
      return result;
    } catch (error) {
      console.error('Error getting messages:', error);
      throw error;
    }
  }

  /**
   * Add a participant to a chat session
   */
  async addParticipant(sessionId: Id<"chatSessions">, userId: Id<"users">): Promise<Id<"chatSessions">> {
    try {
      const result = await convex.mutation(api.chat.addParticipant, {
        sessionId,
        userId
      });
      return result;
    } catch (error) {
      console.error('Error adding participant to chat session:', error);
      throw error;
    }
  }

  /**
   * Remove a participant from a chat session
   */
  async removeParticipant(sessionId: Id<"chatSessions">, userId: Id<"users">): Promise<Id<"chatSessions">> {
    try {
      const result = await convex.mutation(api.chat.removeParticipant, {
        sessionId,
        userId
      });
      return result;
    } catch (error) {
      console.error('Error removing participant from chat session:', error);
      throw error;
    }
  }

}

// Export a singleton instance
export const chatSessionRepository = new ChatSessionRepository();
