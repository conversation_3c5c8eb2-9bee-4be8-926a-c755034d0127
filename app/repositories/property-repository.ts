import { db } from '@/app/db';
import { properties, property_areas } from '@/app/db/schema';
import { eq, or, like, asc } from 'drizzle-orm'; // Removed unused 'and', 'desc'
import crypto from 'crypto';

// Define types for the repository
export type Property = typeof properties.$inferSelect;
export type NewProperty = typeof properties.$inferInsert;
export type PropertyArea = typeof property_areas.$inferSelect;
export type NewPropertyArea = typeof property_areas.$inferInsert;

/**
 * Repository for property-related database operations
 */
export class PropertyRepository {
  /**
   * Find a property by ID
   */
  async findById(id: string): Promise<Property | null> {
    try {
      const result = await db.query.properties.findFirst({
        where: eq(properties.id, id),
      });
      return result || null;
    } catch (error) {
      console.error('Error finding property by ID:', error);
      throw error;
    }
  }

  /**
   * Get all properties
   */
  async getAll(options?: { limit?: number; offset?: number }): Promise<Property[]> {
    try {
      const { limit = 100, offset = 0 } = options || {};
      
      const result = await db.select()
        .from(properties)
        .orderBy(asc(properties.name))
        .limit(limit)
        .offset(offset);
      
      return result;
    } catch (error) {
      console.error('Error getting all properties:', error);
      throw error;
    }
  }

  /**
   * Create a new property
   */
  async create(propertyData: Omit<NewProperty, 'id' | 'created_at' | 'updated_at'>): Promise<Property> {
    try {
      const newProperty: NewProperty = {
        ...propertyData,
        id: crypto.randomUUID(),
        created_at: new Date(),
        updated_at: null,
      };
      
      const result = await db.insert(properties).values(newProperty).returning();
      
      if (!result.length) {
        throw new Error('Failed to create property');
      }
      
      return result[0];
    } catch (error) {
      console.error('Error creating property:', error);
      throw error;
    }
  }

  /**
   * Update a property
   */
  async update(id: string, propertyData: Partial<Omit<NewProperty, 'id' | 'created_at' | 'updated_at'>>): Promise<Property | null> {
    try {
      const result = await db.update(properties)
        .set({
          ...propertyData,
          updated_at: new Date(),
        })
        .where(eq(properties.id, id))
        .returning();
      
      if (!result.length) {
        return null;
      }
      
      return result[0];
    } catch (error) {
      console.error('Error updating property:', error);
      throw error;
    }
  }

  /**
   * Delete a property
   */
  async delete(id: string): Promise<boolean> {
    try {
      // First delete all areas associated with the property
      await db.delete(property_areas)
        .where(eq(property_areas.property_id, id));
      
      // Then delete the property itself
      const result = await db.delete(properties)
        .where(eq(properties.id, id))
        .returning();
      
      return result.length > 0;
    } catch (error) {
      console.error('Error deleting property:', error);
      throw error;
    }
  }

  /**
   * Search properties by name, address, or suburb
   */
  async search(query: string, options?: { limit?: number; offset?: number }): Promise<Property[]> {
    try {
      const { limit = 100, offset = 0 } = options || {};
      const searchTerm = `%${query}%`;
      
      const result = await db.select()
        .from(properties)
        .where(
          or(
            like(properties.name, searchTerm),
            like(properties.address, searchTerm),
            like(properties.suburb, searchTerm)
          )
        )
        .orderBy(asc(properties.name))
        .limit(limit)
        .offset(offset);
      
      return result;
    } catch (error) {
      console.error('Error searching properties:', error);
      throw error;
    }
  }

  /**
   * Get properties by manager
   */
  async getByManager(managerId: string, options?: { limit?: number; offset?: number }): Promise<Property[]> {
    try {
      const { limit = 100, offset = 0 } = options || {};
      
      const result = await db.select()
        .from(properties)
        .where(eq(properties.manager_id, managerId))
        .orderBy(asc(properties.name))
        .limit(limit)
        .offset(offset);
      
      return result;
    } catch (error) {
      console.error('Error getting properties by manager:', error);
      throw error;
    }
  }

  /**
   * Get properties by region
   */
  async getByRegion(region: string, options?: { limit?: number; offset?: number }): Promise<Property[]> {
    try {
      const { limit = 100, offset = 0 } = options || {};
      
      const result = await db.select()
        .from(properties)
        .where(eq(properties.region, region))
        .orderBy(asc(properties.name))
        .limit(limit)
        .offset(offset);
      
      return result;
    } catch (error) {
      console.error('Error getting properties by region:', error);
      throw error;
    }
  }

  /**
   * Get properties by type
   */
  async getByType(type: string, options?: { limit?: number; offset?: number }): Promise<Property[]> {
    try {
      const { limit = 100, offset = 0 } = options || {};
      
      const result = await db.select()
        .from(properties)
        .where(eq(properties.type, type))
        .orderBy(asc(properties.name))
        .limit(limit)
        .offset(offset);
      
      return result;
    } catch (error) {
      console.error('Error getting properties by type:', error);
      throw error;
    }
  }

  /**
   * Get properties by status
   */
  async getByStatus(status: string, options?: { limit?: number; offset?: number }): Promise<Property[]> {
    try {
      const { limit = 100, offset = 0 } = options || {};
      
      const result = await db.select()
        .from(properties)
        .where(eq(properties.status, status))
        .orderBy(asc(properties.name))
        .limit(limit)
        .offset(offset);
      
      return result;
    } catch (error) {
      console.error('Error getting properties by status:', error);
      throw error;
    }
  }

  /**
   * Add an area to a property
   */
  async addArea(area: Omit<NewPropertyArea, 'id'>): Promise<PropertyArea> {
    try {
      const newArea: NewPropertyArea = {
        ...area,
        id: crypto.randomUUID(),
      };
      
      const result = await db.insert(property_areas).values(newArea).returning();
      
      if (!result.length) {
        throw new Error('Failed to add area to property');
      }
      
      return result[0];
    } catch (error) {
      console.error('Error adding area to property:', error);
      throw error;
    }
  }

  /**
   * Get areas for a property
   */
  async getAreas(propertyId: string): Promise<PropertyArea[]> {
    try {
      const result = await db.select()
        .from(property_areas)
        .where(eq(property_areas.property_id, propertyId))
        .orderBy(asc(property_areas.name));
      
      return result;
    } catch (error) {
      console.error('Error getting areas for property:', error);
      throw error;
    }
  }

  /**
   * Update a property area
   */
  async updateArea(id: string, areaData: Partial<Omit<NewPropertyArea, 'id' | 'property_id'>>): Promise<PropertyArea | null> {
    try {
      const result = await db.update(property_areas)
        .set(areaData)
        .where(eq(property_areas.id, id))
        .returning();
      
      if (!result.length) {
        return null;
      }
      
      return result[0];
    } catch (error) {
      console.error('Error updating property area:', error);
      throw error;
    }
  }

  /**
   * Delete a property area
   */
  async deleteArea(id: string): Promise<boolean> {
    try {
      const result = await db.delete(property_areas)
        .where(eq(property_areas.id, id))
        .returning();
      
      return result.length > 0;
    } catch (error) {
      console.error('Error deleting property area:', error);
      throw error;
    }
  }

  /**
   * Count total properties
   */
  async count(): Promise<number> {
    try {
      const result = await db.select({ count: db.fn.count() }).from(properties);
      return Number(result[0].count) || 0;
    } catch (error) {
      console.error('Error counting properties:', error);
      throw error;
    }
  }

  /**
   * Get property statistics by region
   */
  async getStatsByRegion(): Promise<{ region: string; count: number }[]> {
    try {
      const result = await db.select({
        region: properties.region,
        count: db.fn.count(),
      })
      .from(properties)
      .groupBy(properties.region);
      
      return result.map(item => ({
        region: item.region,
        count: Number(item.count) || 0,
      }));
    } catch (error) {
      console.error('Error getting property statistics by region:', error);
      throw error;
    }
  }

  /**
   * Get property statistics by type
   */
  async getStatsByType(): Promise<{ type: string; count: number }[]> {
    try {
      const result = await db.select({
        type: properties.type,
        count: db.fn.count(),
      })
      .from(properties)
      .groupBy(properties.type);
      
      return result.map(item => ({
        type: item.type,
        count: Number(item.count) || 0,
      }));
    } catch (error) {
      console.error('Error getting property statistics by type:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const propertyRepository = new PropertyRepository();
