import { db } from '@/app/db';
import { 
  inspection_reports, 
  inspection_actions, 
  report_attachments,
  inspection_templates,
  InspectionReport,
  NewInspectionReport
} from '@/app/db/schema';
import { eq, and, or, desc, asc, gte, lte } from 'drizzle-orm'; // Removed unused 'like', 'isNull', 'isNotNull'
import crypto from 'crypto';

// Define types for the repository
export type InspectionAction = typeof inspection_actions.$inferSelect;
export type NewInspectionAction = typeof inspection_actions.$inferInsert;
export type ReportAttachment = typeof report_attachments.$inferSelect;
export type NewReportAttachment = typeof report_attachments.$inferInsert;
export type InspectionTemplate = typeof inspection_templates.$inferSelect;
export type NewInspectionTemplate = typeof inspection_templates.$inferInsert;

/**
 * Repository for inspection report-related database operations
 */
export class InspectionRepository {
  /**
   * Find an inspection report by ID
   */
  async findById(id: string): Promise<InspectionReport | null> {
    try {
      const result = await db.query.inspection_reports.findFirst({
        where: eq(inspection_reports.id, id),
      });
      return result || null;
    } catch (error) {
      console.error('Error finding inspection report by ID:', error);
      throw error;
    }
  }

  /**
   * Get all inspection reports
   */
  async getAll(options?: { limit?: number; offset?: number }): Promise<InspectionReport[]> {
    try {
      const { limit = 100, offset = 0 } = options || {};
      
      const result = await db.select()
        .from(inspection_reports)
        .orderBy(desc(inspection_reports.date))
        .limit(limit)
        .offset(offset);
      
      return result;
    } catch (error) {
      console.error('Error getting all inspection reports:', error);
      throw error;
    }
  }

  /**
   * Create a new inspection report
   */
  async create(reportData: Omit<NewInspectionReport, 'id' | 'date' | 'created_at'>): Promise<InspectionReport> {
    try {
      const newReport: NewInspectionReport = {
        ...reportData,
        id: crypto.randomUUID(),
        date: new Date(),
        created_at: new Date(),
        submitted_at: null,
        reviewed_at: null,
      };
      
      const result = await db.insert(inspection_reports).values(newReport).returning();
      
      if (!result.length) {
        throw new Error('Failed to create inspection report');
      }
      
      return result[0];
    } catch (error) {
      console.error('Error creating inspection report:', error);
      throw error;
    }
  }

  /**
   * Update an inspection report
   */
  async update(id: string, reportData: Partial<Omit<NewInspectionReport, 'id' | 'date' | 'created_at'>>): Promise<InspectionReport | null> {
    try {
      const result = await db.update(inspection_reports)
        .set(reportData)
        .where(eq(inspection_reports.id, id))
        .returning();
      
      if (!result.length) {
        return null;
      }
      
      return result[0];
    } catch (error) {
      console.error('Error updating inspection report:', error);
      throw error;
    }
  }

  /**
   * Delete an inspection report
   */
  async delete(id: string): Promise<boolean> {
    try {
      // First delete all actions associated with the report
      await db.delete(inspection_actions)
        .where(eq(inspection_actions.report_id, id));
      
      // Then delete all attachments
      await db.delete(report_attachments)
        .where(eq(report_attachments.report_id, id));
      
      // Finally delete the report itself
      const result = await db.delete(inspection_reports)
        .where(eq(inspection_reports.id, id))
        .returning();
      
      return result.length > 0;
    } catch (error) {
      console.error('Error deleting inspection report:', error);
      throw error;
    }
  }

  /**
   * Get inspection reports by property
   */
  async getByProperty(propertyId: string, options?: { limit?: number; offset?: number }): Promise<InspectionReport[]> {
    try {
      const { limit = 100, offset = 0 } = options || {};
      
      const result = await db.select()
        .from(inspection_reports)
        .where(eq(inspection_reports.property_id, propertyId))
        .orderBy(desc(inspection_reports.date))
        .limit(limit)
        .offset(offset);
      
      return result;
    } catch (error) {
      console.error('Error getting inspection reports by property:', error);
      throw error;
    }
  }

  /**
   * Get inspection reports by inspector
   */
  async getByInspector(inspectorId: string, options?: { limit?: number; offset?: number }): Promise<InspectionReport[]> {
    try {
      const { limit = 100, offset = 0 } = options || {};
      
      const result = await db.select()
        .from(inspection_reports)
        .where(eq(inspection_reports.inspector, inspectorId))
        .orderBy(desc(inspection_reports.date))
        .limit(limit)
        .offset(offset);
      
      return result;
    } catch (error) {
      console.error('Error getting inspection reports by inspector:', error);
      throw error;
    }
  }

  /**
   * Get inspection reports by status
   */
  async getByStatus(status: string, options?: { limit?: number; offset?: number }): Promise<InspectionReport[]> {
    try {
      const { limit = 100, offset = 0 } = options || {};
      
      const result = await db.select()
        .from(inspection_reports)
        .where(eq(inspection_reports.status, status))
        .orderBy(desc(inspection_reports.date))
        .limit(limit)
        .offset(offset);
      
      return result;
    } catch (error) {
      console.error('Error getting inspection reports by status:', error);
      throw error;
    }
  }

  /**
   * Get inspection reports by date range
   */
  async getByDateRange(startDate: Date, endDate: Date, options?: { limit?: number; offset?: number }): Promise<InspectionReport[]> {
    try {
      const { limit = 100, offset = 0 } = options || {};
      
      const result = await db.select()
        .from(inspection_reports)
        .where(
          and(
            gte(inspection_reports.date, startDate),
            lte(inspection_reports.date, endDate)
          )
        )
        .orderBy(desc(inspection_reports.date))
        .limit(limit)
        .offset(offset);
      
      return result;
    } catch (error) {
      console.error('Error getting inspection reports by date range:', error);
      throw error;
    }
  }

  /**
   * Submit an inspection report
   */
  async submit(id: string): Promise<InspectionReport | null> {
    try {
      const result = await db.update(inspection_reports)
        .set({
          status: 'submitted',
          submitted_at: new Date(),
        })
        .where(eq(inspection_reports.id, id))
        .returning();
      
      if (!result.length) {
        return null;
      }
      
      return result[0];
    } catch (error) {
      console.error('Error submitting inspection report:', error);
      throw error;
    }
  }

  /**
   * Review an inspection report
   */
  async review(id: string, reviewerId: string): Promise<InspectionReport | null> {
    try {
      const result = await db.update(inspection_reports)
        .set({
          status: 'reviewed',
          reviewed_by: reviewerId,
          reviewed_at: new Date(),
        })
        .where(eq(inspection_reports.id, id))
        .returning();
      
      if (!result.length) {
        return null;
      }
      
      return result[0];
    } catch (error) {
      console.error('Error reviewing inspection report:', error);
      throw error;
    }
  }

  /**
   * Approve an inspection report
   */
  async approve(id: string): Promise<InspectionReport | null> {
    try {
      const result = await db.update(inspection_reports)
        .set({
          status: 'approved',
        })
        .where(eq(inspection_reports.id, id))
        .returning();
      
      if (!result.length) {
        return null;
      }
      
      return result[0];
    } catch (error) {
      console.error('Error approving inspection report:', error);
      throw error;
    }
  }

  /**
   * Get pending inspection reports
   */
  async getPending(options?: { limit?: number; offset?: number }): Promise<InspectionReport[]> {
    try {
      const { limit = 100, offset = 0 } = options || {};
      
      const result = await db.select()
        .from(inspection_reports)
        .where(
          or(
            eq(inspection_reports.status, 'draft'),
            eq(inspection_reports.status, 'submitted')
          )
        )
        .orderBy(desc(inspection_reports.date))
        .limit(limit)
        .offset(offset);
      
      return result;
    } catch (error) {
      console.error('Error getting pending inspection reports:', error);
      throw error;
    }
  }

  /**
   * Add an action to an inspection report
   */
  async addAction(action: Omit<NewInspectionAction, 'id' | 'created_at' | 'updated_at'>): Promise<InspectionAction> {
    try {
      const newAction: NewInspectionAction = {
        ...action,
        id: crypto.randomUUID(),
        created_at: new Date(),
        updated_at: null,
      };
      
      const result = await db.insert(inspection_actions).values(newAction).returning();
      
      if (!result.length) {
        throw new Error('Failed to add action to inspection report');
      }
      
      return result[0];
    } catch (error) {
      console.error('Error adding action to inspection report:', error);
      throw error;
    }
  }

  /**
   * Get actions for an inspection report
   */
  async getActions(reportId: string): Promise<InspectionAction[]> {
    try {
      const result = await db.select()
        .from(inspection_actions)
        .where(eq(inspection_actions.report_id, reportId))
        .orderBy(asc(inspection_actions.due_date));
      
      return result;
    } catch (error) {
      console.error('Error getting actions for inspection report:', error);
      throw error;
    }
  }

  /**
   * Update an action
   */
  async updateAction(id: string, actionData: Partial<Omit<NewInspectionAction, 'id' | 'report_id' | 'created_at' | 'updated_at'>>): Promise<InspectionAction | null> {
    try {
      const result = await db.update(inspection_actions)
        .set({
          ...actionData,
          updated_at: new Date(),
        })
        .where(eq(inspection_actions.id, id))
        .returning();
      
      if (!result.length) {
        return null;
      }
      
      return result[0];
    } catch (error) {
      console.error('Error updating inspection action:', error);
      throw error;
    }
  }

  /**
   * Delete an action
   */
  async deleteAction(id: string): Promise<boolean> {
    try {
      const result = await db.delete(inspection_actions)
        .where(eq(inspection_actions.id, id))
        .returning();
      
      return result.length > 0;
    } catch (error) {
      console.error('Error deleting inspection action:', error);
      throw error;
    }
  }

  /**
   * Add an attachment to an inspection report
   */
  async addAttachment(attachment: Omit<NewReportAttachment, 'id' | 'created_at'>): Promise<ReportAttachment> {
    try {
      const newAttachment: NewReportAttachment = {
        ...attachment,
        id: crypto.randomUUID(),
        created_at: new Date(),
      };
      
      const result = await db.insert(report_attachments).values(newAttachment).returning();
      
      if (!result.length) {
        throw new Error('Failed to add attachment to inspection report');
      }
      
      return result[0];
    } catch (error) {
      console.error('Error adding attachment to inspection report:', error);
      throw error;
    }
  }

  /**
   * Get attachments for an inspection report
   */
  async getAttachments(reportId: string): Promise<ReportAttachment[]> {
    try {
      const result = await db.select()
        .from(report_attachments)
        .where(eq(report_attachments.report_id, reportId))
        .orderBy(desc(report_attachments.created_at));
      
      return result;
    } catch (error) {
      console.error('Error getting attachments for inspection report:', error);
      throw error;
    }
  }

  /**
   * Delete an attachment
   */
  async deleteAttachment(id: string): Promise<boolean> {
    try {
      const result = await db.delete(report_attachments)
        .where(eq(report_attachments.id, id))
        .returning();
      
      return result.length > 0;
    } catch (error) {
      console.error('Error deleting inspection attachment:', error);
      throw error;
    }
  }

  /**
   * Create a new inspection template
   */
  async createTemplate(templateData: Omit<NewInspectionTemplate, 'id' | 'created_at'>): Promise<InspectionTemplate> {
    try {
      const newTemplate: NewInspectionTemplate = {
        ...templateData,
        id: crypto.randomUUID(),
        created_at: new Date(),
      };
      
      const result = await db.insert(inspection_templates).values(newTemplate).returning();
      
      if (!result.length) {
        throw new Error('Failed to create inspection template');
      }
      
      return result[0];
    } catch (error) {
      console.error('Error creating inspection template:', error);
      throw error;
    }
  }

  /**
   * Get all inspection templates
   */
  async getAllTemplates(options?: { limit?: number; offset?: number }): Promise<InspectionTemplate[]> {
    try {
      const { limit = 100, offset = 0 } = options || {};
      
      const result = await db.select()
        .from(inspection_templates)
        .orderBy(asc(inspection_templates.name))
        .limit(limit)
        .offset(offset);
      
      return result;
    } catch (error) {
      console.error('Error getting all inspection templates:', error);
      throw error;
    }
  }

  /**
   * Get active inspection templates
   */
  async getActiveTemplates(): Promise<InspectionTemplate[]> {
    try {
      const result = await db.select()
        .from(inspection_templates)
        .where(eq(inspection_templates.is_active, true))
        .orderBy(asc(inspection_templates.name));
      
      return result;
    } catch (error) {
      console.error('Error getting active inspection templates:', error);
      throw error;
    }
  }

  /**
   * Find an inspection template by ID
   */
  async findTemplateById(id: string): Promise<InspectionTemplate | null> {
    try {
      const result = await db.query.inspection_templates.findFirst({
        where: eq(inspection_templates.id, id),
      });
      return result || null;
    } catch (error) {
      console.error('Error finding inspection template by ID:', error);
      throw error;
    }
  }

  /**
   * Update an inspection template
   */
  async updateTemplate(id: string, templateData: Partial<Omit<NewInspectionTemplate, 'id' | 'created_at'>>): Promise<InspectionTemplate | null> {
    try {
      const result = await db.update(inspection_templates)
        .set(templateData)
        .where(eq(inspection_templates.id, id))
        .returning();
      
      if (!result.length) {
        return null;
      }
      
      return result[0];
    } catch (error) {
      console.error('Error updating inspection template:', error);
      throw error;
    }
  }

  /**
   * Delete an inspection template
   */
  async deleteTemplate(id: string): Promise<boolean> {
    try {
      const result = await db.delete(inspection_templates)
        .where(eq(inspection_templates.id, id))
        .returning();
      
      return result.length > 0;
    } catch (error) {
      console.error('Error deleting inspection template:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const inspectionRepository = new InspectionRepository();
