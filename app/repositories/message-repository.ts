import { db } from '@/app/db';
import { messages, Message, NewMessage } from '@/app/db/schema';
import { eq, like, desc, asc } from 'drizzle-orm'; // Removed unused 'and', 'or'
import crypto from 'crypto';

/**
 * Repository for message-related database operations
 */
export class MessageRepository {
  /**
   * Find a message by ID
   */
  async findById(id: string): Promise<Message | null> {
    try {
      const result = await db.query.messages.findFirst({
        where: eq(messages.id, id),
      });
      return result || null;
    } catch (error) {
      console.error('Error finding message by ID:', error);
      throw error;
    }
  }

  /**
   * Get messages by session ID
   */
  async getBySessionId(sessionId: string, options?: { limit?: number; offset?: number }): Promise<Message[]> {
    try {
      const { limit = 100, offset = 0 } = options || {};
      
      const result = await db.select()
        .from(messages)
        .where(eq(messages.session_id, sessionId))
        .orderBy(asc(messages.created_at))
        .limit(limit)
        .offset(offset);
      
      return result;
    } catch (error) {
      console.error('Error getting messages by session ID:', error);
      throw error;
    }
  }

  /**
   * Create a new message
   */
  async create(messageData: Omit<NewMessage, 'id' | 'created_at'>): Promise<Message> {
    try {
      const newMessage: NewMessage = {
        ...messageData,
        id: crypto.randomUUID(),
        created_at: new Date(),
      };
      
      const result = await db.insert(messages).values(newMessage).returning();
      
      if (!result.length) {
        throw new Error('Failed to create message');
      }
      
      return result[0];
    } catch (error) {
      console.error('Error creating message:', error);
      throw error;
    }
  }

  /**
   * Update a message
   */
  async update(id: string, messageData: Partial<Omit<NewMessage, 'id' | 'created_at'>>): Promise<Message | null> {
    try {
      const result = await db.update(messages)
        .set(messageData)
        .where(eq(messages.id, id))
        .returning();
      
      if (!result.length) {
        return null;
      }
      
      return result[0];
    } catch (error) {
      console.error('Error updating message:', error);
      throw error;
    }
  }

  /**
   * Delete a message
   */
  async delete(id: string): Promise<boolean> {
    try {
      const result = await db.delete(messages)
        .where(eq(messages.id, id))
        .returning();
      
      return result.length > 0;
    } catch (error) {
      console.error('Error deleting message:', error);
      throw error;
    }
  }

  /**
   * Delete all messages in a session
   */
  async deleteBySessionId(sessionId: string): Promise<number> {
    try {
      const result = await db.delete(messages)
        .where(eq(messages.session_id, sessionId))
        .returning();
      
      return result.length;
    } catch (error) {
      console.error('Error deleting messages by session ID:', error);
      throw error;
    }
  }

  /**
   * Search messages by content
   */
  async searchByContent(query: string, options?: { limit?: number; offset?: number }): Promise<Message[]> {
    try {
      const { limit = 100, offset = 0 } = options || {};
      const searchTerm = `%${query}%`;
      
      const result = await db.select()
        .from(messages)
        .where(like(messages.content_transcript, searchTerm))
        .orderBy(desc(messages.created_at))
        .limit(limit)
        .offset(offset);
      
      return result;
    } catch (error) {
      console.error('Error searching messages by content:', error);
      throw error;
    }
  }

  /**
   * Get messages by sender
   */
  async getBySender(senderId: string, options?: { limit?: number; offset?: number }): Promise<Message[]> {
    try {
      const { limit = 100, offset = 0 } = options || {};
      
      const result = await db.select()
        .from(messages)
        .where(eq(messages.sender_id, senderId))
        .orderBy(desc(messages.created_at))
        .limit(limit)
        .offset(offset);
      
      return result;
    } catch (error) {
      console.error('Error getting messages by sender:', error);
      throw error;
    }
  }

  /**
   * Count messages in a session
   */
  async countBySessionId(sessionId: string): Promise<number> {
    try {
      const result = await db.select({ count: db.fn.count() })
        .from(messages)
        .where(eq(messages.session_id, sessionId));
      
      return Number(result[0].count) || 0;
    } catch (error) {
      console.error('Error counting messages by session ID:', error);
      throw error;
    }
  }

  /**
   * Get latest message in a session
   */
  async getLatestBySessionId(sessionId: string): Promise<Message | null> {
    try {
      const result = await db.select()
        .from(messages)
        .where(eq(messages.session_id, sessionId))
        .orderBy(desc(messages.created_at))
        .limit(1);
      
      if (!result.length) {
        return null;
      }
      
      return result[0];
    } catch (error) {
      console.error('Error getting latest message by session ID:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const messageRepository = new MessageRepository();
