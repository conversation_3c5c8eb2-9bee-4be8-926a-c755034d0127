import { ConvexHttpClient } from 'convex/browser';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';

const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

// Define types based on Convex schema
export interface User {
  _id: Id<"users">;
  clerkUserId: string;
  email: string;
  name: string;
  imageUrl?: string;
  role: string;
  department?: string;
  phone?: string;
  organizationId?: Id<"organizations">;
  preferences?: Record<string, any>;
  _creationTime: number;
}

export interface NewUser {
  clerkUserId: string;
  email: string;
  name: string;
  imageUrl?: string;
  role: string;
  department?: string;
  phone?: string;
  organizationId?: Id<"organizations">;
  preferences?: Record<string, any>;
}

/**
 * Repository for user-related database operations
 * Wraps Convex queries and mutations with repository pattern
 */
export class UserRepository {
  /**
   * Find a user by email
   */
  async findByEmail(email: string): Promise<User | null> {
    try {
      const result = await convex.query(api.users.findByEmail, { email });
      return result;
    } catch (error) {
      console.error('Error finding user by email:', error);
      throw error;
    }
  }

  /**
   * Find a user by Convex ID
   */
  async findById(id: Id<"users">): Promise<User | null> {
    try {
      const result = await convex.query(api.users.findById, { userId: id });
      return result;
    } catch (error) {
      console.error('Error finding user by ID:', error);
      throw error;
    }
  }

  /**
   * Find a user by Clerk ID
   */
  async findByClerkId(clerkUserId: string): Promise<User | null> {
    try {
      const result = await convex.query(api.users.findByClerkId, { clerkUserId });
      return result;
    } catch (error) {
      console.error('Error finding user by Clerk ID:', error);
      throw error;
    }
  }

  /**
   * Get current user (requires authentication context)
   */
  async getCurrentUser(): Promise<User | null> {
    try {
      const result = await convex.query(api.users.getCurrentUser, {});
      return result;
    } catch (error) {
      console.error('Error getting current user:', error);
      throw error;
    }
  }

  /**
   * Get all users in current organization
   */
  async getAll(options?: { limit?: number }): Promise<User[]> {
    try {
      const result = await convex.query(api.users.listUsers, { 
        limit: options?.limit 
      });
      return result;
    } catch (error) {
      console.error('Error getting all users:', error);
      throw error;
    }
  }

  /**
   * Search users by name, email, or department
   */
  async search(query: string, options?: { limit?: number }): Promise<User[]> {
    try {
      const result = await convex.query(api.users.searchUsers, { 
        query,
        limit: options?.limit 
      });
      return result;
    } catch (error) {
      console.error('Error searching users:', error);
      throw error;
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(userId: Id<"users">, userData: {
    department?: string;
    phone?: string;
    preferences?: Record<string, any>;
  }): Promise<Id<"users">> {
    try {
      const result = await convex.mutation(api.users.updateProfile, {
        userId,
        ...userData
      });
      return result;
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  }

  /**
   * Find users by role
   */
  async findByRole(role: string, options?: { limit?: number }): Promise<User[]> {
    try {
      const result = await convex.query(api.users.findByRole, { 
        role,
        limit: options?.limit 
      });
      return result;
    } catch (error) {
      console.error('Error finding users by role:', error);
      throw error;
    }
  }

  /**
   * Find users by department
   */
  async findByDepartment(department: string, options?: { limit?: number }): Promise<User[]> {
    try {
      const result = await convex.query(api.users.findByDepartment, { 
        department,
        limit: options?.limit 
      });
      return result;
    } catch (error) {
      console.error('Error finding users by department:', error);
      throw error;
    }
  }

  /**
   * Count total users in organization
   */
  async count(): Promise<number> {
    try {
      const users = await this.getAll();
      return users.length;
    } catch (error) {
      console.error('Error counting users:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const userRepository = new UserRepository();
