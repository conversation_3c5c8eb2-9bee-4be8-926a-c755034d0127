import { db } from '@/app/db';
import { users, User, NewUser } from '@/app/db/schema';
import { eq, or, like } from 'drizzle-orm'; // Removed unused 'and', 'desc'
import crypto from 'crypto';

/**
 * Repository for user-related database operations
 */
export class UserRepository {
  /**
   * Find a user by email
   */
  async findByEmail(email: string): Promise<User | null> {
    try {
      const result = await db.query.users.findFirst({
        where: eq(users.email, email),
      });
      return result || null;
    } catch (error) {
      console.error('Error finding user by email:', error);
      throw error;
    }
  }

  /**
   * Find a user by ID
   */
  async findById(id: string): Promise<User | null> {
    try {
      const result = await db.query.users.findFirst({
        where: eq(users.id, id),
      });
      return result || null;
    } catch (error) {
      console.error('Error finding user by ID:', error);
      throw error;
    }
  }

  /**
   * Get all users
   */
  async getAll(options?: { limit?: number; offset?: number }): Promise<User[]> {
    try {
      const { limit = 100, offset = 0 } = options || {};
      const result = await db.select().from(users).limit(limit).offset(offset);
      return result;
    } catch (error) {
      console.error('Error getting all users:', error);
      throw error;
    }
  }

  /**
   * Search users by name, email, or department
   */
  async search(query: string, options?: { limit?: number; offset?: number }): Promise<User[]> {
    try {
      const { limit = 100, offset = 0 } = options || {};
      const searchTerm = `%${query}%`;
      
      const result = await db.select()
        .from(users)
        .where(
          or(
            like(users.name, searchTerm),
            like(users.email, searchTerm),
            like(users.department, searchTerm)
          )
        )
        .limit(limit)
        .offset(offset);
      
      return result;
    } catch (error) {
      console.error('Error searching users:', error);
      throw error;
    }
  }

  /**
   * Create a new user
   */
  async create(userData: Omit<NewUser, 'id'>): Promise<User> {
    try {
      const newUser: NewUser = {
        ...userData,
        id: crypto.randomUUID(),
      };
      
      const result = await db.insert(users).values(newUser).returning();
      
      if (!result.length) {
        throw new Error('Failed to create user');
      }
      
      return result[0];
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  /**
   * Update a user
   */
  async update(id: string, userData: Partial<Omit<NewUser, 'id'>>): Promise<User | null> {
    try {
      const result = await db.update(users)
        .set({
          ...userData,
          // Don't update these fields if not provided
          name: userData.name !== undefined ? userData.name : undefined,
          email: userData.email !== undefined ? userData.email : undefined,
          password: userData.password !== undefined ? userData.password : undefined,
        })
        .where(eq(users.id, id))
        .returning();
      
      if (!result.length) {
        return null;
      }
      
      return result[0];
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  }

  /**
   * Delete a user
   */
  async delete(id: string): Promise<boolean> {
    try {
      const result = await db.delete(users)
        .where(eq(users.id, id))
        .returning();
      
      return result.length > 0;
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }

  /**
   * Update user's last login timestamp
   */
  async updateLastLogin(id: string): Promise<boolean> {
    try {
      const result = await db.update(users)
        .set({
          last_login: new Date(),
        })
        .where(eq(users.id, id))
        .returning();
      
      return result.length > 0;
    } catch (error) {
      console.error('Error updating user last login:', error);
      throw error;
    }
  }

  /**
   * Find users by role
   */
  async findByRole(role: string, options?: { limit?: number; offset?: number }): Promise<User[]> {
    try {
      const { limit = 100, offset = 0 } = options || {};
      
      const result = await db.select()
        .from(users)
        .where(eq(users.role, role))
        .limit(limit)
        .offset(offset);
      
      return result;
    } catch (error) {
      console.error('Error finding users by role:', error);
      throw error;
    }
  }

  /**
   * Find users by department
   */
  async findByDepartment(department: string, options?: { limit?: number; offset?: number }): Promise<User[]> {
    try {
      const { limit = 100, offset = 0 } = options || {};
      
      const result = await db.select()
        .from(users)
        .where(eq(users.department, department))
        .limit(limit)
        .offset(offset);
      
      return result;
    } catch (error) {
      console.error('Error finding users by department:', error);
      throw error;
    }
  }

  /**
   * Count total users
   */
  async count(): Promise<number> {
    try {
      const result = await db.select({ count: db.fn.count() }).from(users);
      return Number(result[0].count) || 0;
    } catch (error) {
      console.error('Error counting users:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const userRepository = new UserRepository();
