import { db } from '@/app/db';
import { contacts, Contact, NewContact } from '@/app/db/schema';
import { eq, or, like } from 'drizzle-orm'; // Removed unused 'and', 'desc'
import crypto from 'crypto';

/**
 * Repository for contact-related database operations
 */
export class ContactRepository {
  /**
   * Find a contact by ID
   */
  async findById(id: string): Promise<Contact | null> {
    try {
      const result = await db.query.contacts.findFirst({
        where: eq(contacts.id, id),
      });
      return result || null;
    } catch (error) {
      console.error('Error finding contact by ID:', error);
      throw error;
    }
  }

  /**
   * Get all contacts
   */
  async getAll(options?: { limit?: number; offset?: number }): Promise<Contact[]> {
    try {
      const { limit = 100, offset = 0 } = options || {};
      const result = await db.select().from(contacts).limit(limit).offset(offset);
      return result;
    } catch (error) {
      console.error('Error getting all contacts:', error);
      throw error;
    }
  }

  /**
   * Search contacts by name, company, or role
   */
  async search(query: string, options?: { limit?: number; offset?: number }): Promise<Contact[]> {
    try {
      const { limit = 100, offset = 0 } = options || {};
      const searchTerm = `%${query}%`;
      
      const result = await db.select()
        .from(contacts)
        .where(
          or(
            like(contacts.name, searchTerm),
            like(contacts.company, searchTerm),
            like(contacts.role, searchTerm)
          )
        )
        .limit(limit)
        .offset(offset);
      
      return result;
    } catch (error) {
      console.error('Error searching contacts:', error);
      throw error;
    }
  }

  /**
   * Create a new contact
   */
  async create(contactData: Omit<NewContact, 'id' | 'created_at' | 'updated_at'>): Promise<Contact> {
    try {
      const newContact: NewContact = {
        ...contactData,
        id: crypto.randomUUID(),
        created_at: new Date(),
        updated_at: null,
      };
      
      const result = await db.insert(contacts).values(newContact).returning();
      
      if (!result.length) {
        throw new Error('Failed to create contact');
      }
      
      return result[0];
    } catch (error) {
      console.error('Error creating contact:', error);
      throw error;
    }
  }

  /**
   * Update a contact
   */
  async update(id: string, contactData: Partial<Omit<NewContact, 'id' | 'created_at' | 'updated_at'>>): Promise<Contact | null> {
    try {
      const result = await db.update(contacts)
        .set({
          ...contactData,
          updated_at: new Date(),
        })
        .where(eq(contacts.id, id))
        .returning();
      
      if (!result.length) {
        return null;
      }
      
      return result[0];
    } catch (error) {
      console.error('Error updating contact:', error);
      throw error;
    }
  }

  /**
   * Delete a contact
   */
  async delete(id: string): Promise<boolean> {
    try {
      const result = await db.delete(contacts)
        .where(eq(contacts.id, id))
        .returning();
      
      return result.length > 0;
    } catch (error) {
      console.error('Error deleting contact:', error);
      throw error;
    }
  }

  /**
   * Toggle favorite status
   */
  async toggleFavorite(id: string): Promise<Contact | null> {
    try {
      // First get the current contact to check its favorite status
      const contact = await this.findById(id);
      
      if (!contact) {
        return null;
      }
      
      // Toggle the is_favorite status
      const result = await db.update(contacts)
        .set({
          is_favorite: !contact.is_favorite,
          updated_at: new Date(),
        })
        .where(eq(contacts.id, id))
        .returning();
      
      if (!result.length) {
        return null;
      }
      
      return result[0];
    } catch (error) {
      console.error('Error toggling contact favorite status:', error);
      throw error;
    }
  }

  /**
   * Find contacts by company
   */
  async findByCompany(company: string, options?: { limit?: number; offset?: number }): Promise<Contact[]> {
    try {
      const { limit = 100, offset = 0 } = options || {};
      
      const result = await db.select()
        .from(contacts)
        .where(eq(contacts.company, company))
        .limit(limit)
        .offset(offset);
      
      return result;
    } catch (error) {
      console.error('Error finding contacts by company:', error);
      throw error;
    }
  }

  /**
   * Find contacts by role
   */
  async findByRole(role: string, options?: { limit?: number; offset?: number }): Promise<Contact[]> {
    try {
      const { limit = 100, offset = 0 } = options || {};
      
      const result = await db.select()
        .from(contacts)
        .where(eq(contacts.role, role))
        .limit(limit)
        .offset(offset);
      
      return result;
    } catch (error) {
      console.error('Error finding contacts by role:', error);
      throw error;
    }
  }

  /**
   * Get favorite contacts
   */
  async getFavorites(options?: { limit?: number; offset?: number }): Promise<Contact[]> {
    try {
      const { limit = 100, offset = 0 } = options || {};
      
      const result = await db.select()
        .from(contacts)
        .where(eq(contacts.is_favorite, true))
        .limit(limit)
        .offset(offset);
      
      return result;
    } catch (error) {
      console.error('Error getting favorite contacts:', error);
      throw error;
    }
  }

  /**
   * Count total contacts
   */
  async count(): Promise<number> {
    try {
      const result = await db.select({ count: db.fn.count() }).from(contacts);
      return Number(result[0].count) || 0;
    } catch (error) {
      console.error('Error counting contacts:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const contactRepository = new ContactRepository();
