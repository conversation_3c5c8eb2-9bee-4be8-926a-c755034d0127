import { loadEnvConfig } from '@next/env';
import { neon } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-http';
import { assertEnv } from '@/lib/assertEnv';

// Import the schema definitions
import * as schema from './schema';

// In Edge Runtime, environment variables are automatically loaded
// Only load environment variables in Node.js environment
if (typeof process !== 'undefined' && typeof window === 'undefined' && !process.env.NEXT_RUNTIME) {
  // We're in a Node.js environment, not Edge Runtime
  loadEnvConfig(process.cwd());
}

// Ensure DATABASE_URL is defined at runtime
const DATABASE_URL = assertEnv('DATABASE_URL');

// Initialize the Neon serverless driver
const sql = neon(DATABASE_URL);

// Initialize Drizzle ORM with the Neon driver and the schema
// The schema object contains all the table definitions from schema.ts
export const db = drizzle(sql, { schema });

// Export schema for use in other files
export * from './schema';