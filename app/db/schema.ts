import {
  pgTable,
  text,
  timestamp,
  integer,
  boolean,
  jsonb,
  index,
  decimal,
  varchar,
  primaryKey,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import {
  organizations,
  organization_members,
  organizationsRelations,
  organizationMembersRelations,
  type Organization,
  type NewOrganization,
  type OrganizationMember,
  type NewOrganizationMember,
} from './organizations';
// Users table
export const users = pgTable('users', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  role: text('role').notNull(),
  department: text('department').notNull(),
  email: text('email').unique().notNull(),
  password: text('password').notNull(),
  phone: text('phone'),
  avatar: text('avatar'),
  preferences: jsonb('preferences'),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  last_login: timestamp('last_login', { withTimezone: true }),
});

// User relations
export const usersRelations = relations(users, ({ many }) => ({
  inspection_reports: many(inspection_reports, { relationName: 'user_inspection_reports' }),
  inspection_actions: many(inspection_actions, { relationName: 'user_inspection_actions' }),
}));

// User roles and permissions
export const roles = pgTable('roles', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  permissions: jsonb('permissions').notNull(),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
});

// User sessions
export const user_sessions = pgTable('user_sessions', {
  id: text('id').primaryKey(),
  user_id: text('user_id').references(() => users.id).notNull(),
  token: text('token').notNull(),
  expires_at: timestamp('expires_at', { withTimezone: true }),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  metadata: jsonb('metadata'),
});

// User sessions relations
export const userSessionsRelations = relations(user_sessions, ({ one }) => ({
  user: one(users, {
    fields: [user_sessions.user_id],
    references: [users.id],
  }),
}));

// Messages table
export const messages = pgTable('messages', {
  id: text('id').primaryKey(),
  session_id: text('session_id').references(() => chat_sessions.id),
  content_type: text('content_type'),
  content_transcript: text('content_transcript'),
  object: text('object'),
  role: text('role'),
  status: text('status'),
  type: text('type'),
  sender_id: text('sender_id').references(() => users.id),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
}, (table) => {
  return {
    sessionCreatedAtIdx: index('idx_session_created_at').on(table.session_id, table.created_at),
  };
});

// Messages relations
export const messagesRelations = relations(messages, ({ one }) => ({
  session: one(chat_sessions, {
    fields: [messages.session_id],
    references: [chat_sessions.id],
    relationName: 'session_messages',
  }),
  sender: one(users, {
    fields: [messages.sender_id],
    references: [users.id],
  }),
}));

// Contacts table
export const contacts = pgTable('contacts', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  role: text('role').notNull(),
  company: text('company').notNull(),
  phone: text('phone'),
  email: text('email'),
  location: text('location'),
  is_favorite: boolean('is_favorite').default(false).notNull(),
  avatar: text('avatar'),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updated_at: timestamp('updated_at', { withTimezone: true }),
});

// Companies table
export const companies = pgTable('companies', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  type: text('type').notNull(),
  primary_contact_id: text('primary_contact_id').references(() => contacts.id),
  address: text('address'),
  phone: text('phone'),
  email: text('email'),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
});

// Companies relations
export const companiesRelations = relations(companies, ({ one }) => ({
  primary_contact: one(contacts, {
    fields: [companies.primary_contact_id],
    references: [contacts.id],
  }),
}));

// Chat sessions table
export const chat_sessions = pgTable('chat_sessions', {
  id: text('id').primaryKey(),
  title: text('title').notNull(),
  preview: text('preview'),
  date: timestamp('date', { withTimezone: true }).defaultNow().notNull(),
  is_starred: boolean('is_starred').default(false).notNull(),
  created_by: text('created_by').references(() => users.id),
});

// Chat sessions relations
export const chatSessionsRelations = relations(chat_sessions, ({ many, one }) => ({
  messages: many(messages, {
    relationName: 'session_messages',
  }),
  participants: many(chat_participants),
  creator: one(users, {
    fields: [chat_sessions.created_by],
    references: [users.id],
  }),
}));

// Chat participants table
export const chat_participants = pgTable('chat_participants', {
  session_id: text('session_id').references(() => chat_sessions.id).notNull(),
  user_id: text('user_id').references(() => users.id).notNull(),
  joined_at: timestamp('joined_at', { withTimezone: true }).defaultNow().notNull(),
  last_read_at: timestamp('last_read_at', { withTimezone: true }),
}, (table) => {
  return {
    pk: primaryKey({ columns: [table.session_id, table.user_id] }),
  };
});

// Chat participants relations
export const chatParticipantsRelations = relations(chat_participants, ({ one }) => ({
  session: one(chat_sessions, {
    fields: [chat_participants.session_id],
    references: [chat_sessions.id],
  }),
  user: one(users, {
    fields: [chat_participants.user_id],
    references: [users.id],
  }),
}));

// Properties table
export const properties = pgTable('properties', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  address: text('address').notNull(),
  suburb: text('suburb').notNull(),
  state: varchar('state', { length: 3 }).notNull(),
  postcode: varchar('postcode', { length: 4 }).notNull(),
  type: text('type').notNull(),
  tier: integer('tier').notNull(),
  region: text('region').notNull(),
  size_sqm: decimal('size_sqm', { precision: 10, scale: 2 }),
  category: text('category').notNull(),
  status: text('status').default('active').notNull(),
  organization_id: text('organization_id'),
  manager_id: text('manager_id').references(() => users.id),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updated_at: timestamp('updated_at', { withTimezone: true }),
}, (table) => {
  return {
    managerIdx: index('idx_property_manager').on(table.manager_id),
    statusIdx: index('idx_property_status').on(table.status),
    regionIdx: index('idx_property_region').on(table.region),
  };
});

// Properties relations
export const propertiesRelations = relations(properties, ({ one, many }) => ({
  manager: one(users, {
    fields: [properties.manager_id],
    references: [users.id],
  }),
  areas: many(property_areas),
  inspection_reports: many(inspection_reports),
}));

// Property areas table
export const property_areas = pgTable('property_areas', {
  id: text('id').primaryKey(),
  property_id: text('property_id').references(() => properties.id).notNull(),
  name: text('name').notNull(),
  type: text('type').notNull(),
  size_sqm: decimal('size_sqm', { precision: 10, scale: 2 }),
  floor_level: text('floor_level'),
  notes: text('notes'),
});

// Property areas relations
export const propertyAreasRelations = relations(property_areas, ({ one }) => ({
  property: one(properties, {
    fields: [property_areas.property_id],
    references: [properties.id],
  }),
}));

// Inspection templates table
export const inspection_templates = pgTable('inspection_templates', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  category: text('category').notNull(),
  type: text('type').notNull(),
  version: integer('version').notNull(),
  is_active: boolean('is_active').default(true).notNull(),
  created_by: text('created_by').references(() => users.id),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  sections: jsonb('sections').notNull(),
}, (table) => {
  return {
    categoryIdx: index('idx_template_category').on(table.category),
  };
});

// Inspection templates relations
export const inspectionTemplatesRelations = relations(inspection_templates, ({ one, many }) => ({
  creator: one(users, {
    fields: [inspection_templates.created_by],
    references: [users.id],
  }),
  reports: many(inspection_reports),
}));

// Inspection reports table
export const inspection_reports = pgTable('inspection_reports', {
  id: text('id').primaryKey(),
  property_id: text('property_id').references(() => properties.id).notNull(),
  template_id: text('template_id').references(() => inspection_templates.id).notNull(),
  title: text('title').notNull(),
  location: text('location').notNull(),
  date: timestamp('date', { withTimezone: true }).defaultNow().notNull(),
  inspector: text('inspector').references(() => users.id).notNull(),
  status: text('status').notNull(),
  score: integer('score'),
  summary: text('summary'),
  sections: jsonb('sections'),
  weather_conditions: text('weather_conditions'),
  temperature: decimal('temperature', { precision: 5, scale: 2 }),
  photos: integer('photos').default(0).notNull(),
  voice_notes: integer('voice_notes').default(0).notNull(),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  submitted_at: timestamp('submitted_at', { withTimezone: true }),
  reviewed_by: text('reviewed_by').references(() => users.id),
  reviewed_at: timestamp('reviewed_at', { withTimezone: true }),
}, (table) => {
  return {
    propertyIdx: index('idx_report_property').on(table.property_id),
    dateIdx: index('idx_inspection_date').on(table.date),
    inspectorIdx: index('idx_inspection_inspector').on(table.inspector),
  };
});

// Inspection reports relations
export const inspectionReportsRelations = relations(inspection_reports, ({ one, many }) => ({
  property: one(properties, {
    fields: [inspection_reports.property_id],
    references: [properties.id],
  }),
  template: one(inspection_templates, {
    fields: [inspection_reports.template_id],
    references: [inspection_templates.id],
  }),
  inspector_user: one(users, {
    fields: [inspection_reports.inspector],
    references: [users.id],
    relationName: 'user_inspection_reports',
  }),
  reviewer: one(users, {
    fields: [inspection_reports.reviewed_by],
    references: [users.id],
  }),
  actions: many(inspection_actions),
  attachments: many(report_attachments),
}));

// Report attachments table
export const report_attachments = pgTable('report_attachments', {
  id: text('id').primaryKey(),
  report_id: text('report_id').references(() => inspection_reports.id).notNull(),
  type: text('type').notNull(),
  url: text('url').notNull(),
  thumbnail_url: text('thumbnail_url'),
  description: text('description'),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
});

// Report attachments relations
export const reportAttachmentsRelations = relations(report_attachments, ({ one }) => ({
  report: one(inspection_reports, {
    fields: [report_attachments.report_id],
    references: [inspection_reports.id],
  }),
}));

// Inspection actions table
export const inspection_actions = pgTable('inspection_actions', {
  id: text('id').primaryKey(),
  report_id: text('report_id').references(() => inspection_reports.id).notNull(),
  title: text('title').notNull(),
  priority: text('priority').notNull(),
  assignee: text('assignee').references(() => users.id).notNull(),
  due_date: timestamp('due_date', { withTimezone: true }),
  status: text('status').notNull(),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updated_at: timestamp('updated_at', { withTimezone: true }),
}, (table) => {
  return {
    reportIdx: index('idx_action_report').on(table.report_id),
    assigneeIdx: index('idx_action_assignee').on(table.assignee),
  };
});

// Inspection actions relations
export const inspectionActionsRelations = relations(inspection_actions, ({ one }) => ({
  report: one(inspection_reports, {
    fields: [inspection_actions.report_id],
    references: [inspection_reports.id],
  }),
  assignee_user: one(users, {
    fields: [inspection_actions.assignee],
    references: [users.id],
    relationName: 'user_inspection_actions',
  }),
}));

// Dashboard metrics table
export const dashboard_metrics = pgTable('dashboard_metrics', {
  id: text('id').primaryKey(),
  metric_type: text('metric_type').notNull(),
  value: decimal('value', { precision: 10, scale: 2 }).notNull(),
  period: text('period').notNull(),
  category: text('category').notNull(),
  property_id: text('property_id').references(() => properties.id).notNull(),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
}, (table) => {
  return {
    typeAndDateIdx: index('idx_metric_type_date').on(table.metric_type, table.created_at),
    propertyIdx: index('idx_metric_property').on(table.property_id),
  };
});

// Dashboard metrics relations
export const dashboardMetricsRelations = relations(dashboard_metrics, ({ one }) => ({
  property: one(properties, {
    fields: [dashboard_metrics.property_id],
    references: [properties.id],
  }),
}));

// KPI targets table
export const kpi_targets = pgTable('kpi_targets', {
  id: text('id').primaryKey(),
  metric_type: text('metric_type').notNull(),
  target_value: decimal('target_value', { precision: 10, scale: 2 }).notNull(),
  period: text('period').notNull(),
  category: text('category').notNull(),
  property_type: text('property_type'),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
}, (table) => {
  return {
    typeIdx: index('idx_kpi_type').on(table.metric_type),
  };
});

// Cleaning areas table
export const cleaning_areas = pgTable('cleaning_areas', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  // description: text('description'), // Note: This was dropped in migration 0003, but kept here for now. Consider removing later if not needed.
  area_category: text('area_category').notNull(),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updated_at: timestamp('updated_at', { withTimezone: true }),
}, (table) => {
  return {
    areaCategoryIdx: index('idx_cleaning_areas_category').on(table.area_category),
  };
});

// Cleaning tasks table
export const cleaning_tasks = pgTable('cleaning_tasks', {
  id: text('id').primaryKey(),
  area_id: text('area_id').references(() => cleaning_areas.id).notNull(),
  name: text('name').notNull(),
  description: text('description'),
  task_type: text('task_type').notNull(),
  standard_duration_minutes: integer('standard_duration_minutes'),
  equipment_required: jsonb('equipment_required'),
  materials_required: jsonb('materials_required'),
  safety_requirements: text('safety_requirements'),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updated_at: timestamp('updated_at', { withTimezone: true }),
}, (table) => {
  return { // Opening return for index object
    areaIdx: index('idx_task_area').on(table.area_id),
    typeIdx: index('idx_task_type').on(table.task_type),
  };
});
// Cleaning tasks relations
export const cleaningTasksRelations = relations(cleaning_tasks, ({ one }) => ({
  area: one(cleaning_areas, {
    fields: [cleaning_tasks.area_id],
    references: [cleaning_areas.id],
  }),
}));

// Frequency types table
export const frequency_types = pgTable('frequency_types', {
  id: text('id').primaryKey(),
  name: text('name').notNull().unique(),
  description: text('description'),
  times_per_year: integer('times_per_year'),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updated_at: timestamp('updated_at', { withTimezone: true }),
});

// Tier specifications table
export const tier_specifications = pgTable('tier_specifications', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  description: text('description'),
  tier_level: integer('tier_level').notNull(),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updated_at: timestamp('updated_at', { withTimezone: true }),
}, (table) => {
  return {
    tierLevelIdx: index('idx_tier_level').on(table.tier_level),
  };
});

// Contract specifications table
export const contract_specifications = pgTable('contract_specifications', {
  id: text('id').primaryKey(),
  client_id: text('client_id').notNull(),
  client_name: text('client_name').notNull(),
  contract_name: text('contract_name').notNull(),
  contract_number: text('contract_number'),
  start_date: timestamp('start_date', { withTimezone: true }).notNull(),
  end_date: timestamp('end_date', { withTimezone: true }),
  contract_value: decimal('contract_value', { precision: 10, scale: 2 }),
  contract_manager_id: text('contract_manager_id').references(() => users.id),
  organization_id: text('organization_id'),
  status: text('status').default('active').notNull(),
  special_requirements: text('special_requirements'),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updated_at: timestamp('updated_at', { withTimezone: true }),
}, (table) => {
  return {
    clientIdx: index('idx_contract_client').on(table.client_id),
    statusIdx: index('idx_contract_status').on(table.status),
    managerIdx: index('idx_contract_manager').on(table.contract_manager_id),
  };
});

// Contract specifications relations
export const contractSpecificationsRelations = relations(contract_specifications, ({ one, many }) => ({
  manager: one(users, {
    fields: [contract_specifications.contract_manager_id],
    references: [users.id],
  }),
  cleaning_specifications: many(facility_cleaning_specifications),
}));

// Facility cleaning specifications table
export const facility_cleaning_specifications = pgTable('facility_cleaning_specifications', {
  id: text('id').primaryKey(),
  contract_id: text('contract_id').references(() => contract_specifications.id).notNull(),
  property_id: text('property_id').references(() => properties.id).notNull(),
  task_id: text('task_id').references(() => cleaning_tasks.id).notNull(),
  tier_id: text('tier_id').references(() => tier_specifications.id).notNull(),
  frequency_id: text('frequency_id').references(() => frequency_types.id).notNull(),
  custom_frequency: text('custom_frequency'),
  special_instructions: text('special_instructions'),
  is_active: boolean('is_active').default(true).notNull(),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updated_at: timestamp('updated_at', { withTimezone: true }),
}, (table) => {
  return {
    contractIdx: index('idx_spec_contract').on(table.contract_id),
    propertyIdx: index('idx_spec_property').on(table.property_id),
    taskIdx: index('idx_spec_task').on(table.task_id),
    tierIdx: index('idx_spec_tier').on(table.tier_id),
  };
});

// Facility cleaning specifications relations
export const facilityCleaningSpecificationsRelations = relations(facility_cleaning_specifications, ({ one }) => ({
  contract: one(contract_specifications, {
    fields: [facility_cleaning_specifications.contract_id],
    references: [contract_specifications.id],
  }),
  property: one(properties, {
    fields: [facility_cleaning_specifications.property_id],
    references: [properties.id],
  }),
  task: one(cleaning_tasks, {
    fields: [facility_cleaning_specifications.task_id],
    references: [cleaning_tasks.id],
  }),
  tier: one(tier_specifications, {
    fields: [facility_cleaning_specifications.tier_id],
    references: [tier_specifications.id],
  }),
  frequency: one(frequency_types, {
    fields: [facility_cleaning_specifications.frequency_id],
    references: [frequency_types.id],
  }),
}));

// Periodical services table
export const periodical_services = pgTable('periodical_services', {
  id: text('id').primaryKey(),
  property_id: text('property_id').references(() => properties.id).notNull(),
  service_name: text('service_name').notNull(),
  service_description: text('service_description'),
  frequency_id: text('frequency_id').references(() => frequency_types.id).notNull(),
  last_service_date: timestamp('last_service_date', { withTimezone: true }),
  next_service_date: timestamp('next_service_date', { withTimezone: true }),
  assigned_to: text('assigned_to').references(() => users.id),
  status: text('status').default('scheduled').notNull(),
  special_requirements: text('special_requirements'),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updated_at: timestamp('updated_at', { withTimezone: true }),
}, (table) => {
  return {
    propertyIdx: index('idx_periodical_property').on(table.property_id),
    nextServiceIdx: index('idx_periodical_next_service').on(table.next_service_date),
    statusIdx: index('idx_periodical_status').on(table.status),
  };
});

// Periodical services relations
export const periodicalServicesRelations = relations(periodical_services, ({ one }) => ({
  property: one(properties, {
    fields: [periodical_services.property_id],
    references: [properties.id],
  }),
  frequency: one(frequency_types, {
    fields: [periodical_services.frequency_id],
    references: [frequency_types.id],
  }),
  assignee: one(users, {
    fields: [periodical_services.assigned_to],
    references: [users.id],
  }),
}));

// Scheduled tasks table
export const scheduled_tasks = pgTable('scheduled_tasks', {
  id: text('id').primaryKey(),
  property_id: text('property_id').references(() => properties.id).notNull(),
  task_id: text('task_id').references(() => cleaning_tasks.id).notNull(),
  scheduled_date: timestamp('scheduled_date', { withTimezone: true }).notNull(),
  scheduled_start_time: timestamp('scheduled_start_time', { withTimezone: true }),
  scheduled_end_time: timestamp('scheduled_end_time', { withTimezone: true }),
  assigned_to: text('assigned_to').references(() => users.id),
  status: text('status').default('scheduled').notNull(),
  completion_date: timestamp('completion_date', { withTimezone: true }),
  completion_notes: text('completion_notes'),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updated_at: timestamp('updated_at', { withTimezone: true }),
}, (table) => {
  return {
    propertyIdx: index('idx_scheduled_property').on(table.property_id),
    taskIdx: index('idx_scheduled_task').on(table.task_id),
    dateIdx: index('idx_scheduled_date').on(table.scheduled_date),
    statusIdx: index('idx_scheduled_status').on(table.status),
  };
});

// Scheduled tasks relations
export const scheduledTasksRelations = relations(scheduled_tasks, ({ one }) => ({
  property: one(properties, {
    fields: [scheduled_tasks.property_id],
    references: [properties.id],
  }),
  task: one(cleaning_tasks, {
    fields: [scheduled_tasks.task_id],
    references: [cleaning_tasks.id],
  }),
  assignee: one(users, {
    fields: [scheduled_tasks.assigned_to],
    references: [users.id],
  }),
}));

// Retail cleaning scope table
export const retail_cleaning_scope = pgTable('retail_cleaning_scope', {
  id: text('id').primaryKey(),
  area: text('area').notNull(),
  element: text('element').notNull(),
  requirement: text('requirement').notNull(),
  frequency: text('frequency').notNull(),
  frequency_id: text('frequency_id').references(() => frequency_types.id).notNull(),
  category: text('category').notNull(),
  notes: text('notes'),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updated_at: timestamp('updated_at', { withTimezone: true }),
}, (table) => {
  return {
    areaIdx: index('idx_retail_area').on(table.area),
    categoryIdx: index('idx_retail_category').on(table.category),
    frequencyIdx: index('idx_retail_frequency_id').on(table.frequency_id),
  };
});

// Retail cleaning scope relations
export const retailCleaningScopeRelations = relations(retail_cleaning_scope, ({ one }) => ({
  frequencyType: one(frequency_types, {
    fields: [retail_cleaning_scope.frequency_id],
    references: [frequency_types.id],
  }),
}));

// Add type exports for the new tables
export type CleaningArea = typeof cleaning_areas.$inferSelect;
export type NewCleaningArea = typeof cleaning_areas.$inferInsert;

export type CleaningTask = typeof cleaning_tasks.$inferSelect;
export type NewCleaningTask = typeof cleaning_tasks.$inferInsert;

export type FrequencyType = typeof frequency_types.$inferSelect;
export type NewFrequencyType = typeof frequency_types.$inferInsert;

export type TierSpecification = typeof tier_specifications.$inferSelect;
export type NewTierSpecification = typeof tier_specifications.$inferInsert;

export type ContractSpecification = typeof contract_specifications.$inferSelect;
export type NewContractSpecification = typeof contract_specifications.$inferInsert;

export type FacilityCleaningSpecification = typeof facility_cleaning_specifications.$inferSelect;
export type NewFacilityCleaningSpecification = typeof facility_cleaning_specifications.$inferInsert;

export type PeriodicalService = typeof periodical_services.$inferSelect;
export type NewPeriodicalService = typeof periodical_services.$inferInsert;

export type ScheduledTask = typeof scheduled_tasks.$inferSelect;
export type NewScheduledTask = typeof scheduled_tasks.$inferInsert;

export type RetailCleaningScope = typeof retail_cleaning_scope.$inferSelect;
export type NewRetailCleaningScope = typeof retail_cleaning_scope.$inferInsert;

// Export types for use in the application
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;

export type Contact = typeof contacts.$inferSelect;
export type NewContact = typeof contacts.$inferInsert;

export type InspectionReport = typeof inspection_reports.$inferSelect;
export type NewInspectionReport = typeof inspection_reports.$inferInsert;

export type Message = typeof messages.$inferSelect;
export type NewMessage = typeof messages.$inferInsert;

// Re-export organizations schema
export {
  organizations,
  organization_members,
  organizationsRelations,
  organizationMembersRelations,
  type Organization,
  type NewOrganization,
  type OrganizationMember,
  type NewOrganizationMember,
};
