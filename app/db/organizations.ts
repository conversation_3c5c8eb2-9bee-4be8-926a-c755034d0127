import {
  pgTable,
  text,
  timestamp,
  integer,
  boolean,
  jsonb,
  index,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

// Organizations table
export const organizations = pgTable('organizations', {
  id: text('id').primaryKey(), // Clerk organization ID
  name: text('name').notNull(),
  slug: text('slug').unique().notNull(),
  image_url: text('image_url'),
  max_memberships: integer('max_memberships'),
  admin_delete_enabled: boolean('admin_delete_enabled').default(true),
  public_metadata: jsonb('public_metadata').default({}),
  private_metadata: jsonb('private_metadata').default({}),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updated_at: timestamp('updated_at', { withTimezone: true }),
}, (table) => {
  return {
    slugIdx: index('idx_organization_slug').on(table.slug),
  };
});

// Organization members table
export const organization_members = pgTable('organization_members', {
  organization_id: text('organization_id').references(() => organizations.id).notNull(),
  user_id: text('user_id').notNull(),
  role: text('role').notNull(),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updated_at: timestamp('updated_at', { withTimezone: true }),
}, (table) => {
  return {
    orgUserIdx: index('idx_org_member_user').on(table.organization_id, table.user_id),
  };
});

// Organizations relations
export const organizationsRelations = relations(organizations, ({ many }) => ({
  members: many(organization_members),
}));

// Organization members relations
export const organizationMembersRelations = relations(organization_members, ({ one }) => ({
  organization: one(organizations, {
    fields: [organization_members.organization_id],
    references: [organizations.id],
  }),
}));

// Export types for use in the application
export type Organization = typeof organizations.$inferSelect;
export type NewOrganization = typeof organizations.$inferInsert;

export type OrganizationMember = typeof organization_members.$inferSelect;
export type NewOrganizationMember = typeof organization_members.$inferInsert;
