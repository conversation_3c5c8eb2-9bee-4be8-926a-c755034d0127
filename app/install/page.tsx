import { StatusBar } from "@/components/ui/status-bar"
import { HomeIndicator } from "@/components/ui/home-indicator"
import Link from "next/link"

export default function InstallPage() {
  return (
    <div className="relative w-full h-full min-h-screen bg-black flex flex-col items-center justify-center p-6">
      <StatusBar />

      <div className="w-20 h-20 rounded-2xl bg-zinc-900 flex items-center justify-center mb-6 shadow-lg">
        <svg viewBox="0 0 24 24" className="w-12 h-12">
          <path d="M6 7L10 3L18 3L18 11L14 15" fill="none" stroke="#A4D321" strokeWidth="2" strokeLinecap="round" />
          <path d="M14 9L6 9L6 17L14 17" fill="none" stroke="#A4D321" strokeWidth="2" strokeLinecap="round" />
          <path d="M10 13L10 21" fill="none" stroke="#3D4D61" strokeWidth="2" strokeLinecap="round" />
          <path d="M14 13L18 17" fill="none" stroke="#3D4D61" strokeWidth="2" strokeLinecap="round" />
        </svg>
      </div>

      <h1 className="text-white text-2xl font-bold mb-2">Install AskARA</h1>
      <p className="text-zinc-400 text-center mb-8">Install our app for the best experience</p>

      <div className="w-full max-w-md bg-zinc-900 rounded-xl p-6 mb-6">
        <h2 className="text-white text-lg font-medium mb-4">Installation Instructions</h2>

        <div className="space-y-6">
          <div>
            <h3 className="text-white font-medium mb-2">For iPhone/iPad:</h3>
            <ol className="text-zinc-400 space-y-2 list-decimal pl-5">
              <li>
                Tap the share button{" "}
                <span className="inline-flex items-center">
                  <svg className="w-4 h-4 mx-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M12 4V20M20 12L4 12"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </span>{" "}
                at the bottom of your screen
              </li>
              <li>Scroll down and tap &quot;Add to Home Screen&quot;</li>
              <li>Tap &quot;Add&quot; in the top right corner</li>
            </ol>
          </div>

          <div>
            <h3 className="text-white font-medium mb-2">For Android:</h3>
            <ol className="text-zinc-400 space-y-2 list-decimal pl-5">
              <li>Tap the menu button (three dots) in the top right</li>
              <li>Tap &quot;Install app&quot; or &quot;Add to Home screen&quot;</li>
              <li>Follow the on-screen instructions</li>
            </ol>
          </div>

          <div>
            <h3 className="text-white font-medium mb-2">For Desktop:</h3>
            <ol className="text-zinc-400 space-y-2 list-decimal pl-5">
              <li>Look for the install icon in the address bar</li>
              <li>Click &quot;Install&quot; and follow the prompts</li>
            </ol>
          </div>
        </div>
      </div>

      <Link
        href="/"
        className="py-2.5 px-5 bg-gradient-to-r from-[#3D4D61] to-[#A4D321] text-white font-medium rounded-md"
      >
        Return to App
      </Link>

      <HomeIndicator />
    </div>
  )
}
