"use client"

import React, { useEffect } from 'react'

export const dynamic = 'force-dynamic' // Add this line
import { useOrganizationList } from '@clerk/nextjs'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'

export default function OrganizationRedirect({ params }: { params: { slug: string } }) {
  const { userMemberships, isLoaded, setActive } = useOrganizationList()
  const router = useRouter()
  const { slug } = params

  useEffect(() => {
    async function switchOrganization() {
      if (!isLoaded || !userMemberships) {
        return
      }

      try {
        // Find the organization by slug
        const membership = userMemberships.items.find(
          (item) => item.organization.slug === slug
        )

        if (!membership) {
          toast.error('Organization not found')
          router.push('/')
          return
        }

        // Set the active organization
        await setActive({ organization: membership.organization.id })
        
        // Redirect to home page
        router.push('/')
        
        toast.success(`Switched to ${membership.organization.name}`)
      } catch (error) {
        console.error('Error switching organization:', error)
        toast.error('Failed to switch organization')
        router.push('/')
      }
    }

    switchOrganization()
  }, [isLoaded, userMemberships, slug, setActive, router])

  return (
    <div className="flex flex-col min-h-screen bg-black text-white items-center justify-center">
      <div className="animate-pulse text-xl">Switching organizations...</div>
    </div>
  )
}
