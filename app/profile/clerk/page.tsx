'use client';

import { useUserProfile } from '@/hooks/use-user-profile';
import { UserProfile } from '@/components/user-profile';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useRouter } from 'next/navigation';
import { useClerk } from '@clerk/nextjs';
import { Loader2 } from 'lucide-react';

export default function ClerkProfilePage() {
  const { profile, isLoading, isSignedIn } = useUserProfile();
  const router = useRouter();
  const { signOut } = useClerk();

  if (isLoading) {
    return (
      <div className="container max-w-4xl py-10 flex items-center justify-center min-h-[50vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!isSignedIn || !profile) {
    // Redirect to login if not authenticated
    router.push('/sign-in');
    return null;
  }

  const handleLogout = () => {
    signOut();
    router.push('/');
  };

  return (
    <div className="container max-w-4xl py-10">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">My Profile (Clerk Auth)</h1>
        <Button variant="outline" onClick={() => router.back()}>
          Back
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>Profile Information</CardTitle>
            <CardDescription>Your personal information</CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center">
            <UserProfile variant="full" />
          </CardContent>
          <CardFooter className="flex flex-col gap-4">
            <Button 
              variant="destructive" 
              className="w-full"
              onClick={handleLogout}
            >
              Log Out
            </Button>
          </CardFooter>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Profile Details</CardTitle>
            <CardDescription>
              Your profile information from Neon database
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Full Name</h3>
                  <p className="text-sm text-gray-500">{profile.name}</p>
                </div>
                
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Email Address</h3>
                  <p className="text-sm text-gray-500">{profile.email}</p>
                </div>
                
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Phone Number</h3>
                  <p className="text-sm text-gray-500">{profile.phone || "Not provided"}</p>
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <h3 className="text-sm font-medium">Role</h3>
                <p className="text-sm text-gray-500">{profile.role}</p>
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-medium">Department</h3>
                <p className="text-sm text-gray-500">{profile.department}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
