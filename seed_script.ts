// Add explicit dotenv loading at the top
import dotenv from 'dotenv';
dotenv.config(); // Load .env file variables

// Imports
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { sql } from 'drizzle-orm';
// Remove PgTable import
// import { PgTable } from 'drizzle-orm/pg-core'; 

// Import ONLY db and pool from lib/neon-db
import { db, pool } from './lib/neon-db.js';

// Remove schema import - no longer needed
// import * as schema from './app/db/schema.ts'; 

// --- Configuration ---
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const seedsDir = path.join(__dirname, 'knowledge', 'seeds');

console.log(`🌱 Seeding data from: ${seedsDir}`);
type SeedRecord = Record<string, any>;

// Define an interface for the ID sets object
interface ValidIdSets {
    validUserIds: Set<string>;
    validPropertyIds: Set<string>;
    validContractSpecIds: Set<string>;
    validCleaningTaskIds: Set<string>;
    validTierSpecIds: Set<string>;
    validFrequencyTypeIds: Set<string>;
    validContactIds: Set<string>;
    validInspectionReportIds: Set<string>;
}

// Store valid IDs from parent tables
let validUserIds: Set<string> = new Set();
let validPropertyIds: Set<string> = new Set();
let validContractSpecIds: Set<string> = new Set();
let validCleaningTaskIds: Set<string> = new Set();
let validTierSpecIds: Set<string> = new Set();
let validFrequencyTypeIds: Set<string> = new Set();
let validContactIds: Set<string> = new Set();
let validInspectionReportIds: Set<string> = new Set(); // Added for inspection_actions
let validChatSessionIds: Set<string> = new Set(); // Added for chat_participants

const loadIds = async (filePath: string, idField: string, idSet: Set<string>, setName: string) => {
    const fullPath = path.join(seedsDir, filePath);
    if (fs.existsSync(fullPath)) {
        const jsonString = fs.readFileSync(fullPath, 'utf-8');
        const data: SeedRecord[] = JSON.parse(jsonString);
        data.forEach(item => {
            if (item[idField] != null) {
                idSet.add(String(item[idField]));
            }
        });
        console.log(`Loaded ${idSet.size} valid ${setName} IDs.`);
    } else {
        console.warn(`⚠️ ${filePath} not found, cannot validate FKs referencing ${setName}.`);
    }
};

// --- Main Seeding Function ---
async function main() {
  console.log('🚀 Starting database seeding (SQL generation mode)...');
  let overallSuccess = true;
  const startTime = Date.now();
  const allSqlStatements: string[] = [];

  // Add the ID loading try...catch block here
  try {
    console.log('Loading IDs for foreign key validation...'); // Add log
    await loadIds('users.json', 'id', validUserIds, 'user');
    await loadIds('properties.json', 'id', validPropertyIds, 'property');
    await loadIds('contract_specifications.json', 'id', validContractSpecIds, 'contract specification');
    await loadIds('cleaning_tasks.json', 'id', validCleaningTaskIds, 'cleaning task');
    await loadIds('tier_specifications.json', 'id', validTierSpecIds, 'tier specification');
    await loadIds('frequency_types.json', 'id', validFrequencyTypeIds, 'frequency type');
    await loadIds('contacts.json', 'id', validContactIds, 'contact');
    // NOTE: We don't load inspection_report IDs here because they depend on properties which might be filtered.
    // Instead, the filtering happens *after* reports are processed but *before* actions.
    // For now, validInspectionReportIds will remain empty as all reports are filtered out.
    console.log('Finished loading validation IDs.'); // Add log

  } catch (error: any) {
    console.error('❌ Error loading IDs for validation:', error.message);
    overallSuccess = false; // Mark failure if IDs can't load
  }
  // --- End ID Loading Block ---

  // Define the seeding order (table names and file names)
  // We don't need the schema objects here anymore
  const seedOrder: [string, string][] = [
    // Independent tables first (or those with fewer dependencies)
    ['roles', 'roles.json'],
    ['frequency_types', 'frequency_types.json'],
    ['tier_specifications', 'tier_specifications.json'],
    ['cleaning_areas', 'cleaning_areas.json'],
    ['contacts', 'contacts.json'],
    ['kpi_targets', 'kpi_targets.json'],
    ['users', 'users.json'],

    // Tables dependent on users, contacts, properties etc.
    ['properties', 'properties.json'], // Depends on users (manager_id)
    ['cleaning_tasks', 'cleaning_tasks.json'], // Depends on cleaning_areas
    ['contract_specifications', 'contract_specifications.json'], // Depends on users
    ['periodical_services', 'periodical_services.json'], // Depends on properties, frequencies, users
    ['inspection_templates', 'inspection_templates.json'], // Depends on users
    ['facility_cleaning_specifications', 'facility_cleaning_specifications.json'], // Depends on contracts, properties, tasks, tiers, frequencies
    ['scheduled_tasks', 'scheduled_tasks.json'], // Depends on properties, tasks, users
    ['property_areas', 'property_areas.json'], // Depends on properties
    ['companies', 'companies.json'], // <-- Moved here, after contacts
    ['inspection_reports', 'inspection_reports.json'], // Depends on properties, templates, users
    ['inspection_actions', 'inspection_actions.json'], // Depends on reports, users
    ['chat_sessions', 'chat_sessions.json'], // Depends on users
    ['chat_participants', 'chat_participants.json'], // Depends on sessions, users
    ['messages', 'messages.json'], // Depends on sessions, users
    ['dashboard_metrics', 'dashboard_metrics.json'], // Depends on properties
    ['user_sessions', 'user_sessions.json'], // Depends on users
    ['retail_cleaning_scope', 'retail_cleaning_scope.json'], // Depends on frequencies?
  ];

  try {
    // Generate SQL for all tables
    for (const [tableName, jsonFile] of seedOrder) {
      // Remove the table schema lookup
      // const tableSchema = (schema as any)[tableName];
      // if (!tableSchema) {
      //     console.warn(`⚠️ Schema not found for table ${tableName} in main loop, skipping.`);
      //     continue; 
      // }
      
      // Pass only IDs to the generation function
      const statements = await generateSqlForTable(tableName, jsonFile, {
          validUserIds, validPropertyIds, validContractSpecIds,
          validCleaningTaskIds, validTierSpecIds, validFrequencyTypeIds,
          validContactIds,
          validInspectionReportIds
      }); // Remove tableSchema argument
      
      if (statements.length > 0) {
        allSqlStatements.push(...statements);
      } else {
        // Log if a file was skipped or had errors during generation
        console.log(`⚪ No SQL generated for ${tableName}`);
      }
    }

    if (allSqlStatements.length > 0) {
        console.log(`\nExecuting ${allSqlStatements.length} SQL statements...`);
        // Combine into a single transaction block for efficiency
        const combinedSql = `BEGIN;\n${allSqlStatements.join('\n')}\nCOMMIT;`;
        // Execute the raw SQL using Drizzle's execute method
        await db.execute(combinedSql);
        console.log(`✅ SQL execution completed.`);
    } else {
        console.log('⚪ No SQL statements were generated to execute.');
    }

  } catch (error: any) {
    console.error('❌ An error occurred during SQL generation or execution:', error.message);
    // If using transactions, errors should cause a rollback automatically usually.
    // Log the full error for debugging if needed: console.error(error);
    overallSuccess = false;
  } finally {
    const endTime = Date.now();
    console.log(`\n⏱️ Seeding process took ${(endTime - startTime) / 1000} seconds.`);
    if (pool && typeof pool.end === 'function') {
      console.log('\n🔌 Closing database connection pool...');
      try {
        await pool.end();
        console.log('✅ Connection pool closed.');
      } catch (closeError) {
        console.error('❌ Error closing connection pool:', closeError);
      }
    }
  }

  if (overallSuccess) {
    console.log('\n✅✅✅ Database seeding completed successfully!');
  } else {
    console.log('\n⚠️ Database seeding completed with errors during SQL generation or execution.');
  }

  return overallSuccess;
}

// Map JSON keys to SQL columns and apply transformations
const mapRecordToSql = (record: Record<string, any>, tableName: string): Record<string, any> => {
  const outputRecord: Record<string, any> = {};

  // Selectively copy and transform fields based on target table
  if (tableName === 'contacts') {
    outputRecord.id = record.id;
    if (record.first_name && record.last_name) { // Combine names
      outputRecord.name = `${record.first_name} ${record.last_name}`;
    } else {
      outputRecord.name = record.name; // Fallback if no first/last
    }
    if (record.role_title) outputRecord.role = record.role_title; // Rename
    if (record.company_name) outputRecord.company = record.company_name; // Rename
    if (record.phone) outputRecord.phone = record.phone;
    if (record.email) outputRecord.email = record.email;
    // Copy other known valid fields for contacts if they exist
    if (record.location) outputRecord.location = record.location;
    if (record.is_favorite != null) outputRecord.is_favorite = record.is_favorite;
    if (record.avatar) outputRecord.avatar = record.avatar;
    if (record.created_at) outputRecord.created_at = record.created_at;
    if (record.updated_at) outputRecord.updated_at = record.updated_at;

  } else if (tableName === 'periodical_services') {
     // Copy known valid fields, excluding ap_site_id
     for (const key in record) {
       if (key !== 'ap_site_id') {
         outputRecord[key] = record[key];
       }
     }
  } else if (tableName === 'chat_sessions') {
     // Copy known valid fields, excluding created_at
     for (const key in record) {
       if (key !== 'created_at') {
         outputRecord[key] = record[key];
       }
     }
  } else {
    // Default: Copy all fields for other tables (assuming they match schema)
    // This might need refinement if other tables have mismatches
    Object.assign(outputRecord, record);
  }

  // Apply generic transformations (like date strings) AFTER selective copying
  for (const key in outputRecord) { // Iterate over the outputRecord now
      const value = outputRecord[key];
      const isLikelyDateColumn = key.endsWith('_at') || key.endsWith('_date');
      if (isLikelyDateColumn && typeof value === 'string' && value) {
        const dateValue = new Date(value);
        if (!isNaN(dateValue.getTime())) {
          outputRecord[key] = dateValue; // Modify the outputRecord
        }
      }
  }

  return outputRecord;
};

// --- Helper Function to Generate SQL INSERTs ---
async function generateSqlForTable(
  tableName: string,
  jsonDataPath: string,
  idSets: ValidIdSets
): Promise<string[]> {
  console.log(`\nGenerating SQL for ${tableName}...`);
  const fullPath = path.join(seedsDir, jsonDataPath);
  const sqlStatements: string[] = [];

  if (!fs.existsSync(fullPath)) {
    console.warn(`⚠️ Seed file not found: ${fullPath}. Skipping ${tableName}.`);
    return sqlStatements; // Return empty array
  }

  try {
    const jsonString = fs.readFileSync(fullPath, 'utf-8');
    const data: SeedRecord[] = JSON.parse(jsonString);

    if (!Array.isArray(data) || data.length === 0) {
      console.log(`⚪ No data found in ${jsonDataPath} for ${tableName}.`);
      return sqlStatements;
    }

    console.log(`Processing ${data.length} records for ${tableName}...`);

    // Filter out records missing required fields or with invalid FKs
    let finalData = data;
    // Filter periodical_services missing property_id
    if (tableName === 'periodical_services') {
      const initialCount = finalData.length;
      finalData = finalData.filter(record => record.property_id != null);
      if (finalData.length < initialCount) {
        console.warn(`⚠️ Filtered out ${initialCount - finalData.length} records from periodical_services missing required property_id.`);
      }
    }
    // Filter inspection_templates missing created_by or with invalid user ID
    if (tableName === 'inspection_templates') {
      const initialCount = finalData.length;
      finalData = finalData.filter(record => {
          const createdById = record.created_by;
          const isValid = createdById != null && idSets.validUserIds.has(String(createdById));
          if (createdById != null && !isValid) {
              console.warn(`   - Invalid/missing user ID (${createdById}) found in inspection_template record ID: ${record.id || '(no ID)'}`);
          }
          return isValid;
      });
      if (finalData.length < initialCount) {
        console.warn(`⚠️ Filtered out ${initialCount - finalData.length} records from inspection_templates missing required created_by or having invalid user ID.`);
      }
    }
    // Add similar filters for other tables/required fields if necessary
    if (tableName === 'facility_cleaning_specifications') {
        const initialCount = finalData.length;
        finalData = finalData.filter(record => {
            const propId = record.property_id;
            const contractId = record.contract_id;
            const taskId = record.task_id;
            const tierId = record.tier_id;
            const freqId = record.frequency_id;

            const propValid = propId != null && idSets.validPropertyIds.has(String(propId));
            const contractValid = contractId != null && idSets.validContractSpecIds.has(String(contractId));
            const taskValid = taskId != null && idSets.validCleaningTaskIds.has(String(taskId));
            const tierValid = tierId != null && idSets.validTierSpecIds.has(String(tierId));
            const freqValid = freqId != null && idSets.validFrequencyTypeIds.has(String(freqId));

            const allValid = propValid && contractValid && taskValid && tierValid && freqValid;

            if (!allValid) {
                // Log which specific FK failed for better debugging
                let errors = [];
                if (!propValid) errors.push(`property_id (${propId})`);
                if (!contractValid) errors.push(`contract_id (${contractId})`);
                if (!taskValid) errors.push(`task_id (${taskId})`);
                if (!tierValid) errors.push(`tier_id (${tierId})`);
                if (!freqValid) errors.push(`frequency_id (${freqId})`);
                console.warn(`   - Invalid/missing FK(s) [${errors.join(', ')}] found in facility_cleaning_spec record ID: ${record.id || '(no ID)'}`);
            }
            return allValid;
        });
        if (finalData.length < initialCount) {
            console.warn(`⚠️ Filtered out ${initialCount - finalData.length} records from facility_cleaning_specifications due to missing or invalid foreign keys.`);
        }
    }
    // Add similar filters for other tables/required fields if necessary
    if (tableName === 'scheduled_tasks') {
        const initialCount = finalData.length;
        finalData = finalData.filter(record => {
            const propId = record.property_id;
            const isValid = propId != null && idSets.validPropertyIds.has(String(propId));
            if (propId != null && !isValid) {
                console.warn(`   - Invalid/missing property ID (${propId}) found in scheduled_task record ID: ${record.id || '(no ID)'}`);
            }
            return isValid;
        });
        if (finalData.length < initialCount) {
            console.warn(`⚠️ Filtered out ${initialCount - finalData.length} records from scheduled_tasks due to missing or invalid property ID.`);
        }
    }
    // Add similar filters for other tables/required fields if necessary
    if (tableName === 'property_areas') {
        const initialCount = finalData.length;
        finalData = finalData.filter(record => {
            const propId = record.property_id;
            const isValid = propId != null && idSets.validPropertyIds.has(String(propId));
            if (propId != null && !isValid) {
                console.warn(`   - Invalid/missing property ID (${propId}) found in property_area record ID: ${record.id || '(no ID)'}`);
            }
            return isValid;
        });
        if (finalData.length < initialCount) {
            console.warn(`⚠️ Filtered out ${initialCount - finalData.length} records from property_areas due to missing or invalid property ID.`);
        }
    }
    // Add similar filters for other tables/required fields if necessary
    if (tableName === 'companies') {
        const initialCount = finalData.length;
        finalData = finalData.filter(record => {
            const contactId = record.primary_contact_id;
            // Allow null contact IDs, but if present, it must be valid
            const isValid = contactId == null || idSets.validContactIds.has(String(contactId));
            if (contactId != null && !isValid) {
                console.warn(`   - Invalid/missing contact ID (${contactId}) found in company record ID: ${record.id || '(no ID)'}`);
            }
            return isValid;
        });
        if (finalData.length < initialCount) {
            console.warn(`⚠️ Filtered out ${initialCount - finalData.length} records from companies due to invalid primary_contact_id.`);
        }
    }

    // Filter inspection_reports missing property_id
    if (tableName === 'inspection_reports') {
      const originalCount = finalData.length;
      finalData = finalData.filter(record => {
        const isValid = record.property_id && idSets.validPropertyIds.has(String(record.property_id));
        if (!isValid) {
          console.warn(`Filtered out record from ${tableName} due to missing/invalid property_id: ${record.id || JSON.stringify(record)} (Property ID: ${record.property_id})`);
        }
        return isValid;
      });
      const filteredCount = originalCount - finalData.length;
      console.warn(`⚠️ Filtered out ${filteredCount} records from inspection_reports due to missing/invalid property_id.`);
    }

    // Filter inspection_actions missing report_id
    if (tableName === 'inspection_actions') {
      const originalCount = finalData.length;
      finalData = finalData.filter(record => {
        const isValid = record.report_id && idSets.validInspectionReportIds.has(String(record.report_id));
        if (!isValid) {
          // Since all reports are currently filtered, this log will appear for all actions
          console.warn(`Filtered out record from ${tableName} due to missing/invalid report_id: ${record.id || JSON.stringify(record)} (Report ID: ${record.report_id})`);
        }
        return isValid;
      });
      const filteredCount = originalCount - finalData.length;
      if (filteredCount > 0) {
           console.warn(`⚠️ Filtered out ${filteredCount} records from inspection_actions due to missing/invalid report_id.`);
      }
    }

    // Filter chat_sessions missing or invalid created_by user ID
    if (tableName === 'chat_sessions') {
      const initialCount = finalData.length;
      finalData = finalData.filter(record => {
          const createdById = record.created_by;
          const isValid = createdById != null && idSets.validUserIds.has(String(createdById));
          if (!isValid) {
              console.warn(`   - Invalid/missing user ID (${createdById || 'missing'}) found in ${tableName} record ID: ${record.id || '(no ID)'}`);
          }
          return isValid;
      });
      const filteredCount = initialCount - finalData.length;
      if (filteredCount > 0) {
         console.warn(`⚠️ Filtered out ${filteredCount} records from ${tableName} due to missing/invalid created_by user ID.`);
      }
    }

    // Filter chat_participants missing or invalid user_id
    if (tableName === 'chat_participants') {
      const initialCount = finalData.length;
      finalData = finalData.filter(record => {
          const userId = record.user_id;
          const isValid = userId != null && idSets.validUserIds.has(String(userId));
          if (!isValid) {
              console.warn(`   - Invalid/missing user ID (${userId || 'missing'}) found in ${tableName} record for session ID: ${record.session_id || '(no session ID)'}`);
          }
          return isValid;
      });
      const filteredCount = initialCount - finalData.length;
      if (filteredCount > 0) {
         console.warn(`⚠️ Filtered out ${filteredCount} records from ${tableName} due to missing/invalid user ID.`);
      }
    }

    // Create INSERT statements for each record
    for (const record of finalData) {
        // Basic check for non-empty record
        if (Object.keys(record).length === 0) continue;

        // Apply transformations *before* getting keys/values
        const transformedRecord = mapRecordToSql(record, tableName); // Pass tableName string

        const columns = Object.keys(transformedRecord).map(col => `"${col}"`).join(', '); // Use keys from TRANSFORMED record
        const values = Object.values(transformedRecord).map(formatSqlValue).join(', '); // Use values from transformed record

        // NOTE: This simple INSERT doesn't handle conflicts (like ON CONFLICT DO NOTHING).
        // For idempotent seeding, the tables should ideally be cleared first, or use TRUNCATE.
        // Or construct more complex INSERT ... ON CONFLICT statements if needed.
        const sql = `INSERT INTO "${tableName}" (${columns}) VALUES (${values});`;
        sqlStatements.push(sql);
    }

    console.log(`✅ Generated ${sqlStatements.length} SQL statements for ${tableName}.`);
    return sqlStatements;

  } catch (error: any) {
    console.error(`❌ Error processing ${tableName} from ${jsonDataPath}:`, error.message);
    return []; // Return empty on error to avoid partial execution
  }
}

// --- SQL Value Formatting Helper ---
function formatSqlValue(value: any): string {
  if (value === null || typeof value === 'undefined') {
    return 'NULL';
  }
  if (typeof value === 'string') {
    // Basic escaping: double single quotes
    return "'" + value.replace(/'/g, "''") + "'";
  }
  if (typeof value === 'number' || typeof value === 'boolean') {
    return String(value);
  }
  if (value instanceof Date) {
    // Format date as ISO string for Postgres
    return "'" + value.toISOString() + "'";
  }
  if (typeof value === 'object') {
    // Assume JSONB/JSON - serialize and quote
    try {
      return "'" + JSON.stringify(value).replace(/'/g, "''") + "'";
    } catch (e) {
      console.warn('⚠️ Could not stringify object for SQL, inserting as NULL:', value);
      return 'NULL';
    }
  }
  // Fallback for unknown types
  console.warn('⚠️ Unknown type for SQL formatting, inserting as NULL:', value);
  return 'NULL';
}

// --- Run Seeding ---
main().catch((error) => {
  console.error('❌ An unexpected top-level error occurred:', error);
  process.exit(1);
});
