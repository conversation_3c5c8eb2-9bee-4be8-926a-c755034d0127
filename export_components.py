#!/usr/bin/env python3
import os

output = "COMPONENTS-CODE.md"
files = []
for root, dirs, fnames in os.walk("components"):
    for name in fnames:
        if name.endswith((".tsx", ".ts")):
            files.append(os.path.join(root, name))
files.sort()
with open(output, "w") as f:
    f.write("# Components Code\n\n")
    for file in files:
        f.write(f"## {file}\n\n```tsx\n")
        try:
            with open(file, "r") as g:
                f.write(g.read())
        except Exception as e:
            f.write(f"<!-- Failed to read {file}: {e} -->\n")
        f.write("\n```\n\n")
    f.write(f"Generated {output} with component source code.\n")
print(f"Generated {output}")