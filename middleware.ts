import { clerkMiddleware, ClerkMiddlewareAuth } from '@clerk/nextjs/server';
import { NextResponse, NextRequest } from 'next/server';

// Define public routes that don't require authentication
const publicRoutes = [
  '/',
  '/sign-in(.*)',
  '/sign-up(.*)',
  '/forgot-password(.*)',
  '/api/webhooks(.*)',
  '/logo.png',
  '/favicon.ico',
  '/manifest.webmanifest',
  '/icons/(.*)',
  '/apple-touch-icon.png',
];

// Define routes that require organization access
const orgRoutes = [
  '/properties(.*)',
  '/inspections(.*)',
  '/contracts(.*)',
  '/reports(.*)',
  '/team(.*)',
  '/schedule(.*)',
  '/tasks(.*)',
];

// Define routes that are admin-only
const adminRoutes = [
  '/admin(.*)',
  '/settings/organization(.*)',
];

// ... other imports and const definitions
// ... other imports and const definitions

export default clerkMiddleware((auth, req: NextRequest) => {

  // Handle public routes
  if (publicRoutes.some(pattern => {
    const regex = new RegExp(`^${pattern.replace(/\(.*\)/, '.*')}$`);
    return regex.test(req.nextUrl.pathname);
  })) {
    return NextResponse.next();
  }

  // Handle unauthenticated users
  if (!auth.userId) { // Use auth object's userId
    const signInUrl = new URL('/sign-in', req.url);
    signInUrl.searchParams.set('redirect_url', req.url);
    return NextResponse.redirect(signInUrl);
  }

  // Check if we're on an organization route and have an active organization
  const isOrgRoute = orgRoutes.some(pattern => {
    const regex = new RegExp(`^${pattern.replace(/\(.*\)/, '.*')}$`);
    return regex.test(req.nextUrl.pathname);
  });

  if (isOrgRoute && !auth.orgId) { // Use auth object's orgId
    // Redirect to organization selection or home page
    return NextResponse.redirect(new URL('/', req.url));
  }

  // Check if we're on an admin route and user is not an admin
  const isAdminRoute = adminRoutes.some(pattern => {
    const regex = new RegExp(`^${pattern.replace(/\(.*\)/, '.*')}$`);
    return regex.test(req.nextUrl.pathname);
  });

  if (isAdminRoute && auth.orgRole !== 'admin') { // Use auth object's orgRole
    // Redirect to home page if not an admin
    return NextResponse.redirect(new URL('/', req.url));
  }

  return NextResponse.next();
});

// Stop Middleware from running on static files
// Stop Middleware from running on static files and API routes during build
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - images (public images folder) // Assuming you have /public/images
     * - icons (public icons folder) // Assuming you have /public/icons
     * - logo.png (public logo file)
     * - manifest.webmanifest (public manifest file)
     * - apple-touch-icon.png (public icon file)
     * // Add any other top-level public folders or specific files here
     */
    '/((?!api|_next/static|_next/image|favicon.ico|images|icons|logo.png|manifest.webmanifest|apple-touch-icon.png).*)',
    // Explicitly include the root route if it needs protection and isn't static
    // If your root '/' page is static and doesn't need auth checks, keep it commented out.
    // '/',
  ],
};