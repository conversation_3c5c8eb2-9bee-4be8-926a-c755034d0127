// tests/db/connection.test.ts
import { describe, it, expect } from 'vitest';
// Adjust the import path based on your project structure
import { db } from '@/app/db'; // Assuming 'db' is your exported Drizzle client instance
import { sql } from 'drizzle-orm';

describe('Database Connectivity', () => {
  it('should connect and execute a simple query', async () => {
    try {
      // Execute a simple query that doesn't depend on specific tables
      const result = await db.execute(sql`SELECT 1 as test`);

      // Basic check: Ensure the query executed and returned something
      expect(result).toBeDefined();

      // More specific check (adjust based on your DB driver's return type if needed)
      // For @neondatabase/serverless, result might be an array of objects
      if (Array.isArray(result) && result.length > 0) {
         expect(result[0].test).toBe(1);
      } else if (typeof result === 'object' && result !== null && 'rows' in result && Array.isArray(result.rows)) {
         // For node-postgres (pg) pool used directly
         expect(result.rows[0].test).toBe(1);
      } else {
         // Fallback assertion if structure is unknown, might need adjustment
         expect(result).toHaveProperty('test', 1); // Adjust if necessary
      }

    } catch (error) {
      // Fail the test if the query fails
      console.error("Database connection/query failed:", error);
      throw new Error(`Database connection/query failed: ${error}`);
    }
  });
});