// tests/pages/home.test.tsx
import { describe, it, expect, vi } from 'vitest';
import { render, screen, act } from '@testing-library/react';
import HomePage from '@/app/page'; // Assuming default export from app/page.tsx

// Mock Clerk's auth() - Adjust return value as needed for tests
vi.mock('@clerk/nextjs/server', () => ({
  auth: vi.fn(() => ({ userId: 'test-user-123' })), // <PERSON>ck authenticated state
}));

// Mock Drizzle db query
vi.mock('@/app/db', async (importOriginal) => {
  const original = await importOriginal<typeof import('@/app/db')>();
  return {
    ...original, // Keep other exports like 'messages' schema object
    db: {
      ...original.db, // Keep other db properties/methods if any
      query: {
        ...original.db.query, // Keep other query namespaces
        messages: {
          findFirst: vi.fn().mockResolvedValue(null), // Mock: No existing message initially
        },
      },
    },
  };
});

// Mock Server Actions (Optional but good practice if HomePage calls them directly)
vi.mock('@/app/actions', () => ({
  createUserMessage: vi.fn(),
  deleteUserMessage: vi.fn(),
}));

// Mock Clerk Provider components needed by MobileAIInterface
// This requires mocking the client-side Clerk hooks/context
vi.mock('@clerk/nextjs', () => ({
  ClerkProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  SignedIn: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  SignedOut: ({ children }: { children: React.ReactNode }) => null, // Don't render signed out state
  UserButton: () => <button>User Button Mock</button>,
  // Add mocks for any other Clerk components/hooks used within MobileAIInterface if needed
}));

// Mock context used by MobileAIInterface if necessary
vi.mock('@/contexts/user-context', () => ({
  UserProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useUser: () => ({ // Provide mock user data if needed by MobileAIInterface
    user: { name: 'Test User', role: 'Tester', department: 'Testing' },
    login: vi.fn(),
    logout: vi.fn(),
    signUp: vi.fn(),
    forgotPassword: vi.fn(),
    loading: false,
    error: null,
  }),
}));

// Mock voice assistant provider if needed
vi.mock('@/components/voice-assistant/voice-assistant-provider', () => ({
    useVoiceAssistant: () => ({
        state: 'idle',
        messages: [],
        currentMessage: '',
        isAudioPlaying: false,
        startListening: vi.fn(),
        stopListening: vi.fn(),
        toggleListening: vi.fn(),
        clearConversation: vi.fn(),
    }),
}));

describe('Home Page', () => {
  it('should render without crashing and show message input form', async () => {
    // Reset mocks for specific test cases if needed
    const dbMock = await import('@/app/db');
    (dbMock.db.query.messages.findFirst as ReturnType<typeof vi.fn>).mockResolvedValue(null); // Ensure no message exists

    let renderResult: ReturnType<typeof render>;
    // Use async act for rendering async components
    await act(async () => {
      // HomePage is async, but render expects sync. We await the component promise.
      // This is a common pattern for testing Server Components with RTL.
       renderResult = render(await HomePage());
    });

    // Check for the input field, indicating the "create message" form is shown
    expect(screen.getByPlaceholderText(/enter a message/i)).toBeInTheDocument();

    // Check that the delete button is NOT present
    expect(screen.queryByRole('button', { name: /delete message/i })).not.toBeInTheDocument();

     // Check if MobileAIInterface's placeholder/mock content renders
     // (Adjust based on what MobileAIInterface renders without full context)
     // For example, if it renders a specific header:
     // expect(screen.getByRole('banner')).toBeInTheDocument(); // Assuming AppHeader uses <header>
   });

   it('should render existing message and delete button', async () => {
     // Mock that a message exists
     const dbMock = await import('@/app/db');
     (dbMock.db.query.messages.findFirst as ReturnType<typeof vi.fn>).mockResolvedValue({
         content_transcript: 'Existing test message'
     });

     await act(async () => {
       render(await HomePage());
     });

     // Check for the existing message text
     expect(screen.getByText(/existing test message/i)).toBeInTheDocument();
     // Check for the delete button
     expect(screen.getByRole('button', { name: /delete message/i })).toBeInTheDocument();
     // Check that the input form is NOT present
     expect(screen.queryByPlaceholderText(/enter a message/i)).not.toBeInTheDocument();
   });
});