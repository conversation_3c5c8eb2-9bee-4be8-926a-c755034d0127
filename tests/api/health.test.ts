// tests/api/health.test.ts
import { describe, it, expect, beforeAll, afterAll } from 'vitest';
// Import the handler function directly
import { GET } from '@/app/api/health/route';
import { NextRequest } from 'next/server';

describe.skip('API Health Endpoint', () => { 
  // Optional: Setup/teardown for a test server if you implement one later
  // beforeAll(() => { /* start server */ });
  // afterAll(() => { /* stop server */ });

  it('should return a 200 OK status and status:ok body', async () => {
    // Create a mock Request object (URL is needed but content doesn't matter for this handler)
    const request = new NextRequest('http://localhost/api/health');

    // Call the handler function
    const response = await GET(request);

    // Assert the status code
    expect(response.status).toBe(200);

    // Assert the response body
    const body = await response.json();
    expect(body).toEqual({ status: 'ok' });
  });
});