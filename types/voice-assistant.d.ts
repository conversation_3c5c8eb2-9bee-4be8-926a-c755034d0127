// Type definitions for voice assistant state management

/**
 * Discriminated union type for voice assistant states
 * Ensures type-safe state transitions and prevents impossible states
 */
export type AssistantState = 
  | 'idle'       // Initial state, ready to be activated
  | 'connecting' // Establishing connection to ElevenLabs
  | 'listening'  // Actively listening for user input
  | 'processing' // Processing audio input
  | 'speaking'   // AI is responding vocally
  | 'error';     // Error state when something goes wrong

/**
 * Structured conversation message with metadata
 */
export interface ConversationMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

/**
 * Core context type for voice assistant provider
 * Includes state management and interaction methods
 */
export interface VoiceAssistantContextType {
  /** Current state of the voice assistant */
  state: AssistantState;
  
  /** History of conversation messages */
  messages: ConversationMessage[];
  
  /** Current message being processed or spoken */
  currentMessage: string;
  
  /** Activate voice listening */
  startListening: () => Promise<void>;
  
  /** Stop voice listening and end session */
  stopListening: () => Promise<void>;
  
  /** Toggle between listening and idle states */
  toggleListening: () => Promise<void>;
  
  /** Clear conversation history */
  clearConversation: () => void;
}
