// scripts/seed-db-new.js
import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';

// Get the directory of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = join(__dirname, '..');

// Load environment variables from .env.local first, then .env
let envLoaded = false;
const envLocalPath = join(rootDir, '.env.local');
const envPath = join(rootDir, '.env');

if (fs.existsSync(envLocalPath)) {
  dotenv.config({ path: envLocalPath });
  envLoaded = true;
  console.log('Loaded environment from .env.local');
}

if (!envLoaded && fs.existsSync(envPath)) {
  dotenv.config({ path: envPath });
  console.log('Loaded environment from .env');
}

async function seedDatabase() {
  console.log('🌱 Seeding database...');
  
  const databaseUrl = process.env.DATABASE_URL;
  
  if (!databaseUrl) {
    console.error('❌ DATABASE_URL not found in environment variables');
    return false;
  }
  
  try {
    console.log('Connecting to database...');
    const sql = neon(databaseUrl);
    
    // Test the connection
    const result = await sql('SELECT 1 as test');
    if (result && result[0] && result[0].test === 1) {
      console.log('✅ Database connection successful');
    } else {
      console.error('❌ Database connection failed: Unexpected response');
      return false;
    }
    
    // Seed users
    console.log('Seeding users...');
    
    const users = [
      {
        id: uuidv4(),
        name: 'John Doe',
        role: 'Admin',
        department: 'Management',
        email: '<EMAIL>',
        password: 'ARAjohnPS!',
        phone: '************',
        preferences: JSON.stringify({ theme: 'dark', notifications: true }),
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Jane Smith',
        role: 'Manager',
        department: 'Operations',
        email: '<EMAIL>',
        password: 'ARAjanePS!',
        phone: '************',
        preferences: JSON.stringify({ theme: 'light', notifications: true }),
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Bob Johnson',
        role: 'Inspector',
        department: 'Field Operations',
        email: '<EMAIL>',
        password: 'ARAbobPS!',
        phone: '************',
        preferences: JSON.stringify({ theme: 'system', notifications: false }),
        created_at: new Date(),
      },
    ];
    
    for (const user of users) {
      // Check if user already exists
      const existingUser = await sql`
        SELECT id FROM users WHERE email = ${user.email} LIMIT 1
      `;
      
      if (existingUser.length === 0) {
        await sql`
          INSERT INTO users (id, name, role, department, email, password, phone, preferences, created_at)
          VALUES (${user.id}, ${user.name}, ${user.role}, ${user.department}, ${user.email}, ${user.password}, ${user.phone}, ${user.preferences}, ${user.created_at})
          ON CONFLICT (email) DO NOTHING
        `;
        console.log(`✅ Created user: ${user.name}`);
      } else {
        console.log(`⏭️ User already exists: ${user.name}`);
      }
    }
    
    // Get user IDs for references
    const adminUser = await sql`SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1`;
    const managerUser = await sql`SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1`;
    const inspectorUser = await sql`SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1`;
    
    const adminId = adminUser.length > 0 ? adminUser[0].id : users[0].id;
    const managerId = managerUser.length > 0 ? managerUser[0].id : users[1].id;
    const inspectorId = inspectorUser.length > 0 ? inspectorUser[0].id : users[2].id;
    
    // Seed properties
    console.log('\nSeeding properties...');
    
    const properties = [
      {
        id: uuidv4(),
        name: 'Riverside Apartments',
        address: '123 River Road',
        suburb: 'Riverside',
        state: 'NSW',
        postcode: '2000',
        type: 'Residential',
        tier: 1,
        region: 'Sydney',
        size_sqm: 5000,
        category: 'Apartment',
        status: 'active',
        manager_id: managerId,
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Central Office Building',
        address: '456 Main Street',
        suburb: 'CBD',
        state: 'VIC',
        postcode: '3000',
        type: 'Commercial',
        tier: 2,
        region: 'Melbourne',
        size_sqm: 10000,
        category: 'Office',
        status: 'active',
        manager_id: managerId,
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Westfield Shopping Centre',
        address: '789 West Road',
        suburb: 'Westfield',
        state: 'QLD',
        postcode: '4000',
        type: 'Retail',
        tier: 1,
        region: 'Brisbane',
        size_sqm: 15000,
        category: 'Shopping Centre',
        status: 'active',
        manager_id: managerId,
        created_at: new Date(),
      },
    ];
    
    for (const property of properties) {
      // Check if property already exists
      const existingProperty = await sql`
        SELECT id FROM properties WHERE name = ${property.name} LIMIT 1
      `;
      
      if (existingProperty.length === 0) {
        await sql`
          INSERT INTO properties (id, name, address, suburb, state, postcode, type, tier, region, size_sqm, category, status, manager_id, created_at)
          VALUES (${property.id}, ${property.name}, ${property.address}, ${property.suburb}, ${property.state}, ${property.postcode}, ${property.type}, ${property.tier}, ${property.region}, ${property.size_sqm}, ${property.category}, ${property.status}, ${property.manager_id}, ${property.created_at})
          ON CONFLICT (id) DO NOTHING
        `;
        console.log(`✅ Created property: ${property.name}`);
      } else {
        console.log(`⏭️ Property already exists: ${property.name}`);
      }
    }
    
    // Seed inspection templates
    console.log('\nSeeding inspection templates...');
    
    const templates = [
      {
        id: uuidv4(),
        name: 'Residential Property Inspection',
        category: 'Residential',
        type: 'Standard',
        version: 1,
        is_active: true,
        created_by: adminId,
        created_at: new Date(),
        sections: JSON.stringify([
          {
            name: 'Exterior',
            items: [
              { name: 'Roof', type: 'rating', options: ['Poor', 'Fair', 'Good', 'Excellent'] },
              { name: 'Walls', type: 'rating', options: ['Poor', 'Fair', 'Good', 'Excellent'] },
              { name: 'Windows', type: 'rating', options: ['Poor', 'Fair', 'Good', 'Excellent'] },
              { name: 'Doors', type: 'rating', options: ['Poor', 'Fair', 'Good', 'Excellent'] },
              { name: 'Landscaping', type: 'rating', options: ['Poor', 'Fair', 'Good', 'Excellent'] },
            ],
          },
          {
            name: 'Interior',
            items: [
              { name: 'Floors', type: 'rating', options: ['Poor', 'Fair', 'Good', 'Excellent'] },
              { name: 'Walls', type: 'rating', options: ['Poor', 'Fair', 'Good', 'Excellent'] },
              { name: 'Ceilings', type: 'rating', options: ['Poor', 'Fair', 'Good', 'Excellent'] },
              { name: 'Lighting', type: 'rating', options: ['Poor', 'Fair', 'Good', 'Excellent'] },
              { name: 'Plumbing', type: 'rating', options: ['Poor', 'Fair', 'Good', 'Excellent'] },
            ],
          },
        ]),
      },
      {
        id: uuidv4(),
        name: 'Commercial Property Inspection',
        category: 'Commercial',
        type: 'Standard',
        version: 1,
        is_active: true,
        created_by: adminId,
        created_at: new Date(),
        sections: JSON.stringify([
          {
            name: 'Building Exterior',
            items: [
              { name: 'Roof', type: 'rating', options: ['Poor', 'Fair', 'Good', 'Excellent'] },
              { name: 'Walls', type: 'rating', options: ['Poor', 'Fair', 'Good', 'Excellent'] },
              { name: 'Windows', type: 'rating', options: ['Poor', 'Fair', 'Good', 'Excellent'] },
              { name: 'Doors', type: 'rating', options: ['Poor', 'Fair', 'Good', 'Excellent'] },
              { name: 'Parking', type: 'rating', options: ['Poor', 'Fair', 'Good', 'Excellent'] },
            ],
          },
          {
            name: 'Building Interior',
            items: [
              { name: 'Floors', type: 'rating', options: ['Poor', 'Fair', 'Good', 'Excellent'] },
              { name: 'Walls', type: 'rating', options: ['Poor', 'Fair', 'Good', 'Excellent'] },
              { name: 'Ceilings', type: 'rating', options: ['Poor', 'Fair', 'Good', 'Excellent'] },
              { name: 'Lighting', type: 'rating', options: ['Poor', 'Fair', 'Good', 'Excellent'] },
              { name: 'HVAC', type: 'rating', options: ['Poor', 'Fair', 'Good', 'Excellent'] },
            ],
          },
          {
            name: 'Safety & Compliance',
            items: [
              { name: 'Fire Safety', type: 'rating', options: ['Poor', 'Fair', 'Good', 'Excellent'] },
              { name: 'Emergency Exits', type: 'rating', options: ['Poor', 'Fair', 'Good', 'Excellent'] },
              { name: 'Accessibility', type: 'rating', options: ['Poor', 'Fair', 'Good', 'Excellent'] },
              { name: 'Electrical', type: 'rating', options: ['Poor', 'Fair', 'Good', 'Excellent'] },
              { name: 'Plumbing', type: 'rating', options: ['Poor', 'Fair', 'Good', 'Excellent'] },
            ],
          },
        ]),
      },
    ];
    
    for (const template of templates) {
      // Check if template already exists
      const existingTemplate = await sql`
        SELECT id FROM inspection_templates WHERE name = ${template.name} LIMIT 1
      `;
      
      if (existingTemplate.length === 0) {
        await sql`
          INSERT INTO inspection_templates (id, name, category, type, version, is_active, created_by, created_at, sections)
          VALUES (${template.id}, ${template.name}, ${template.category}, ${template.type}, ${template.version}, ${template.is_active}, ${template.created_by}, ${template.created_at}, ${template.sections})
          ON CONFLICT (id) DO NOTHING
        `;
        console.log(`✅ Created template: ${template.name}`);
      } else {
        console.log(`⏭️ Template already exists: ${template.name}`);
      }
    }
    
    // Seed contacts
    console.log('\nSeeding contacts...');
    
    const contacts = [
      {
        id: uuidv4(),
        name: 'Michael Brown',
        role: 'Property Manager',
        company: 'ABC Property Management',
        phone: '************',
        email: '<EMAIL>',
        location: 'Sydney',
        is_favorite: true,
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Sarah Wilson',
        role: 'Maintenance Supervisor',
        company: 'XYZ Maintenance',
        phone: '************',
        email: '<EMAIL>',
        location: 'Melbourne',
        is_favorite: false,
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'David Lee',
        role: 'Building Inspector',
        company: 'City Council',
        phone: '************',
        email: '<EMAIL>',
        location: 'Brisbane',
        is_favorite: true,
        created_at: new Date(),
      },
    ];
    
    for (const contact of contacts) {
      // Check if contact already exists
      const existingContact = await sql`
        SELECT id FROM contacts WHERE email = ${contact.email} LIMIT 1
      `;
      
      if (existingContact.length === 0) {
        await sql`
          INSERT INTO contacts (id, name, role, company, phone, email, location, is_favorite, created_at)
          VALUES (${contact.id}, ${contact.name}, ${contact.role}, ${contact.company}, ${contact.phone}, ${contact.email}, ${contact.location}, ${contact.is_favorite}, ${contact.created_at})
          ON CONFLICT (id) DO NOTHING
        `;
        console.log(`✅ Created contact: ${contact.name}`);
      } else {
        console.log(`⏭️ Contact already exists: ${contact.name}`);
      }
    }
    
    console.log('\n✅ Database seeding completed successfully!');
    return true;
  } catch (error) {
    console.error('❌ Error seeding database:', error);
    return false;
  }
}

// Run the seed function
seedDatabase()
  .then(success => {
    if (success) {
      console.log('\n✅ Database seeding completed successfully');
    } else {
      console.error('\n❌ Database seeding failed');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
