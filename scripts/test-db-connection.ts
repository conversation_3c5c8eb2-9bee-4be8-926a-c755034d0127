import { loadEnvConfig } from '@next/env';
import { neon } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-http';
import * as schema from '../app/db/schema';

// Load environment variables
loadEnvConfig(process.cwd());

// Check if the database URL is set
if (!process.env.DATABASE_URL) {
  throw new Error('DATABASE_URL must be a Neon postgres connection string');
}

// Initialize the Neon serverless driver
const sql = neon(process.env.DATABASE_URL);

// Initialize Drizzle ORM with the Neon driver and the schema
const db = drizzle(sql, { schema });

async function testDatabaseConnection() {
  console.log('Testing database connection...');
  
  try {
    // Test the connection with a simple query
    const result = await sql`SELECT 1 as test`;
    
    if (result && result.rows && result.rows[0] && result.rows[0].test === 1) {
      console.log('✅ Database connection successful');
      
      // Test querying the users table
      console.log('\nTesting users table...');
      const users = await db.select().from(schema.users).limit(5);
      
      if (users.length > 0) {
        console.log(`✅ Successfully retrieved ${users.length} users`);
        console.log('Sample users:');
        users.forEach(user => {
          console.log(`- ${user.name} (${user.email})`);
        });
      } else {
        console.log('⚠️ No users found in the database');
      }
      
      // Test querying the properties table
      console.log('\nTesting properties table...');
      const properties = await db.select().from(schema.properties).limit(5);
      
      if (properties.length > 0) {
        console.log(`✅ Successfully retrieved ${properties.length} properties`);
        console.log('Sample properties:');
        properties.forEach(property => {
          console.log(`- ${property.name} (${property.address}, ${property.suburb}, ${property.state})`);
        });
      } else {
        console.log('⚠️ No properties found in the database');
      }
      
    } else {
      console.error('❌ Database connection failed: Unexpected response');
    }
  } catch (error) {
    console.error('❌ Database connection error:', error);
  }
}

// Run the test function
testDatabaseConnection();
