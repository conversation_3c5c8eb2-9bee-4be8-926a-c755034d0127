/**
 * Safe Organization Migration Script
 * 
 * This script applies the organization migration (0005_add_organizations.sql) with 
 * proper transactional boundaries and verification steps. It includes:
 * 
 * 1. Verification of existing schema before migration
 * 2. Transactional execution of the migration SQL
 * 3. Verification of successful migration
 * 4. Rollback capability in case of failure
 */

import { neon } from '@neondatabase/serverless';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';

// Load environment variables
dotenv.config();

// Get file paths
const __dirname = path.dirname(fileURLToPath(import.meta.url));
const rootDir = path.join(__dirname, '..');
const migrationPath = path.join(rootDir, 'drizzle', '0005_add_organizations.sql');

// Database connection
const databaseUrl = process.env.DATABASE_URL;
if (!databaseUrl) {
  console.error('❌ DATABASE_URL environment variable not set.');
  process.exit(1);
}

// Initialize Neon client
const sql = neon(databaseUrl);

/**
 * Check if the migration has already been applied
 */
async function checkMigrationStatus() {
  try {
    console.log('Checking if migration has already been applied...');
    
    const organizationsExists = await sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'organizations'
      ) as exists
    `;
    
    if (organizationsExists[0]?.exists) {
      console.log('✅ Migration has already been applied (organizations table exists)');
      return true;
    }
    
    console.log('⚠️ Migration has not been applied yet (organizations table not found)');
    return false;
  } catch (error) {
    console.error('❌ Error checking migration status:', error);
    throw error;
  }
}

/**
 * Verify database schema before migration
 */
async function verifyPreMigrationSchema() {
  try {
    console.log('Verifying database schema before migration...');
    
    // Check required tables exist before migration
    const requiredTables = ['users', 'properties', 'contract_specifications'];
    
    for (const table of requiredTables) {
      const tableExists = await sql`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = ${table}
        ) as exists
      `;
      
      if (!tableExists[0]?.exists) {
        throw new Error(`Required table '${table}' does not exist. Migration cannot proceed.`);
      }
    }
    
    console.log('✅ Pre-migration schema verification successful');
    return true;
  } catch (error) {
    console.error('❌ Pre-migration schema verification failed:', error);
    throw error;
  }
}

/**
 * Apply migration with transaction
 */
async function applyMigration() {
  try {
    console.log('Reading migration SQL...');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('Starting migration transaction...');
    
    // Begin transaction
    await sql`BEGIN`;
    
    try {
      // Execute migration SQL
      console.log('Executing migration SQL...');
      await sql.unsafe(migrationSQL);
      
      // Verify migration applied correctly
      console.log('Verifying migration...');
      
      // Check organizations table
      const orgTableCheck = await sql`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = 'organizations'
        ) as exists
      `;
      
      if (!orgTableCheck[0]?.exists) {
        throw new Error('Migration verification failed: organizations table not created');
      }
      
      // Check organization_members table
      const membersTableCheck = await sql`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = 'organization_members'
        ) as exists
      `;
      
      if (!membersTableCheck[0]?.exists) {
        throw new Error('Migration verification failed: organization_members table not created');
      }
      
      // Check initial organization
      const initialOrgCheck = await sql`
        SELECT COUNT(*) as count FROM organizations 
        WHERE id = 'org_2vgJJLPin926eyZLua1fijgiXFJ'
      `;
      
      if (initialOrgCheck[0]?.count < 1) {
        throw new Error('Migration verification failed: initial organization not created');
      }
      
      // Check properties organization_id column
      const propertiesColumnCheck = await sql`
        SELECT EXISTS (
          SELECT FROM information_schema.columns 
          WHERE table_schema = 'public' 
          AND table_name = 'properties' 
          AND column_name = 'organization_id'
        ) as exists
      `;
      
      if (!propertiesColumnCheck[0]?.exists) {
        throw new Error('Migration verification failed: organization_id column not added to properties table');
      }
      
      // Check contracts organization_id column
      const contractsColumnCheck = await sql`
        SELECT EXISTS (
          SELECT FROM information_schema.columns 
          WHERE table_schema = 'public' 
          AND table_name = 'contract_specifications' 
          AND column_name = 'organization_id'
        ) as exists
      `;
      
      if (!contractsColumnCheck[0]?.exists) {
        throw new Error('Migration verification failed: organization_id column not added to contract_specifications table');
      }
      
      // Commit transaction if all verifications pass
      console.log('✅ Migration verification successful, committing transaction...');
      await sql`COMMIT`;
      
      console.log('✅ Migration successfully applied and committed');
      return true;
    } catch (error) {
      // Rollback transaction if any error occurs
      console.error('❌ Migration failed, rolling back transaction:', error);
      await sql`ROLLBACK`;
      throw error;
    }
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

/**
 * Generate migration report
 */
async function generateMigrationReport() {
  try {
    console.log('\n========== MIGRATION REPORT ==========\n');
    
    // Check tables
    const tables = await sql`
      SELECT table_name, (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) as column_count
      FROM information_schema.tables t
      WHERE table_schema = 'public'
      AND table_name IN ('organizations', 'organization_members')
    `;
    
    console.log('Tables created:');
    tables.forEach(table => {
      console.log(`- ${table.table_name} (${table.column_count} columns)`);
    });
    
    // Check organization data
    const orgs = await sql`SELECT id, name, slug FROM organizations`;
    
    console.log('\nOrganizations:');
    orgs.forEach(org => {
      console.log(`- ${org.name} (${org.slug}) [${org.id}]`);
    });
    
    // Check columns added
    const columns = await sql`
      SELECT table_name, column_name
      FROM information_schema.columns
      WHERE table_schema = 'public'
      AND table_name IN ('properties', 'contract_specifications')
      AND column_name = 'organization_id'
    `;
    
    console.log('\nColumns added:');
    columns.forEach(col => {
      console.log(`- ${col.table_name}.${col.column_name}`);
    });
    
    console.log('\n✅ Migration successfully completed and verified');
  } catch (error) {
    console.error('\n❌ Error generating migration report:', error);
  }
}

/**
 * Create a rollback script
 */
function createRollbackScript() {
  const rollbackSQL = `
-- Rollback script for 0005_add_organizations.sql
BEGIN;

-- Remove organization relation from contracts
ALTER TABLE "contract_specifications" DROP COLUMN IF EXISTS "organization_id";

-- Remove organization relation from properties
ALTER TABLE "properties" DROP COLUMN IF EXISTS "organization_id";

-- Drop organization members table
DROP TABLE IF EXISTS "organization_members";

-- Drop organizations table
DROP TABLE IF EXISTS "organizations";

COMMIT;
  `;
  
  const rollbackPath = path.join(rootDir, 'drizzle', '0005_rollback_organizations.sql');
  fs.writeFileSync(rollbackPath, rollbackSQL);
  
  console.log(`\nRollback script created at: ${rollbackPath}`);
  console.log('To rollback the migration, execute this SQL file against your database.');
}

/**
 * Main execution
 */
async function main() {
  try {
    console.log('=== SAFE ORGANIZATION MIGRATION SCRIPT ===\n');
    
    // Check if migration already applied
    const alreadyApplied = await checkMigrationStatus();
    if (alreadyApplied) {
      console.log('\n✅ No action taken. Migration has already been applied.');
      return;
    }
    
    // Verify pre-migration schema
    await verifyPreMigrationSchema();
    
    // Confirm before proceeding
    console.log('\n⚠️ WARNING: This script will apply the organization migration to your database.');
    console.log('The migration will be executed in a transaction and can be rolled back if needed.');
    console.log('To proceed, set the CONFIRM_MIGRATION environment variable to "YES".');
    
    if (process.env.CONFIRM_MIGRATION !== 'YES') {
      console.log('\n❌ Migration aborted. Set CONFIRM_MIGRATION=YES to proceed.');
      return;
    }
    
    // Apply migration
    await applyMigration();
    
    // Generate migration report
    await generateMigrationReport();
    
    // Create rollback script
    createRollbackScript();
    
    console.log('\n=== MIGRATION COMPLETED SUCCESSFULLY ===');
  } catch (error) {
    console.error('\n❌ Migration failed:', error);
    process.exit(1);
  }
}

main();
