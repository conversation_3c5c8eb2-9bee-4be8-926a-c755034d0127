// scripts/run-migration.js
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import dotenv from 'dotenv';
import fs from 'fs';

// Get the directory of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = join(__dirname, '..');

// Load environment variables from .env.local first, then .env
let envLoaded = false;
const envLocalPath = join(rootDir, '.env.local');
const envPath = join(rootDir, '.env');

if (fs.existsSync(envLocalPath)) {
  dotenv.config({ path: envLocalPath });
  envLoaded = true;
  console.log('Loaded environment from .env.local for migration');
}

if (!envLoaded && fs.existsSync(envPath)) {
  dotenv.config({ path: envPath });
  console.log('Loaded environment from .env for migration');
}

// Static imports removed, will use dynamic import inside function
// Calculate the absolute path to the migrations folder
// const __filename = fileURLToPath(import.meta.url); // Moved up
// const __dirname = dirname(__filename); // Moved up
const migrationsFolder = join(__dirname, '..', 'drizzle');

// Main migration function
async function runMigration() {
  // Dynamically import modules after env vars are loaded
  const { migrate } = await import('drizzle-orm/neon-serverless/migrator');
  const { db, pool } = await import('../lib/neon-db.js');

  console.log('🚀 Starting database migration...');
  let migrationSuccessful = false;
  try {
    // Apply migrations
    await migrate(db, { migrationsFolder });
    console.log('✅ Database migration completed successfully');
    migrationSuccessful = true;
  } catch (error) {
    console.error('❌ Database migration failed:', error);
    // Keep the process running to ensure finally block executes
  } finally {
    console.log('🔌 Closing database connection...');
    try {
      await pool.end(); // Ensure connection is closed
      console.log('✅ Database connection closed successfully');
    } catch (closeError) {
      console.error('❌ Error closing database connection:', closeError);
      // If closing fails, it's a critical issue, exit
      process.exit(1);
    }
  }

  // Exit with appropriate code after connection is closed
  if (!migrationSuccessful) {
    process.exit(1);
  } else {
    process.exit(0); // Explicitly exit with success code
  }
}

// Run the migration
runMigration();
