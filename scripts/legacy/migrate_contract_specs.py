#!/usr/bin/env python3
import os
import sys
import json
import time
import psycopg2
from psycopg2 import sql
from dotenv import load_dotenv

# Load environment variables
if os.path.exists('.env.local'):
    load_dotenv('.env.local')
    print("Loaded environment from .env.local")
else:
    load_dotenv('.env')
    print("Loaded environment from .env")

# Get database connection string
database_url = os.getenv('DATABASE_URL')
if not database_url:
    print("❌ DATABASE_URL not found in environment variables")
    sys.exit(1)

def migrate_contract_specifications():
    """Run the contract specifications migration"""
    print("Migrating contract specifications...")
    
    try:
        # Connect to the database
        print("Connecting to database...")
        conn = psycopg2.connect(database_url)
        conn.autocommit = True
        cursor = conn.cursor()
        
        # Test the connection
        cursor.execute("SELECT 1 as test")
        result = cursor.fetchone()
        if result and result[0] == 1:
            print("✅ Database connection successful")
        else:
            print("❌ Database connection failed: Unexpected response")
            return False
        
        # Read the migration SQL file
        migration_path = os.path.join('drizzle', '0001_contract_specifications.sql')
        with open(migration_path, 'r') as f:
            migration_sql = f.read()
        
        # Split the SQL into individual statements
        statements = [stmt.strip() for stmt in migration_sql.split(';') if stmt.strip()]
        
        print(f"Executing {len(statements)} SQL statements...")
        
        # Execute each statement
        for i, statement in enumerate(statements):
            if statement:
                try:
                    cursor.execute(statement)
                    print(f"✅ Executed statement {i + 1}/{len(statements)}")
                except psycopg2.errors.DuplicateTable:
                    print(f"⚠️ Table already exists (statement {i + 1}/{len(statements)})")
                except Exception as e:
                    print(f"❌ Error executing statement {i + 1}/{len(statements)}: {e}")
                    if "already exists" not in str(e):
                        return False
        
        # Update the journal
        journal_path = os.path.join('drizzle', 'meta', '_journal.json')
        with open(journal_path, 'r') as f:
            journal = json.load(f)
        
        # Check if the migration is already in the journal
        migration_exists = any(entry.get('tag') == '0001_contract_specifications' for entry in journal.get('entries', []))
        
        if not migration_exists:
            # Add the migration to the journal
            journal['entries'].append({
                "idx": 1,
                "version": "5",
                "when": int(time.time() * 1000),
                "tag": "0001_contract_specifications",
                "breakpoints": True
            })
            
            # Write the updated journal
            with open(journal_path, 'w') as f:
                json.dump(journal, f, indent=2)
            print("✅ Updated migration journal")
        else:
            print("⚠️ Migration already in journal")
        
        print("✅ Migration completed successfully")
        return True
    
    except Exception as e:
        print(f"❌ Migration error: {e}")
        return False
    finally:
        if 'conn' in locals() and conn:
            conn.close()

if __name__ == "__main__":
    success = migrate_contract_specifications()
    if success:
        print("\n✅ Contract specifications migration completed successfully")
    else:
        print("\n❌ Contract specifications migration failed")
        sys.exit(1)
