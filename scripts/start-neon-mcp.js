#!/usr/bin/env node

const { spawn } = require('child_process');
require('dotenv').config({ path: '.env.local' });

// Check if DATABASE_URL is defined
if (!process.env.DATABASE_URL) {
  console.error('❌ DATABASE_URL environment variable is not defined in .env.local');
  process.exit(1);
}

console.log('Starting Neon MCP server...');

try {
  // Extract connection parameters from DATABASE_URL
  const databaseUrl = process.env.DATABASE_URL;
  const url = new URL(databaseUrl);

  // Get the host, username, password, and database name
  const host = url.hostname;
  const username = url.username;
  const password = url.password;
  const database = url.pathname.substring(1); // Remove leading '/'

  console.log(`Connecting to Neon database at ${host}...`);

  // Start the MCP server with the correct arguments
  const mcpServer = spawn('npx', [
    '@neondatabase/mcp-server-neon',
    'start',
    '--host', host,
    '--user', username,
    '--password', password,
    '--database', database
  ], {
    stdio: 'inherit'
  });

  mcpServer.on('error', (error) => {
    console.error('❌ Failed to start Neon MCP server:', error.message);
    process.exit(1);
  });

  // Handle server exit
  mcpServer.on('exit', (code) => {
    if (code !== 0) {
      console.error(`❌ Neon MCP server exited with code ${code}`);
      process.exit(code);
    }
    console.log('✅ Neon MCP server exited successfully');
  });

  console.log('✅ Neon MCP server started successfully');

  // Keep the process running
  process.on('SIGINT', () => {
    console.log('Shutting down Neon MCP server...');
    mcpServer.kill();
    process.exit(0);
  });
} catch (error) {
  console.error('❌ Failed to start Neon MCP server:', error.message);
  process.exit(1);
}
