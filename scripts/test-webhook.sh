#!/bin/bash

# Test the Convex webhook endpoint
echo "Testing Convex webhook endpoint..."

# First, test if the endpoint is accessible
curl -I https://healthy-ram-536.convex.cloud/clerk-webhook

echo -e "\n\nWebhook endpoint is configured and ready to receive Clerk events."
echo "To complete the setup:"
echo "1. Update the webhook URL in Clerk Dashboard to: https://healthy-ram-536.convex.cloud/clerk-webhook"
echo "2. Keep the same webhook secret: whsec_B/7RJ0JIQIk/7udsSxNuHxFWUYrpPC8k"
echo "3. Use the 'Test' button in Clerk Dashboard to verify the connection"