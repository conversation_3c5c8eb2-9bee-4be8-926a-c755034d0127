import { loadEnvConfig } from '@next/env';
import { neon } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-http';
import * as schema from '../../app/db/schema';
import crypto from 'crypto';

// Load environment variables
loadEnvConfig(process.cwd());

// Check if the database URL is set
if (!process.env.DATABASE_URL) {
  throw new Error('DATABASE_URL must be a Neon postgres connection string');
}

// Initialize the Neon serverless driver
const sql = neon(process.env.DATABASE_URL);

// Initialize Drizzle ORM with the Neon driver and the schema
const db = drizzle(sql, { schema });

async function seedARAUsers() {
  console.log('Seeding ARA Property Services users...');
  
  try {
    // ARA Property Services leadership team and staff
    const araUsers = [
      // Leadership Team
      {
        id: crypto.randomUUID(),
        name: "<PERSON>",
        role: "CEO",
        department: "Executive",
        email: "<EMAIL>",
        password: "ARApaulPS!",
        phone: "0403 244 691",
        preferences: { theme: "light", notifications: true },
        created_at: new Date(),
      },
      {
        id: crypto.randomUUID(),
        name: "Gaurav Majumdar",
        role: "Head of Innovation",
        department: "Innovation & Compliance",
        email: "<EMAIL>",
        password: "ARAgauravPS!",
        phone: "0417 741 543",
        preferences: { theme: "dark", notifications: true },
        created_at: new Date(),
      },
      {
        id: crypto.randomUUID(),
        name: "Mark Brady",
        role: "Senior Estimator",
        department: "Sales",
        email: "<EMAIL>",
        password: "ARAmarkPS!",
        phone: "0477 806 648",
        preferences: { theme: "system", notifications: true },
        created_at: new Date(),
      },
      {
        id: crypto.randomUUID(),
        name: "Charles McCann",
        role: "Content Creator",
        department: "Marketing",
        email: "<EMAIL>",
        password: "ARAcharlesPS!",
        phone: "0404 459 623",
        preferences: { theme: "light", notifications: false },
        created_at: new Date(),
      },
      
      // Operations Staff - NSW
      {
        id: crypto.randomUUID(),
        name: "Sarah Johnson",
        role: "Regional Manager",
        department: "Operations",
        email: "<EMAIL>",
        password: "ARAsarahPS!",
        phone: "0412 345 678",
        preferences: { theme: "light", notifications: true },
        created_at: new Date(),
      },
      {
        id: crypto.randomUUID(),
        name: "Michael Chen",
        role: "Site Supervisor",
        department: "Operations",
        email: "<EMAIL>",
        password: "ARAmichaelPS!",
        phone: "0423 456 789",
        preferences: { theme: "dark", notifications: true },
        created_at: new Date(),
      },
      
      // Operations Staff - QLD
      {
        id: crypto.randomUUID(),
        name: "Emma Wilson",
        role: "Regional Manager",
        department: "Operations",
        email: "<EMAIL>",
        password: "ARAemmaPS!",
        phone: "0434 567 890",
        preferences: { theme: "system", notifications: true },
        created_at: new Date(),
      },
      {
        id: crypto.randomUUID(),
        name: "David Thompson",
        role: "Site Supervisor",
        department: "Operations",
        email: "<EMAIL>",
        password: "ARAdavidPS!",
        phone: "0445 678 901",
        preferences: { theme: "light", notifications: true },
        created_at: new Date(),
      },
      
      // Operations Staff - VIC
      {
        id: crypto.randomUUID(),
        name: "Jessica Brown",
        role: "Regional Manager",
        department: "Operations",
        email: "<EMAIL>",
        password: "ARAjessicaPS!",
        phone: "0456 789 012",
        preferences: { theme: "dark", notifications: true },
        created_at: new Date(),
      },
      {
        id: crypto.randomUUID(),
        name: "Robert Lee",
        role: "Site Supervisor",
        department: "Operations",
        email: "<EMAIL>",
        password: "ARArobertPS!",
        phone: "0467 890 123",
        preferences: { theme: "system", notifications: true },
        created_at: new Date(),
      },
      
      // Inspectors
      {
        id: crypto.randomUUID(),
        name: "Amanda Garcia",
        role: "Inspector",
        department: "Quality Assurance",
        email: "<EMAIL>",
        password: "ARAamandaPS!",
        phone: "0478 901 234",
        preferences: { theme: "light", notifications: true },
        created_at: new Date(),
      },
      {
        id: crypto.randomUUID(),
        name: "James Wilson",
        role: "Inspector",
        department: "Quality Assurance",
        email: "<EMAIL>",
        password: "ARAjamesPS!",
        phone: "0489 012 345",
        preferences: { theme: "dark", notifications: true },
        created_at: new Date(),
      },
      
      // Admin Staff
      {
        id: crypto.randomUUID(),
        name: "Olivia Martinez",
        role: "Office Manager",
        department: "Administration",
        email: "<EMAIL>",
        password: "ARAoliviaPS!",
        phone: "0490 123 456",
        preferences: { theme: "light", notifications: true },
        created_at: new Date(),
      },
      {
        id: crypto.randomUUID(),
        name: "William Taylor",
        role: "HR Coordinator",
        department: "Human Resources",
        email: "<EMAIL>",
        password: "ARAwilliamPS!",
        phone: "0401 234 567",
        preferences: { theme: "system", notifications: true },
        created_at: new Date(),
      },
    ];

    // Insert users
    for (const user of araUsers) {
      // Check if user already exists
      const existingUser = await db.select()
        .from(schema.users)
        .where(schema.users.email.equals(user.email))
        .limit(1);
      
      if (existingUser.length === 0) {
        await db.insert(schema.users).values(user);
        console.log(`✅ Created user: ${user.name}`);
      } else {
        console.log(`⏭️ User already exists: ${user.name}`);
      }
    }
    
    console.log('ARA Property Services users seeded successfully!');
    
    // Verify the data was inserted
    const result = await db.select().from(schema.users);
    console.log(`Total users in database: ${result.length}`);
    
  } catch (error) {
    console.error('Error seeding ARA users:', error);
  }
}

// Run the function
seedARAUsers();
