import { loadEnvConfig } from '@next/env';
import { neon } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-http';
import * as schema from '../../app/db/schema';
import crypto from 'crypto';

// Load environment variables
loadEnvConfig(process.cwd());

// Check if the database URL is set
if (!process.env.DATABASE_URL) {
  throw new Error('DATABASE_URL must be a Neon postgres connection string');
}

// Initialize the Neon serverless driver
const sql = neon(process.env.DATABASE_URL);

// Initialize Drizzle ORM with the Neon driver and the schema
const db = drizzle(sql, { schema });

async function createTables() {
  console.log('Creating tables...');
  
  try {
    // Create tables if they don't exist
    // This is a simplified approach - in production, you would use a proper migration tool
    
    // Create users table
    await sql`
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        role TEXT NOT NULL,
        department TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        phone TEXT,
        avatar TEXT,
        preferences JSONB,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        last_login TIMESTAMP WITH TIME ZONE
      )
    `;
    console.log('✅ Users table created or already exists');
    
    // Create roles table
    await sql`
      CREATE TABLE IF NOT EXISTS roles (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        permissions JSONB NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `;
    console.log('✅ Roles table created or already exists');
    
    // Create user_sessions table
    await sql`
      CREATE TABLE IF NOT EXISTS user_sessions (
        id TEXT PRIMARY KEY,
        user_id TEXT REFERENCES users(id) NOT NULL,
        token TEXT NOT NULL,
        expires_at TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `;
    console.log('✅ User sessions table created or already exists');
    
    // Create messages table
    await sql`
      CREATE TABLE IF NOT EXISTS messages (
        id TEXT PRIMARY KEY,
        session_id TEXT,
        content_type TEXT,
        content_transcript TEXT,
        object TEXT,
        role TEXT,
        status TEXT,
        type TEXT,
        sender_id TEXT REFERENCES users(id),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `;
    console.log('✅ Messages table created or already exists');
    
    // Create contacts table
    await sql`
      CREATE TABLE IF NOT EXISTS contacts (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        role TEXT NOT NULL,
        company TEXT NOT NULL,
        phone TEXT,
        email TEXT,
        location TEXT,
        is_favorite BOOLEAN DEFAULT false NOT NULL,
        avatar TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE
      )
    `;
    console.log('✅ Contacts table created or already exists');
    
    // Create companies table
    await sql`
      CREATE TABLE IF NOT EXISTS companies (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        primary_contact_id TEXT REFERENCES contacts(id),
        address TEXT,
        phone TEXT,
        email TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `;
    console.log('✅ Companies table created or already exists');
    
    // Create chat_sessions table
    await sql`
      CREATE TABLE IF NOT EXISTS chat_sessions (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        preview TEXT,
        date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        is_starred BOOLEAN DEFAULT false NOT NULL,
        created_by TEXT REFERENCES users(id)
      )
    `;
    console.log('✅ Chat sessions table created or already exists');
    
    // Create chat_participants table
    await sql`
      CREATE TABLE IF NOT EXISTS chat_participants (
        session_id TEXT REFERENCES chat_sessions(id) NOT NULL,
        user_id TEXT REFERENCES users(id) NOT NULL,
        joined_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        last_read_at TIMESTAMP WITH TIME ZONE,
        PRIMARY KEY (session_id, user_id)
      )
    `;
    console.log('✅ Chat participants table created or already exists');
    
    // Create properties table
    await sql`
      CREATE TABLE IF NOT EXISTS properties (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        address TEXT NOT NULL,
        suburb TEXT NOT NULL,
        state VARCHAR(3) NOT NULL,
        postcode VARCHAR(4) NOT NULL,
        type TEXT NOT NULL,
        tier INTEGER NOT NULL,
        region TEXT NOT NULL,
        size_sqm DECIMAL(10, 2),
        category TEXT NOT NULL,
        status TEXT DEFAULT 'active' NOT NULL,
        manager_id TEXT REFERENCES users(id),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE
      )
    `;
    console.log('✅ Properties table created or already exists');
    
    console.log('All tables created successfully!');
  } catch (error) {
    console.error('Error creating tables:', error);
  }
}

// Run the function
createTables();
