import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

async function runMigrations() {
  console.log('Running all database migrations...');
  
  try {
    // Run migrations in order
    console.log('\n1. Creating tables...');
    await execAsync('npx tsx scripts/db-migrations/01_create_tables.ts');
    
    console.log('\n2. Seeding ARA users...');
    await execAsync('npx tsx scripts/db-migrations/02_seed_ara_users.ts');
    
    console.log('\n3. Seeding properties...');
    await execAsync('npx tsx scripts/db-migrations/03_seed_properties.ts');
    
    console.log('\nAll migrations completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
  }
}

runMigrations();
