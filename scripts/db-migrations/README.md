# Database Migration Scripts

This folder contains scripts for setting up and populating the Neon Postgres database for the ARA Property Services application.

## Overview

The migration scripts create the necessary database tables and seed them with initial data for:

- ARA Property Services users (leadership team and staff)
- Properties (Australia Post and Star Track facilities)
- Inspection templates and reports
- Other application data

## Scripts

1. **01_create_tables.ts**: Creates the database tables based on the schema defined in `app/db/schema.ts`
2. **02_seed_ara_users.ts**: Seeds the database with ARA Property Services users
3. **03_seed_properties.ts**: Seeds the database with Australia Post and Star Track properties
4. **run-all.ts**: Runs all migration scripts in sequence

## Running the Migrations

To run all migrations in sequence:

```bash
npx tsx scripts/db-migrations/run-all.ts
```

To run individual migration scripts:

```bash
npx tsx scripts/db-migrations/01_create_tables.ts
npx tsx scripts/db-migrations/02_seed_ara_users.ts
npx tsx scripts/db-migrations/03_seed_properties.ts
```

## Testing the Database

After running the migrations, you can test the database connection and verify the data:

```bash
npx tsx scripts/test-db-connection.ts
```

## Testing Authentication

To test the authentication functionality:

```bash
npx tsx scripts/test-auth-store.ts
```

## Mock Data

For UI development and testing, mock data is available in the `mocks` folder:

- `mocks/users.ts`: Mock ARA Property Services users
- `mocks/properties.ts`: Mock Australia Post and Star Track properties
- `mocks/inspections.ts`: Mock inspection reports and actions

## Database Schema

The database schema is defined in `app/db/schema.ts` and includes tables for:

- Users and authentication
- Properties and areas
- Inspection templates and reports
- Chat sessions and messages
- Contacts and companies
- And more

## Environment Variables

Make sure the following environment variables are set:

```
DATABASE_URL=postgres://user:<EMAIL>/dbname
```

## Notes

- The migration scripts use the Neon Postgres serverless driver and Drizzle ORM
- The scripts are designed to be idempotent (can be run multiple times without duplicating data)
- The scripts check if data already exists before inserting new records
