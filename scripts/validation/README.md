# Deployment Validation Scripts

This directory contains validation scripts to ensure the ARA Property Services app is properly configured and ready for deployment.

## Available Scripts

### Environment Variables Validator

Checks if all required environment variables are set and have valid formats.

```bash
# Run the environment validation script
pnpm validate:env
```

This script:
- Verifies all required environment variables are set
- Checks if variables have valid formats
- Generates a template `.env.template` file
- Outputs Vercel CLI commands to set environment variables

### Deployment Validator

Comprehensive validator that checks all aspects of the deployment to ensure everything is ready for production.

```bash
# Run the deployment validation script
pnpm validate:deployment
```

This script checks:
- Database connection and schema integrity
- API endpoints accessibility
- MCP server connection (if running)
- Clerk webhook configuration
- And more...

### Run All Validations

Run both validation scripts in sequence:

```bash
# Run all validation scripts
pnpm validate:all
```

## Adding to CI/CD Pipeline

Add the validation scripts to your CI/CD pipeline to ensure deployment readiness:

```yaml
# Example workflow step
- name: Validate Deployment
  run: pnpm validate:all
```

## Fixing Validation Errors

### Environment Variables

If environment variables are missing or invalid:

1. Copy the generated `.env.template` file to `.env.local`
2. Fill in the missing values
3. For production deployment, set these variables in your Vercel project settings

### Database Issues

If database validation fails:

1. Check database connection string
2. Verify the database exists and tables are created
3. Run migrations if needed: `pnpm db:migrate`

### MCP Server Issues

If MCP server validation fails:

1. Start the MCP server: `node neon-mcp-server.js`
2. Check if the server is running on the expected port (default: 3005)

## Contributing

When adding new features or configuration requirements:

1. Update the validation scripts to include checks for new components
2. Update the `.env.template` file with new environment variables
3. Document any new validation requirements in this README