/**
 * Deployment Validation Script for ARA Property Services App
 * 
 * This script verifies that all components of the multi-tenant implementation
 * are correctly deployed and functioning as expected.
 */

import dotenv from 'dotenv';
import pkg from '@next/env';
const { loadEnvConfig } = pkg;
import { neon } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-http';
import { EventSource } from 'eventsource';
import https from 'https';
import fetch from 'node-fetch';
import chalk from 'chalk';

// Load environment variables
loadEnvConfig(process.cwd());
dotenv.config({ path: '.env.local' });

// Track validation results
const results = {
  passed: 0,
  failed: 0,
  warnings: 0,
  details: []
};

/**
 * Log a passed test
 */
function logPass(message) {
  console.log(chalk.green('✓ PASS:'), message);
  results.passed++;
  results.details.push({ status: 'pass', message });
}

/**
 * Log a failed test
 */
function logFail(message, error = null) {
  console.log(chalk.red('✗ FAIL:'), message);
  if (error) {
    console.log(chalk.gray('  Error:'), error.message || error);
  }
  results.failed++;
  results.details.push({ status: 'fail', message, error: error?.message || error });
}

/**
 * Log a warning
 */
function logWarning(message) {
  console.log(chalk.yellow('⚠ WARNING:'), message);
  results.warnings++;
  results.details.push({ status: 'warning', message });
}

/**
 * Verify that required environment variables are set
 */
async function validateEnvironmentVariables() {
  console.log(chalk.blue('\n--- Validating Environment Variables ---'));
  
  const requiredVars = [
    'DATABASE_URL',
    'NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY',
    'CLERK_SECRET_KEY',
    'CLERK_WEBHOOK_SECRET',
    'NEXT_PUBLIC_CLERK_SIGN_IN_URL',
    'NEXT_PUBLIC_CLERK_SIGN_UP_URL',
    'NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL',
    'NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL'
  ];
  
  for (const envVar of requiredVars) {
    if (!process.env[envVar]) {
      logFail(`Missing required environment variable: ${envVar}`);
    } else {
      logPass(`Environment variable ${envVar} is set`);
    }
  }
}

/**
 * Validate database connection and schema
 */
async function validateDatabase() {
  console.log(chalk.blue('\n--- Validating Database Connection and Schema ---'));
  
  if (!process.env.DATABASE_URL) {
    logFail('DATABASE_URL is not set, skipping database validation');
    return;
  }
  
  try {
    // Connect to the database
    const sql = neon(process.env.DATABASE_URL);
    const db = drizzle(sql);
    
    // Test database connection
    const result = await sql`SELECT current_timestamp`;
    if (result && result.length > 0) {
      logPass('Successfully connected to Neon PostgreSQL database');
    } else {
      logFail('Database connection returned unexpected result');
    }
    
    // Check if organizations table exists
    try {
      const orgsResult = await sql`SELECT COUNT(*) FROM organizations`;
      logPass(`Organizations table exists with ${orgsResult[0].count} organizations`);
    } catch (error) {
      logFail('Organizations table does not exist or cannot be queried', error);
    }
    
    // Check if organization_members table exists
    try {
      const membersResult = await sql`SELECT COUNT(*) FROM organization_members`;
      logPass(`Organization members table exists with ${membersResult[0].count} members`);
    } catch (error) {
      logFail('Organization members table does not exist or cannot be queried', error);
    }
    
    // Verify initial organization exists
    try {
      const araOrgResult = await sql`SELECT * FROM organizations WHERE slug = 'araps'`;
      if (araOrgResult && araOrgResult.length > 0) {
        logPass(`Initial ARA Property Services organization exists with ID: ${araOrgResult[0].id}`);
      } else {
        logWarning('Initial ARA Property Services organization not found');
      }
    } catch (error) {
      logFail('Error checking for initial organization', error);
    }
    
    // Verify foreign keys on properties and contracts
    try {
      const tablesWithFk = await sql`
        SELECT table_name, column_name
        FROM information_schema.columns
        WHERE column_name = 'organization_id'
        AND table_name IN ('properties', 'contract_specifications')
      `;
      
      if (tablesWithFk.length >= 2) {
        logPass('Foreign key relationships properly set up for organization_id');
      } else {
        logWarning('Not all expected tables have organization_id foreign key');
      }
    } catch (error) {
      logFail('Error checking foreign key relationships', error);
    }
    
  } catch (error) {
    logFail('Failed to connect to database', error);
  }
}

/**
 * Verify API endpoints using sample requests
 */
async function validateAPIEndpoints() {
  console.log(chalk.blue('\n--- Validating API Endpoints ---'));
  
  const apiEndpoints = [
    '/api/health',
    '/api/organizations'
  ];
  
  // Base URL for API requests
  const baseURL = process.env.NEXT_PUBLIC_VERCEL_URL 
    ? `https://${process.env.NEXT_PUBLIC_VERCEL_URL}`
    : 'http://localhost:3000';
  
  for (const endpoint of apiEndpoints) {
    try {
      const response = await fetch(`${baseURL}${endpoint}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (response.status === 401) {
        // 401 is expected for protected routes when not authenticated
        logPass(`API endpoint ${endpoint} returns 401 as expected for unauthenticated request`);
      } else if (response.ok) {
        logPass(`API endpoint ${endpoint} returns ${response.status}`);
      } else {
        logFail(`API endpoint ${endpoint} returns unexpected status ${response.status}`);
      }
    } catch (error) {
      logFail(`Failed to access API endpoint ${endpoint}`, error);
    }
  }
}

/**
 * Verify MCP server connection
 */
async function validateMCPServer() {
  console.log(chalk.blue('\n--- Validating MCP Server ---'));
  
  const mcpPort = process.env.MCP_PORT || 3005;
  const mcpUrl = `http://localhost:${mcpPort}/sse`;
  
  try {
    // Try to connect to the SSE endpoint
    const source = new EventSource(mcpUrl);
    
    // Set up a promise to resolve or reject based on connection events
    const connectionPromise = new Promise((resolve, reject) => {
      let timeout = setTimeout(() => {
        source.close();
        reject(new Error('Connection timeout after 5 seconds'));
      }, 5000);
      
      source.onopen = () => {
        clearTimeout(timeout);
        resolve();
      };
      
      source.onerror = (err) => {
        clearTimeout(timeout);
        reject(err);
      };
    });
    
    await connectionPromise;
    source.close();
    logPass(`Successfully connected to MCP server at ${mcpUrl}`);
  } catch (error) {
    logFail('Failed to connect to MCP server', error);
    logWarning('MCP server might not be running, start it with: node neon-mcp-server.js');
  }
}

/**
 * Validate Clerk webhook configuration
 */
async function validateClerkWebhook() {
  console.log(chalk.blue('\n--- Validating Clerk Webhook Configuration ---'));
  
  if (!process.env.CLERK_WEBHOOK_SECRET) {
    logFail('CLERK_WEBHOOK_SECRET is not set, skipping webhook validation');
    return;
  }
  
  // We can't actually test the webhook directly, but we can verify the route exists
  const baseURL = process.env.NEXT_PUBLIC_VERCEL_URL 
    ? `https://${process.env.NEXT_PUBLIC_VERCEL_URL}`
    : 'http://localhost:3000';
  
  try {
    const response = await fetch(`${baseURL}/api/webhooks/clerk`, {
      method: 'GET', // Should return 405 Method Not Allowed as it only accepts POST
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    if (response.status === 405) {
      logPass('Clerk webhook endpoint exists and returns correct status for invalid method');
    } else {
      logWarning(`Clerk webhook endpoint returns unexpected status ${response.status}`);
    }
  } catch (error) {
    logFail('Failed to access Clerk webhook endpoint', error);
  }
}

/**
 * Run all validations and report results
 */
async function runAllValidations() {
  console.log(chalk.cyan.bold('=== ARA Property Services Deployment Validator ==='));
  console.log(chalk.cyan(`Timestamp: ${new Date().toISOString()}`));
  
  try {
    await validateEnvironmentVariables();
    await validateDatabase();
    await validateAPIEndpoints();
    await validateMCPServer();
    await validateClerkWebhook();
    
    console.log(chalk.blue('\n--- Validation Summary ---'));
    console.log(chalk.green(`Passed: ${results.passed}`));
    console.log(chalk.red(`Failed: ${results.failed}`));
    console.log(chalk.yellow(`Warnings: ${results.warnings}`));
    
    if (results.failed === 0) {
      console.log(chalk.green.bold('\n✓ Deployment validation successful!'));
      if (results.warnings > 0) {
        console.log(chalk.yellow('But please address the warnings above.'));
      }
    } else {
      console.log(chalk.red.bold('\n✗ Deployment validation failed!'));
      console.log(chalk.yellow('Please fix the issues above before proceeding with deployment.'));
    }
  } catch (error) {
    console.error(chalk.red('Validation process error:'), error);
  }
}

// Execute all validations
runAllValidations().catch(error => {
  console.error('Fatal error during validation:', error);
  process.exit(1);
});