/**
 * Environment Variables Validator for ARA Property Services App
 *
 * This script checks if all required environment variables are set and have valid formats
 * It can also generate a template .env file for production deployment
 */

import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';
import chalk from 'chalk';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Define required environment variables and their validation rules
const ENV_VARS = [
  {
    name: 'DATABASE_URL',
    required: true,
    format: /^postgres:\/\/.+:.+@.+(\.aws\.neon\.tech)(:\d+)?\/.+(\?sslmode=require)?$/,
    description: 'PostgreSQL connection string for Neon database'
  },
  {
    name: 'NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY',
    required: true,
    format: /^pk_(live|test)_/,
    description: 'Clerk publishable key (starts with pk_live_ or pk_test_)'
  },
  {
    name: 'CLERK_SECRET_KEY',
    required: true,
    format: /^sk_(live|test)_/,
    description: 'Clerk secret key (starts with sk_live_ or sk_test_)'
  },
  {
    name: 'CLERK_WEBHOOK_SECRET',
    required: true,
    format: /^whsec_/,
    description: 'Clerk webhook secret (starts with whsec_)'
  },
  {
    name: 'NEXT_PUBLIC_CLERK_SIGN_IN_URL',
    required: true,
    format: /^\/sign-in/,
    description: 'URL path for sign in page (should be /sign-in)'
  },
  {
    name: 'NEXT_PUBLIC_CLERK_SIGN_UP_URL',
    required: true,
    format: /^\/sign-up/,
    description: 'URL path for sign up page (should be /sign-up)'
  },
  {
    name: 'NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL',
    required: true,
    format: /^\//,
    description: 'URL path to redirect after sign in (should be /)'
  },
  {
    name: 'NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL',
    required: true,
    format: /^\//,
    description: 'URL path to redirect after sign up (should be /)'
  },
  {
    name: 'NEXT_PUBLIC_VERCEL_URL',
    required: false,
    format: /^.*$/,
    description: 'Vercel deployment URL (automatically set by Vercel)'
  },
  {
    name: 'NODE_ENV',
    required: false,
    format: /^(development|production|test)$/,
    description: 'Node environment (automatically set)'
  }
];

/**
 * Validate environment variables
 */
function validateEnvVars() {
  console.log(chalk.cyan.bold('=== Environment Variables Validator ==='));

  let missing = 0;
  let invalid = 0;
  let valid = 0;

  for (const envVar of ENV_VARS) {
    const value = process.env[envVar.name];

    if (!value && envVar.required) {
      console.log(chalk.red(`✗ ${envVar.name}: Missing (Required)`));
      console.log(chalk.gray(`  Description: ${envVar.description}`));
      missing++;
    } else if (value && !envVar.format.test(value)) {
      console.log(chalk.yellow(`⚠ ${envVar.name}: Invalid format`));
      console.log(chalk.gray(`  Description: ${envVar.description}`));
      console.log(chalk.gray(`  Expected format: ${envVar.format}`));
      invalid++;
    } else if (value) {
      const masked = value.replace(/^(.)(.+)(.{4})$/, (match, first, middle, last) => {
        return first + '*'.repeat(middle.length) + last;
      });
      console.log(chalk.green(`✓ ${envVar.name}: ${masked}`));
      valid++;
    } else {
      console.log(chalk.blue(`- ${envVar.name}: Not set (Optional)`));
      console.log(chalk.gray(`  Description: ${envVar.description}`));
    }
  }

  console.log('\n--- Summary ---');
  console.log(chalk.green(`Valid: ${valid}`));
  console.log(chalk.red(`Missing: ${missing}`));
  console.log(chalk.yellow(`Invalid: ${invalid}`));

  return { valid, missing, invalid };
}

/**
 * Generate a template .env file
 */
function generateEnvTemplate() {
  const templatePath = path.join(process.cwd(), '.env.template');

  let template = `# ARA Property Services App - Environment Variables Template
# Generated: ${new Date().toISOString()}
#
# Instructions:
# 1. Copy this file to .env.local for local development
# 2. Fill in the values for all required variables
# 3. For production, configure these variables in your Vercel project settings
# 4. Never commit .env.local to version control

`;

  for (const envVar of ENV_VARS) {
    template += `\n# ${envVar.description}`;
    template += envVar.required ? ' (REQUIRED)' : ' (Optional)';
    template += `\n${envVar.name}=`;

    // Add example values for common variables
    if (envVar.name === 'DATABASE_URL') {
      template += 'postgres://user:password@hostname:port/database';
    } else if (envVar.name === 'NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY') {
      template += 'pk_live_YOUR_PUBLISHABLE_KEY';
    } else if (envVar.name === 'CLERK_SECRET_KEY') {
      template += 'sk_live_YOUR_SECRET_KEY';
    } else if (envVar.name === 'CLERK_WEBHOOK_SECRET') {
      template += 'whsec_YOUR_WEBHOOK_SECRET';
    } else if (envVar.name === 'NEXT_PUBLIC_CLERK_SIGN_IN_URL') {
      template += '/sign-in';
    } else if (envVar.name === 'NEXT_PUBLIC_CLERK_SIGN_UP_URL') {
      template += '/sign-up';
    } else if (envVar.name === 'NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL') {
      template += '/';
    } else if (envVar.name === 'NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL') {
      template += '/';
    }

    template += '\n';
  }

  fs.writeFileSync(templatePath, template);
  console.log(chalk.green(`\n✓ Environment template generated at: ${templatePath}`));
}

/**
 * Generate Vercel CLI commands to set environment variables
 */
function generateVercelCommands() {
  console.log(chalk.cyan.bold('\n=== Vercel Environment Variables Commands ==='));
  console.log('Run these commands to set environment variables in your Vercel project:\n');

  for (const envVar of ENV_VARS) {
    // Skip variables that are automatically set by Vercel
    if (envVar.name === 'NEXT_PUBLIC_VERCEL_URL' || envVar.name === 'NODE_ENV') {
      continue;
    }

    const value = process.env[envVar.name] || '';
    console.log(`vercel env add ${envVar.name}`);
  }

  console.log('\nOr set them all at once using the Vercel dashboard: https://vercel.com/dashboard');
}

// Run validations
const { missing, invalid } = validateEnvVars();

// Generate template .env file
generateEnvTemplate();

// Generate Vercel CLI commands if needed
if (missing > 0 || invalid > 0) {
  generateVercelCommands();
}

// Exit with appropriate code
if (missing > 0) {
  console.log(chalk.red.bold('\n✗ Missing required environment variables. Please set them before deploying.'));
  process.exit(1);
} else if (invalid > 0) {
  console.log(chalk.yellow.bold('\n⚠ Some environment variables have invalid formats. Please check and fix them.'));
  process.exit(0);
} else {
  console.log(chalk.green.bold('\n✓ All required environment variables are properly set!'));
  process.exit(0);
}