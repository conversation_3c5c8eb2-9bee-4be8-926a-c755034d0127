import { api } from "../../convex/_generated/api";
import { ConvexClient } from "convex/browser";

export async function migrateInspectionTemplates(client: ConvexClient) {
  const templates = [
    {
      name: "Industrial Property Inspection",
      category: "Industrial",
      type: "Regular",
      version: 1,
      is_active: true,
      sections: [
        {
          name: "Safety",
          items: [
            { name: "Emergency Exits", type: "rating" },
            { name: "Fire Equipment", type: "rating" },
            { name: "First Aid", type: "rating" },
          ],
        },
        {
          name: "Equipment",
          items: [
            { name: "HVAC Systems", type: "rating" },
            { name: "Lighting", type: "rating" },
            { name: "Security Systems", type: "rating" },
          ],
        },
      ],
    },
    // Add more templates...
  ];

  for (const template of templates) {
    await client.mutation(api.inspection_templates.create, { template });
  }
}