import { api } from "../../convex/_generated/api";
import { ConvexClient } from "convex/browser";

export async function migrateInitialSetup(client: ConvexClient) {
  // Create default roles
  const roles = [
    {
      name: "Admin",
      permissions: {
        users: ["create", "read", "update", "delete"],
        properties: ["create", "read", "update", "delete"],
        inspections: ["create", "read", "update", "delete"],
      },
    },
    {
      name: "Property Manager",
      permissions: {
        properties: ["read", "update"],
        inspections: ["create", "read", "update"],
      },
    },
    {
      name: "Inspector",
      permissions: {
        properties: ["read"],
        inspections: ["create", "read", "update"],
      },
    },
  ];

  for (const role of roles) {
    await client.mutation(api.roles.create, { role });
  }

  // Create initial KPI targets
  const kpiTargets = [
    {
      metric_type: "inspection_score",
      target_value: 90,
      period: "monthly",
      category: "quality",
      property_type: "industrial",
    },
    {
      metric_type: "response_time",
      target_value: 24,
      period: "daily",
      category: "service",
      property_type: "all",
    },
  ];

  for (const target of kpiTargets) {
    await client.mutation(api.kpi_targets.create, { target });
  }
}