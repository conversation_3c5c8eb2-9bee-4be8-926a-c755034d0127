#!/usr/bin/env node

import { execSync } from 'child_process';
import { loadEnvConfig } from '@next/env';

// Load environment variables
loadEnvConfig(process.cwd());

console.log('Pushing schema to database...');

try {
  // Run drizzle-kit push
  execSync('npx drizzle-kit push:pg', { stdio: 'inherit' });
  
  console.log('✅ Schema pushed successfully!');
} catch (error) {
  console.error('❌ Error pushing schema:', error.message);
  process.exit(1);
}
