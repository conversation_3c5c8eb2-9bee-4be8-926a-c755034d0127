import 'dotenv/config';
import { neon } from '@neondatabase/serverless';
import fetch from 'node-fetch';

async function testElevenLabsCredentials() {
  console.log('Testing ElevenLabs credentials...');
  
  const apiKey = process.env.ELEVEN_LABS_API_KEY;
  const agentId = process.env.ELEVEN_LABS_AGENT_ID;
  
  if (!apiKey || !agentId) {
    console.error('❌ ElevenLabs credentials not found in environment variables');
    return false;
  }
  
  try {
    // Test the API key by making a simple request to the ElevenLabs API
    const apiUrl = new URL('https://api.elevenlabs.io/v1/user');
    
    const response = await fetch(apiUrl.toString(), {
      headers: { 'xi-api-key': apiKey },
    });
    
    if (!response.ok) {
      console.error(`❌ ElevenLabs API request failed: ${response.statusText}`);
      return false;
    }
    
    const data = await response.json();
    console.log('✅ ElevenLabs API credentials are valid');
    console.log(`Subscription: ${data.subscription.tier}`);
    
    // Test agent ID
    const agentUrl = new URL(`https://api.elevenlabs.io/v1/convai/agents/${agentId}`);
    
    const agentResponse = await fetch(agentUrl.toString(), {
      headers: { 'xi-api-key': apiKey },
    });
    
    if (!agentResponse.ok) {
      console.error(`❌ ElevenLabs Agent ID is invalid: ${agentResponse.statusText}`);
      return false;
    }
    
    const agentData = await agentResponse.json();
    console.log(`✅ ElevenLabs Agent ID is valid: "${agentData.name}"`);
    
    return true;
  } catch (error) {
    console.error('❌ Error testing ElevenLabs credentials:', error);
    return false;
  }
}

async function testDatabaseConnection() {
  console.log('\nTesting database connection...');
  
  const databaseUrl = process.env.DATABASE_URL;
  
  if (!databaseUrl) {
    console.error('❌ DATABASE_URL not found in environment variables');
    return false;
  }
  
  try {
    const sql = neon(databaseUrl);
    
    // Test the connection by executing a simple query
    const result = await sql('SELECT 1 as test');
    
    if (result && result[0] && result[0].test === 1) {
      console.log('✅ Database connection successful');
      return true;
    } else {
      console.error('❌ Database connection failed: Unexpected response');
      return false;
    }
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return false;
  }
}

async function runTests() {
  console.log('=== CREDENTIAL TESTING ===');
  
  const elevenLabsResult = await testElevenLabsCredentials();
  const databaseResult = await testDatabaseConnection();
  
  console.log('\n=== TEST RESULTS ===');
  console.log(`ElevenLabs Credentials: ${elevenLabsResult ? '✅ VALID' : '❌ INVALID'}`);
  console.log(`Database Connection: ${databaseResult ? '✅ VALID' : '❌ INVALID'}`);
  
  if (elevenLabsResult && databaseResult) {
    console.log('\n✅ All credentials are valid and working!');
  } else {
    console.log('\n❌ Some credentials are invalid or not working properly.');
  }
}

runTests().catch(console.error);
