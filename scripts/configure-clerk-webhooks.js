/**
 * Configure Clerk Webhooks
 * 
 * This script provides instructions and verification for setting up Clerk webhooks
 * for the multi-tenant architecture. It will guide you through:
 * 
 * 1. Required webhook endpoints for Clerk
 * 2. Verification of webhook configuration
 * 3. CORS and allowed origins setup
 * 4. Testing webhook functionality
 */

import fetch from 'node-fetch';
import crypto from 'crypto';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Configuration
const deploymentUrl = process.env.NEXT_PUBLIC_VERCEL_URL || 'https://askara-prod-final.vercel.app';
const webhookUrl = `${deploymentUrl}/api/webhooks/clerk`;
const webhookSecret = process.env.CLERK_WEBHOOK_SECRET;
const clerkSecretKey = process.env.CLERK_SECRET_KEY;

// Required webhook events for multi-tenant
const requiredEvents = [
  // User events
  'user.created',
  'user.updated',
  'user.deleted',
  
  // Session events
  'session.created',
  'session.ended',
  
  // Organization events
  'organization.created',
  'organization.updated',
  'organization.deleted',
  
  // Organization membership events
  'organizationMembership.created',
  'organizationMembership.updated',
  'organizationMembership.deleted',
  
  // Organization invitation events
  'organizationInvitation.created',
  'organizationInvitation.accepted',
];

/**
 * Print Clerk webhook setup instructions
 */
function printSetupInstructions() {
  console.log('=== CLERK WEBHOOK CONFIGURATION GUIDE ===\n');
  
  console.log('To configure Clerk webhooks for ARA Property Services App:\n');
  
  console.log('1. Log in to the Clerk Dashboard: https://dashboard.clerk.dev');
  console.log('2. Navigate to your application instance');
  console.log('3. Go to "Webhooks" in the left sidebar');
  console.log('4. Click "Add Endpoint"\n');
  
  console.log('5. Enter the following information:');
  console.log(`   - Endpoint URL: ${webhookUrl}`);
  console.log('   - Message Filtering: Select specific events (see below)');
  if (webhookSecret) {
    console.log(`   - Signing Secret: Already configured in environment (${webhookSecret.substring(0, 5)}...)`);
  } else {
    console.log('   - Signing Secret: Generate a new one and add it to your environment variables as CLERK_WEBHOOK_SECRET');
  }
  console.log('   - HTTP Headers: No additional headers needed\n');
  
  console.log('6. Select the following events to subscribe to:');
  requiredEvents.forEach(event => {
    console.log(`   - ${event}`);
  });
  
  console.log('\n7. Save the webhook configuration');
  console.log('8. Test the webhook using the "Trigger example" button\n');
  
  console.log('=== CORS CONFIGURATION ===\n');
  console.log('Ensure your Clerk application has the correct CORS configuration:');
  console.log('1. In the Clerk Dashboard, go to "JWT Templates" in the left sidebar');
  console.log('2. Under "CORS", add the following allowed origins:');
  console.log(`   - ${deploymentUrl}`);
  console.log('   - http://localhost:3000 (for local development)');
  
  console.log('\n=== VERCEL ENVIRONMENT VARIABLES ===\n');
  console.log('Ensure the following environment variables are set in your Vercel deployment:');
  console.log('- CLERK_SECRET_KEY');
  console.log('- CLERK_WEBHOOK_SECRET');
  console.log('- NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY');
  console.log('- NEXT_PUBLIC_CLERK_SIGN_IN_URL="/sign-in"');
  console.log('- NEXT_PUBLIC_CLERK_SIGN_UP_URL="/sign-up"');
  console.log('- NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL="/"');
  console.log('- NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL="/"');
}

/**
 * Check if Clerk API keys are available
 */
function checkClerkConfiguration() {
  const missingVars = [];
  
  if (!clerkSecretKey) missingVars.push('CLERK_SECRET_KEY');
  if (!webhookSecret) missingVars.push('CLERK_WEBHOOK_SECRET');
  
  if (missingVars.length > 0) {
    console.error('\n❌ Missing required environment variables:');
    missingVars.forEach(variable => {
      console.error(`   - ${variable}`);
    });
    console.error('\nThese variables are required to verify the webhook configuration.');
    return false;
  }
  
  return true;
}

/**
 * Generate a test webhook payload
 */
function generateTestWebhook() {
  // Mock user data for testing
  const testUser = {
    id: 'user_test123456789',
    email_addresses: [{
      email_address: '<EMAIL>',
      verification: { status: 'verified' }
    }],
    first_name: 'Test',
    last_name: 'User',
    created_at: new Date().toISOString()
  };
  
  // Create webhook payload
  const payload = {
    data: testUser,
    type: 'user.created',
    object: 'event'
  };
  
  // Generate timestamp (seconds since epoch)
  const timestamp = Math.floor(Date.now() / 1000);
  
  // Generate signature
  const signature = crypto
    .createHmac('sha256', webhookSecret || 'test-secret')
    .update(`${timestamp}.${JSON.stringify(payload)}`)
    .digest('hex');
  
  return {
    payload,
    headers: {
      'svix-id': `evt_${crypto.randomUUID()}`,
      'svix-timestamp': timestamp.toString(),
      'svix-signature': `v1,${signature}`
    }
  };
}

/**
 * Generate webhook verification code example
 */
function generateVerificationCodeExample() {
  console.log('\n=== WEBHOOK VERIFICATION CODE EXAMPLE ===\n');
  
  console.log('This is an example of how to verify Clerk webhooks in your Next.js API route:');
  
  console.log(`
// File: app/api/webhooks/clerk/route.ts
import { Webhook } from 'svix';
import { headers } from 'next/headers';
import { WebhookEvent } from '@clerk/nextjs/server';
import { db } from '@/app/db';
import { organizations, organizationMembers, users } from '@/app/db/schema';
import { eq } from 'drizzle-orm';

export async function POST(req: Request) {
  // Get the headers
  const headerPayload = headers();
  const svix_id = headerPayload.get('svix-id');
  const svix_timestamp = headerPayload.get('svix-timestamp');
  const svix_signature = headerPayload.get('svix-signature');

  // If there are no svix headers, error out
  if (!svix_id || !svix_timestamp || !svix_signature) {
    return new Response('Error: Missing svix headers', {
      status: 400
    });
  }

  // Get the body
  const payload = await req.json();
  const body = JSON.stringify(payload);

  // Create a new Svix instance with your webhook secret
  const wh = new Webhook(process.env.CLERK_WEBHOOK_SECRET || '');

  let evt: WebhookEvent;

  // Verify the webhook
  try {
    evt = wh.verify(body, {
      'svix-id': svix_id,
      'svix-timestamp': svix_timestamp,
      'svix-signature': svix_signature,
    }) as WebhookEvent;
  } catch (err) {
    console.error('Error verifying webhook:', err);
    return new Response('Error verifying webhook', {
      status: 400
    });
  }

  // Handle the webhook event
  const eventType = evt.type;
  
  try {
    switch (eventType) {
      case 'user.created':
        await handleUserCreated(evt.data);
        break;
      case 'organization.created':
        await handleOrganizationCreated(evt.data);
        break;
      case 'organizationMembership.created':
        await handleOrganizationMembershipCreated(evt.data);
        break;
      // Add other event handlers as needed
    }

    return new Response('Webhook processed successfully', {
      status: 200
    });
  } catch (error) {
    console.error('Error processing webhook:', error);
    return new Response('Error processing webhook', {
      status: 500
    });
  }
}

// Example handler for user.created event
async function handleUserCreated(data: any) {
  const { id, email_addresses, first_name, last_name } = data;
  
  // Create user in your database
  await db.insert(users).values({
    id,
    name: \`\${first_name || ''} \${last_name || ''}\`.trim() || 'User',
    email: email_addresses[0]?.email_address,
    created_at: new Date(),
  });
}

// Example handler for organization.created event
async function handleOrganizationCreated(data: any) {
  const { id, name, slug } = data;
  
  // Create organization in your database
  await db.insert(organizations).values({
    id,
    name,
    slug,
    created_at: new Date(),
  });
}

// Example handler for organizationMembership.created event
async function handleOrganizationMembershipCreated(data: any) {
  const { id, organization, public_user_data, role } = data;
  
  // Create organization membership in your database
  await db.insert(organizationMembers).values({
    id,
    organization_id: organization.id,
    user_id: public_user_data.user_id,
    role,
    joined_at: new Date(),
  });
}
`);
}

/**
 * Test webhook endpoint if available
 */
async function testWebhookEndpoint() {
  if (!checkClerkConfiguration()) {
    return false;
  }
  
  try {
    console.log('\n=== TESTING WEBHOOK ENDPOINT ===\n');
    console.log(`Testing endpoint: ${webhookUrl}`);
    
    // Generate test webhook
    const { payload, headers } = generateTestWebhook();
    
    console.log('Sending test webhook payload...');
    console.log('Headers:', headers);
    console.log('Payload:', JSON.stringify(payload, null, 2));
    
    // Send test request
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      body: JSON.stringify(payload)
    });
    
    // Check response
    if (response.ok) {
      console.log(`\n✅ Webhook test successful (${response.status} ${response.statusText})`);
      console.log('Response:', await response.text());
      return true;
    } else {
      console.error(`\n❌ Webhook test failed (${response.status} ${response.statusText})`);
      console.error('Response:', await response.text());
      return false;
    }
  } catch (error) {
    console.error('\n❌ Error testing webhook endpoint:', error);
    return false;
  }
}

/**
 * Main execution
 */
async function main() {
  // Print setup instructions
  printSetupInstructions();
  
  // Generate verification code example
  generateVerificationCodeExample();
  
  // Attempt to test the webhook endpoint if we have the necessary configuration
  if (process.env.TEST_WEBHOOK === 'true') {
    await testWebhookEndpoint();
  }
  
  console.log('\n=== NEXT STEPS ===\n');
  console.log('1. Set up webhooks in the Clerk Dashboard according to the instructions above');
  console.log('2. Ensure all environment variables are set in your Vercel deployment');
  console.log('3. Deploy your application with the webhook route implementation');
  console.log('4. Test the webhook endpoint by triggering events in the Clerk Dashboard');
  console.log('5. Set TEST_WEBHOOK=true and run this script again to test the live endpoint\n');
}

main().catch(console.error);
