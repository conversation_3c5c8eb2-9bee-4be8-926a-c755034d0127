#!/usr/bin/env node

import { execSync } from 'child_process';
import { loadEnvConfig } from '@next/env';

// Load environment variables
loadEnvConfig(process.cwd());

console.log('🚀 Setting up database...');

try {
  // Step 1: Generate migrations
  console.log('\n--- STEP 1: Generate Migrations ---');
  execSync('npx drizzle-kit generate:pg', { stdio: 'inherit' });

  // Step 2: Apply migrations
  console.log('\n--- STEP 2: Apply Migrations ---');
  execSync('npx tsx drizzle/migrate.ts', { stdio: 'inherit' });

  // Step 3: Push schema to database (for any changes not covered by migrations)
  console.log('\n--- STEP 3: Push Schema to Database ---');
  execSync('npx drizzle-kit push:pg', { stdio: 'inherit' });

  // Step 4: Verify schema
  console.log('\n--- STEP 4: Verify Schema ---');
  execSync('node scripts/verify-schema.js', { stdio: 'inherit' });

  // Step 5: Seed database
  console.log('\n--- STEP 5: Seed Database ---');
  execSync('node scripts/seed-db-new.js', { stdio: 'inherit' });

  // Step 6: Seed contract specifications
  console.log('\n--- STEP 6: Seed Contract Specifications ---');
  execSync('node scripts/seed-contract-specs.js', { stdio: 'inherit' });

  console.log('\n✅ Database setup completed successfully!');
} catch (error) {
  console.error('\n❌ Error setting up database:', error.message);
  process.exit(1);
}
