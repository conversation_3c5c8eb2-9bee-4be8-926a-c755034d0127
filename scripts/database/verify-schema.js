import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

// Get the directory of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = join(__dirname, '..');

// Load environment variables from .env.local first, then .env
let envLoaded = false;
const envLocalPath = join(rootDir, '.env.local');
const envPath = join(rootDir, '.env');

if (fs.existsSync(envLocalPath)) {
  dotenv.config({ path: envLocalPath });
  envLoaded = true;
  console.log('Loaded environment from .env.local');
}

if (!envLoaded && fs.existsSync(envPath)) {
  dotenv.config({ path: envPath });
  console.log('Loaded environment from .env');
}

// Expected tables based on schema.ts
const expectedTables = [
  'users',
  'messages',
  'contacts',
  'chat_sessions',
  'inspection_reports',
  'inspection_actions'
];

async function verifySchema() {
  console.log('Verifying database schema...');
  
  const databaseUrl = process.env.DATABASE_URL;
  
  if (!databaseUrl) {
    console.error('❌ DATABASE_URL not found in environment variables');
    return false;
  }
  
  try {
    console.log('Connecting to database...');
    const sql = neon(databaseUrl);
    
    // Get all tables
    const tables = await sql(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `);
    
    const tableNames = tables.map(t => t.table_name);
    
    console.log('\nVerifying tables:');
    let allTablesExist = true;
    
    for (const expectedTable of expectedTables) {
      if (tableNames.includes(expectedTable)) {
        console.log(`✅ Table '${expectedTable}' exists`);
        
        // Check table structure
        const columns = await sql(`
          SELECT column_name, data_type, is_nullable
          FROM information_schema.columns
          WHERE table_schema = 'public' AND table_name = $1
        `, [expectedTable]);
        
        console.log(`   Columns: ${columns.map(c => c.column_name).join(', ')}`);
      } else {
        console.error(`❌ Table '${expectedTable}' is missing`);
        allTablesExist = false;
      }
    }
    
    return allTablesExist;
  } catch (error) {
    console.error('❌ Database error:', error);
    return false;
  }
}

// Run the verification
verifySchema()
  .then(success => {
    if (success) {
      console.log('\n✅ Schema verification completed successfully. All expected tables exist.');
    } else {
      console.error('\n❌ Schema verification failed. Some tables are missing.');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
