#!/usr/bin/env node

import { execSync } from 'child_process';
import pkg from '@next/env';
const { loadEnvConfig } = pkg;

// Load environment variables
loadEnvConfig(process.cwd());

console.log('🚀 Running database migrations and seeding...');

try {
  // Step 1: Run migrations
  console.log('\n--- STEP 1: Run Migrations ---');
  execSync('node scripts/run-migrations.js', { stdio: 'inherit' });
  
  // Step 2: Seed database using the working command
  console.log('\n--- STEP 2: Seed database using JSON seed script ---');
  execSync('pnpm exec tsx seed_script.ts', { stdio: 'inherit' });
  
  console.log('\n✅ Database migrations and seeding completed successfully!');
} catch (error) {
  console.error('\n❌ Error running database migrations and seeding:', error.message);
  process.exit(1);
}
