import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';

// Load environment variables from .env.local
dotenv.config({ path: '.env.local' });

async function createMessagesTable() {
  console.log('Setting up database schema...');
  
  if (!process.env.DATABASE_URL) {
    throw new Error('DATABASE_URL environment variable not found.');
  }
  
  const sql = neon(process.env.DATABASE_URL);
  
  try {
    // Create messages table if it doesn't exist
    await sql(`
      CREATE TABLE IF NOT EXISTS messages (
        created_at SERIAL,
        id TEXT PRIMARY KEY,
        session_id TEXT,
        content_type TEXT,
        content_transcript TEXT,
        object TEXT,
        role TEXT,
        status TEXT,
        type TEXT
      );
    `);
    
    // Create index for efficient querying by session
    await sql(`
      CREATE INDEX IF NOT EXISTS idx_session_created_at 
      ON messages (session_id, created_at);
    `);
    
    console.log('✅ Database schema setup successfully.');
  } catch (error) {
    console.error('❌ Failed to set up database schema:', error);
    throw error;
  }
}

// Run the schema setup
createMessagesTable()
  .then(() => console.log('Schema setup complete.'))
  .catch(error => {
    console.error('Schema setup failed:', error);
    process.exit(1);
  });
