import { neon } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-http';
import { eq } from 'drizzle-orm';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';
import crypto from 'crypto';

// Get the directory of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = join(__dirname, '..');

// Load environment variables from .env.local first, then .env
let envLoaded = false;
const envLocalPath = join(rootDir, '.env.local');
const envPath = join(rootDir, '.env');

if (fs.existsSync(envLocalPath)) {
  dotenv.config({ path: envLocalPath });
  envLoaded = true;
  console.log('Loaded environment from .env.local');
}

if (!envLoaded && fs.existsSync(envPath)) {
  dotenv.config({ path: envPath });
  console.log('Loaded environment from .env');
}

// Import schema dynamically
async function importSchema() {
  try {
    // Use dynamic import to load the schema
    const schemaModule = await import('../app/db/schema.js');
    return schemaModule;
  } catch (error) {
    console.error('Error importing schema:', error);
    throw error;
  }
}

async function testDrizzle() {
  console.log('Testing Drizzle ORM functionality...');
  
  const databaseUrl = process.env.DATABASE_URL;
  
  if (!databaseUrl) {
    console.error('❌ DATABASE_URL not found in environment variables');
    return false;
  }
  
  try {
    // Import schema
    const schema = await importSchema();
    
    // Initialize Neon and Drizzle
    console.log('Initializing Drizzle with Neon...');
    const sql = neon(databaseUrl);
    const db = drizzle(sql, { schema });
    
    // Test user operations
    console.log('\nTesting user operations:');
    
    // 1. Create a test user
    const testUser = {
      id: crypto.randomUUID(),
      name: 'Test User',
      role: 'Tester',
      department: 'QA',
      email: `test-${Date.now()}@example.com`,
      password: 'test-password',
      preferences: { theme: 'dark', notifications: true }
    };
    
    console.log('Creating test user...');
    const insertResult = await db.insert(schema.users).values(testUser).returning();
    
    if (insertResult && insertResult.length > 0) {
      console.log('✅ User created successfully:', insertResult[0].email);
      
      // 2. Find the user by email
      console.log('Finding user by email...');
      const foundUser = await db.select().from(schema.users).where(eq(schema.users.email, testUser.email)).limit(1);
      
      if (foundUser && foundUser.length > 0) {
        console.log('✅ User found by email:', foundUser[0].email);
        
        // 3. Delete the test user
        console.log('Deleting test user...');
        const deleteResult = await db.delete(schema.users).where(eq(schema.users.id, testUser.id)).returning();
        
        if (deleteResult && deleteResult.length > 0) {
          console.log('✅ User deleted successfully');
        } else {
          console.error('❌ Failed to delete user');
          return false;
        }
      } else {
        console.error('❌ Failed to find user by email');
        return false;
      }
    } else {
      console.error('❌ Failed to create user');
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('❌ Drizzle test error:', error);
    return false;
  }
}

// Run the test
testDrizzle()
  .then(success => {
    if (success) {
      console.log('\n✅ Drizzle ORM functionality test completed successfully');
    } else {
      console.error('\n❌ Drizzle ORM functionality test failed');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
