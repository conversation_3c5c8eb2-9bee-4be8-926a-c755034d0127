// scripts/seed-core-data.js
import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';

// Get the directory of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = join(__dirname, '..');

// Load environment variables from .env.local first, then .env
let envLoaded = false;
const envLocalPath = join(rootDir, '.env.local');
const envPath = join(rootDir, '.env');

if (fs.existsSync(envLocalPath)) {
  dotenv.config({ path: envLocalPath });
  envLoaded = true;
  console.log('Loaded environment from .env.local');
}

if (!envLoaded && fs.existsSync(envPath)) {
  dotenv.config({ path: envPath });
  console.log('Loaded environment from .env');
}

async function seedCoreData() {
  console.log('🌱 Seeding core data...');
  
  const databaseUrl = process.env.DATABASE_URL;
  
  if (!databaseUrl) {
    console.error('❌ DATABASE_URL not found in environment variables');
    return false;
  }
  
  try {
    console.log('Connecting to database...');
    const sql = neon(databaseUrl);
    
    // Test the connection
    const result = await sql('SELECT 1 as test');
    if (result && result[0] && result[0].test === 1) {
      console.log('✅ Database connection successful');
    } else {
      console.error('❌ Database connection failed: Unexpected response');
      return false;
    }
    
    // Get existing users for references
    const users = await sql`SELECT id, name, role FROM users LIMIT 10`;
    
    if (users.length === 0) {
      console.error('❌ No users found in the database. Please run the user seed script first.');
      return false;
    }
    
    // Find admin, manager, and staff users
    const adminUser = users.find(user => user.role === 'Admin') || users[0];
    const managerUser = users.find(user => user.role === 'Manager') || users[1];
    const staffUser = users.find(user => user.role === 'Inspector' || user.role === 'Staff') || users[2];
    
    console.log(`Using admin user: ${adminUser.name} (${adminUser.id})`);
    console.log(`Using manager user: ${managerUser.name} (${managerUser.id})`);
    console.log(`Using staff user: ${staffUser.name} (${staffUser.id})`);
    
    // Seed clients
    console.log('\nSeeding clients...');
    
    const clients = [
      {
        id: uuidv4(),
        name: 'Australia Post',
        primary_contact_name: 'Sarah Johnson',
        primary_contact_email: '<EMAIL>',
        primary_contact_phone: '0412 345 678',
        address: '111 Bourke Street',
        suburb: 'Melbourne',
        state: 'VIC',
        postcode: '3000',
        sector: 'Government',
        account_manager_id: managerUser.id,
        status: 'active',
        notes: 'Major national client with multiple site types across Australia',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Star Track Express',
        primary_contact_name: 'Michael Chen',
        primary_contact_email: '<EMAIL>',
        primary_contact_phone: '0423 456 789',
        address: '222 Collins Street',
        suburb: 'Melbourne',
        state: 'VIC',
        postcode: '3000',
        sector: 'Logistics',
        account_manager_id: managerUser.id,
        status: 'active',
        notes: 'Subsidiary of Australia Post focusing on express delivery',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'NextDC',
        primary_contact_name: 'Jessica Lee',
        primary_contact_email: '<EMAIL>',
        primary_contact_phone: '0434 567 890',
        address: '333 Exhibition Street',
        suburb: 'Melbourne',
        state: 'VIC',
        postcode: '3000',
        sector: 'Technology',
        account_manager_id: managerUser.id,
        status: 'active',
        notes: 'Data center provider with specialized cleaning requirements',
        created_at: new Date(),
      },
    ];
    
    for (const client of clients) {
      // Check if client already exists
      const existingClient = await sql`
        SELECT id FROM clients WHERE name = ${client.name} LIMIT 1
      `;
      
      if (existingClient.length === 0) {
        await sql`
          INSERT INTO clients (id, name, primary_contact_name, primary_contact_email, primary_contact_phone, 
                              address, suburb, state, postcode, sector, account_manager_id, status, notes, created_at)
          VALUES (${client.id}, ${client.name}, ${client.primary_contact_name}, ${client.primary_contact_email}, 
                 ${client.primary_contact_phone}, ${client.address}, ${client.suburb}, ${client.state}, 
                 ${client.postcode}, ${client.sector}, ${client.account_manager_id}, ${client.status}, 
                 ${client.notes}, ${client.created_at})
          ON CONFLICT (id) DO NOTHING
        `;
        console.log(`✅ Created client: ${client.name}`);
      } else {
        console.log(`⏭️ Client already exists: ${client.name}`);
        // Update the ID to use the existing one
        client.id = existingClient[0].id;
      }
    }
    
    // Seed sites
    console.log('\nSeeding sites...');
    
    const ausPostClient = clients.find(client => client.name === 'Australia Post');
    const starTrackClient = clients.find(client => client.name === 'Star Track Express');
    const nextDCClient = clients.find(client => client.name === 'NextDC');
    
    const sites = [
      {
        id: uuidv4(),
        client_id: ausPostClient.id,
        name: 'Melbourne GPO',
        address: '250 Elizabeth Street',
        suburb: 'Melbourne',
        state: 'VIC',
        postcode: '3000',
        site_contact_name: 'David Wilson',
        site_contact_phone: '0445 678 901',
        site_contact_email: '<EMAIL>',
        site_type: 'Retail',
        tier: 1,
        floor_area_sqm: 2500,
        access_instructions: 'Access via loading dock on Little Bourke Street after hours',
        special_requirements: 'Heritage listed building with special cleaning protocols',
        status: 'active',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        client_id: ausPostClient.id,
        name: 'Dandenong Processing Facility',
        address: '42 Industrial Drive',
        suburb: 'Dandenong',
        state: 'VIC',
        postcode: '3175',
        site_contact_name: 'Emma Taylor',
        site_contact_phone: '0456 789 012',
        site_contact_email: '<EMAIL>',
        site_type: 'Processing',
        tier: 1,
        floor_area_sqm: 15000,
        access_instructions: '24/7 access via security gate, security pass required',
        special_requirements: 'High volume mail processing facility with specialized equipment',
        status: 'active',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        client_id: starTrackClient.id,
        name: 'Sydney Distribution Center',
        address: '123 Parramatta Road',
        suburb: 'Strathfield',
        state: 'NSW',
        postcode: '2135',
        site_contact_name: 'Robert Brown',
        site_contact_phone: '0467 890 123',
        site_contact_email: '<EMAIL>',
        site_type: 'Distribution',
        tier: 2,
        floor_area_sqm: 12000,
        access_instructions: 'Access via main entrance, sign in at security',
        special_requirements: 'Heavy vehicle traffic area, safety vests required',
        status: 'active',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        client_id: ausPostClient.id,
        name: 'Brisbane Delivery Center',
        address: '78 Boundary Street',
        suburb: 'Brisbane',
        state: 'QLD',
        postcode: '4000',
        site_contact_name: 'Lisa Wong',
        site_contact_phone: '0478 901 234',
        site_contact_email: '<EMAIL>',
        site_type: 'Delivery',
        tier: 3,
        floor_area_sqm: 5000,
        access_instructions: 'Access via staff entrance on side of building',
        special_requirements: 'Cleaning to be done after 6pm when staff have left',
        status: 'active',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        client_id: nextDCClient.id,
        name: 'M1 Melbourne Data Center',
        address: '65 Tech Drive',
        suburb: 'Port Melbourne',
        state: 'VIC',
        postcode: '3207',
        site_contact_name: 'Andrew Smith',
        site_contact_phone: '0489 012 345',
        site_contact_email: '<EMAIL>',
        site_type: 'Data Center',
        tier: 1,
        floor_area_sqm: 8000,
        access_instructions: 'Strict security protocols, advance notice required',
        special_requirements: 'Specialized cleaning for data center environment, no liquids near server racks',
        status: 'active',
        created_at: new Date(),
      },
    ];
    
    for (const site of sites) {
      // Check if site already exists
      const existingSite = await sql`
        SELECT id FROM sites WHERE name = ${site.name} AND client_id = ${site.client_id} LIMIT 1
      `;
      
      if (existingSite.length === 0) {
        await sql`
          INSERT INTO sites (id, client_id, name, address, suburb, state, postcode, site_contact_name, 
                           site_contact_phone, site_contact_email, site_type, tier, floor_area_sqm, 
                           access_instructions, special_requirements, status, created_at)
          VALUES (${site.id}, ${site.client_id}, ${site.name}, ${site.address}, ${site.suburb}, 
                 ${site.state}, ${site.postcode}, ${site.site_contact_name}, ${site.site_contact_phone}, 
                 ${site.site_contact_email}, ${site.site_type}, ${site.tier}, ${site.floor_area_sqm}, 
                 ${site.access_instructions}, ${site.special_requirements}, ${site.status}, ${site.created_at})
          ON CONFLICT (id) DO NOTHING
        `;
        console.log(`✅ Created site: ${site.name}`);
      } else {
        console.log(`⏭️ Site already exists: ${site.name}`);
        // Update the ID to use the existing one
        site.id = existingSite[0].id;
      }
    }
    
    // Seed service contracts
    console.log('\nSeeding service contracts...');
    
    const contracts = [
      {
        id: uuidv4(),
        client_id: ausPostClient.id,
        site_id: sites.find(site => site.name === 'Melbourne GPO').id,
        start_date: new Date('2024-01-01'),
        end_date: new Date('2026-12-31'),
        contract_value: 250000,
        billing_frequency: 'Monthly',
        service_type: 'Cleaning',
        service_frequency: 'Daily',
        special_terms: 'Includes periodical services as per schedule',
        status: 'active',
        contract_file_url: 'https://example.com/contracts/melbourne-gpo-2024.pdf',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        client_id: ausPostClient.id,
        site_id: sites.find(site => site.name === 'Dandenong Processing Facility').id,
        start_date: new Date('2024-01-01'),
        end_date: new Date('2026-12-31'),
        contract_value: 450000,
        billing_frequency: 'Monthly',
        service_type: 'Cleaning',
        service_frequency: 'Daily',
        special_terms: 'Includes specialized cleaning for mail processing equipment',
        status: 'active',
        contract_file_url: 'https://example.com/contracts/dandenong-2024.pdf',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        client_id: starTrackClient.id,
        site_id: sites.find(site => site.name === 'Sydney Distribution Center').id,
        start_date: new Date('2024-03-01'),
        end_date: new Date('2027-02-28'),
        contract_value: 380000,
        billing_frequency: 'Monthly',
        service_type: 'Cleaning',
        service_frequency: 'Daily',
        special_terms: 'Includes loading dock degreasing quarterly',
        status: 'active',
        contract_file_url: 'https://example.com/contracts/sydney-dc-2024.pdf',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        client_id: ausPostClient.id,
        site_id: sites.find(site => site.name === 'Brisbane Delivery Center').id,
        start_date: new Date('2024-02-01'),
        end_date: new Date('2026-01-31'),
        contract_value: 180000,
        billing_frequency: 'Monthly',
        service_type: 'Cleaning',
        service_frequency: '3 days per week',
        special_terms: 'Monday, Wednesday, Friday service',
        status: 'active',
        contract_file_url: 'https://example.com/contracts/brisbane-dc-2024.pdf',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        client_id: nextDCClient.id,
        site_id: sites.find(site => site.name === 'M1 Melbourne Data Center').id,
        start_date: new Date('2024-04-01'),
        end_date: new Date('2027-03-31'),
        contract_value: 320000,
        billing_frequency: 'Monthly',
        service_type: 'Specialized Cleaning',
        service_frequency: 'Daily',
        special_terms: 'Includes anti-static cleaning protocols for server rooms',
        status: 'active',
        contract_file_url: 'https://example.com/contracts/nextdc-m1-2024.pdf',
        created_at: new Date(),
      },
    ];
    
    for (const contract of contracts) {
      // Check if contract already exists
      const existingContract = await sql`
        SELECT id FROM service_contracts 
        WHERE client_id = ${contract.client_id} AND site_id = ${contract.site_id} 
        AND start_date = ${contract.start_date} LIMIT 1
      `;
      
      if (existingContract.length === 0) {
        await sql`
          INSERT INTO service_contracts (id, client_id, site_id, start_date, end_date, contract_value, 
                                       billing_frequency, service_type, service_frequency, special_terms, 
                                       status, contract_file_url, created_at)
          VALUES (${contract.id}, ${contract.client_id}, ${contract.site_id}, ${contract.start_date}, 
                 ${contract.end_date}, ${contract.contract_value}, ${contract.billing_frequency}, 
                 ${contract.service_type}, ${contract.service_frequency}, ${contract.special_terms}, 
                 ${contract.status}, ${contract.contract_file_url}, ${contract.created_at})
          ON CONFLICT (id) DO NOTHING
        `;
        console.log(`✅ Created contract for: ${sites.find(site => site.id === contract.site_id).name}`);
      } else {
        console.log(`⏭️ Contract already exists for: ${sites.find(site => site.id === contract.site_id).name}`);
      }
    }
    
    // Seed equipment
    console.log('\nSeeding equipment...');
    
    const equipment = [
      {
        id: uuidv4(),
        name: 'Industrial Floor Scrubber',
        type: 'Cleaning',
        serial_number: 'FS-2024-001',
        purchase_date: new Date('2023-10-15'),
        cost: 12500,
        current_location: 'Melbourne Warehouse',
        status: 'operational',
        last_serviced_date: new Date('2024-03-10'),
        service_interval_days: 90,
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Commercial Vacuum Cleaner',
        type: 'Cleaning',
        serial_number: 'VC-2024-005',
        purchase_date: new Date('2023-11-20'),
        cost: 1800,
        current_location: 'Melbourne GPO',
        status: 'operational',
        last_serviced_date: new Date('2024-02-15'),
        service_interval_days: 60,
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'High Pressure Washer',
        type: 'Cleaning',
        serial_number: 'HPW-2023-012',
        purchase_date: new Date('2023-08-05'),
        cost: 3500,
        current_location: 'Sydney Warehouse',
        status: 'operational',
        last_serviced_date: new Date('2024-01-20'),
        service_interval_days: 90,
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Carpet Extractor',
        type: 'Cleaning',
        serial_number: 'CE-2023-008',
        purchase_date: new Date('2023-09-12'),
        cost: 4200,
        current_location: 'Brisbane Warehouse',
        status: 'operational',
        last_serviced_date: new Date('2024-03-05'),
        service_interval_days: 90,
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Anti-Static Vacuum',
        type: 'Specialized Cleaning',
        serial_number: 'ASV-2024-003',
        purchase_date: new Date('2024-01-10'),
        cost: 2500,
        current_location: 'M1 Melbourne Data Center',
        status: 'operational',
        last_serviced_date: new Date('2024-04-01'),
        service_interval_days: 60,
        created_at: new Date(),
      },
    ];
    
    for (const item of equipment) {
      // Check if equipment already exists
      const existingEquipment = await sql`
        SELECT id FROM equipment WHERE serial_number = ${item.serial_number} LIMIT 1
      `;
      
      if (existingEquipment.length === 0) {
        await sql`
          INSERT INTO equipment (id, name, type, serial_number, purchase_date, cost, current_location, 
                               status, last_serviced_date, service_interval_days, created_at)
          VALUES (${item.id}, ${item.name}, ${item.type}, ${item.serial_number}, ${item.purchase_date}, 
                 ${item.cost}, ${item.current_location}, ${item.status}, ${item.last_serviced_date}, 
                 ${item.service_interval_days}, ${item.created_at})
          ON CONFLICT (id) DO NOTHING
        `;
        console.log(`✅ Created equipment: ${item.name}`);
      } else {
        console.log(`⏭️ Equipment already exists: ${item.name}`);
      }
    }
    
    // Seed inventory
    console.log('\nSeeding inventory...');
    
    const inventory = [
      {
        id: uuidv4(),
        item_name: 'All-Purpose Cleaner',
        item_category: 'Cleaning Supplies',
        current_stock: 120,
        unit_of_measure: 'Bottle',
        reorder_threshold: 30,
        cost_per_unit: 5.50,
        storage_location: 'Melbourne Warehouse',
        last_restocked_date: new Date('2024-04-01'),
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        item_name: 'Disinfectant',
        item_category: 'Cleaning Supplies',
        current_stock: 85,
        unit_of_measure: 'Bottle',
        reorder_threshold: 25,
        cost_per_unit: 7.25,
        storage_location: 'Melbourne Warehouse',
        last_restocked_date: new Date('2024-04-01'),
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        item_name: 'Microfiber Cloths',
        item_category: 'Cleaning Supplies',
        current_stock: 250,
        unit_of_measure: 'Cloth',
        reorder_threshold: 50,
        cost_per_unit: 2.00,
        storage_location: 'Melbourne Warehouse',
        last_restocked_date: new Date('2024-03-15'),
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        item_name: 'Floor Polish',
        item_category: 'Cleaning Supplies',
        current_stock: 40,
        unit_of_measure: 'Bottle',
        reorder_threshold: 10,
        cost_per_unit: 15.75,
        storage_location: 'Melbourne Warehouse',
        last_restocked_date: new Date('2024-02-20'),
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        item_name: 'Anti-Static Cleaning Solution',
        item_category: 'Specialized Cleaning',
        current_stock: 25,
        unit_of_measure: 'Bottle',
        reorder_threshold: 8,
        cost_per_unit: 22.50,
        storage_location: 'Melbourne Warehouse',
        last_restocked_date: new Date('2024-03-10'),
        created_at: new Date(),
      },
    ];
    
    for (const item of inventory) {
      // Check if inventory item already exists
      const existingInventory = await sql`
        SELECT id FROM inventory WHERE item_name = ${item.item_name} LIMIT 1
      `;
      
      if (existingInventory.length === 0) {
        await sql`
          INSERT INTO inventory (id, item_name, item_category, current_stock, unit_of_measure, 
                               reorder_threshold, cost_per_unit, storage_location, last_restocked_date, created_at)
          VALUES (${item.id}, ${item.item_name}, ${item.item_category}, ${item.current_stock}, 
                 ${item.unit_of_measure}, ${item.reorder_threshold}, ${item.cost_per_unit}, 
                 ${item.storage_location}, ${item.last_restocked_date}, ${item.created_at})
          ON CONFLICT (id) DO NOTHING
        `;
        console.log(`✅ Created inventory item: ${item.item_name}`);
      } else {
        console.log(`⏭️ Inventory item already exists: ${item.item_name}`);
      }
    }
    
    console.log('\n✅ Core data seeding completed successfully!');
    return true;
  } catch (error) {
    console.error('❌ Error seeding core data:', error);
    return false;
  }
}

// Run the seed function
seedCoreData()
  .then(success => {
    if (success) {
      console.log('\n✅ Core data seeding completed successfully');
    } else {
      console.error('\n❌ Core data seeding failed');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
