// Script to seed test data for development
const { neon } = require('@neondatabase/serverless');
const { drizzle } = require('drizzle-orm/neon-http');
const { users, roles } = require('../app/db/schema');
const { v4: uuidv4 } = require('uuid');
const bcrypt = require('bcryptjs');
require('dotenv').config();

async function seedTestData() {
  console.log('Seeding test data...');
  
  try {
    if (!process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL environment variable is not defined');
    }
    
    // Initialize the Neon serverless driver
    const sql = neon(process.env.DATABASE_URL);
    
    // Initialize Drizzle ORM with the Neon driver and the schema
    const db = drizzle(sql);
    
    // Check if we already have users
    const existingUsers = await db.select({ count: { value: users.id } })
      .from(users)
      .then(result => result[0]?.count?.value || 0);
    
    if (existingUsers > 0) {
      console.log(`Database already has ${existingUsers} users. Skipping seed.`);
      return;
    }
    
    // Create roles
    console.log('Creating roles...');
    await db.insert(roles).values([
      {
        id: uuidv4(),
        name: 'Admin',
        permissions: JSON.stringify({
          users: ['create', 'read', 'update', 'delete'],
          properties: ['create', 'read', 'update', 'delete'],
          inspections: ['create', 'read', 'update', 'delete'],
        }),
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Manager',
        permissions: JSON.stringify({
          users: ['read'],
          properties: ['create', 'read', 'update'],
          inspections: ['create', 'read', 'update'],
        }),
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Inspector',
        permissions: JSON.stringify({
          users: ['read'],
          properties: ['read'],
          inspections: ['create', 'read', 'update'],
        }),
        created_at: new Date(),
      },
    ]);
    
    // Create test users
    console.log('Creating test users...');
    const hashedPassword = await bcrypt.hash('password123', 10);
    
    await db.insert(users).values([
      {
        id: uuidv4(),
        name: 'Admin User',
        role: 'Admin',
        department: 'Management',
        email: '<EMAIL>',
        password: hashedPassword,
        phone: '0412345678',
        created_at: new Date(),
        last_login: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Manager User',
        role: 'Manager',
        department: 'Operations',
        email: '<EMAIL>',
        password: hashedPassword,
        phone: '0423456789',
        created_at: new Date(),
        last_login: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Inspector User',
        role: 'Inspector',
        department: 'Field',
        email: '<EMAIL>',
        password: hashedPassword,
        phone: '0434567890',
        created_at: new Date(),
        last_login: new Date(),
      },
    ]);
    
    console.log('Test data seeded successfully.');
  } catch (error) {
    console.error('Error seeding test data:', error);
  }
}

seedTestData();
