// Script to seed contract specifications data
const { neon } = require('@neondatabase/serverless');
const { drizzle } = require('drizzle-orm/neon-http');
const { v4: uuidv4 } = require('uuid');
require('dotenv').config();

async function seedContractSpecifications() {
  console.log('Seeding contract specifications data...');
  
  try {
    if (!process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL environment variable is not defined');
    }
    
    // Initialize the Neon serverless driver
    const sql = neon(process.env.DATABASE_URL);
    
    // Initialize Drizzle ORM with the Neon driver
    const db = drizzle(sql);
    
    // Seed cleaning areas
    console.log('Creating cleaning areas...');
    const cleaningAreas = [
      {
        id: uuidv4(),
        name: 'Toilets',
        description: 'Bathroom and toilet facilities',
        category: 'Sanitation',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Kitchen',
        description: 'Kitchen and break room areas',
        category: 'Food Preparation',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Office',
        description: 'Office and administrative areas',
        category: 'Workspace',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Retail',
        description: 'Customer-facing retail areas',
        category: 'Public',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Production',
        description: 'Mail sorting and processing areas',
        category: 'Industrial',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'External',
        description: 'External areas including entrances and loading docks',
        category: 'Outdoor',
        created_at: new Date(),
      },
    ];
    
    for (const area of cleaningAreas) {
      await sql`
        INSERT INTO cleaning_areas (id, name, description, category, created_at)
        VALUES (${area.id}, ${area.name}, ${area.description}, ${area.category}, ${area.created_at})
        ON CONFLICT (id) DO NOTHING
      `;
    }
    console.log('✅ Cleaning areas created');
    
    // Seed frequency types
    console.log('Creating frequency types...');
    const frequencyTypes = [
      {
        id: uuidv4(),
        name: '3x Daily',
        description: 'Three times per day',
        times_per_year: 1095,
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Daily',
        description: 'Once per day',
        times_per_year: 365,
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: '3 of 5 days',
        description: 'Three days per week',
        times_per_year: 156,
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Weekly',
        description: 'Once per week',
        times_per_year: 52,
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Monthly',
        description: 'Once per month',
        times_per_year: 12,
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Quarterly',
        description: 'Once every three months',
        times_per_year: 4,
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Biannual',
        description: 'Twice per year',
        times_per_year: 2,
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Annual',
        description: 'Once per year',
        times_per_year: 1,
        created_at: new Date(),
      },
    ];
    
    for (const frequency of frequencyTypes) {
      await sql`
        INSERT INTO frequency_types (id, name, description, times_per_year, created_at)
        VALUES (${frequency.id}, ${frequency.name}, ${frequency.description}, ${frequency.times_per_year}, ${frequency.created_at})
        ON CONFLICT (name) DO NOTHING
      `;
    }
    console.log('✅ Frequency types created');
    
    // Seed tier specifications
    console.log('Creating tier specifications...');
    const tierSpecifications = [
      {
        id: uuidv4(),
        name: 'Tier 1',
        description: 'Large industrial facilities operating 24/7 with multiple daily cleaning requirements',
        tier_level: 1,
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Tier 2',
        description: 'Smaller industrial centers with daily cleaning schedules',
        tier_level: 2,
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Tier 3',
        description: 'Smaller delivery centers with reduced frequency (3 out of 5 operational days)',
        tier_level: 3,
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Tier 4',
        description: 'Smaller delivery centers with reduced frequency (3 out of 5 operational days)',
        tier_level: 4,
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Tier 5',
        description: 'Smaller delivery centers with reduced frequency (3 out of 5 operational days)',
        tier_level: 5,
        created_at: new Date(),
      },
    ];
    
    for (const tier of tierSpecifications) {
      await sql`
        INSERT INTO tier_specifications (id, name, description, tier_level, created_at)
        VALUES (${tier.id}, ${tier.name}, ${tier.description}, ${tier.tier_level}, ${tier.created_at})
        ON CONFLICT (id) DO NOTHING
      `;
    }
    console.log('✅ Tier specifications created');
    
    // Seed cleaning tasks
    console.log('Creating cleaning tasks...');
    // Get the cleaning areas IDs
    const toiletsArea = await sql`SELECT id FROM cleaning_areas WHERE name = 'Toilets' LIMIT 1`;
    const kitchenArea = await sql`SELECT id FROM cleaning_areas WHERE name = 'Kitchen' LIMIT 1`;
    const officeArea = await sql`SELECT id FROM cleaning_areas WHERE name = 'Office' LIMIT 1`;
    const retailArea = await sql`SELECT id FROM cleaning_areas WHERE name = 'Retail' LIMIT 1`;
    const productionArea = await sql`SELECT id FROM cleaning_areas WHERE name = 'Production' LIMIT 1`;
    const externalArea = await sql`SELECT id FROM cleaning_areas WHERE name = 'External' LIMIT 1`;
    
    const toiletsAreaId = toiletsArea.rows[0]?.id;
    const kitchenAreaId = kitchenArea.rows[0]?.id;
    const officeAreaId = officeArea.rows[0]?.id;
    const retailAreaId = retailArea.rows[0]?.id;
    const productionAreaId = productionArea.rows[0]?.id;
    const externalAreaId = externalArea.rows[0]?.id;
    
    const cleaningTasks = [
      {
        id: uuidv4(),
        area_id: toiletsAreaId,
        name: 'Clean and disinfect fixtures',
        description: 'Clean and disinfect all toilet fixtures, sinks, and countertops',
        task_type: 'Regular',
        category: 'Sanitation',
        standard_duration_minutes: 15,
        equipment_required: JSON.stringify(['Mop', 'Bucket', 'Disinfectant']),
        materials_required: JSON.stringify(['Disinfectant', 'Cloths', 'Gloves']),
        safety_requirements: 'Wear gloves and eye protection when using disinfectants',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        area_id: toiletsAreaId,
        name: 'Spot clean walls and mirrors',
        description: 'Clean mirrors and spot clean walls and partitions',
        task_type: 'Regular',
        category: 'Sanitation',
        standard_duration_minutes: 10,
        equipment_required: JSON.stringify(['Glass cleaner', 'Microfiber cloth']),
        materials_required: JSON.stringify(['Glass cleaner', 'Cloths']),
        safety_requirements: 'Ensure proper ventilation',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        area_id: kitchenAreaId,
        name: 'Clean sinks and surfaces',
        description: 'Clean and sanitize kitchen sinks and countertops',
        task_type: 'Regular',
        category: 'Kitchen',
        standard_duration_minutes: 15,
        equipment_required: JSON.stringify(['Sanitizer', 'Cloths']),
        materials_required: JSON.stringify(['Sanitizer', 'Cloths', 'Gloves']),
        safety_requirements: 'Wear gloves when using cleaning chemicals',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        area_id: kitchenAreaId,
        name: 'Clean appliances',
        description: 'Clean exterior of appliances including microwaves and refrigerators',
        task_type: 'Regular',
        category: 'Kitchen',
        standard_duration_minutes: 20,
        equipment_required: JSON.stringify(['All-purpose cleaner', 'Microfiber cloth']),
        materials_required: JSON.stringify(['All-purpose cleaner', 'Cloths']),
        safety_requirements: 'Ensure appliances are unplugged if necessary',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        area_id: officeAreaId,
        name: 'Clean desks and surfaces',
        description: 'Dust and clean desks, tables, and other surfaces',
        task_type: 'Regular',
        category: 'Office',
        standard_duration_minutes: 30,
        equipment_required: JSON.stringify(['Duster', 'All-purpose cleaner']),
        materials_required: JSON.stringify(['All-purpose cleaner', 'Cloths']),
        safety_requirements: 'Do not disturb papers or personal items',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        area_id: retailAreaId,
        name: 'Clean glass displays',
        description: 'Clean and polish glass display cases and counters',
        task_type: 'Regular',
        category: 'Retail',
        standard_duration_minutes: 20,
        equipment_required: JSON.stringify(['Glass cleaner', 'Microfiber cloth']),
        materials_required: JSON.stringify(['Glass cleaner', 'Cloths']),
        safety_requirements: 'Use caution around fragile items',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        area_id: productionAreaId,
        name: 'Sweep production floor',
        description: 'Sweep and clean production area floors',
        task_type: 'Regular',
        category: 'Industrial',
        standard_duration_minutes: 45,
        equipment_required: JSON.stringify(['Industrial sweeper', 'Dust mop']),
        materials_required: JSON.stringify(['Dust control compound']),
        safety_requirements: 'Wear high-visibility vest in active areas',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        area_id: externalAreaId,
        name: 'Clean entrance areas',
        description: 'Clean and maintain entrance areas including doors and mats',
        task_type: 'Regular',
        category: 'External',
        standard_duration_minutes: 15,
        equipment_required: JSON.stringify(['Broom', 'Mop']),
        materials_required: JSON.stringify(['All-purpose cleaner']),
        safety_requirements: 'Use caution signs for wet floors',
        created_at: new Date(),
      },
    ];
    
    for (const task of cleaningTasks) {
      await sql`
        INSERT INTO cleaning_tasks (id, area_id, name, description, task_type, category, standard_duration_minutes, equipment_required, materials_required, safety_requirements, created_at)
        VALUES (${task.id}, ${task.area_id}, ${task.name}, ${task.description}, ${task.task_type}, ${task.category}, ${task.standard_duration_minutes}, ${task.equipment_required}, ${task.materials_required}, ${task.safety_requirements}, ${task.created_at})
        ON CONFLICT (id) DO NOTHING
      `;
    }
    console.log('✅ Cleaning tasks created');
    
    // Seed retail cleaning scope
    console.log('Creating retail cleaning scope...');
    const retailCleaningScope = [
      {
        id: uuidv4(),
        area: 'Front of House',
        element: 'Hard Flooring',
        requirement: 'Clean and maintain',
        frequency: 'Daily',
        category: 'Flooring',
        notes: 'Risk controls required',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        area: 'Front of House',
        element: 'Carpets',
        requirement: 'Clean and maintain',
        frequency: 'Daily',
        category: 'Flooring',
        notes: 'Avoid trailing cables',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        area: 'Front of House',
        element: 'Windows',
        requirement: 'Clean and maintain',
        frequency: 'Daily',
        category: 'Windows',
        notes: 'Includes entrance glass',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        area: 'Front of House',
        element: 'Displays',
        requirement: 'Clean and maintain',
        frequency: 'Daily',
        category: 'Retail',
        notes: 'Glass displays included',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        area: 'Back of House',
        element: 'Toilets',
        requirement: 'Clean and sanitize',
        frequency: 'Daily',
        category: 'Sanitation',
        notes: 'Include mirrors weekly',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        area: 'Back of House',
        element: 'Kitchen',
        requirement: 'Clean and maintain',
        frequency: 'Daily',
        category: 'Kitchen',
        notes: 'Weekly deep clean',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        area: 'Back of House',
        element: 'Office',
        requirement: 'Clean and maintain',
        frequency: 'Daily',
        category: 'Office',
        notes: 'Staff coordination required',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        area: 'Specialist',
        element: 'Deep Clean',
        requirement: 'Strip and seal floors',
        frequency: '6 monthly',
        category: 'Specialist',
        notes: 'Both front and back',
        created_at: new Date(),
      },
    ];
    
    for (const scope of retailCleaningScope) {
      await sql`
        INSERT INTO retail_cleaning_scope (id, area, element, requirement, frequency, category, notes, created_at)
        VALUES (${scope.id}, ${scope.area}, ${scope.element}, ${scope.requirement}, ${scope.frequency}, ${scope.category}, ${scope.notes}, ${scope.created_at})
        ON CONFLICT (id) DO NOTHING
      `;
    }
    console.log('✅ Retail cleaning scope created');
    
    console.log('Contract specifications data seeded successfully.');
  } catch (error) {
    console.error('Error seeding contract specifications data:', error);
  }
}

seedContractSpecifications();
