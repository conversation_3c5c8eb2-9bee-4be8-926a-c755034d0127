// Test script for authentication flow
const { auth, clerkClient: createClerkClient } = require('@clerk/nextjs/server');
const { db } = require('@/app/db');
const { users } = require('@/app/db/schema');
const { eq } = require('drizzle-orm');

async function testAuth() {
  console.log('Testing authentication flow...');
  
  try {
    // Test database connection
    console.log('Testing database connection...');
    const allUsers = await db.query.users.findMany({
      limit: 5,
    });
    
    console.log(`Found ${allUsers.length} users in the database.`);
    
    // Test Clerk connection
    console.log('Testing Clerk connection...');
    // This would normally be done in a server context with a valid session
    // For testing purposes, we'll just check if the environment variables are set
    
    if (!process.env.CLERK_SECRET_KEY) {
      console.error('CLERK_SECRET_KEY is not set.');
      return;
    }
    
    if (!process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY) {
      console.error('NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY is not set.');
      return;
    }
    
    console.log('Clerk environment variables are set.');
    
    // Test webhook secret
    if (!process.env.CLERK_WEBHOOK_SECRET) {
      console.error('CLERK_WEBHOOK_SECRET is not set.');
      return;
    }
    
    console.log('Clerk webhook secret is set.');
    
    console.log('Authentication flow test completed successfully.');
  } catch (error) {
    console.error('Error testing authentication flow:', error);
  }
}

testAuth();
