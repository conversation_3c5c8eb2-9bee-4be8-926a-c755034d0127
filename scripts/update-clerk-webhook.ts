#!/usr/bin/env tsx

import { Clerk } from '@clerk/backend';

// Initialize Clerk with your secret key
const clerk = new Clerk({
  secretKey: process.env.CLERK_SECRET_KEY!
});

async function updateWebhook() {
  try {
    // First, list all webhooks to find the one to update
    const webhooks = await clerk.webhooks.getWebhookList();
    
    // Find the webhook with the Svix playground URL
    const webhookToUpdate = webhooks.data.find(webhook => 
      webhook.url.includes('play.svix.com')
    );
    
    if (!webhookToUpdate) {
      console.error('No webhook found with Svix playground URL');
      return;
    }
    
    console.log('Found webhook to update:', webhookToUpdate.id);
    
    // Update the webhook
    const updatedWebhook = await clerk.webhooks.updateWebhook(webhookToUpdate.id, {
      url: 'https://healthy-ram-536.convex.cloud/clerk-webhook',
      events: [
        'user.created',
        'user.updated',
        'organization.created',
        'organization.updated',
        'organizationMembership.created',
        'organizationMembership.updated',
        'organizationMembership.deleted',
        'organization.deleted'
      ]
    });
    
    console.log('Webhook updated successfully!');
    console.log('New URL:', updatedWebhook.url);
    console.log('Webhook Secret:', updatedWebhook.svixSecret);
    
  } catch (error) {
    console.error('Error updating webhook:', error);
  }
}

updateWebhook();