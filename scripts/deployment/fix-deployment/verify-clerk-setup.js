/**
 * Clerk Verification Script
 * 
 * This script verifies that the Clerk environment variables are correctly set
 * and validates that the webhook route is properly configured.
 */

import 'dotenv/config';
import fetch from 'node-fetch';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '../..');

async function verifyClerkConfig() {
  console.log('Verifying Clerk configuration...');
  
  // Check environment variables
  const requiredEnvVars = [
    'NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY',
    'CLERK_SECRET_KEY',
    'CLERK_WEBHOOK_SECRET'
  ];
  
  let missingVars = [];
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      missingVars.push(envVar);
    }
  }
  
  if (missingVars.length > 0) {
    console.error(`❌ Missing required environment variables: ${missingVars.join(', ')}`);
    console.log('Please ensure these variables are set in your .env and .env.production files.');
    process.exit(1);
  }
  
  console.log('✅ Required environment variables are present');
  
  // Check Clerk webhook route
  const webhookPath = path.join(rootDir, 'app', 'api', 'webhooks', 'clerk', 'route.ts');
  if (!fs.existsSync(webhookPath)) {
    console.error('❌ Clerk webhook route not found at expected path:', webhookPath);
    console.log('Please create the webhook route file.');
    process.exit(1);
  }
  
  console.log('✅ Clerk webhook route exists');
  
  // Check middleware configuration
  const middlewarePath = path.join(rootDir, 'middleware.ts');
  if (!fs.existsSync(middlewarePath)) {
    console.error('❌ Next.js middleware file not found at:', middlewarePath);
    console.log('Please create the middleware file for Clerk authentication.');
    process.exit(1);
  }
  
  // Validate middleware content
  const middlewareContent = fs.readFileSync(middlewarePath, 'utf8');
  if (!middlewareContent.includes('authMiddleware') || !middlewareContent.includes('publicRoutes')) {
    console.error('❌ Next.js middleware does not appear to be correctly configured for Clerk.');
    console.log('Please ensure the middleware is using Clerk\'s authMiddleware with appropriate publicRoutes.');
  } else {
    console.log('✅ Clerk middleware appears to be configured correctly');
  }
  
  // Validate sign-in and sign-up route files
  const signInPath = path.join(rootDir, 'app', 'sign-in', '[[...sign-in]]', 'page.tsx');
  const signUpPath = path.join(rootDir, 'app', 'sign-up', '[[...sign-up]]', 'page.tsx');
  
  if (!fs.existsSync(signInPath)) {
    console.error('❌ Sign-in route file not found at expected path:', signInPath);
  } else {
    console.log('✅ Sign-in route file exists');
  }
  
  if (!fs.existsSync(signUpPath)) {
    console.error('❌ Sign-up route file not found at expected path:', signUpPath);
  } else {
    console.log('✅ Sign-up route file exists');
  }
  
  // Validate user-context.tsx exists
  const userContextPath = path.join(rootDir, 'contexts', 'user-context.tsx');
  if (!fs.existsSync(userContextPath)) {
    console.error('❌ User context file not found at expected path:', userContextPath);
  } else {
    console.log('✅ User context file exists');
  }
  
  console.log('\nVerification complete! If all checks passed, your Clerk integration should be properly configured.');
  console.log('If you still encounter issues, please check the Clerk documentation and ensure your Clerk dashboard settings are correct.');
  
  // Additional deployment reminders
  console.log('\n========== DEPLOYMENT REMINDERS ==========');
  console.log('1. Ensure your Vercel project has all required environment variables set');
  console.log('2. Configure your Clerk application with the correct redirect URLs for your deployed domain');
  console.log('3. Update the Clerk webhook endpoint in your Clerk dashboard to point to your deployed webhook URL');
  console.log('4. Check that database migrations have been run for the production database');
}

verifyClerkConfig().catch(err => {
  console.error('Error verifying Clerk configuration:', err);
  process.exit(1);
});
