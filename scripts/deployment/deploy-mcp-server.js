/**
 * Production MCP Server Deployment Script
 * 
 * This script prepares the Neon MCP server for production deployment
 * by implementing:
 * 
 * 1. Production-grade error handling and logging
 * 2. Connection pooling for database operations
 * 3. Authentication for secure MCP server access
 * 4. Monitoring and health checks
 */

import { neon } from '@neondatabase/serverless';
import { createServer } from 'http';
import { parse } from 'url';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import crypto from 'crypto';

// Load environment variables
dotenv.config();

// Get file paths
const __dirname = path.dirname(fileURLToPath(import.meta.url));
const logDir = path.join(__dirname, '..', 'logs');

// Ensure logs directory exists
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// Configuration
const PORT = process.env.MCP_PORT || 3005;
const HOST = process.env.MCP_HOST || 'localhost';
const LOG_LEVEL = process.env.MCP_LOG_LEVEL || 'info';
const AUTH_ENABLED = process.env.MCP_AUTH_ENABLED === 'true';
const AUTH_TOKEN = process.env.MCP_AUTH_TOKEN || crypto.randomUUID();
const POOL_SIZE = parseInt(process.env.MCP_POOL_SIZE || '10', 10);
const CONNECTION_TIMEOUT = parseInt(process.env.MCP_CONNECTION_TIMEOUT || '30000', 10);
const REQUEST_TIMEOUT = parseInt(process.env.MCP_REQUEST_TIMEOUT || '15000', 10);

// Logging configuration
const LOG_LEVELS = {
  error: 0,
  warn: 1,
  info: 2,
  debug: 3
};

// Connection pool
const connectionPool = new Array(POOL_SIZE);
const connectionStatus = new Array(POOL_SIZE).fill(false); // false = available, true = in use

/**
 * Initialize connection pool
 */
async function initializeConnectionPool() {
  logger.info(`Initializing connection pool with ${POOL_SIZE} connections`);
  
  for (let i = 0; i < POOL_SIZE; i++) {
    try {
      connectionPool[i] = neon(process.env.DATABASE_URL);
      connectionStatus[i] = false;
      logger.debug(`Connection ${i} initialized successfully`);
    } catch (error) {
      logger.error(`Failed to initialize connection ${i}:`, error);
    }
  }
  
  logger.info('Connection pool initialization completed');
}

/**
 * Get connection from pool
 */
function getConnection() {
  const availableIndex = connectionStatus.findIndex(status => !status);
  
  if (availableIndex === -1) {
    logger.warn('No available connections in pool, creating temporary connection');
    return neon(process.env.DATABASE_URL);
  }
  
  connectionStatus[availableIndex] = true;
  logger.debug(`Using connection ${availableIndex} from pool`);
  return {
    connection: connectionPool[availableIndex],
    release: () => {
      connectionStatus[availableIndex] = false;
      logger.debug(`Released connection ${availableIndex} back to pool`);
    }
  };
}

/**
 * Logger implementation
 */
const logger = {
  error: (message, ...args) => {
    if (LOG_LEVELS[LOG_LEVEL] >= LOG_LEVELS.error) {
      console.error(`[ERROR] ${new Date().toISOString()} - ${message}`, ...args);
      logToFile('error', message, args);
    }
  },
  warn: (message, ...args) => {
    if (LOG_LEVELS[LOG_LEVEL] >= LOG_LEVELS.warn) {
      console.warn(`[WARNING] ${new Date().toISOString()} - ${message}`, ...args);
      logToFile('warn', message, args);
    }
  },
  info: (message, ...args) => {
    if (LOG_LEVELS[LOG_LEVEL] >= LOG_LEVELS.info) {
      console.info(`[INFO] ${new Date().toISOString()} - ${message}`, ...args);
      logToFile('info', message, args);
    }
  },
  debug: (message, ...args) => {
    if (LOG_LEVELS[LOG_LEVEL] >= LOG_LEVELS.debug) {
      console.debug(`[DEBUG] ${new Date().toISOString()} - ${message}`, ...args);
      logToFile('debug', message, args);
    }
  }
};

/**
 * Log to file
 */
function logToFile(level, message, args) {
  const date = new Date();
  const logFile = path.join(logDir, `mcp-${date.toISOString().split('T')[0]}.log`);
  
  const logEntry = `[${level.toUpperCase()}] ${date.toISOString()} - ${message} ${args.length > 0 ? JSON.stringify(args) : ''}\n`;
  
  fs.appendFile(logFile, logEntry, (err) => {
    if (err) {
      console.error(`Failed to write to log file:`, err);
    }
  });
}

/**
 * Define MCP tools
 */
const tools = [
  {
    name: 'list_projects',
    description: 'Lists all Neon projects associated with your account',
    parameters: {}
  },
  {
    name: 'list_tables',
    description: 'Lists all tables in the current Neon database',
    parameters: {}
  },
  {
    name: 'run_query',
    description: 'Runs a SQL query against the Neon database',
    parameters: {
      query: {
        type: 'string',
        description: 'The SQL query to execute'
      }
    }
  },
  {
    name: 'get_messages',
    description: 'Gets all messages for a specific session',
    parameters: {
      session_id: {
        type: 'string',
        description: 'The session ID to get messages for'
      }
    }
  },
  {
    name: 'create_message',
    description: 'Creates a new message in the database',
    parameters: {
      session_id: {
        type: 'string',
        description: 'The session ID for the message'
      },
      content: {
        type: 'string',
        description: 'The content of the message'
      },
      role: {
        type: 'string',
        description: 'The role of the message sender (user or assistant)'
      }
    }
  }
];

/**
 * Verify authentication token
 */
function verifyAuth(req) {
  if (!AUTH_ENABLED) {
    return true;
  }
  
  const authHeader = req.headers.authorization;
  
  if (!authHeader) {
    return false;
  }
  
  const parts = authHeader.split(' ');
  
  if (parts.length !== 2 || parts[0] !== 'Bearer') {
    return false;
  }
  
  return parts[1] === AUTH_TOKEN;
}

/**
 * Create HTTP server with enhanced error handling
 */
const server = createServer(async (req, res) => {
  // Start request timer
  const requestStartTime = Date.now();
  const requestId = crypto.randomUUID();
  
  try {
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    // Add request ID header
    res.setHeader('X-Request-ID', requestId);
    
    // Handle preflight requests
    if (req.method === 'OPTIONS') {
      res.writeHead(200);
      res.end();
      return;
    }
    
    // Parse request URL
    const { pathname } = parse(req.url, true);
    
    // Health check endpoint
    if (pathname === '/health') {
      try {
        const { connection, release } = getConnection();
        const result = await connection`SELECT 1 as health_check`;
        release();
        
        if (result && result[0] && result[0].health_check === 1) {
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            status: 'healthy',
            version: process.env.npm_package_version || '1.0.0',
            uptime: process.uptime(),
            database: 'connected',
            connections: {
              total: POOL_SIZE,
              active: connectionStatus.filter(status => status).length
            }
          }));
        } else {
          throw new Error('Database health check failed');
        }
      } catch (error) {
        logger.error(`Health check failed:`, error);
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          status: 'unhealthy',
          error: 'Database connection failed'
        }));
      }
      return;
    }
    
    // Handle SSE connection for MCP
    if (pathname === '/sse') {
      // Verify authentication
      if (AUTH_ENABLED && !verifyAuth(req)) {
        logger.warn(`Authentication failed for request ${requestId}`);
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          error: 'Unauthorized',
          message: 'Authentication required'
        }));
        return;
      }
      
      // Set headers for SSE
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive'
      });
      
      // Send initial message with tools
      const initialMessage = {
        type: 'tools',
        tools: tools
      };
      
      res.write(`data: ${JSON.stringify(initialMessage)}\n\n`);
      logger.info(`SSE connection established for request ${requestId}`);
      
      // Set request timeout
      const requestTimeout = setTimeout(() => {
        logger.warn(`Request timeout reached for request ${requestId}`);
        res.end();
      }, REQUEST_TIMEOUT);
      
      // Keep connection alive
      const keepAliveInterval = setInterval(() => {
        res.write(': keepalive\n\n');
      }, 30000);
      
      // Handle client disconnection
      req.on('close', () => {
        clearInterval(keepAliveInterval);
        clearTimeout(requestTimeout);
        logger.info(`Client disconnected for request ${requestId}`);
      });
      
      // Handle tool calls
      let body = '';
      
      req.on('data', chunk => {
        body += chunk.toString();
        
        // Check if we have a complete JSON object
        try {
          const message = JSON.parse(body);
          body = '';
          
          if (message.type === 'tool_call') {
            handleToolCall(message, res, requestId);
          }
        } catch (e) {
          // Not a complete JSON object yet, continue collecting data
        }
      });
    } else {
      logger.warn(`Invalid endpoint requested: ${pathname}`);
      res.writeHead(404);
      res.end('Not Found');
    }
  } catch (error) {
    logger.error(`Unhandled error in HTTP server:`, error);
    
    // Send error response if headers not sent yet
    if (!res.headersSent) {
      res.writeHead(500, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        error: 'Internal Server Error',
        message: 'An unexpected error occurred'
      }));
    } else {
      // If headers already sent (like in SSE), try to send error event
      try {
        const errorMessage = {
          type: 'error',
          error: 'Internal Server Error',
          message: 'An unexpected error occurred'
        };
        
        res.write(`data: ${JSON.stringify(errorMessage)}\n\n`);
      } catch (sendError) {
        logger.error(`Failed to send error message:`, sendError);
      }
    }
  } finally {
    // Log request completion
    const requestDuration = Date.now() - requestStartTime;
    logger.debug(`Request ${requestId} completed in ${requestDuration}ms`);
  }
});

/**
 * Handle tool calls
 */
async function handleToolCall(message, res, requestId) {
  const { id, name, parameters } = message.tool_call;
  
  logger.info(`Received tool call ${name} (${id}) for request ${requestId}`);
  logger.debug(`Tool call parameters:`, parameters);
  
  let connectionObj = null;
  
  try {
    // Get database connection
    connectionObj = getConnection();
    const sql = connectionObj.connection;
    
    let result;
    const startTime = Date.now();
    
    switch (name) {
      case 'list_projects':
        result = await listProjects();
        break;
      case 'list_tables':
        result = await listTables(sql);
        break;
      case 'run_query':
        result = await runQuery(sql, parameters.query);
        break;
      case 'get_messages':
        result = await getMessages(sql, parameters.session_id);
        break;
      case 'create_message':
        result = await createMessage(
          sql,
          parameters.session_id,
          parameters.content,
          parameters.role
        );
        break;
      default:
        throw new Error(`Unknown tool: ${name}`);
    }
    
    const duration = Date.now() - startTime;
    logger.info(`Tool call ${name} (${id}) completed in ${duration}ms`);
    
    // Send tool result
    const toolResultMessage = {
      type: 'tool_result',
      id,
      result
    };
    
    res.write(`data: ${JSON.stringify(toolResultMessage)}\n\n`);
  } catch (error) {
    logger.error(`Error handling tool call ${name} (${id}):`, error);
    
    // Retry logic for transient errors
    if (error.code === 'CONNECTION_ERROR' || error.code === 'TIMEOUT') {
      logger.info(`Retrying tool call ${name} (${id}) after error`);
      
      try {
        // Get fresh connection for retry
        if (connectionObj) {
          connectionObj.release();
        }
        
        const retryConnectionObj = getConnection();
        const sql = retryConnectionObj.connection;
        
        let result;
        const startTime = Date.now();
        
        switch (name) {
          case 'list_projects':
            result = await listProjects();
            break;
          case 'list_tables':
            result = await listTables(sql);
            break;
          case 'run_query':
            result = await runQuery(sql, parameters.query);
            break;
          case 'get_messages':
            result = await getMessages(sql, parameters.session_id);
            break;
          case 'create_message':
            result = await createMessage(
              sql,
              parameters.session_id,
              parameters.content,
              parameters.role
            );
            break;
          default:
            throw new Error(`Unknown tool: ${name}`);
        }
        
        const duration = Date.now() - startTime;
        logger.info(`Retry of tool call ${name} (${id}) completed in ${duration}ms`);
        
        // Send tool result
        const toolResultMessage = {
          type: 'tool_result',
          id,
          result
        };
        
        res.write(`data: ${JSON.stringify(toolResultMessage)}\n\n`);
        
        retryConnectionObj.release();
        return;
      } catch (retryError) {
        logger.error(`Retry of tool call ${name} (${id}) failed:`, retryError);
        
        // Send error result
        const errorResultMessage = {
          type: 'tool_error',
          id,
          error: `Operation failed after retry: ${retryError.message}`
        };
        
        res.write(`data: ${JSON.stringify(errorResultMessage)}\n\n`);
      }
    } else {
      // Send error result
      const errorResultMessage = {
        type: 'tool_error',
        id,
        error: error.message
      };
      
      res.write(`data: ${JSON.stringify(errorResultMessage)}\n\n`);
    }
  } finally {
    // Release connection back to pool
    if (connectionObj && connectionObj.release) {
      connectionObj.release();
    }
  }
}

/**
 * MCP action handlers
 */
async function listProjects() {
  logger.debug('Executing listProjects action');
  // In a real implementation, this would call the Neon API to list projects
  // For now, we'll return a mock response
  return [
    {
      id: 'project-1',
      name: 'Ask Ara',
      region: 'us-east-1',
      created_at: new Date().toISOString()
    }
  ];
}

async function listTables(sql) {
  logger.debug('Executing listTables action');
  const tables = await sql`
    SELECT table_name
    FROM information_schema.tables
    WHERE table_schema = 'public'
  `;
  
  return tables.map(table => table.table_name);
}

async function runQuery(sql, query) {
  logger.debug('Executing runQuery action');
  // Validate query to prevent SQL injection
  if (!query || typeof query !== 'string') {
    throw new Error('Invalid query parameter');
  }
  
  // Execute the query with a timeout
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error('Query execution timed out')), CONNECTION_TIMEOUT);
  });
  
  const queryPromise = sql.unsafe(query);
  
  return await Promise.race([queryPromise, timeoutPromise]);
}

async function getMessages(sql, sessionId) {
  logger.debug(`Executing getMessages action for session ${sessionId}`);
  if (!sessionId) {
    throw new Error('Session ID is required');
  }
  
  return await sql`
    SELECT * FROM messages
    WHERE session_id = ${sessionId}
    ORDER BY created_at ASC
  `;
}

async function createMessage(sql, sessionId, content, role) {
  logger.debug(`Executing createMessage action for session ${sessionId}`);
  if (!sessionId || !content || !role) {
    throw new Error('Session ID, content, and role are required');
  }
  
  const id = `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  
  await sql`
    INSERT INTO messages (id, session_id, content_transcript, role, status, type, object)
    VALUES (${id}, ${sessionId}, ${content}, ${role}, ${'completed'}, ${'message'}, ${'realtime.item'})
  `;
  
  return { id, session_id: sessionId, content, role };
}

/**
 * Perform graceful shutdown
 */
function shutdownGracefully() {
  logger.info('Shutting down server gracefully...');
  server.close(() => {
    logger.info('Server stopped accepting new connections');
    
    // Close all active connections
    logger.info('Closing database connections...');
    connectionPool.forEach((_, index) => {
      if (connectionStatus[index]) {
        logger.debug(`Marking connection ${index} as available for cleanup`);
        connectionStatus[index] = false;
      }
    });
    
    logger.info('Shutdown complete');
    process.exit(0);
  });
  
  // Force shutdown after timeout
  setTimeout(() => {
    logger.error('Forced shutdown due to timeout');
    process.exit(1);
  }, 10000);
}

// Handle process termination signals
process.on('SIGTERM', shutdownGracefully);
process.on('SIGINT', shutdownGracefully);

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught exception:', error);
  shutdownGracefully();
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason) => {
  logger.error('Unhandled promise rejection:', reason);
});

// Initialize connection pool and start server
initializeConnectionPool().then(() => {
  server.listen(PORT, HOST, () => {
    logger.info(`Neon MCP server running on ${HOST}:${PORT}`);
    
    if (AUTH_ENABLED) {
      logger.info('Authentication is enabled');
      logger.info(`Auth token: ${AUTH_TOKEN.substring(0, 8)}...`);
    } else {
      logger.warn('Authentication is disabled, server is running in insecure mode');
    }
    
    logger.info(`Log level: ${LOG_LEVEL}`);
    logger.info(`Connection pool size: ${POOL_SIZE}`);
    logger.info(`Connection timeout: ${CONNECTION_TIMEOUT}ms`);
    logger.info(`Request timeout: ${REQUEST_TIMEOUT}ms`);
    
    // Print available tools
    logger.info(`Available tools: ${tools.map(t => t.name).join(', ')}`);
  });
}).catch((error) => {
  logger.error('Failed to initialize server:', error);
  process.exit(1);
});
