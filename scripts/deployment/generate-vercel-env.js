// Script to generate Vercel environment variables from .env files
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Define paths to env files
const envPaths = [
  path.join(__dirname, '../.env'),
  path.join(__dirname, '../.env.local'),
  path.join(__dirname, '../.env.production'),
];

// Output file
const outputFile = path.join(__dirname, '../vercel.env.json');

// Collect all environment variables
let allEnvVars = {};

// Process each env file
envPaths.forEach(envPath => {
  if (fs.existsSync(envPath)) {
    console.log(`Processing ${envPath}...`);
    const envContent = fs.readFileSync(envPath, 'utf8');
    const envVars = dotenv.parse(envContent);
    
    // Merge with existing vars (newer files override older ones)
    allEnvVars = { ...allEnvVars, ...envVars };
  }
});

// Generate Vercel-compatible env format
const vercelEnv = {
  version: 2,
  env: Object.entries(allEnvVars).map(([key, value]) => ({
    key,
    value,
    target: ['production', 'preview', 'development']
  }))
};

// Write to file
fs.writeFileSync(outputFile, JSON.stringify(vercelEnv, null, 2));

console.log(`Generated Vercel environment config at ${outputFile}`);
console.log(`Total environment variables: ${vercelEnv.env.length}`);
console.log('You can now import this file in the Vercel dashboard or use it with the Vercel CLI.');
