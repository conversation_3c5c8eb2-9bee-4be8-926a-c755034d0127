/**
 * Prepare Vercel Environment Variables Script
 * 
 * This script helps verify that all required environment variables for Vercel deployment
 * are properly documented and configured. It checks the current .env files and generates
 * a template that can be used to set up environment variables in the Vercel dashboard.
 */

import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const rootDir = path.join(__dirname, '..');

// Environment variables required for deployment
const requiredVariables = {
  // Database
  DATABASE_URL: {
    description: 'Neon PostgreSQL connection string',
    required: true,
    isSecret: true
  },
  
  // Clerk Authentication
  NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: {
    description: 'Clerk publishable key (client-side)',
    required: true,
    isSecret: false
  },
  CLERK_SECRET_KEY: {
    description: 'Clerk secret key (server-side)',
    required: true,
    isSecret: true
  },
  CLERK_WEBHOOK_SECRET: {
    description: 'Secret for verifying Clerk webhooks',
    required: true,
    isSecret: true
  },
  
  // Clerk Redirect URLs
  NEXT_PUBLIC_CLERK_SIGN_IN_URL: {
    description: 'URL for the sign-in page',
    required: true,
    isSecret: false,
    defaultValue: '/sign-in'
  },
  NEXT_PUBLIC_CLERK_SIGN_UP_URL: {
    description: 'URL for the sign-up page',
    required: true,
    isSecret: false,
    defaultValue: '/sign-up'
  },
  NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL: {
    description: 'URL to redirect to after sign-in',
    required: true,
    isSecret: false,
    defaultValue: '/'
  },
  NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL: {
    description: 'URL to redirect to after sign-up',
    required: true,
    isSecret: false,
    defaultValue: '/'
  }
};

// Optional variables that might be needed for specific features
const optionalVariables = {
  MCP_PORT: {
    description: 'Port for MCP server',
    required: false,
    isSecret: false,
    defaultValue: '3005'
  },
  NODE_ENV: {
    description: 'Node environment (development/production)',
    required: false,
    isSecret: false,
    defaultValue: 'production'
  }
};

// Read environment files
function readEnvFiles() {
  const envFiles = [
    { path: path.join(rootDir, '.env'), name: '.env' },
    { path: path.join(rootDir, '.env.local'), name: '.env.local' },
    { path: path.join(rootDir, '.env.production'), name: '.env.production' }
  ];
  
  const envVariables = {};
  
  for (const file of envFiles) {
    if (fs.existsSync(file.path)) {
      console.log(`Reading ${file.name}...`);
      const content = fs.readFileSync(file.path, 'utf8');
      const parsed = dotenv.parse(content);
      
      for (const [key, value] of Object.entries(parsed)) {
        if (!envVariables[key]) {
          envVariables[key] = {
            value,
            sources: [file.name]
          };
        } else {
          envVariables[key].sources.push(file.name);
        }
      }
    }
  }
  
  return envVariables;
}

// Generate environment variable report
function generateReport(envVariables) {
  console.log('\n========== ENVIRONMENT VARIABLE REPORT ==========\n');
  
  // Check required variables
  console.log('REQUIRED VARIABLES:');
  console.log('-------------------');
  
  let missingRequired = 0;
  
  for (const [key, config] of Object.entries(requiredVariables)) {
    const variable = envVariables[key];
    
    if (variable) {
      console.log(`✅ ${key}`);
      console.log(`   Description: ${config.description}`);
      console.log(`   Found in: ${variable.sources.join(', ')}`);
      console.log(`   Secret: ${config.isSecret ? 'Yes' : 'No'}`);
      if (!config.isSecret) {
        console.log(`   Value: ${variable.value}`);
      }
    } else {
      console.log(`❌ ${key} (MISSING)`);
      console.log(`   Description: ${config.description}`);
      console.log(`   Secret: ${config.isSecret ? 'Yes' : 'No'}`);
      if (config.defaultValue) {
        console.log(`   Default value: ${config.defaultValue}`);
      }
      missingRequired++;
    }
    console.log();
  }
  
  // Check optional variables
  console.log('\nOPTIONAL VARIABLES:');
  console.log('-------------------');
  
  for (const [key, config] of Object.entries(optionalVariables)) {
    const variable = envVariables[key];
    
    if (variable) {
      console.log(`✅ ${key}`);
      console.log(`   Description: ${config.description}`);
      console.log(`   Found in: ${variable.sources.join(', ')}`);
      console.log(`   Secret: ${config.isSecret ? 'Yes' : 'No'}`);
      if (!config.isSecret) {
        console.log(`   Value: ${variable.value}`);
      }
    } else {
      console.log(`⚠️ ${key} (NOT SET)`);
      console.log(`   Description: ${config.description}`);
      console.log(`   Secret: ${config.isSecret ? 'Yes' : 'No'}`);
      if (config.defaultValue) {
        console.log(`   Default value: ${config.defaultValue}`);
      }
    }
    console.log();
  }
  
  // Check for additional variables
  const allDefinedVariables = { ...requiredVariables, ...optionalVariables };
  const additionalVariables = Object.keys(envVariables).filter(
    key => !allDefinedVariables[key]
  );
  
  if (additionalVariables.length > 0) {
    console.log('\nADDITIONAL VARIABLES:');
    console.log('--------------------');
    
    for (const key of additionalVariables) {
      const variable = envVariables[key];
      console.log(`ℹ️ ${key}`);
      console.log(`   Found in: ${variable.sources.join(', ')}`);
      console.log();
    }
  }
  
  // Summary
  console.log('\nSUMMARY:');
  console.log('--------');
  console.log(`Required variables: ${Object.keys(requiredVariables).length - missingRequired}/${Object.keys(requiredVariables).length} set`);
  console.log(`Optional variables: ${Object.keys(optionalVariables).filter(key => envVariables[key]).length}/${Object.keys(optionalVariables).length} set`);
  console.log(`Additional variables: ${additionalVariables.length}`);
  
  if (missingRequired > 0) {
    console.log('\n⚠️ WARNING: Some required variables are missing! Check the report above.');
  } else {
    console.log('\n✅ All required variables are set.');
  }
}

// Generate a template for Vercel environment variables
function generateVercelTemplate() {
  const template = {};
  
  // Add required variables
  for (const [key, config] of Object.entries(requiredVariables)) {
    template[key] = {
      description: config.description,
      value: config.defaultValue || 'YOUR_VALUE_HERE'
    };
  }
  
  // Add optional variables
  for (const [key, config] of Object.entries(optionalVariables)) {
    template[key] = {
      description: config.description,
      value: config.defaultValue || 'YOUR_VALUE_HERE'
    };
  }
  
  const outputPath = path.join(rootDir, 'vercel-env-template.json');
  fs.writeFileSync(outputPath, JSON.stringify(template, null, 2));
  
  console.log(`\nVercel environment template generated at: ${outputPath}`);
  console.log('You can use this template to set up environment variables in the Vercel dashboard.');
}

// Main execution
const envVariables = readEnvFiles();
generateReport(envVariables);
generateVercelTemplate();

console.log('\nNEXT STEPS:');
console.log('1. Resolve any missing required variables');
console.log('2. Use the generated vercel-env-template.json to set up your Vercel environment');
console.log('3. Make sure to set all sensitive values properly in the Vercel dashboard');
console.log('4. Deploy your application to Vercel\n');
