// scripts/seed-contract-specs.js
import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';

// Get the directory of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = join(__dirname, '..');

// Load environment variables from .env.local first, then .env
let envLoaded = false;
const envLocalPath = join(rootDir, '.env.local');
const envPath = join(rootDir, '.env');

if (fs.existsSync(envLocalPath)) {
  dotenv.config({ path: envLocalPath });
  envLoaded = true;
  console.log('Loaded environment from .env.local');
}

if (!envLoaded && fs.existsSync(envPath)) {
  dotenv.config({ path: envPath });
  console.log('Loaded environment from .env');
}

async function seedContractSpecifications() {
  console.log('Seeding contract specifications data...');
  
  const databaseUrl = process.env.DATABASE_URL;
  
  if (!databaseUrl) {
    console.error('❌ DATABASE_URL not found in environment variables');
    return false;
  }
  
  try {
    console.log('Connecting to database...');
    const sql = neon(databaseUrl);
    
    // Test the connection
    const result = await sql('SELECT 1 as test');
    if (result && result[0] && result[0].test === 1) {
      console.log('✅ Database connection successful');
    } else {
      console.error('❌ Database connection failed: Unexpected response');
      return false;
    }
    
    // Seed cleaning areas
    console.log('Creating cleaning areas...');
    const cleaningAreas = [
      {
        id: uuidv4(),
        name: 'Toilets',
        description: 'Bathroom and toilet facilities',
        category: 'Sanitation',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Kitchen',
        description: 'Kitchen and break room areas',
        category: 'Food Preparation',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Office',
        description: 'Office and administrative areas',
        category: 'Workspace',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Retail',
        description: 'Customer-facing retail areas',
        category: 'Public',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Production',
        description: 'Mail sorting and processing areas',
        category: 'Industrial',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'External',
        description: 'External areas including entrances and loading docks',
        category: 'Outdoor',
        created_at: new Date(),
      },
    ];
    
    for (const area of cleaningAreas) {
      await sql`
        INSERT INTO cleaning_areas (id, name, description, category, created_at)
        VALUES (${area.id}, ${area.name}, ${area.description}, ${area.category}, ${area.created_at})
        ON CONFLICT (id) DO NOTHING
      `;
    }
    console.log('✅ Cleaning areas created');
    
    // Seed frequency types
    console.log('Creating frequency types...');
    const frequencyTypes = [
      {
        id: uuidv4(),
        name: '3x Daily',
        description: 'Three times per day',
        times_per_year: 1095,
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Daily',
        description: 'Once per day',
        times_per_year: 365,
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: '3 of 5 days',
        description: 'Three days per week',
        times_per_year: 156,
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Weekly',
        description: 'Once per week',
        times_per_year: 52,
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Monthly',
        description: 'Once per month',
        times_per_year: 12,
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Quarterly',
        description: 'Once every three months',
        times_per_year: 4,
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Biannual',
        description: 'Twice per year',
        times_per_year: 2,
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Annual',
        description: 'Once per year',
        times_per_year: 1,
        created_at: new Date(),
      },
    ];
    
    for (const frequency of frequencyTypes) {
      await sql`
        INSERT INTO frequency_types (id, name, description, times_per_year, created_at)
        VALUES (${frequency.id}, ${frequency.name}, ${frequency.description}, ${frequency.times_per_year}, ${frequency.created_at})
        ON CONFLICT (name) DO NOTHING
      `;
    }
    console.log('✅ Frequency types created');
    
    // Seed tier specifications
    console.log('Creating tier specifications...');
    const tierSpecifications = [
      {
        id: uuidv4(),
        name: 'Tier 1',
        description: 'Large industrial facilities operating 24/7 with multiple daily cleaning requirements',
        tier_level: 1,
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Tier 2',
        description: 'Smaller industrial centers with daily cleaning schedules',
        tier_level: 2,
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Tier 3',
        description: 'Smaller delivery centers with reduced frequency (3 out of 5 operational days)',
        tier_level: 3,
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Tier 4',
        description: 'Smaller delivery centers with reduced frequency (3 out of 5 operational days)',
        tier_level: 4,
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Tier 5',
        description: 'Smaller delivery centers with reduced frequency (3 out of 5 operational days)',
        tier_level: 5,
        created_at: new Date(),
      },
    ];
    
    for (const tier of tierSpecifications) {
      await sql`
        INSERT INTO tier_specifications (id, name, description, tier_level, created_at)
        VALUES (${tier.id}, ${tier.name}, ${tier.description}, ${tier.tier_level}, ${tier.created_at})
        ON CONFLICT (id) DO NOTHING
      `;
    }
    console.log('✅ Tier specifications created');
    
    // Get the cleaning areas IDs
    const toiletsArea = await sql`SELECT id FROM cleaning_areas WHERE name = 'Toilets' LIMIT 1`;
    const kitchenArea = await sql`SELECT id FROM cleaning_areas WHERE name = 'Kitchen' LIMIT 1`;
    const officeArea = await sql`SELECT id FROM cleaning_areas WHERE name = 'Office' LIMIT 1`;
    const retailArea = await sql`SELECT id FROM cleaning_areas WHERE name = 'Retail' LIMIT 1`;
    const productionArea = await sql`SELECT id FROM cleaning_areas WHERE name = 'Production' LIMIT 1`;
    const externalArea = await sql`SELECT id FROM cleaning_areas WHERE name = 'External' LIMIT 1`;
    
    // Seed retail cleaning scope
    console.log('Creating retail cleaning scope...');
    const retailCleaningScope = [
      {
        id: uuidv4(),
        area: 'Front of House',
        element: 'Hard Flooring',
        requirement: 'Clean and maintain',
        frequency: 'Daily',
        category: 'Flooring',
        notes: 'Risk controls required',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        area: 'Front of House',
        element: 'Carpets',
        requirement: 'Clean and maintain',
        frequency: 'Daily',
        category: 'Flooring',
        notes: 'Avoid trailing cables',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        area: 'Front of House',
        element: 'Windows',
        requirement: 'Clean and maintain',
        frequency: 'Daily',
        category: 'Windows',
        notes: 'Includes entrance glass',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        area: 'Front of House',
        element: 'Displays',
        requirement: 'Clean and maintain',
        frequency: 'Daily',
        category: 'Retail',
        notes: 'Glass displays included',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        area: 'Back of House',
        element: 'Toilets',
        requirement: 'Clean and sanitize',
        frequency: 'Daily',
        category: 'Sanitation',
        notes: 'Include mirrors weekly',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        area: 'Back of House',
        element: 'Kitchen',
        requirement: 'Clean and maintain',
        frequency: 'Daily',
        category: 'Kitchen',
        notes: 'Weekly deep clean',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        area: 'Back of House',
        element: 'Office',
        requirement: 'Clean and maintain',
        frequency: 'Daily',
        category: 'Office',
        notes: 'Staff coordination required',
        created_at: new Date(),
      },
      {
        id: uuidv4(),
        area: 'Specialist',
        element: 'Deep Clean',
        requirement: 'Strip and seal floors',
        frequency: '6 monthly',
        category: 'Specialist',
        notes: 'Both front and back',
        created_at: new Date(),
      },
    ];
    
    for (const scope of retailCleaningScope) {
      await sql`
        INSERT INTO retail_cleaning_scope (id, area, element, requirement, frequency, category, notes, created_at)
        VALUES (${scope.id}, ${scope.area}, ${scope.element}, ${scope.requirement}, ${scope.frequency}, ${scope.category}, ${scope.notes}, ${scope.created_at})
        ON CONFLICT (id) DO NOTHING
      `;
    }
    console.log('✅ Retail cleaning scope created');
    
    console.log('Contract specifications data seeded successfully.');
    return true;
  } catch (error) {
    console.error('❌ Error seeding contract specifications data:', error);
    return false;
  }
}

// Run the seed
seedContractSpecifications()
  .then(success => {
    if (success) {
      console.log('\n✅ Contract specifications seeding completed successfully');
    } else {
      console.error('\n❌ Contract specifications seeding failed');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
