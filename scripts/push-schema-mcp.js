#!/usr/bin/env node

// This script pushes the schema to the database using the MCP client
const { createClient } = require('@neondatabase/mcp');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Load MCP configuration from mcp.json
const mcpConfigPath = path.join(process.cwd(), 'mcp.json');
let mcpConfig;

try {
  const configFile = fs.readFileSync(mcpConfigPath, 'utf8');
  mcpConfig = JSON.parse(configFile);
  console.log('Loaded MCP configuration from mcp.json');
} catch (error) {
  console.error('Error loading MCP configuration:', error);
  process.exit(1);
}

async function pushSchema() {
  console.log('Pushing schema to database using MCP...');
  
  try {
    // Create a Neon MCP client
    const client = createClient({
      connectionString: mcpConfig.connectionString,
      mcpServerUrl: mcpConfig.mcpServers['github.com/neondatabase/mcp-server-neon'].url,
    });
    
    // Test the connection
    const result = await client.query('SELECT 1 as test');
    
    if (result && result.rows && result.rows[0] && result.rows[0].test === 1) {
      console.log('✅ MCP connection successful');
      
      // Close the client connection
      await client.end();
      
      // Run drizzle-kit push
      console.log('\nRunning drizzle-kit push...');
      
      try {
        execSync('npx drizzle-kit push:pg', { stdio: 'inherit' });
        console.log('✅ Schema pushed successfully');
      } catch (error) {
        console.error('❌ Error pushing schema:', error.message);
        process.exit(1);
      }
    } else {
      console.error('❌ MCP connection failed: Unexpected response');
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ MCP connection error:', error);
    process.exit(1);
  }
}

// Run the push
pushSchema();
