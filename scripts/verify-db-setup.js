#!/usr/bin/env node

import { loadEnvConfig } from '@next/env';
import { neon } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-http';
import fs from 'fs';
import path from 'path';

// Load environment variables
loadEnvConfig(process.cwd());

// Import schema
import * as schema from '../app/db/schema.js';

// Check if DATABASE_URL is defined
if (!process.env.DATABASE_URL) {
  console.error('❌ DATABASE_URL environment variable is not defined');
  process.exit(1);
}

// Create a Neon client
const sql = neon(process.env.DATABASE_URL);

// Create a Drizzle client
const db = drizzle(sql, { schema });

// Verify database setup
const verifyDbSetup = async () => {
  console.log('🔍 Verifying database setup...');
  
  try {
    // Check if the database is accessible
    console.log('\n1. Checking database connection...');
    const result = await sql`SELECT 1 as test`;
    
    if (result && result.rows && result.rows[0] && result.rows[0].test === 1) {
      console.log('✅ Database connection successful');
    } else {
      console.error('❌ Database connection failed');
      process.exit(1);
    }
    
    // Check if migrations exist
    console.log('\n2. Checking migrations...');
    const migrationsDir = path.join(process.cwd(), 'drizzle');
    
    if (fs.existsSync(migrationsDir)) {
      const migrationFiles = fs.readdirSync(migrationsDir).filter(file => file.endsWith('.sql'));
      console.log(`✅ Found ${migrationFiles.length} migration files`);
    } else {
      console.warn('⚠️ Migrations directory not found');
    }
    
    // Check if tables exist
    console.log('\n3. Checking database tables...');
    const expectedTables = [
      'users',
      'properties',
      'inspection_templates',
      'inspection_reports',
      'contacts',
      'chat_sessions',
      'messages'
    ];
    
    const existingTables = [];
    const missingTables = [];
    
    for (const table of expectedTables) {
      try {
        const count = await sql`SELECT COUNT(*) as count FROM ${sql(table)}`;
        existingTables.push({ table, count: count.rows[0].count });
      } catch (error) {
        missingTables.push(table);
      }
    }
    
    if (existingTables.length > 0) {
      console.log('✅ Found the following tables:');
      existingTables.forEach(({ table, count }) => {
        console.log(`  - ${table} (${count} records)`);
      });
    }
    
    if (missingTables.length > 0) {
      console.warn('⚠️ The following tables are missing:');
      missingTables.forEach(table => {
        console.log(`  - ${table}`);
      });
    }
    
    // Check if data exists
    console.log('\n4. Checking if data exists...');
    const userCount = await db.select({ count: sql`count(*)` }).from(schema.users);
    
    if (userCount[0].count > 0) {
      console.log(`✅ Found ${userCount[0].count} users in the database`);
    } else {
      console.warn('⚠️ No users found in the database');
    }
    
    console.log('\n✅ Database verification completed!');
    
    // Provide next steps
    console.log('\nNext steps:');
    
    if (missingTables.length > 0) {
      console.log('1. Run `npm run db:push` to create missing tables');
      console.log('2. Run `npm run db:seed` to populate the database with initial data');
    } else if (userCount[0].count === 0) {
      console.log('1. Run `npm run db:seed` to populate the database with initial data');
    } else {
      console.log('Your database is set up correctly! You can now:');
      console.log('- Run `npm run db:studio` to view and edit data');
      console.log('- Run `npm run example:drizzle` to see an example of using Drizzle with Neon');
    }
    
  } catch (error) {
    console.error('\n❌ Error verifying database setup:', error);
    process.exit(1);
  }
};

// Run the verification function
verifyDbSetup();
