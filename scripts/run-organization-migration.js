// Run the organization migration
import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

async function runMigration() {
  try {
    console.log('Connecting to database...');
    const sql = neon(process.env.DATABASE_URL);
    
    console.log('Running migration...');
    
    // Create organizations table
    await sql`
      CREATE TABLE IF NOT EXISTS "organizations" (
        "id" TEXT PRIMARY KEY,
        "name" TEXT NOT NULL,
        "slug" TEXT NOT NULL UNIQUE,
        "image_url" TEXT,
        "max_memberships" INTEGER,
        "admin_delete_enabled" BOOLEAN DEFAULT true,
        "public_metadata" JSONB DEFAULT '{}',
        "private_metadata" JSONB DEFAULT '{}',
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
        "updated_at" TIMESTAMP WITH TIME ZONE
      )
    `;
    console.log('Organizations table created');
    
    // Create index on slug
    await sql`CREATE INDEX IF NOT EXISTS "idx_organization_slug" ON "organizations" ("slug")`;
    console.log('Organization slug index created');
    
    // Create organization members table
    await sql`
      CREATE TABLE IF NOT EXISTS "organization_members" (
        "id" TEXT PRIMARY KEY,
        "organization_id" TEXT NOT NULL REFERENCES "organizations" ("id") ON DELETE CASCADE,
        "user_id" TEXT NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE,
        "role" TEXT NOT NULL,
        "joined_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
      )
    `;
    console.log('Organization members table created');
    
    // Create index on organization_id and user_id
    await sql`CREATE INDEX IF NOT EXISTS "idx_org_user" ON "organization_members" ("organization_id", "user_id")`;
    console.log('Organization members index created');
    
    // Add initial ARA Property Services organization
    await sql`
      INSERT INTO "organizations" ("id", "name", "slug", "image_url", "created_at", "updated_at")
      VALUES (
        'org_2vgJJLPin926eyZLua1fijgiXFJ',
        'ARA Property Services',
        'araps',
        'https://img.clerk.com/eyJ0eXBlIjoiZGVmYXVsdCIsImlpZCI6Imluc18ydXhxaVMxbnpvQlpWSFhEVVBuT1hvM2dTckwiLCJyaWQiOiJvcmdfMnZnSkpMUGluOTI2ZXlaTHVhMWZpamdpWEZKIiwiaW5pdGlhbHMiOiJBIn0',
        NOW(),
        NOW()
      )
      ON CONFLICT (id) DO UPDATE
      SET 
        name = EXCLUDED.name,
        slug = EXCLUDED.slug,
        image_url = EXCLUDED.image_url,
        updated_at = NOW()
    `;
    console.log('ARA Property Services organization added');
    
    try {
      // Add organization relation to properties
      await sql`ALTER TABLE "properties" ADD COLUMN IF NOT EXISTS "organization_id" TEXT REFERENCES "organizations" ("id")`;
      console.log('Organization ID column added to properties table');
      
      // Add index on organization_id in properties
      await sql`CREATE INDEX IF NOT EXISTS "idx_property_organization" ON "properties" ("organization_id")`;
      console.log('Properties organization index created');
    } catch (error) {
      console.warn('Could not add organization column to properties:', error.message);
    }
    
    try {
      // Add organization relation to contracts
      await sql`ALTER TABLE "contract_specifications" ADD COLUMN IF NOT EXISTS "organization_id" TEXT REFERENCES "organizations" ("id")`;
      console.log('Organization ID column added to contract_specifications table');
      
      // Add index on organization_id in contracts
      await sql`CREATE INDEX IF NOT EXISTS "idx_contract_organization" ON "contract_specifications" ("organization_id")`;
      console.log('Contract specifications organization index created');
    } catch (error) {
      console.warn('Could not add organization column to contract_specifications:', error.message);
    }
    
    console.log('Migration completed successfully!');
    
    // Test if tables were created
    const orgCheck = await sql`SELECT * FROM organizations LIMIT 1`;
    console.log('Organizations table check:', orgCheck.length > 0 ? 'Success' : 'Empty');
    
    const membersCheck = await sql`SELECT * FROM organization_members LIMIT 1`;
    console.log('Organization members table check:', membersCheck.length > 0 ? 'Success (has records)' : 'Success (empty)');
    
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

runMigration();
