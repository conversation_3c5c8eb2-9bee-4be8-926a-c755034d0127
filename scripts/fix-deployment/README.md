# ARA App Deployment Fix Guide

This guide addresses and resolves common issues with Clerk authentication in the ARA Property Services App when deployed to Vercel.

## Common Issues & Fixes

1. **Clerk Authentication Not Working**
   - Missing/incorrect environment variables
   - Improper middleware configuration
   - Webhook configuration issues
   - Redirect URL mismatches

2. **Database Synchronization Issues**
   - User records not being properly created in database
   - Problems with syncing Clerk users with app database

## Quick Fix Steps

### 1. Environment Variables

Ensure these environment variables are set in your Vercel project:

```
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_Y2xlcmsuYXJhLmFsaWFzbGFicy5haSQ
CLERK_SECRET_KEY=**************************************************
CLERK_WEBHOOK_SECRET=whsec_B/7RJQJIQIk/7udsSxNuHxFWUYrpPC8k
CLERK_ENCRYPTION_KEY=6a3ff267ca834fb5f9fe38ad2419c8bb87cb2c0c7e2f9149f81537e2864c0251

NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/
```

### 2. Clerk Dashboard Configuration

1. Go to your [Clerk Dashboard](https://dashboard.clerk.dev/)
2. Select your application
3. Navigate to **Sessions & Authentication**
4. Under **Redirect URLs**:
   - Add your Vercel deployment URL (e.g., `https://your-app.vercel.app`)
   - Add paths for sign-in and sign-up redirects
5. Navigate to **Webhooks**
   - Ensure webhook endpoint is: `https://your-app.vercel.app/api/webhooks/clerk`
   - Verify webhook signing secret matches your `CLERK_WEBHOOK_SECRET` env var

### 3. Run the Verification Script

```bash
node scripts/fix-deployment/verify-clerk-setup.js
```

This script will check your local configuration and provide guidance on any issues.

### 4. Trigger Database Migration

Ensure your database schema is properly migrated:

```bash
# Locally
npm run db:migrate

# Or through Vercel CLI
vercel env pull
npx drizzle-kit push
```

## Detailed Fixes Applied

1. **Fixed Environment Variables**
   - Corrected malformed `CLERK_ENCRYPTION_KEY` in `.env.production`
   - Added required redirect URL variables

2. **Updated Middleware Configuration**
   - Fixed public routes configuration to properly allow authentication paths
   - Added proper redirect handling for unauthenticated users

3. **Enhanced User Context**
   - Improved user context to better handle sync between Clerk and app database
   - Added fallback profile creation and error handling

4. **Created Profile Sync API**
   - New endpoint to ensure user profiles are properly created in the database

5. **Updated Mobile Interface**
   - Fixed authentication flow in mobile interface
   - Properly integrated with Clerk authentication

## Troubleshooting

If you still encounter issues:

1. **Check Vercel Logs** - Look for authentication or webhook errors
2. **Database Connection** - Verify the app can connect to the Neon database
3. **Clerk Events** - Check Clerk dashboard for webhook delivery events
4. **Clear Browser Cache** - Try accessing the app in a private/incognito window

## Need More Help?

Contact support at [<EMAIL>](mailto:<EMAIL>) for assistance.
