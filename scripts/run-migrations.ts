import { ConvexClient } from "convex/browser";
import { migrateInitialSetup } from "./migrations/01_initial_setup";
import { migrateInspectionTemplates } from "./migrations/02_inspection_templates";
import { migrateARAUsers } from "./migrations/03_ara_users";

async function runMigrations() {
  console.log("Starting migrations...");
  
  // Create a Convex client
  const client = new ConvexClient(process.env.NEXT_PUBLIC_CONVEX_URL || "");
  
  try {
    // Run migrations in order
    console.log("\n1. Running initial setup migration...");
    await migrateInitialSetup(client);
    
    console.log("\n2. Running inspection templates migration...");
    await migrateInspectionTemplates(client);
    
    console.log("\n3. Running ARA users migration...");
    await migrateARAUsers(client);
    
    console.log("\nAll migrations completed successfully!");
  } catch (error) {
    console.error("Migration failed:", error);
  }
}

runMigrations();
