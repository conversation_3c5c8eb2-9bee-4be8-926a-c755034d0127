import { execSync } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Get the directory of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = join(__dirname, '..');

// Function to run a command and log output
function runCommand(command, description) {
  console.log(`\n🚀 ${description}...`);
  try {
    const output = execSync(command, { 
      cwd: rootDir,
      stdio: 'inherit'
    });
    console.log(`✅ ${description} completed successfully`);
    return true;
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    return false;
  }
}

// Main migration function
async function completeMigration() {
  console.log('Starting complete Drizzle migration process...');
  
  // Step 1: Check database connection
  console.log('\n--- STEP 1: Check Database Connection ---');
  const checkDbSuccess = runCommand(
    'node scripts/check-db.js',
    'Checking database connection'
  );
  
  if (!checkDbSuccess) {
    console.error('❌ Database connection check failed. Please check your DATABASE_URL and try again.');
    process.exit(1);
  }
  
  // Step 2: Run migration
  console.log('\n--- STEP 2: Run Drizzle Migration ---');
  const migrationSuccess = runCommand(
    'node scripts/run-migration.js',
    'Running Drizzle migration'
  );
  
  if (!migrationSuccess) {
    console.error('❌ Migration failed. Please check the error messages and try again.');
    process.exit(1);
  }
  
  // Step 3: Verify schema
  console.log('\n--- STEP 3: Verify Database Schema ---');
  const verifySuccess = runCommand(
    'node scripts/verify-schema.js',
    'Verifying database schema'
  );
  
  if (!verifySuccess) {
    console.error('❌ Schema verification failed. Please check the error messages and try again.');
    process.exit(1);
  }
  
  // Step 4: Test Drizzle ORM
  console.log('\n--- STEP 4: Test Drizzle ORM Functionality ---');
  const testSuccess = runCommand(
    'node scripts/test-drizzle.js',
    'Testing Drizzle ORM functionality'
  );
  
  if (!testSuccess) {
    console.error('❌ Drizzle ORM test failed. Please check the error messages and try again.');
    process.exit(1);
  }
  
  console.log('\n✅ Complete migration process finished successfully!');
  console.log('The database has been successfully migrated to Drizzle ORM with Neon.');
}

// Run the migration
completeMigration().catch(error => {
  console.error('Unexpected error:', error);
  process.exit(1);
});
