# Scripts Directory

This directory contains utility scripts organized by purpose.

## Directory Structure

### `database/`
Database-related scripts for setup, migration, and testing:
- `check-db.js` - Check database connection
- `verify-schema.js` - Verify database schema
- `test-drizzle.js` - Test Drizzle ORM functionality
- `verify-db-setup.js` - Verify complete database setup
- `run-migrations-and-seed.js` - Run migrations and seed data
- `setup-db.js` - Database setup script
- `setup-schema.js` - Schema setup script
- `seed-*.js` - Data seeding scripts

### `testing/`
Testing and validation scripts:
- `test-clerk-integration.js` - Test Clerk authentication
- `test-neon-connection.js` - Test Neon database connection
- `test-auth.js` - Test authentication functionality
- `test-auth-store.ts` - Test authentication store
- `test-credentials.js` - Test API credentials
- `test-db-connection.ts` - Test database connection
- `test-mcp-connection.js` - Test MCP connection

### `deployment/`
Deployment and environment configuration:
- `fix-deployment/` - Deployment troubleshooting scripts
- `generate-vercel-env.js` - Generate Vercel environment config
- `prepare-vercel-env.js` - Prepare Vercel environment

### `validation/`
Environment and deployment validation:
- `env-validator.js` - Validate environment variables
- `deployment-validator.js` - Validate deployment configuration

### `legacy/`
Legacy scripts from previous implementations:
- Migration scripts for old database systems
- Deprecated utilities
- Archive of old deployment scripts

## Usage

### Database Operations
```bash
# Check database connection
pnpm db:check

# Verify database schema
pnpm db:verify

# Test Drizzle ORM
pnpm db:test

# Seed database with contracts
pnpm db:seed-contracts

# Complete migration and seeding
pnpm db:migrate-and-seed
```

### Testing
```bash
# Test Clerk integration
pnpm test:clerk

# Test Neon database connection
pnpm test:neon
```

### Validation
```bash
# Validate environment variables
pnpm validate:env

# Validate deployment configuration
pnpm validate:deployment

# Run all validations
pnpm validate:all
```

### Utility Scripts
```bash
# Reset pnpm and clean project
pnpm reset

# Update Clerk webhook configuration
node scripts/update-clerk-webhook.ts

# Test webhook functionality
./scripts/test-webhook.sh
```

## Development Guidelines

### Adding New Scripts
1. Place scripts in the appropriate subdirectory
2. Use descriptive names
3. Add proper error handling
4. Include usage documentation
5. Update package.json if creating npm scripts

### Script Naming Conventions
- Use kebab-case for file names
- Prefix with action (test-, check-, setup-, etc.)
- Use .js for Node.js scripts
- Use .ts for TypeScript scripts
- Use .sh for shell scripts

### Error Handling
All scripts should:
- Exit with appropriate codes (0 for success, 1+ for errors)
- Provide clear error messages
- Log important operations
- Handle missing environment variables gracefully

## Legacy Scripts

Scripts in the `legacy/` directory are from previous implementations and may not work with the current system. They are kept for reference but should not be used in production.

## Environment Requirements

Most scripts require:
- Node.js 18.x or later
- Proper environment variables in `.env.local`
- Database connection (for database scripts)
- API keys (for testing scripts)

Check the individual script files for specific requirements.
