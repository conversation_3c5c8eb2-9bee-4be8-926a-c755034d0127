// Test script for database connection
const { neon } = require('@neondatabase/serverless');
const { drizzle } = require('drizzle-orm/neon-http');
require('dotenv').config();

async function testDatabaseConnection() {
  console.log('Testing database connection...');
  
  try {
    if (!process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL environment variable is not defined');
    }
    
    console.log('Database URL is defined.');
    
    // Initialize the Neon serverless driver
    const sql = neon(process.env.DATABASE_URL);
    
    // Test the connection with a simple query
    const result = await sql('SELECT 1 as test');
    
    if (result && result.rows && result.rows[0] && result.rows[0].test === 1) {
      console.log('✅ Database connection successful');
    } else {
      console.error('❌ Database connection failed: Unexpected response');
    }
  } catch (error) {
    console.error('❌ Database connection error:', error);
  }
}

testDatabaseConnection();
