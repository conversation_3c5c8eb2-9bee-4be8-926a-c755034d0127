import { loadEnvConfig } from '@next/env';
import { neon } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-http';
import * as schema from '../app/db/schema';
import crypto from 'crypto';

// Load environment variables
loadEnvConfig(process.cwd());

// Check if the database URL is set
if (!process.env.DATABASE_URL) {
  throw new Error('DATABASE_URL must be a Neon postgres connection string');
}

// Initialize the Neon serverless driver
const sql = neon(process.env.DATABASE_URL);

// Initialize Drizzle ORM with the Neon driver and the schema
const db = drizzle(sql, { schema });

// Mock Clerk user data
const mockClerkUser = {
  id: `clerk_${crypto.randomUUID()}`,
  firstName: 'Clerk',
  lastName: 'Test',
  emailAddresses: [
    {
      emailAddress: '<EMAIL>',
      id: crypto.randomUUID(),
      verification: { status: 'verified' },
    },
  ],
  phoneNumbers: [
    {
      phoneNumber: '0400 123 456',
      id: crypto.randomUUID(),
      verification: { status: 'verified' },
    },
  ],
  imageUrl: 'https://example.com/avatar.jpg',
  createdAt: new Date().toISOString(),
};

async function testClerkIntegration() {
  console.log('Testing Clerk integration with database...');
  
  try {
    // 1. Simulate Clerk webhook user creation event
    console.log('\n1. Simulating Clerk user creation webhook...');
    
    // Check if user already exists
    const existingUser = await db.select()
      .from(schema.users)
      .where(schema.users.id.equals(mockClerkUser.id))
      .limit(1);
    
    if (existingUser.length === 0) {
      // Create user in our database based on Clerk user data
      const newUser = {
        id: mockClerkUser.id,
        name: `${mockClerkUser.firstName} ${mockClerkUser.lastName}`,
        role: 'User', // Default role
        department: 'General', // Default department
        email: mockClerkUser.emailAddresses[0].emailAddress,
        password: 'clerk-managed', // Clerk manages authentication, so this is just a placeholder
        phone: mockClerkUser.phoneNumbers[0]?.phoneNumber || null,
        avatar: mockClerkUser.imageUrl,
        preferences: { theme: 'light', notifications: true },
        created_at: new Date(),
      };
      
      await db.insert(schema.users).values(newUser);
      console.log('✅ Created user from Clerk data:', newUser.name);
    } else {
      console.log('⏭️ User already exists:', existingUser[0].name);
    }
    
    // 2. Simulate Clerk user login
    console.log('\n2. Simulating Clerk user login...');
    
    // Update last login timestamp
    await db.update(schema.users)
      .set({ last_login: new Date() })
      .where(schema.users.id.equals(mockClerkUser.id));
    
    console.log('✅ Updated last login timestamp for user');
    
    // 3. Retrieve user profile
    console.log('\n3. Retrieving user profile...');
    
    const userProfile = await db.select()
      .from(schema.users)
      .where(schema.users.id.equals(mockClerkUser.id))
      .limit(1);
    
    if (userProfile.length > 0) {
      console.log('✅ Retrieved user profile:');
      console.log({
        id: userProfile[0].id,
        name: userProfile[0].name,
        email: userProfile[0].email,
        role: userProfile[0].role,
        department: userProfile[0].department,
        lastLogin: userProfile[0].last_login,
      });
    } else {
      console.log('❌ Failed to retrieve user profile');
    }
    
    console.log('\nClerk integration testing completed!');
  } catch (error) {
    console.error('Error testing Clerk integration:', error);
  }
}

// Run the test function
testClerkIntegration();
