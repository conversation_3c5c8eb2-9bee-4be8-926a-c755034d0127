import { addUser, findUserByEmail, verifyCredentials, getAllUsers } from '../lib/auth-store';

async function testAuthStore() {
  console.log('Testing auth-store.ts functionality...');
  
  try {
    // 1. Test adding a new user
    console.log('\n1. Testing addUser function...');
    const newUser = await addUser({
      email: '<EMAIL>',
      password: 'ARAtestPS!',
      name: 'Test User',
      role: 'Tester',
      department: 'IT',
    });
    
    if (newUser) {
      console.log('✅ Successfully added new user:', newUser.name);
    } else {
      console.log('❌ Failed to add new user or user already exists');
    }
    
    // 2. Test finding a user by email
    console.log('\n2. Testing findUserByEmail function...');
    const foundUser = await findUserByEmail('<EMAIL>');
    
    if (foundUser) {
      console.log('✅ Successfully found user by email:', foundUser.name);
    } else {
      console.log('❌ Failed to find user by email');
    }
    
    // 3. Test verifying credentials
    console.log('\n3. Testing verifyCredentials function...');
    const verifiedUser = await verifyCredentials('<EMAIL>', 'ARAtestPS!');
    
    if (verifiedUser) {
      console.log('✅ Successfully verified user credentials:', verifiedUser.name);
    } else {
      console.log('❌ Failed to verify user credentials');
    }
    
    // 4. Test verifying with incorrect credentials
    console.log('\n4. Testing verifyCredentials with incorrect password...');
    const invalidUser = await verifyCredentials('<EMAIL>', 'WrongPassword');
    
    if (!invalidUser) {
      console.log('✅ Correctly rejected invalid credentials');
    } else {
      console.log('❌ Incorrectly accepted invalid credentials');
    }
    
    // 5. Test getting all users
    console.log('\n5. Testing getAllUsers function...');
    const allUsers = await getAllUsers();
    
    if (allUsers && allUsers.length > 0) {
      console.log(`✅ Successfully retrieved ${allUsers.length} users`);
      console.log('Sample users:');
      allUsers.slice(0, 3).forEach(user => {
        console.log(`- ${user.name} (${user.email})`);
      });
    } else {
      console.log('❌ Failed to retrieve users or no users exist');
    }
    
    console.log('\nAuth store testing completed!');
  } catch (error) {
    console.error('Error testing auth store:', error);
  }
}

// Run the test function
testAuthStore();
