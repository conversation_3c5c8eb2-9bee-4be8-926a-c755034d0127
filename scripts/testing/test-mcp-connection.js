#!/usr/bin/env node

// This script tests the MCP connection
const { createClient } = require('@neondatabase/mcp');
const fs = require('fs');
const path = require('path');

// Load MCP configuration from mcp.json
const mcpConfigPath = path.join(process.cwd(), 'mcp.json');
let mcpConfig;

try {
  const configFile = fs.readFileSync(mcpConfigPath, 'utf8');
  mcpConfig = JSON.parse(configFile);
  console.log('Loaded MCP configuration from mcp.json');
} catch (error) {
  console.error('Error loading MCP configuration:', error);
  process.exit(1);
}

async function testConnection() {
  console.log('Testing MCP connection...');
  
  try {
    // Create a Neon MCP client
    const client = createClient({
      connectionString: mcpConfig.connectionString,
      mcpServerUrl: mcpConfig.mcpServers['github.com/neondatabase/mcp-server-neon'].url,
    });
    
    // Test the connection
    const result = await client.query('SELECT 1 as test');
    
    if (result && result.rows && result.rows[0] && result.rows[0].test === 1) {
      console.log('✅ MCP connection successful');
      
      // List tables in the database
      const tables = await client.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
      `);
      
      console.log('\nTables in the database:');
      if (tables.rows.length === 0) {
        console.log('No tables found');
      } else {
        tables.rows.forEach(row => {
          console.log(`- ${row.table_name}`);
        });
      }
      
      // Close the connection
      await client.end();
    } else {
      console.error('❌ MCP connection failed: Unexpected response');
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ MCP connection error:', error);
    process.exit(1);
  }
}

// Run the test
testConnection();
