#!/usr/bin/env node

import { loadEnvConfig } from '@next/env';
import { neon } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-http';

// Load environment variables
loadEnvConfig(process.cwd());

// Import schema
import * as schema from '../app/db/schema.js';

// Check if DATABASE_URL is defined
if (!process.env.DATABASE_URL) {
  console.error('❌ DATABASE_URL environment variable is not defined');
  process.exit(1);
}

// Create a Neon client
const sql = neon(process.env.DATABASE_URL);

// Create a Drizzle client
const db = drizzle(sql, { schema });

// Test the connection
const testConnection = async () => {
  console.log('🔍 Testing Neon database connection...');
  
  try {
    // Test raw SQL query
    console.log('\n1. Testing raw SQL query...');
    const result = await sql`SELECT 1 as test`;
    
    if (result && result.rows && result.rows[0] && result.rows[0].test === 1) {
      console.log('✅ Raw SQL query successful');
    } else {
      console.error('❌ Raw SQL query failed');
    }
    
    // Test Drizzle query
    console.log('\n2. Testing Drizzle query...');
    const users = await db.select().from(schema.users).limit(5);
    
    console.log(`✅ Drizzle query successful - Found ${users.length} users`);
    
    if (users.length > 0) {
      console.log('\nSample user data:');
      console.table(users.map(user => ({
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        department: user.department
      })));
    }
    
    // Test table existence
    console.log('\n3. Testing table existence...');
    const tables = [
      'users',
      'properties',
      'inspection_templates',
      'inspection_reports',
      'contacts'
    ];
    
    for (const table of tables) {
      try {
        const count = await sql`SELECT COUNT(*) as count FROM ${sql(table)}`;
        console.log(`✅ Table '${table}' exists with ${count.rows[0].count} records`);
      } catch (error) {
        console.error(`❌ Table '${table}' does not exist or is not accessible`);
      }
    }
    
    console.log('\n✅ Database connection test completed successfully!');
  } catch (error) {
    console.error('\n❌ Error testing database connection:', error);
    process.exit(1);
  }
};

// Run the test function
testConnection();
