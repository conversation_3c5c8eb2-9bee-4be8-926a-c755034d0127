/**
 * Post-Deployment Script for ARA Property Services App
 * 
 * This script should be run after each production deployment to ensure that
 * all necessary database migrations and configurations are applied.
 */

import dotenv from 'dotenv';
import { neon } from '@neondatabase/serverless';
import chalk from 'chalk';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Define the steps to run after deployment
const postDeploymentSteps = [
  {
    name: 'Verify Database Connection',
    handler: async () => {
      const sql = neon(process.env.DATABASE_URL);
      const result = await sql`SELECT current_timestamp`;
      return {
        success: !!result,
        message: 'Successfully connected to Neon PostgreSQL database'
      };
    }
  },
  {
    name: 'Check Organizations Table',
    handler: async () => {
      const sql = neon(process.env.DATABASE_URL);
      try {
        const orgsResult = await sql`
          SELECT table_name 
          FROM information_schema.tables 
          WHERE table_schema = 'public' AND table_name = 'organizations'
        `;
        
        if (orgsResult.length > 0) {
          return {
            success: true,
            message: 'Organizations table exists',
            skipNextStep: true // Skip the organization migration if table exists
          };
        } else {
          return {
            success: false,
            message: 'Organizations table does not exist',
            skipNextStep: false
          };
        }
      } catch (error) {
        return {
          success: false,
          message: `Error checking for organizations table: ${error.message}`,
          skipNextStep: false
        };
      }
    }
  },
  {
    name: 'Run Organization Migration',
    handler: async () => {
      const sql = neon(process.env.DATABASE_URL);
      
      try {
        // Create organizations table
        await sql`
          CREATE TABLE IF NOT EXISTS "organizations" (
            "id" TEXT PRIMARY KEY,
            "name" TEXT NOT NULL,
            "slug" TEXT NOT NULL UNIQUE,
            "image_url" TEXT,
            "max_memberships" INTEGER,
            "admin_delete_enabled" BOOLEAN DEFAULT true,
            "public_metadata" JSONB DEFAULT '{}',
            "private_metadata" JSONB DEFAULT '{}',
            "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            "updated_at" TIMESTAMP WITH TIME ZONE
          )
        `;
        
        // Create index on slug
        await sql`CREATE INDEX IF NOT EXISTS "idx_organization_slug" ON "organizations" ("slug")`;
        
        // Create organization members table
        await sql`
          CREATE TABLE IF NOT EXISTS "organization_members" (
            "id" TEXT PRIMARY KEY,
            "organization_id" TEXT NOT NULL REFERENCES "organizations" ("id") ON DELETE CASCADE,
            "user_id" TEXT NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE,
            "role" TEXT NOT NULL,
            "joined_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
          )
        `;
        
        // Create index on organization_id and user_id
        await sql`CREATE INDEX IF NOT EXISTS "idx_org_user" ON "organization_members" ("organization_id", "user_id")`;
        
        // Add initial ARA Property Services organization
        await sql`
          INSERT INTO "organizations" ("id", "name", "slug", "image_url", "created_at", "updated_at")
          VALUES (
            'org_2vgJJLPin926eyZLua1fijgiXFJ',
            'ARA Property Services',
            'araps',
            'https://img.clerk.com/eyJ0eXBlIjoiZGVmYXVsdCIsImlpZCI6Imluc18ydXhxaVMxbnpvQlpWSFhEVVBuT1hvM2dTckwiLCJyaWQiOiJvcmdfMnZnSkpMUGluOTI2ZXlaTHVhMWZpamdpWEZKIiwiaW5pdGlhbHMiOiJBIn0',
            NOW(),
            NOW()
          )
          ON CONFLICT (id) DO UPDATE
          SET 
            name = EXCLUDED.name,
            slug = EXCLUDED.slug,
            image_url = EXCLUDED.image_url,
            updated_at = NOW()
        `;
        
        try {
          // Add organization relation to properties
          await sql`ALTER TABLE "properties" ADD COLUMN IF NOT EXISTS "organization_id" TEXT REFERENCES "organizations" ("id")`;
          await sql`CREATE INDEX IF NOT EXISTS "idx_property_organization" ON "properties" ("organization_id")`;
        } catch (error) {
          console.warn('Could not add organization column to properties:', error.message);
        }
        
        try {
          // Add organization relation to contracts
          await sql`ALTER TABLE "contract_specifications" ADD COLUMN IF NOT EXISTS "organization_id" TEXT REFERENCES "organizations" ("id")`;
          await sql`CREATE INDEX IF NOT EXISTS "idx_contract_organization" ON "contract_specifications" ("organization_id")`;
        } catch (error) {
          console.warn('Could not add organization column to contract_specifications:', error.message);
        }
        
        return {
          success: true,
          message: 'Organization migration completed successfully'
        };
      } catch (error) {
        return {
          success: false,
          message: `Organization migration failed: ${error.message}`
        };
      }
    }
  },
  {
    name: 'Verify Organization Migration',
    handler: async () => {
      const sql = neon(process.env.DATABASE_URL);
      
      try {
        // Verify organizations table
        const orgsResult = await sql`SELECT COUNT(*) FROM organizations`;
        
        // Verify organization members table
        const membersResult = await sql`SELECT COUNT(*) FROM organization_members`;
        
        // Verify ARA Property Services organization
        const araOrgResult = await sql`SELECT * FROM organizations WHERE slug = 'araps'`;
        
        return {
          success: true,
          message: `Verification successful: ${orgsResult[0].count} organizations, ${membersResult[0].count} members, ARA Property Services organization ${araOrgResult.length > 0 ? 'exists' : 'missing'}`
        };
      } catch (error) {
        return {
          success: false,
          message: `Verification failed: ${error.message}`
        };
      }
    }
  }
];

/**
 * Run all post-deployment steps
 */
async function runPostDeployment() {
  console.log(chalk.cyan.bold('=== ARA Property Services Post-Deployment Script ==='));
  console.log(chalk.cyan(`Timestamp: ${new Date().toISOString()}`));
  
  let skipNextStep = false;
  
  for (const step of postDeploymentSteps) {
    if (skipNextStep) {
      console.log(chalk.yellow(`Skipping: ${step.name}`));
      skipNextStep = false;
      continue;
    }
    
    console.log(chalk.blue(`\n--- ${step.name} ---`));
    
    try {
      const result = await step.handler();
      
      if (result.success) {
        console.log(chalk.green(`✓ ${result.message}`));
        
        if (result.skipNextStep) {
          skipNextStep = true;
        }
      } else {
        console.log(chalk.red(`✗ ${result.message}`));
      }
    } catch (error) {
      console.error(chalk.red(`Error in ${step.name}:`), error);
    }
  }
  
  console.log(chalk.cyan.bold('\n=== Post-Deployment Complete ==='));
}

// Run the post-deployment steps
runPostDeployment().catch(error => {
  console.error('Fatal error during post-deployment:', error);
  process.exit(1);
});
