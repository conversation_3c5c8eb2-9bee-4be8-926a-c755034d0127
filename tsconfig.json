{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": false, "allowImportingTsExtensions": true, "target": "ES6", "skipLibCheck": true, "strict": true, "noImplicitAny": true, "exactOptionalPropertyTypes": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", "__archive__", "reference", "app/assistant", "scripts", "clerk-docs-temp"]}