# AskARA Deployment Guide

## Convex Deployments

### Development
- **URL**: https://doting-chicken-81.convex.cloud
- **Deployment**: dev:doting-chicken-81
- **Status**: Currently active

### Production
- **URL**: https://healthy-ram-536.convex.cloud
- **Deployment**: prod:healthy-ram-536
- **Status**: Ready for deployment

## Deployment Steps

### 1. Environment Variables

#### For Vercel/Production:
```env
# Convex Production
NEXT_PUBLIC_CONVEX_URL=https://healthy-ram-536.convex.cloud
CONVEX_DEPLOYMENT=prod:healthy-ram-536

# Clerk (from your Clerk Dashboard)
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_YOUR_KEY
CLERK_SECRET_KEY=sk_live_YOUR_KEY
CLERK_WEBHOOK_SECRET=whsec_YOUR_SECRET
CLERK_ISSUER_URL=https://YOUR_DOMAIN.clerk.accounts.dev
```

#### For Local Development:
```env
# Convex Development
NEXT_PUBLIC_CONVEX_URL=https://doting-chicken-81.convex.cloud
CONVEX_DEPLOYMENT=dev:doting-chicken-81

# Clerk Test Keys (if available)
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_YOUR_KEY
CLERK_SECRET_KEY=sk_test_YOUR_KEY
```

### 2. Deploy to Production

#### Deploy Convex Functions:
```bash
npx convex deploy --prod
```

#### Deploy to Vercel:
```bash
vercel --prod
```

### 3. Configure Webhooks

#### Production Webhook:
1. Go to Clerk Dashboard → Webhooks
2. Create new endpoint:
   - URL: `https://healthy-ram-536.convex.cloud/clerk-webhook`
   - Events: All user and organization events

#### Development Webhook:
- URL: `https://doting-chicken-81.convex.cloud/clerk-webhook`

### 4. Post-Deployment Checklist

- [ ] Verify Convex functions are deployed
- [ ] Check Clerk webhook is receiving events
- [ ] Test user sign-up/sign-in flow
- [ ] Verify organization creation works
- [ ] Test multi-tenant data isolation
- [ ] Check all API endpoints are working

## Monitoring

### Convex Dashboard
- Dev: https://dashboard.convex.dev/d/doting-chicken-81
- Prod: https://dashboard.convex.dev/d/healthy-ram-536

### Logs
- Check Convex logs for function execution
- Monitor Clerk webhook events
- Review Vercel function logs

## Rollback

If issues occur:
1. Revert Vercel deployment: `vercel rollback`
2. Revert Convex: Use dashboard to restore previous version
3. Update environment variables if needed