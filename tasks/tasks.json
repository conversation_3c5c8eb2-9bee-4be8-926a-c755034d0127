[{"id": 1, "title": "Package‑manager hygiene", "description": "Remove bun.lock and package-lock.json, keep pnpm only. Run \"pnpm install --frozen-lockfile\" and commit the updated pnpm‑lock.yaml.", "status": "pending", "priority": "high", "dependencies": [], "details": "Delete conflicting lock files to avoid dual installers and ensure deterministic builds.", "testStrategy": "Run pnpm install, then pnpm dev to confirm no missing deps.", "subtasks": [{"id": 1, "title": "Delete bun.lock"}, {"id": 2, "title": "Delete package-lock.json"}, {"id": 3, "title": "Run pnpm install --frozen-lockfile"}, {"id": 4, "title": "Commit pnpm-lock.yaml"}]}, {"id": 2, "title": "TypeScript‑only source", "description": "Deduplicate parallel .jsx/.tsx files; enforce allowJs=false in tsconfig.", "status": "pending", "priority": "high", "dependencies": [1], "details": "Remove redundant JSX copies when TSX exists; update tsconfig.", "testStrategy": "pnpm lint && pnpm tsc passes.", "subtasks": [{"id": 1, "title": "Survey .jsx files with .tsx twins"}, {"id": 2, "title": "Delete duplicates"}, {"id": 3, "title": "Set allowJs=false in tsconfig"}]}, {"id": 3, "title": "Environment hygiene", "description": "Create .env.example and purge real secrets from repo.", "status": "pending", "priority": "medium", "dependencies": [1], "details": "Provide placeholder env values; add assertEnv helper.", "testStrategy": "App boots locally with example env after copying/setting real keys.", "subtasks": [{"id": 1, "title": "Create .env.example"}, {"id": 2, "title": "Implement lib/assertEnv.ts"}, {"id": 3, "title": "Refactor config access"}]}, {"id": 4, "title": "CI pipeline", "description": "Add GitHub Actions workflow to lint, type‑check, build, & verify migrations.", "status": "pending", "priority": "medium", "dependencies": [1, 2, 3], "details": "Write .github/workflows/ci.yml using pnpm action.", "testStrategy": "CI passes on PR.", "subtasks": []}, {"id": 5, "title": "DB automation", "description": "Run migrations & seeds automatically on deploy.", "status": "pending", "priority": "low", "dependencies": [4], "details": "Add postinstall script guarded by CI env.", "testStrategy": "Deployment succeeds with up‑to‑date DB.", "subtasks": []}, {"id": 6, "title": "Smoke tests", "description": "Introduce Vitest tests for health endpoint & DB connectivity.", "status": "pending", "priority": "low", "dependencies": [4], "details": "Write minimal tests in tests/smoke/**.", "testStrategy": "Vitest passes locally and in CI.", "subtasks": []}]