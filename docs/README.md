# ARA Property Services - Documentation

This directory contains all project documentation organized by topic.

## Quick Start

1. **Setup**: See [SETUP.md](./SETUP.md) for installation and configuration
2. **Development**: See [DEVELOPMENT.md](./DEVELOPMENT.md) for development workflow
3. **Deployment**: See [DEPLOYMENT.md](./DEPLOYMENT.md) for deployment instructions
4. **Database**: See [DATABASE.md](./DATABASE.md) for database setup and migrations

## Documentation Structure

### Core Documentation
- `SETUP.md` - Complete setup guide for new developers
- `DEVELOPMENT.md` - Development workflow and best practices
- `DEPLOYMENT.md` - Deployment instructions and environment configuration
- `DATABASE.md` - Database schema, migrations, and management
- `ARCHITECTURE.md` - System architecture and design decisions

### Feature Documentation
- `AUTHENTICATION.md` - Clerk authentication setup and multi-tenant configuration
- `VOICE_ASSISTANT.md` - Voice assistant implementation and configuration
- `API.md` - API endpoints and usage

### Migration Documentation
- `CONVEX_MIGRATION.md` - Migration from Neon/Drizzle to Convex
- `MIGRATION_STATUS.md` - Current migration status and next steps

### Legacy Documentation
- `legacy/` - Archived documentation from previous implementations

## Project Overview

The ARA Property Services App is a comprehensive property management solution with:

- 🏢 **Multi-Tenant Architecture**: Support for multiple organizations
- 🔐 **Secure Authentication**: Powered by Clerk
- 🗣️ **Voice Assistant**: AI-powered voice interface
- 💬 **Chat Interface**: Text-based AI assistant
- 📱 **Mobile-First Design**: Optimized for field use
- 📋 **Inspection Management**: Property inspection workflows
- 📅 **Task Scheduling**: Cleaning and maintenance task management

## Tech Stack

- **Frontend**: Next.js 15 + React 19 + Tailwind CSS
- **Backend**: Convex (real-time database and functions)
- **Authentication**: Clerk with organization support
- **AI**: OpenAI (chat), ElevenLabs (voice)
- **Deployment**: Vercel

## Getting Help

- Check the relevant documentation file for your topic
- Review the [TROUBLESHOOTING.md](./TROUBLESHOOTING.md) guide
- Contact the development team for additional support

## Contributing

When adding new documentation:
1. Place it in the appropriate category
2. Update this README with a link
3. Follow the existing documentation format
4. Keep documentation up-to-date with code changes
