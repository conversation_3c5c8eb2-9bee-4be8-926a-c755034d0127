# Setting Up Clerk Authentication with Neon Postgres

This guide explains how to set up Clerk authentication with Neon Postgres in the Ask ARA application.

## Prerequisites

- A Clerk account and application
- A Neon Postgres database
- Node.js 18.x or later
- pnpm 8.x or later

## Environment Variables

Create a `.env.local` file in the root of your project with the following variables:

```bash
# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your-publishable-key
CLERK_SECRET_KEY=sk_test_your-secret-key
CLERK_WEBHOOK_SECRET=whsec_your-webhook-secret

# Neon Database
DATABASE_URL=postgres://user:password@hostname:port/database
```

## Installation

1. Install dependencies:

```bash
pnpm install
```

2. Push the database schema:

```bash
pnpm db:push
```

## Database Setup

The application uses Drizzle ORM to interact with Neon Postgres. The database connection is configured in `app/db/index.ts`:

```typescript
import { loadEnvConfig } from '@next/env';
import { neon } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-http';
import * as schema from './schema';

// Load environment variables
loadEnvConfig(process.cwd());

// Check if the database URL is set
if (!process.env.DATABASE_URL) {
  throw new Error('DATABASE_URL must be a Neon postgres connection string');
}

// Initialize the Neon serverless driver
const sql = neon(process.env.DATABASE_URL);

// Initialize Drizzle ORM with the Neon driver and the schema
export const db = drizzle(sql, { schema });

// Export schema for use in other files
export * from './schema';
```

## Clerk Integration

### Middleware

The application uses Clerk's middleware to protect routes and sync user data with the database. The middleware is configured in `middleware.ts`:

```typescript
import { authMiddleware } from '@clerk/nextjs';
import { NextResponse } from 'next/server';
import { clerkSyncService } from '@/app/services/clerk-sync-service';

// This function is called after the user is authenticated
async function syncUserData(userId: string | null) {
  if (userId) {
    try {
      await clerkSyncService.syncCurrentUser();
    } catch (error) {
      console.error('Error syncing user in middleware:', error);
    }
  }
}

export default authMiddleware({
  // Routes that can be accessed without authentication
  publicRoutes: [
    '/',
    '/sign-in(.*)',
    '/sign-up(.*)',
    '/api/public/(.*)',
    '/api/webhooks/(.*)', // Webhooks must be public
    '/install',
    '/offline',
    '/test-askara',
  ],
  
  // Routes that should sync user data
  async afterAuth(auth, req) {
    // If the user is authenticated and accessing a protected route
    if (auth.userId && !auth.isPublicRoute) {
      const url = new URL(req.url);
      const path = url.pathname;
      
      // Check if the route should sync user data
      const shouldSyncUser = [
        '/profile',
        '/dashboard',
        '/settings',
        '/api/user',
      ].some(route => path.startsWith(route));
      
      if (shouldSyncUser) {
        await syncUserData(auth.userId);
      }
    }
    
    return NextResponse.next();
  },
});
```

### User Synchronization

The application syncs Clerk user data with the database using the `ClerkSyncService` in `app/services/clerk-sync-service.ts`:

```typescript
import { auth, clerkClient } from '@clerk/nextjs/server';
import { db } from '@/app/db';
import { users } from '@/app/db/schema';
import { eq } from 'drizzle-orm';

export class ClerkSyncService {
  async syncCurrentUser() {
    try {
      const { userId } = await auth();
      
      if (!userId) {
        return null;
      }
      
      // Get the user from Clerk
      const clerkUser = await clerkClient.users.getUser(userId);
      
      // Check if the user exists in our database
      const existingUser = await db.query.users.findFirst({
        where: eq(users.id, userId),
      });
      
      if (existingUser) {
        // Update the user
        return await db.update(users)
          .set({
            name: `${clerkUser.firstName || ''} ${clerkUser.lastName || ''}`.trim(),
            email: clerkUser.emailAddresses[0]?.emailAddress || '',
            role: existingUser.role || 'User',
            department: existingUser.department || 'General',
            avatar: clerkUser.imageUrl || null,
            last_login: new Date(),
          })
          .where(eq(users.id, userId))
          .returning();
      } else {
        // Create the user
        return await db.insert(users)
          .values({
            id: userId,
            name: `${clerkUser.firstName || ''} ${clerkUser.lastName || ''}`.trim(),
            email: clerkUser.emailAddresses[0]?.emailAddress || '',
            role: 'User',
            department: 'General',
            password: 'clerk-auth', // Not used with Clerk but required by schema
            avatar: clerkUser.imageUrl || null,
            created_at: new Date(),
            last_login: new Date(),
          })
          .returning();
      }
    } catch (error) {
      console.error('Error syncing user:', error);
      return null;
    }
  }
}

// Export a singleton instance
export const clerkSyncService = new ClerkSyncService();
```

### Webhooks

The application uses Clerk webhooks to sync user data with the database. The webhook handler is configured in `app/api/webhooks/clerk/route.ts`:

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { WebhookEvent } from '@clerk/nextjs/server';
import { Webhook } from 'svix';
import { headers } from 'next/headers';
import { db } from '@/app/db';
import { users } from '@/app/db/schema';
import { eq } from 'drizzle-orm';

export async function POST(req: NextRequest) {
  // Get the headers
  const headerPayload = headers();
  const svix_id = headerPayload.get('svix-id');
  const svix_timestamp = headerPayload.get('svix-timestamp');
  const svix_signature = headerPayload.get('svix-signature');

  // If there are no headers, error out
  if (!svix_id || !svix_timestamp || !svix_signature) {
    return new NextResponse('Error: Missing svix headers', { status: 400 });
  }

  // Get the body
  const payload = await req.json();
  const body = JSON.stringify(payload);

  // Create a new Svix instance with your webhook secret
  const wh = new Webhook(process.env.CLERK_WEBHOOK_SECRET || '');

  let evt: WebhookEvent;

  // Verify the webhook
  try {
    evt = wh.verify(body, {
      'svix-id': svix_id,
      'svix-timestamp': svix_timestamp,
      'svix-signature': svix_signature,
    }) as WebhookEvent;
  } catch (err) {
    console.error('Error verifying webhook:', err);
    return new NextResponse('Error verifying webhook', { status: 400 });
  }

  // Handle the webhook
  const eventType = evt.type;
  
  if (eventType === 'user.created') {
    // A new user was created in Clerk
    const { id, email_addresses, first_name, last_name, image_url } = evt.data;
    
    try {
      // Create the user in our database
      await db.insert(users).values({
        id,
        name: `${first_name || ''} ${last_name || ''}`.trim(),
        email: email_addresses[0]?.email_address || '',
        role: 'User',
        department: 'General',
        password: 'clerk-auth', // Not used with Clerk but required by schema
        avatar: image_url || null,
        created_at: new Date(),
        last_login: new Date(),
      });
      
      console.log(`User created: ${id}`);
    } catch (error) {
      console.error('Error creating user:', error);
    }
  } else if (eventType === 'user.updated') {
    // A user was updated in Clerk
    const { id, email_addresses, first_name, last_name, image_url } = evt.data;
    
    try {
      // Update the user in our database
      await db.update(users)
        .set({
          name: `${first_name || ''} ${last_name || ''}`.trim(),
          email: email_addresses[0]?.email_address || '',
          avatar: image_url || null,
          last_login: new Date(),
        })
        .where(eq(users.id, id));
      
      console.log(`User updated: ${id}`);
    } catch (error) {
      console.error('Error updating user:', error);
    }
  } else if (eventType === 'user.deleted') {
    // A user was deleted in Clerk
    const { id } = evt.data;
    
    try {
      // Delete the user from our database
      await db.delete(users).where(eq(users.id, id));
      
      console.log(`User deleted: ${id}`);
    } catch (error) {
      console.error('Error deleting user:', error);
    }
  }

  return NextResponse.json({ success: true });
}
```

## Testing

To test the Clerk integration with Neon Postgres, run:

```bash
pnpm test:clerk
```

This will check the database connection, verify the users table exists, and list a few users from the database.

## Development

To start the development server with Turbopack:

```bash
pnpm dev
```

This will start the development server at http://localhost:3000.
