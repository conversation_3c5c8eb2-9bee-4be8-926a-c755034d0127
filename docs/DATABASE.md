# Database Setup for ARA Property Services

This document provides instructions for setting up and working with the Neon Postgres database for the ARA Property Services application.

## Table of Contents

- [Overview](#overview)
- [Prerequisites](#prerequisites)
- [Environment Setup](#environment-setup)
- [Database Schema](#database-schema)
- [Database Scripts](#database-scripts)
- [Testing the Database](#testing-the-database)
- [Example Usage](#example-usage)
- [Troubleshooting](#troubleshooting)
- [Additional Resources](#additional-resources)

## Overview

The ARA Property Services application uses:

- [Neon](https://neon.tech/) - A serverless Postgres database
- [Drizzle ORM](https://orm.drizzle.team/) - A TypeScript ORM for SQL databases
- [@neondatabase/serverless](https://www.npmjs.com/package/@neondatabase/serverless) - The Neon serverless driver

## Prerequisites

- Node.js 18 or higher
- A Neon Postgres database
- Access to the Neon console to get your database connection string

## Environment Setup

1. Create a `.env` file in the root of your project (if it doesn't exist already)
2. Add your Neon database connection string:

```
DATABASE_URL="****************************************************************"
```

Replace `username`, `password`, `hostname`, and `database` with your actual Neon database credentials.

## Database Schema

The database schema is defined in `app/db/schema.ts`. This file contains all the table definitions and relationships for:

- Users and authentication
- Properties and areas
- Inspection templates and reports
- Chat sessions and messages
- Contacts and companies
- And more

## Database Scripts

The application includes several scripts for working with the database:

- `npm run db:push` - Push schema changes to the database
- `npm run db:studio` - Open Drizzle Studio to view and edit data
- `npm run db:generate` - Generate SQL migrations
- `npm run db:seed` - Seed the database with initial data
- `npm run db:setup` - Set up the database (generate migrations, push schema, verify, seed)
- `npm run db:verify-setup` - Verify that the database is set up correctly
- `npm run test:neon` - Test the Neon database connection
- `npm run example:drizzle` - Run the Drizzle + Neon example

### Setting Up the Database

To set up the database from scratch:

```bash
npm run db:setup
```

This will:
1. Generate migrations based on your schema
2. Push the schema to the database
3. Verify the schema
4. Seed the database with initial data

### Verifying the Database Setup

To verify that the database is set up correctly:

```bash
npm run db:verify-setup
```

This will check:
1. If the database is accessible
2. If migrations exist
3. If tables exist
4. If data exists

### Testing the Database Connection

To test the database connection:

```bash
npm run test:neon
```

This will:
1. Test a raw SQL query
2. Test a Drizzle query
3. Test table existence

## Testing the Database

### Command Line Testing

You can test the database connection from the command line:

```bash
npm run test:neon
```

### Web Interface Testing

You can also test the database connection through the web interface:

1. Start the development server:
   ```bash
   npm run dev
   ```

2. Navigate to [http://localhost:3000/db-test](http://localhost:3000/db-test)

3. Click the "Test Database Connection" button

## Example Usage

### Basic CRUD Operations

```typescript
import { db } from '@/app/db';
import { users } from '@/app/db/schema';
import { eq } from 'drizzle-orm';

// Query all users
const allUsers = await db.select().from(users);

// Query a specific user
const user = await db.select().from(users).where(eq(users.email, '<EMAIL>')).limit(1);

// Insert a new user
const newUser = await db.insert(users).values({
  id: 'user-id',
  name: 'Example User',
  role: 'User',
  department: 'General',
  email: '<EMAIL>',
  password: 'password123',
  created_at: new Date(),
}).returning();

// Update a user
const updatedUser = await db.update(users)
  .set({ name: 'Updated Name' })
  .where(eq(users.id, 'user-id'))
  .returning();

// Delete a user
const deletedUser = await db.delete(users)
  .where(eq(users.id, 'user-id'))
  .returning();
```

### Relationships

```typescript
import { db } from '@/app/db';
import { properties, users } from '@/app/db/schema';
import { eq } from 'drizzle-orm';

// Query properties with their managers
const propertiesWithManagers = await db.select({
  property: properties,
  manager: users,
}).from(properties)
  .leftJoin(users, eq(properties.manager_id, users.id));
```

### Transactions

For HTTP connections, transactions are simulated by batching queries:

```typescript
// Execute multiple operations in a batch
await db.batch([
  db.insert(users).values(user),
  db.insert(properties).values(property),
]);
```

## Troubleshooting

### Common Issues

1. **Database connection error**
   - Check that your `.env` file contains the correct `DATABASE_URL`
   - Verify that your Neon database is running and accessible
   - Check that your IP address is allowed in the Neon database settings

2. **Schema push error**
   - Check that your schema is valid
   - Check that you have the necessary permissions to modify the database

3. **Migration error**
   - Check that your migrations directory exists
   - Check that your migrations are valid SQL

### Debugging

To debug database issues:

1. Run `npm run test:neon` to test the database connection
2. Check the Neon console for any issues with your database
3. Check the application logs for any database-related errors

## Additional Resources

- [Drizzle ORM Documentation](https://orm.drizzle.team/docs/overview)
- [Neon Documentation](https://neon.tech/docs/introduction)
- [Drizzle + Neon Guide](./docs/drizzle-neon-guide.md)
- [Drizzle + Neon Example](./examples/drizzle-neon-example.ts)
