# Contract Specifications Schema

## Overview

This document describes the database schema for contract specifications in the ARA Property Services application. The schema is designed to support the comprehensive cleaning specifications required by Australia Post and Star Track facilities, as well as other clients.

## Core Tables

### cleaning_areas

Stores information about different areas that require cleaning.

```sql
CREATE TABLE cleaning_areas (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  category TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE
);
```

### cleaning_tasks

Stores information about specific cleaning tasks performed within areas.

```sql
CREATE TABLE cleaning_tasks (
  id TEXT PRIMARY KEY,
  area_id TEXT NOT NULL REFERENCES cleaning_areas(id),
  name TEXT NOT NULL,
  description TEXT,
  task_type TEXT NOT NULL,
  category TEXT NOT NULL,
  standard_duration_minutes INTEGER,
  equipment_required JSONB,
  materials_required JSONB,
  safety_requirements TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE
);
```

### frequency_types

Stores information about different cleaning frequencies.

```sql
CREATE TABLE frequency_types (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  times_per_year INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE
);
```

### tier_specifications

Stores information about different facility tiers with specific cleaning requirements.

```sql
CREATE TABLE tier_specifications (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  tier_level INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE
);
```

### contract_specifications

Stores information about service contracts with clients.

```sql
CREATE TABLE contract_specifications (
  id TEXT PRIMARY KEY,
  client_id TEXT NOT NULL,
  client_name TEXT NOT NULL,
  contract_name TEXT NOT NULL,
  contract_number TEXT,
  start_date TIMESTAMP WITH TIME ZONE NOT NULL,
  end_date TIMESTAMP WITH TIME ZONE,
  contract_value DECIMAL(10, 2),
  contract_manager_id TEXT REFERENCES users(id),
  status TEXT DEFAULT 'active' NOT NULL,
  special_requirements TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE
);
```

### facility_cleaning_specifications

Maps cleaning tasks to facilities with frequencies based on tier.

```sql
CREATE TABLE facility_cleaning_specifications (
  id TEXT PRIMARY KEY,
  contract_id TEXT NOT NULL REFERENCES contract_specifications(id),
  property_id TEXT NOT NULL REFERENCES properties(id),
  task_id TEXT NOT NULL REFERENCES cleaning_tasks(id),
  tier_id TEXT NOT NULL REFERENCES tier_specifications(id),
  frequency_id TEXT NOT NULL REFERENCES frequency_types(id),
  custom_frequency TEXT,
  special_instructions TEXT,
  is_active BOOLEAN DEFAULT TRUE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE
);
```

### periodical_services

Stores information about scheduled specialized cleaning services.

```sql
CREATE TABLE periodical_services (
  id TEXT PRIMARY KEY,
  property_id TEXT NOT NULL REFERENCES properties(id),
  service_name TEXT NOT NULL,
  service_description TEXT,
  frequency_id TEXT NOT NULL REFERENCES frequency_types(id),
  last_service_date TIMESTAMP WITH TIME ZONE,
  next_service_date TIMESTAMP WITH TIME ZONE,
  assigned_to TEXT REFERENCES users(id),
  status TEXT DEFAULT 'scheduled' NOT NULL,
  special_requirements TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE
);
```

### scheduled_tasks

Stores information about scheduled cleaning tasks.

```sql
CREATE TABLE scheduled_tasks (
  id TEXT PRIMARY KEY,
  property_id TEXT NOT NULL REFERENCES properties(id),
  task_id TEXT NOT NULL REFERENCES cleaning_tasks(id),
  scheduled_date TIMESTAMP WITH TIME ZONE NOT NULL,
  scheduled_start_time TIMESTAMP WITH TIME ZONE,
  scheduled_end_time TIMESTAMP WITH TIME ZONE,
  assigned_to TEXT REFERENCES users(id),
  status TEXT DEFAULT 'scheduled' NOT NULL,
  completion_date TIMESTAMP WITH TIME ZONE,
  completion_notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE
);
```

### retail_cleaning_scope

Stores information about retail-specific cleaning requirements.

```sql
CREATE TABLE retail_cleaning_scope (
  id TEXT PRIMARY KEY,
  area TEXT NOT NULL,
  element TEXT NOT NULL,
  requirement TEXT NOT NULL,
  frequency TEXT NOT NULL,
  category TEXT NOT NULL,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE
);
```

## Tier-Based Cleaning Specifications

The schema implements a tier-based approach to cleaning specifications:

- **Tier 1 Sites:** Large industrial facilities operating 24/7 with multiple daily cleaning requirements (3x daily for high-traffic areas)
- **Tier 2 Sites:** Smaller industrial centers with daily cleaning schedules
- **Tier 3-5 Sites:** Smaller delivery centers with reduced frequency (3 out of 5 operational days)

## Specialized Cleaning Requirements

The schema supports specialized cleaning requirements unique to Australia Post and Star Track facilities:

### Production Areas
- Walkway cleaning
- Under-mezzanine cleaning
- Machine sweep operations
- Technical area boundaries

### Retail Spaces
- PO Box cleaning
- Parcel locker maintenance
- Counter sanitization
- Front-of-house vs. back-of-house scheduling

### Periodical Services
- Vinyl floor strip and seal (biannual)
- Carpet deep cleaning (biannual)
- Window cleaning (monthly for retail, quarterly for industrial)
- High dusting (biannual)
- Loading dock degreasing (quarterly)

## Usage

To use the contract specifications schema:

1. **Create Contract Specifications:** Define the contract details, including client, start date, and contract value.
2. **Define Cleaning Tasks:** Create cleaning tasks for different areas with specific requirements.
3. **Map Tasks to Facilities:** Use the facility_cleaning_specifications table to map tasks to specific facilities with appropriate frequencies based on tier.
4. **Schedule Periodical Services:** Define specialized cleaning services with appropriate frequencies.
5. **Generate Scheduled Tasks:** Create scheduled tasks based on the specifications and assign them to staff.

## Seed Data

A seed script is provided to populate the database with initial data:

```bash
node scripts/seed-contract-specifications.js
```

This script creates:
- Cleaning areas (Toilets, Kitchen, Office, Retail, Production, External)
- Frequency types (3x Daily, Daily, 3 of 5 days, Weekly, Monthly, Quarterly, Biannual, Annual)
- Tier specifications (Tier 1-5)
- Cleaning tasks for each area
- Retail cleaning scope for front-of-house and back-of-house areas

## Last Updated

2025-04-15
