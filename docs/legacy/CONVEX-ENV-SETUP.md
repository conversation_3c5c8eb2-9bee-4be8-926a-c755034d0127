# Convex Environment Variables Setup

## Required Environment Variables

You need to set these environment variables in your Convex dashboard:

### For Development:
1. **Go to Dev Dashboard**: https://dashboard.convex.dev/d/doting-chicken-81/settings/environment-variables

### For Production:
1. **Go to Prod Dashboard**: https://dashboard.convex.dev/d/healthy-ram-536/settings/environment-variables

2. **Add the following variables**:

### CLERK_ISSUER_URL
- **Value**: `https://YOUR-CLERK-DOMAIN.clerk.accounts.dev`
- **Example**: `https://askara-property-services.clerk.accounts.dev`
- **Where to find**: In your Clerk Dashboard under API Keys, look for "Issuer URL"

### CLERK_WEBHOOK_SECRET
- **Value**: `whsec_YOUR_WEBHOOK_SECRET`
- **Where to find**: In your Clerk Dashboard under Webhooks, after creating a webhook endpoint

## How to Add Variables in Convex

1. Navigate to: https://dashboard.convex.dev/d/doting-chicken-81/settings/environment-variables
2. Click "Add Environment Variable"
3. Enter the variable name (e.g., `CLERK_ISSUER_URL`)
4. Enter the value
5. Click "Save"

## Clerk Webhook Setup

1. **Go to Clerk Dashboard** → Webhooks
2. **Create Endpoint**:
   - URL: `https://doting-chicken-81.convex.cloud/clerk-webhook`
   - Events to subscribe:
     - `user.created`
     - `user.updated`
     - `organization.created`
     - `organization.updated`
     - `organization.deleted`
     - `organizationMembership.created`
     - `organizationMembership.updated`
     - `organizationMembership.deleted`
3. **Copy the Signing Secret** and add it as `CLERK_WEBHOOK_SECRET` in Convex

## Verify Setup

After adding the environment variables, your Convex functions should deploy successfully. The webhook will automatically sync users and organizations from Clerk to Convex.