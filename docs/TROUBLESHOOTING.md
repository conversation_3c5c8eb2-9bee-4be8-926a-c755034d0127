# ARA Property Services - Troubleshooting Guide

Common issues and their solutions for the ARA Property Services application.

## Development Issues

### Installation Problems

#### pnpm install fails
```bash
# Clear cache and reinstall
rm -rf node_modules pnpm-lock.yaml
pnpm store prune
pnpm install
```

#### Port already in use
```bash
# Kill process on port 3000
lsof -ti:3000 | xargs kill -9
# Or use a different port
pnpm dev -- --port 3001
```

#### Node version issues
```bash
# Check Node version (requires 18.x+)
node --version
# Use nvm to switch versions
nvm use 18
```

### Build Issues

#### TypeScript errors
1. Check for missing type definitions
2. Verify import paths are correct
3. Ensure all dependencies are installed
4. Run `pnpm build` to see full error details

#### Next.js build failures
```bash
# Clear Next.js cache
rm -rf .next
pnpm build
```

#### Turbopack issues
```bash
# Disable Turbopack if needed
pnpm dev --turbo=false
```

## Environment Configuration

### Environment variables not loading
1. Ensure `.env.local` is in the root directory
2. Restart the development server
3. Check for syntax errors in the file
4. Verify variable names match exactly

### Missing required variables
Run the validation script:
```bash
pnpm validate:env
```

Common missing variables:
- `NEXT_PUBLIC_CONVEX_URL`
- `CONVEX_DEPLOYMENT`
- `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY`
- `CLERK_SECRET_KEY`

## Authentication Issues

### Clerk authentication not working

#### Check environment variables
```bash
# Verify Clerk keys are set
echo $NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY
echo $CLERK_SECRET_KEY
```

#### Verify Clerk configuration
1. Check Clerk dashboard settings
2. Ensure organizations are enabled
3. Verify webhook endpoints
4. Check redirect URLs

#### Common Clerk errors
- **Invalid publishable key**: Check the key format and environment
- **Webhook signature verification failed**: Verify webhook secret
- **Organization not found**: Ensure organizations are enabled

### User creation issues
1. Check webhook configuration
2. Verify database connection
3. Check user creation logs
4. Ensure proper error handling

## Database Issues

### Convex connection problems

#### Check Convex configuration
```bash
# Verify Convex deployment
npx convex dev --help
```

#### Common Convex errors
- **Deployment not found**: Check `CONVEX_DEPLOYMENT` variable
- **Function not found**: Ensure functions are deployed
- **Schema mismatch**: Check schema definitions

### Legacy database issues (Neon/Drizzle)

#### Connection failures
```bash
# Test database connection
pnpm test:neon
```

#### Migration issues
```bash
# Check migration status
pnpm db:verify
# Push schema changes
pnpm db:push
```

## API Issues

### API routes not working
1. Check route file naming (app directory structure)
2. Verify export format (GET, POST, etc.)
3. Check middleware configuration
4. Ensure proper error handling

### CORS issues
1. Check Next.js configuration
2. Verify API route headers
3. Check client-side request format

## Performance Issues

### Slow development server
1. Use Turbopack: `pnpm dev` (default)
2. Clear cache: `pnpm clean`
3. Check for large files in public directory
4. Optimize imports (use dynamic imports)

### Slow build times
1. Check bundle analyzer output
2. Optimize dependencies
3. Use proper code splitting
4. Remove unused imports

### Memory issues
```bash
# Increase Node.js memory limit
NODE_OPTIONS="--max-old-space-size=4096" pnpm dev
```

## AI Integration Issues

### OpenAI API errors
- **Rate limit exceeded**: Check usage limits
- **Invalid API key**: Verify key format and permissions
- **Model not found**: Check model availability

### ElevenLabs voice issues
- **API key invalid**: Check ElevenLabs dashboard
- **Voice not found**: Verify voice ID
- **Audio playback issues**: Check browser permissions

## Deployment Issues

### Vercel deployment failures

#### Build errors
1. Check build logs in Vercel dashboard
2. Verify environment variables are set
3. Ensure all dependencies are in package.json
4. Check for TypeScript errors

#### Runtime errors
1. Check function logs
2. Verify API routes are working
3. Check environment variable access
4. Ensure proper error boundaries

### Environment variable issues
1. Set variables in Vercel dashboard
2. Redeploy after adding variables
3. Check variable names match exactly
4. Verify sensitive variables are not exposed

## Multi-Tenant Issues

### Organization data isolation
1. Verify organization ID is passed correctly
2. Check database queries include organization filter
3. Test with multiple organizations
4. Ensure proper access controls

### User organization membership
1. Check Clerk organization setup
2. Verify webhook handling
3. Test organization switching
4. Ensure proper role assignments

## Mobile Issues

### Responsive design problems
1. Test on actual devices
2. Check viewport meta tag
3. Verify Tailwind responsive classes
4. Test touch interactions

### Voice assistant on mobile
1. Check microphone permissions
2. Test on different browsers
3. Verify HTTPS requirement
4. Check audio playback

## Getting Help

### Debug Information to Collect
1. Error messages (full stack trace)
2. Browser console logs
3. Network tab information
4. Environment details (Node version, OS)
5. Steps to reproduce

### Resources
- [Next.js Documentation](https://nextjs.org/docs)
- [Convex Documentation](https://docs.convex.dev)
- [Clerk Documentation](https://clerk.com/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)

### Contact Support
- Check existing GitHub issues
- Create detailed bug reports
- Include reproduction steps
- Provide environment information
