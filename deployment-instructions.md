# Deployment Instructions for ARA Property Services

## Fix Dependencies and Build Issues

To fix dependencies and build issues, run the following commands:

```bash
# Remove node_modules and package-lock.json
rm -rf node_modules package-lock.json .next

# Install dependencies with specific versions
pnpm install

# Fix SWC dependencies
pnpm add -D @swc/core@1.3.100 @swc/helpers@0.5.3

# Fix React and Next.js versions
pnpm add next@14.1.0 react@18.2.0 react-dom@18.2.0

# Fix Clerk imports
pnpm add @clerk/nextjs@5.1.2

# Fix Drizzle ORM
pnpm add drizzle-orm@0.29.5 drizzle-kit@0.20.14
```

## Deploy to Vercel

After fixing the dependencies, you can deploy the application to Vercel:

```bash
# Install Vercel CLI if not already installed
npm install -g vercel

# Deploy to Vercel
vercel --prod
```

## Database Setup

To set up the database, run the following commands:

```bash
# Push schema to database
pnpm db:push

# Seed the database
pnpm db:seed

# Open Drizzle Studio to view and manage data
pnpm db:studio
```

## Environment Variables

Make sure to set the following environment variables in your Vercel project:

- `DATABASE_URL`: Neon PostgreSQL connection string
- `DATABASE_URL_UNPOOLED`: Neon PostgreSQL connection string (unpooled)
- `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY`: Clerk publishable key
- `CLERK_SECRET_KEY`: Clerk secret key
- `CLERK_WEBHOOK_SECRET`: Clerk webhook secret
- `CLERK_ENCRYPTION_KEY`: Clerk encryption key
- `OPENAI_API_KEY`: OpenAI API key
- `AGENT_ID`: ElevenLabs agent ID
- `XI_API_KEY`: ElevenLabs API key
- `BLOB_READ_WRITE_TOKEN`: Vercel Blob storage token

## Troubleshooting

If you encounter any issues during deployment, try the following:

1. Check the Vercel deployment logs for errors
2. Ensure all environment variables are set correctly
3. Make sure the database schema is up to date
4. Check for any compatibility issues with dependencies
