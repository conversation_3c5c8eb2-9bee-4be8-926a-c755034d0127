import { User } from '@/app/db/schema';

// Mock ARA Property Services users for UI testing
export const mockUsers: User[] = [
  {
    id: '1',
    name: '<PERSON>',
    role: 'CEO',
    department: 'Executive',
    email: '<EMAIL>',
    password: 'ARApaulPS!',
    phone: '0403 244 691',
    avatar: '/avatars/paul.jpg',
    preferences: { theme: 'light', notifications: true },
    created_at: new Date('2025-01-01T00:00:00Z'),
    last_login: new Date('2025-04-15T09:30:00Z'),
  },
  {
    id: '2',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    role: 'Head of Innovation',
    department: 'Innovation & Compliance',
    email: '<PERSON><EMAIL>',
    password: 'ARAgauravPS!',
    phone: '0417 741 543',
    avatar: '/avatars/gaurav.jpg',
    preferences: { theme: 'dark', notifications: true },
    created_at: new Date('2025-01-01T00:00:00Z'),
    last_login: new Date('2025-04-15T10:15:00Z'),
  },
  {
    id: '3',
    name: '<PERSON>',
    role: 'Senior Estimator',
    department: 'Sales',
    email: '<EMAIL>',
    password: 'ARAmarkPS!',
    phone: '0477 806 648',
    avatar: '/avatars/mark.jpg',
    preferences: { theme: 'system', notifications: true },
    created_at: new Date('2025-01-01T00:00:00Z'),
    last_login: new Date('2025-04-14T16:45:00Z'),
  },
  {
    id: '4',
    name: 'Charles McCann',
    role: 'Content Creator',
    department: 'Marketing',
    email: '<EMAIL>',
    password: 'ARAcharlesPS!',
    phone: '0404 459 623',
    avatar: '/avatars/charles.jpg',
    preferences: { theme: 'light', notifications: false },
    created_at: new Date('2025-01-01T00:00:00Z'),
    last_login: new Date('2025-04-15T08:20:00Z'),
  },
  {
    id: '5',
    name: 'Sarah Johnson',
    role: 'Regional Manager',
    department: 'Operations',
    email: '<EMAIL>',
    password: 'ARAsarahPS!',
    phone: '0412 345 678',
    avatar: '/avatars/sarah.jpg',
    preferences: { theme: 'light', notifications: true },
    created_at: new Date('2025-01-01T00:00:00Z'),
    last_login: new Date('2025-04-15T11:10:00Z'),
  },
];

// Function to get a user by email (for mock authentication)
export function getUserByEmail(email: string): User | undefined {
  return mockUsers.find(user => user.email.toLowerCase() === email.toLowerCase());
}

// Function to get a user by ID
export function getUserById(id: string): User | undefined {
  return mockUsers.find(user => user.id === id);
}

// Export default for convenience
export default mockUsers;
