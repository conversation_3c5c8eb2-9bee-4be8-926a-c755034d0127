# Mock Data for UI Development and Testing

This folder contains mock data for UI development and testing of the ARA Property Services application.

## Overview

The mock data provides realistic examples of:

- ARA Property Services users (leadership team and staff)
- Australia Post and Star Track properties
- Inspection reports and actions
- And more

## Files

- **users.ts**: Mock ARA Property Services users with realistic data
- **properties.ts**: Mock Australia Post and Star Track properties
- **inspections.ts**: Mock inspection reports and actions
- **index.ts**: Exports all mock data from a single file

## Usage

### Import All Mock Data

```typescript
import mockData from '@/mocks';

// Access mock data
const users = mockData.users;
const properties = mockData.properties;
const inspections = mockData.inspections.reports;
const actions = mockData.inspections.actions;
```

### Import Specific Mock Data

```typescript
import { mockUsers, getUserByEmail } from '@/mocks/users';
import { mockProperties, getPropertyById } from '@/mocks/properties';
import { mockInspectionReports, mockInspectionActions } from '@/mocks/inspections';
```

### Helper Functions

The mock data files include helper functions for common operations:

#### Users

- `getUserByEmail(email)`: Get a user by email address
- `getUserById(id)`: Get a user by ID

#### Properties

- `getPropertyById(id)`: Get a property by ID
- `getPropertiesByManagerId(managerId)`: Get properties by manager ID
- `getPropertiesByRegion(region)`: Get properties by region

#### Inspections

- `getInspectionReportById(id)`: Get an inspection report by ID
- `getInspectionReportsByPropertyId(propertyId)`: Get inspection reports by property ID
- `getInspectionReportsByInspectorId(inspectorId)`: Get inspection reports by inspector ID
- `getInspectionActionsByReportId(reportId)`: Get inspection actions by report ID

## Data Structure

The mock data follows the same structure as the database schema defined in `app/db/schema.ts`, making it easy to switch between mock data and real database data during development.

## Example

```typescript
import { mockUsers } from '@/mocks/users';
import { mockProperties } from '@/mocks/properties';
import { getInspectionReportsByPropertyId } from '@/mocks/inspections';

// Get a user
const user = mockUsers[0]; // Paul McCann, CEO

// Get properties managed by this user
const userProperties = mockProperties.filter(p => p.manager_id === user.id);

// Get inspection reports for the first property
const propertyReports = getInspectionReportsByPropertyId(userProperties[0].id);

console.log(`${user.name} manages ${userProperties.length} properties`);
console.log(`Property ${userProperties[0].name} has ${propertyReports.length} inspection reports`);
```
