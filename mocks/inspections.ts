import { mockUsers } from './users';
import { mockProperties } from './properties';

// Mock inspection reports for UI testing
export const mockInspectionReports = [
  {
    id: '1',
    property_id: mockProperties[0].id, // Australia Post Sydney Mail Centre
    template_id: '1', // Industrial Property Inspection
    title: 'Monthly Inspection - Sydney Mail Centre',
    location: 'Main Processing Area',
    date: new Date('2025-04-10T09:00:00Z'),
    inspector: mockUsers[4].id, // <PERSON>
    status: 'submitted',
    score: 92,
    summary: 'Overall good condition with minor issues in the equipment section.',
    sections: {
      safety: {
        score: 95,
        items: [
          {
            id: '1-1',
            name: 'Emergency Exits',
            status: 'pass',
            notes: 'All emergency exits clear and well-marked.',
          },
          {
            id: '1-2',
            name: 'Fire Equipment',
            status: 'pass',
            notes: 'Fire extinguishers in place and recently serviced.',
          },
          {
            id: '1-3',
            name: 'First Aid',
            status: 'pass',
            notes: 'First aid kits fully stocked and accessible.',
          },
        ],
      },
      equipment: {
        score: 85,
        items: [
          {
            id: '1-4',
            name: 'HVAC Systems',
            status: 'fail',
            notes: 'Air conditioning unit in sorting area making unusual noise. Requires maintenance.',
          },
          {
            id: '1-5',
            name: 'Lighting',
            status: 'pass',
            notes: 'All lighting functional.',
          },
          {
            id: '1-6',
            name: 'Security Systems',
            status: 'pass',
            notes: 'Security cameras and access control working properly.',
          },
        ],
      },
    },
    weather_conditions: 'Clear',
    temperature: 22.5,
    photos: 8,
    voice_notes: 2,
    created_at: new Date('2025-04-10T09:00:00Z'),
    submitted_at: new Date('2025-04-10T11:30:00Z'),
    reviewed_by: mockUsers[0].id, // Paul McCann
    reviewed_at: new Date('2025-04-11T14:00:00Z'),
  },
  {
    id: '2',
    property_id: mockProperties[1].id, // Australia Post Melbourne Gateway Facility
    template_id: '1', // Industrial Property Inspection
    title: 'Quarterly Inspection - Melbourne Gateway',
    location: 'Sorting Facility',
    date: new Date('2025-04-05T10:00:00Z'),
    inspector: mockUsers[4].id, // Sarah Johnson
    status: 'approved',
    score: 97,
    summary: 'Excellent condition throughout the facility. Minor recommendation for improved signage.',
    sections: {
      safety: {
        score: 98,
        items: [
          {
            id: '2-1',
            name: 'Emergency Exits',
            status: 'pass',
            notes: 'All emergency exits clear and well-marked.',
          },
          {
            id: '2-2',
            name: 'Fire Equipment',
            status: 'pass',
            notes: 'Fire extinguishers in place and recently serviced.',
          },
          {
            id: '2-3',
            name: 'First Aid',
            status: 'pass',
            notes: 'First aid kits fully stocked and accessible.',
          },
        ],
      },
      equipment: {
        score: 95,
        items: [
          {
            id: '2-4',
            name: 'HVAC Systems',
            status: 'pass',
            notes: 'All HVAC systems functioning properly.',
          },
          {
            id: '2-5',
            name: 'Lighting',
            status: 'pass',
            notes: 'All lighting functional.',
          },
          {
            id: '2-6',
            name: 'Security Systems',
            status: 'pass',
            notes: 'Security cameras and access control working properly.',
          },
        ],
      },
    },
    weather_conditions: 'Overcast',
    temperature: 18.0,
    photos: 12,
    voice_notes: 3,
    created_at: new Date('2025-04-05T10:00:00Z'),
    submitted_at: new Date('2025-04-05T13:45:00Z'),
    reviewed_by: mockUsers[1].id, // Gaurav Majumdar
    reviewed_at: new Date('2025-04-06T09:30:00Z'),
  },
  {
    id: '3',
    property_id: mockProperties[3].id, // Australia Post GPO Sydney
    template_id: '2', // Retail Property Inspection
    title: 'Monthly Inspection - GPO Sydney',
    location: 'Customer Service Area',
    date: new Date('2025-04-12T09:30:00Z'),
    inspector: mockUsers[2].id, // Mark Brady
    status: 'draft',
    score: null,
    summary: '',
    sections: {
      safety: {
        score: null,
        items: [
          {
            id: '3-1',
            name: 'Emergency Exits',
            status: 'pass',
            notes: 'All emergency exits clear and well-marked.',
          },
          {
            id: '3-2',
            name: 'Fire Equipment',
            status: 'pass',
            notes: 'Fire extinguishers in place and recently serviced.',
          },
          {
            id: '3-3',
            name: 'First Aid',
            status: 'na',
            notes: 'Inspection pending.',
          },
        ],
      },
      equipment: {
        score: null,
        items: [
          {
            id: '3-4',
            name: 'HVAC Systems',
            status: 'na',
            notes: 'Inspection pending.',
          },
          {
            id: '3-5',
            name: 'Lighting',
            status: 'pass',
            notes: 'All lighting functional.',
          },
          {
            id: '3-6',
            name: 'Security Systems',
            status: 'na',
            notes: 'Inspection pending.',
          },
        ],
      },
    },
    weather_conditions: 'Sunny',
    temperature: 24.0,
    photos: 3,
    voice_notes: 1,
    created_at: new Date('2025-04-12T09:30:00Z'),
    submitted_at: null,
    reviewed_by: null,
    reviewed_at: null,
  },
];

// Mock inspection actions for UI testing
export const mockInspectionActions = [
  {
    id: '1',
    report_id: '1', // Monthly Inspection - Sydney Mail Centre
    title: 'Repair HVAC unit in sorting area',
    priority: 'medium',
    assignee: mockUsers[2].id, // Mark Brady
    due_date: new Date('2025-04-17T00:00:00Z'),
    status: 'in-progress',
    created_at: new Date('2025-04-11T14:00:00Z'),
    updated_at: new Date('2025-04-13T10:30:00Z'),
  },
  {
    id: '2',
    report_id: '2', // Quarterly Inspection - Melbourne Gateway
    title: 'Improve signage in loading dock area',
    priority: 'low',
    assignee: mockUsers[4].id, // Sarah Johnson
    due_date: new Date('2025-04-20T00:00:00Z'),
    status: 'pending',
    created_at: new Date('2025-04-06T09:30:00Z'),
    updated_at: new Date('2025-04-06T09:30:00Z'),
  },
];

// Function to get an inspection report by ID
export function getInspectionReportById(id: string) {
  return mockInspectionReports.find(report => report.id === id);
}

// Function to get inspection reports by property ID
export function getInspectionReportsByPropertyId(propertyId: string) {
  return mockInspectionReports.filter(report => report.property_id === propertyId);
}

// Function to get inspection reports by inspector ID
export function getInspectionReportsByInspectorId(inspectorId: string) {
  return mockInspectionReports.filter(report => report.inspector === inspectorId);
}

// Function to get inspection actions by report ID
export function getInspectionActionsByReportId(reportId: string) {
  return mockInspectionActions.filter(action => action.report_id === reportId);
}

// Export default for convenience
export default {
  reports: mockInspectionReports,
  actions: mockInspectionActions,
};
