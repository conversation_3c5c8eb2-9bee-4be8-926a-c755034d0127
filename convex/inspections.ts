import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { Id } from "./_generated/dataModel";

// Create inspection template
export const createInspectionTemplate = mutation({
  args: {
    name: v.string(),
    description: v.optional(v.string()),
    sections: v.array(v.object({
      name: v.string(),
      items: v.array(v.object({
        name: v.string(),
        type: v.string(),
        required: v.boolean(),
        options: v.optional(v.array(v.string())),
      })),
    })),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Get user's organization
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user || !user.organizationId) {
      throw new Error("User must belong to an organization");
    }

    const templateId = await ctx.db.insert("inspectionTemplates", {
      ...args,
      organizationId: user.organizationId,
      isActive: true,
    });

    return templateId;
  },
});

// List inspection templates
export const listInspectionTemplates = query({
  args: {
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    // Get user's organization
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user || !user.organizationId) {
      return [];
    }

    let query = ctx.db
      .query("inspectionTemplates")
      .withIndex("by_org", (q) => q.eq("organizationId", user.organizationId!));

    if (args.isActive !== undefined) {
      query = query.filter((q) => q.eq(q.field("isActive"), args.isActive));
    }

    return await query.collect();
  },
});

// Create inspection report
export const createInspectionReport = mutation({
  args: {
    templateId: v.optional(v.id("inspectionTemplates")),
    propertyId: v.id("properties"),
    scheduledDate: v.number(),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Get user
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user || !user.organizationId) {
      throw new Error("User must belong to an organization");
    }

    // Verify property belongs to organization
    const property = await ctx.db.get(args.propertyId);
    if (!property || property.organizationId !== user.organizationId) {
      throw new Error("Property not found or unauthorized");
    }

    const reportId = await ctx.db.insert("inspectionReports", {
      ...args,
      inspectorId: user._id,
      status: "scheduled",
      data: {},
      organizationId: user.organizationId,
    });

    return reportId;
  },
});

// Update inspection report
export const updateInspectionReport = mutation({
  args: {
    reportId: v.id("inspectionReports"),
    status: v.optional(v.string()),
    score: v.optional(v.number()),
    data: v.optional(v.any()),
    notes: v.optional(v.string()),
    completedDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Verify report belongs to user's organization
    const report = await ctx.db.get(args.reportId);
    if (!report) {
      throw new Error("Report not found");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user || report.organizationId !== user.organizationId) {
      throw new Error("Unauthorized");
    }

    const { reportId, ...updates } = args;
    await ctx.db.patch(reportId, updates);

    return reportId;
  },
});

// List inspection reports
export const listInspectionReports = query({
  args: {
    propertyId: v.optional(v.id("properties")),
    inspectorId: v.optional(v.id("users")),
    status: v.optional(v.string()),
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    // Get user's organization
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user || !user.organizationId) {
      return [];
    }

    let query = ctx.db
      .query("inspectionReports")
      .withIndex("by_org", (q) => q.eq("organizationId", user.organizationId!));

    // Apply filters
    if (args.propertyId) {
      query = query.filter((q) => q.eq(q.field("propertyId"), args.propertyId));
    }
    if (args.inspectorId) {
      query = query.filter((q) => q.eq(q.field("inspectorId"), args.inspectorId));
    }
    if (args.status) {
      query = query.filter((q) => q.eq(q.field("status"), args.status));
    }
    if (args.startDate) {
      query = query.filter((q) => q.gte(q.field("scheduledDate"), args.startDate!));
    }
    if (args.endDate) {
      query = query.filter((q) => q.lte(q.field("scheduledDate"), args.endDate!));
    }

    const reports = await query.collect();

    // Get related data
    const reportsWithDetails = await Promise.all(
      reports.map(async (report) => {
        const [property, inspector, template] = await Promise.all([
          ctx.db.get(report.propertyId),
          ctx.db.get(report.inspectorId),
          report.templateId ? ctx.db.get(report.templateId) : null,
        ]);

        return {
          ...report,
          property,
          inspector,
          template,
        };
      })
    );

    return reportsWithDetails;
  },
});

// Create inspection action
export const createInspectionAction = mutation({
  args: {
    reportId: v.id("inspectionReports"),
    type: v.string(),
    description: v.string(),
    priority: v.string(),
    assignedToId: v.optional(v.id("users")),
    dueDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Verify report belongs to user's organization
    const report = await ctx.db.get(args.reportId);
    if (!report) {
      throw new Error("Report not found");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user || report.organizationId !== user.organizationId) {
      throw new Error("Unauthorized");
    }

    const actionId = await ctx.db.insert("inspectionActions", {
      ...args,
      status: "pending",
      organizationId: report.organizationId,
    });

    return actionId;
  },
});

// List inspection actions
export const listInspectionActions = query({
  args: {
    reportId: v.optional(v.id("inspectionReports")),
    assignedToId: v.optional(v.id("users")),
    status: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    // Get user's organization
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user || !user.organizationId) {
      return [];
    }

    let query = ctx.db
      .query("inspectionActions")
      .withIndex("by_org", (q) => q.eq("organizationId", user.organizationId!));

    // Apply filters
    if (args.reportId) {
      query = query.filter((q) => q.eq(q.field("reportId"), args.reportId));
    }
    if (args.assignedToId) {
      query = query.filter((q) => q.eq(q.field("assignedToId"), args.assignedToId));
    }
    if (args.status) {
      query = query.filter((q) => q.eq(q.field("status"), args.status));
    }

    const actions = await query.collect();

    // Get assigned user details
    const actionsWithAssignees = await Promise.all(
      actions.map(async (action) => {
        let assignedTo = null;
        if (action.assignedToId) {
          assignedTo = await ctx.db.get(action.assignedToId);
        }
        return {
          ...action,
          assignedTo,
        };
      })
    );

    return actionsWithAssignees;
  },
});

// Update inspection action
export const updateInspectionAction = mutation({
  args: {
    actionId: v.id("inspectionActions"),
    status: v.optional(v.string()),
    assignedToId: v.optional(v.id("users")),
    dueDate: v.optional(v.number()),
    completedDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Verify action belongs to user's organization
    const action = await ctx.db.get(args.actionId);
    if (!action) {
      throw new Error("Action not found");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user || action.organizationId !== user.organizationId) {
      throw new Error("Unauthorized");
    }

    const { actionId, ...updates } = args;
    await ctx.db.patch(actionId, updates);

    return actionId;
  },
});