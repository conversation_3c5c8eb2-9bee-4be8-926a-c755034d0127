import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  // Organizations (synced from Clerk)
  organizations: defineTable({
    clerkOrgId: v.string(), // Clerk organization ID
    name: v.string(),
    slug: v.string(),
    imageUrl: v.optional(v.string()),
    publicMetadata: v.optional(v.any()),
    privateMetadata: v.optional(v.any()),
    maxAllowedMemberships: v.optional(v.number()),
    adminDeleteEnabled: v.optional(v.boolean()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_clerk_id", ["clerkOrgId"])
    .index("by_slug", ["slug"]),

  // Organization Members
  organizationMembers: defineTable({
    organizationId: v.id("organizations"),
    clerkUserId: v.string(),
    clerkOrgId: v.string(),
    role: v.string(),
    publicMetadata: v.optional(v.any()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_org", ["organizationId"])
    .index("by_user", ["clerkUserId"])
    .index("by_clerk_org", ["clerkOrgId"]),

  // Users (extended profile from Clerk)
  users: defineTable({
    clerkUserId: v.string(), // Clerk user ID
    name: v.string(),
    email: v.string(),
    role: v.string(),
    department: v.string(),
    phone: v.optional(v.string()),
    avatar: v.optional(v.string()),
    preferences: v.optional(v.any()),
    organizationId: v.optional(v.id("organizations")),
    lastLogin: v.optional(v.number()),
  })
    .index("by_clerk_id", ["clerkUserId"])
    .index("by_email", ["email"])
    .index("by_org", ["organizationId"]),

  // Properties
  properties: defineTable({
    name: v.string(),
    address: v.string(),
    suburb: v.string(),
    state: v.string(),
    postcode: v.string(),
    type: v.string(),
    tier: v.number(),
    region: v.string(),
    sizeSqm: v.optional(v.number()),
    category: v.string(),
    status: v.string(),
    organizationId: v.id("organizations"),
    managerId: v.optional(v.id("users")),
    metadata: v.optional(v.any()),
  })
    .index("by_org", ["organizationId"])
    .index("by_manager", ["managerId"])
    .index("by_status", ["status"])
    .index("by_region", ["region"]),

  // Property Areas
  propertyAreas: defineTable({
    propertyId: v.id("properties"),
    name: v.string(),
    type: v.string(),
    size: v.optional(v.number()),
    cleaningFrequency: v.optional(v.string()),
    lastCleaned: v.optional(v.number()),
    organizationId: v.id("organizations"),
  })
    .index("by_property", ["propertyId"])
    .index("by_org", ["organizationId"]),

  // Inspection Templates
  inspectionTemplates: defineTable({
    name: v.string(),
    description: v.optional(v.string()),
    sections: v.array(v.object({
      name: v.string(),
      items: v.array(v.object({
        name: v.string(),
        type: v.string(),
        required: v.boolean(),
        options: v.optional(v.array(v.string())),
      })),
    })),
    organizationId: v.id("organizations"),
    isActive: v.boolean(),
  })
    .index("by_org", ["organizationId"])
    .index("by_active", ["isActive"]),

  // Inspection Reports
  inspectionReports: defineTable({
    templateId: v.optional(v.id("inspectionTemplates")),
    propertyId: v.id("properties"),
    inspectorId: v.id("users"),
    status: v.string(),
    score: v.optional(v.number()),
    data: v.any(),
    notes: v.optional(v.string()),
    scheduledDate: v.number(),
    completedDate: v.optional(v.number()),
    organizationId: v.id("organizations"),
  })
    .index("by_property", ["propertyId"])
    .index("by_inspector", ["inspectorId"])
    .index("by_org", ["organizationId"])
    .index("by_status", ["status"])
    .index("by_date", ["scheduledDate"]),

  // Inspection Actions
  inspectionActions: defineTable({
    reportId: v.id("inspectionReports"),
    type: v.string(),
    description: v.string(),
    priority: v.string(),
    status: v.string(),
    assignedToId: v.optional(v.id("users")),
    dueDate: v.optional(v.number()),
    completedDate: v.optional(v.number()),
    organizationId: v.id("organizations"),
  })
    .index("by_report", ["reportId"])
    .index("by_assigned", ["assignedToId"])
    .index("by_org", ["organizationId"])
    .index("by_status", ["status"]),

  // Report Attachments
  reportAttachments: defineTable({
    reportId: v.id("inspectionReports"),
    fileName: v.string(),
    fileUrl: v.string(),
    fileType: v.string(),
    fileSize: v.number(),
    uploadedById: v.id("users"),
    organizationId: v.id("organizations"),
  })
    .index("by_report", ["reportId"])
    .index("by_org", ["organizationId"]),

  // Cleaning Tasks
  cleaningTasks: defineTable({
    name: v.string(),
    description: v.optional(v.string()),
    areaType: v.string(),
    frequency: v.string(),
    estimatedDuration: v.optional(v.number()),
    requiredEquipment: v.optional(v.array(v.string())),
    organizationId: v.id("organizations"),
  })
    .index("by_org", ["organizationId"])
    .index("by_area_type", ["areaType"]),

  // Scheduled Tasks
  scheduledTasks: defineTable({
    taskId: v.id("cleaningTasks"),
    propertyAreaId: v.id("propertyAreas"),
    assignedToId: v.optional(v.id("users")),
    scheduledDate: v.number(),
    status: v.string(),
    completedDate: v.optional(v.number()),
    notes: v.optional(v.string()),
    organizationId: v.id("organizations"),
  })
    .index("by_area", ["propertyAreaId"])
    .index("by_assigned", ["assignedToId"])
    .index("by_org", ["organizationId"])
    .index("by_date", ["scheduledDate"])
    .index("by_status", ["status"]),

  // Contract Specifications
  contractSpecifications: defineTable({
    propertyId: v.id("properties"),
    contractNumber: v.string(),
    startDate: v.number(),
    endDate: v.optional(v.number()),
    specifications: v.any(),
    tierSpecifications: v.optional(v.any()),
    organizationId: v.id("organizations"),
  })
    .index("by_property", ["propertyId"])
    .index("by_org", ["organizationId"])
    .index("by_contract", ["contractNumber"]),

  // Chat Sessions
  chatSessions: defineTable({
    title: v.string(),
    preview: v.optional(v.string()),
    isStarred: v.boolean(),
    createdById: v.id("users"),
    participantIds: v.array(v.id("users")),
    organizationId: v.id("organizations"),
  })
    .index("by_creator", ["createdById"])
    .index("by_org", ["organizationId"])
    .index("by_starred", ["isStarred"]),

  // Messages
  messages: defineTable({
    sessionId: v.id("chatSessions"),
    senderId: v.id("users"),
    content: v.string(),
    contentType: v.string(),
    metadata: v.optional(v.any()),
    organizationId: v.id("organizations"),
  })
    .index("by_session", ["sessionId"])
    .index("by_sender", ["senderId"])
    .index("by_org", ["organizationId"]),

  // Contacts
  contacts: defineTable({
    name: v.string(),
    role: v.string(),
    company: v.string(),
    phone: v.optional(v.string()),
    email: v.optional(v.string()),
    location: v.optional(v.string()),
    isFavorite: v.boolean(),
    avatar: v.optional(v.string()),
    organizationId: v.id("organizations"),
  })
    .index("by_org", ["organizationId"])
    .index("by_favorite", ["isFavorite"]),

  // Dashboard Metrics
  dashboardMetrics: defineTable({
    propertyId: v.optional(v.id("properties")),
    metricType: v.string(),
    value: v.number(),
    period: v.string(),
    date: v.number(),
    organizationId: v.id("organizations"),
  })
    .index("by_property", ["propertyId"])
    .index("by_org", ["organizationId"])
    .index("by_type", ["metricType"])
    .index("by_date", ["date"]),

  // KPI Targets
  kpiTargets: defineTable({
    name: v.string(),
    targetValue: v.number(),
    currentValue: v.number(),
    unit: v.string(),
    period: v.string(),
    propertyId: v.optional(v.id("properties")),
    organizationId: v.id("organizations"),
  })
    .index("by_property", ["propertyId"])
    .index("by_org", ["organizationId"]),

  // Notifications
  notifications: defineTable({
    userId: v.id("users"),
    type: v.string(),
    title: v.string(),
    message: v.string(),
    isRead: v.boolean(),
    metadata: v.optional(v.any()),
    organizationId: v.id("organizations"),
  })
    .index("by_user", ["userId"])
    .index("by_org", ["organizationId"])
    .index("by_read", ["isRead"]),
});