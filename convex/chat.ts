import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { Id } from "./_generated/dataModel";

// Create a new chat session
export const createChatSession = mutation({
  args: {
    title: v.string(),
    preview: v.optional(v.string()),
    participantIds: v.optional(v.array(v.id("users"))),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Get user's organization
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user || !user.organizationId) {
      throw new Error("User must belong to an organization");
    }

    // Include the creator in participants
    const participantIds = args.participantIds || [];
    if (!participantIds.includes(user._id)) {
      participantIds.push(user._id);
    }

    const sessionId = await ctx.db.insert("chatSessions", {
      title: args.title,
      preview: args.preview,
      isStarred: false,
      createdById: user._id,
      participantIds,
      organizationId: user.organizationId,
    });

    return sessionId;
  },
});

// Get chat session by ID
export const getChatSession = query({
  args: { sessionId: v.id("chatSessions") },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      return null;
    }

    // Get creator details
    const creator = await ctx.db.get(session.createdById);

    // Get participants details
    const participants = await Promise.all(
      session.participantIds.map(id => ctx.db.get(id))
    );

    return {
      ...session,
      creator,
      participants: participants.filter(Boolean),
    };
  },
});

// List chat sessions for user
export const listChatSessions = query({
  args: {
    isStarred: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    // Get user's organization
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user || !user.organizationId) {
      return [];
    }

    let query = ctx.db
      .query("chatSessions")
      .withIndex("by_org", (q) => q.eq("organizationId", user.organizationId!))
      .filter((q) => q.eq(q.field("participantIds"), user._id)); // User is a participant

    if (args.isStarred !== undefined) {
      query = query.filter((q) => q.eq(q.field("isStarred"), args.isStarred));
    }

    const sessions = await query.collect();

    // Get creator details for each session
    const sessionsWithDetails = await Promise.all(
      sessions.map(async (session) => {
        const creator = await ctx.db.get(session.createdById);
        
        // Get recent messages count
        const messageCount = await ctx.db
          .query("messages")
          .withIndex("by_session", (q) => q.eq("sessionId", session._id))
          .collect()
          .then(messages => messages.length);

        return {
          ...session,
          creator,
          messageCount,
        };
      })
    );

    return sessionsWithDetails;
  },
});

// Send a message
export const sendMessage = mutation({
  args: {
    sessionId: v.id("chatSessions"),
    content: v.string(),
    contentType: v.string(),
    metadata: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Get user
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user || !user.organizationId) {
      throw new Error("User must belong to an organization");
    }

    // Verify user is participant in the session
    const session = await ctx.db.get(args.sessionId);
    if (!session || !session.participantIds.includes(user._id)) {
      throw new Error("Unauthorized - not a participant in this session");
    }

    // Verify session belongs to user's organization
    if (session.organizationId !== user.organizationId) {
      throw new Error("Unauthorized - session not in user's organization");
    }

    const messageId = await ctx.db.insert("messages", {
      sessionId: args.sessionId,
      senderId: user._id,
      content: args.content,
      contentType: args.contentType,
      metadata: args.metadata,
      organizationId: user.organizationId,
    });

    // Update session preview with latest message
    await ctx.db.patch(args.sessionId, {
      preview: args.content.substring(0, 100),
    });

    return messageId;
  },
});

// Get messages for a chat session
export const getMessages = query({
  args: {
    sessionId: v.id("chatSessions"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    // Get user
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user) {
      return [];
    }

    // Verify user is participant in the session
    const session = await ctx.db.get(args.sessionId);
    if (!session || !session.participantIds.includes(user._id)) {
      return [];
    }

    let query = ctx.db
      .query("messages")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .order("desc");

    if (args.limit) {
      query = query.take(args.limit);
    }

    const messages = await query.collect();

    // Get sender details for each message
    const messagesWithSenders = await Promise.all(
      messages.map(async (message) => {
        const sender = await ctx.db.get(message.senderId);
        return {
          ...message,
          sender,
        };
      })
    );

    return messagesWithSenders.reverse(); // Return in chronological order
  },
});

// Star/unstar a chat session
export const toggleStarChatSession = mutation({
  args: {
    sessionId: v.id("chatSessions"),
    isStarred: v.boolean(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Get user
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user) {
      throw new Error("User not found");
    }

    // Verify user is participant in the session
    const session = await ctx.db.get(args.sessionId);
    if (!session || !session.participantIds.includes(user._id)) {
      throw new Error("Unauthorized - not a participant in this session");
    }

    await ctx.db.patch(args.sessionId, {
      isStarred: args.isStarred,
    });

    return args.sessionId;
  },
});

// Add participant to chat session
export const addParticipant = mutation({
  args: {
    sessionId: v.id("chatSessions"),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Get current user
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user) {
      throw new Error("User not found");
    }

    // Get session
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    // Verify current user is creator or existing participant
    if (session.createdById !== user._id && !session.participantIds.includes(user._id)) {
      throw new Error("Unauthorized");
    }

    // Verify new participant belongs to same organization
    const newParticipant = await ctx.db.get(args.userId);
    if (!newParticipant || newParticipant.organizationId !== session.organizationId) {
      throw new Error("User not found or not in same organization");
    }

    // Add participant if not already in the list
    if (!session.participantIds.includes(args.userId)) {
      const updatedParticipants = [...session.participantIds, args.userId];
      await ctx.db.patch(args.sessionId, {
        participantIds: updatedParticipants,
      });
    }

    return args.sessionId;
  },
});

// Remove participant from chat session
export const removeParticipant = mutation({
  args: {
    sessionId: v.id("chatSessions"),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Get current user
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user) {
      throw new Error("User not found");
    }

    // Get session
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    // Verify current user is creator or removing themselves
    if (session.createdById !== user._id && args.userId !== user._id) {
      throw new Error("Unauthorized - only creator can remove others");
    }

    // Remove participant
    const updatedParticipants = session.participantIds.filter(id => id !== args.userId);
    await ctx.db.patch(args.sessionId, {
      participantIds: updatedParticipants,
    });

    return args.sessionId;
  },
});

// Delete chat session
export const deleteChatSession = mutation({
  args: { sessionId: v.id("chatSessions") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Get user
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user) {
      throw new Error("User not found");
    }

    // Get session
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    // Verify user is creator
    if (session.createdById !== user._id) {
      throw new Error("Unauthorized - only creator can delete session");
    }

    // Delete all messages in the session
    const messages = await ctx.db
      .query("messages")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .collect();

    for (const message of messages) {
      await ctx.db.delete(message._id);
    }

    // Delete the session
    await ctx.db.delete(args.sessionId);

    return args.sessionId;
  },
});