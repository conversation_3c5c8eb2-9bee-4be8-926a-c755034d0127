import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { Id } from "./_generated/dataModel";

// Create a new property
export const createProperty = mutation({
  args: {
    name: v.string(),
    address: v.string(),
    suburb: v.string(),
    state: v.string(),
    postcode: v.string(),
    type: v.string(),
    tier: v.number(),
    region: v.string(),
    sizeSqm: v.optional(v.number()),
    category: v.string(),
    managerId: v.optional(v.id("users")),
    metadata: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Get user's organization
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user || !user.organizationId) {
      throw new Error("User must belong to an organization");
    }

    const propertyId = await ctx.db.insert("properties", {
      ...args,
      status: "active",
      organizationId: user.organizationId,
    });

    return propertyId;
  },
});

// Get property by ID
export const getProperty = query({
  args: { propertyId: v.id("properties") },
  handler: async (ctx, args) => {
    const property = await ctx.db.get(args.propertyId);
    if (!property) {
      return null;
    }

    // Get manager details
    let manager = null;
    if (property.managerId) {
      manager = await ctx.db.get(property.managerId);
    }

    // Get area count
    const areas = await ctx.db
      .query("propertyAreas")
      .withIndex("by_property", (q) => q.eq("propertyId", args.propertyId))
      .collect();

    return {
      ...property,
      manager,
      areaCount: areas.length,
    };
  },
});

// List properties for organization
export const listProperties = query({
  args: {
    status: v.optional(v.string()),
    region: v.optional(v.string()),
    managerId: v.optional(v.id("users")),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    // Get user's organization
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user || !user.organizationId) {
      return [];
    }

    let query = ctx.db
      .query("properties")
      .withIndex("by_org", (q) => q.eq("organizationId", user.organizationId!));

    // Apply filters
    if (args.status) {
      query = query.filter((q) => q.eq(q.field("status"), args.status));
    }
    if (args.region) {
      query = query.filter((q) => q.eq(q.field("region"), args.region));
    }
    if (args.managerId) {
      query = query.filter((q) => q.eq(q.field("managerId"), args.managerId));
    }

    const properties = await query.collect();

    // Get manager details for each property
    const propertiesWithManagers = await Promise.all(
      properties.map(async (property) => {
        let manager = null;
        if (property.managerId) {
          manager = await ctx.db.get(property.managerId);
        }
        return {
          ...property,
          manager,
        };
      })
    );

    return propertiesWithManagers;
  },
});

// Update property
export const updateProperty = mutation({
  args: {
    propertyId: v.id("properties"),
    name: v.optional(v.string()),
    address: v.optional(v.string()),
    suburb: v.optional(v.string()),
    state: v.optional(v.string()),
    postcode: v.optional(v.string()),
    type: v.optional(v.string()),
    tier: v.optional(v.number()),
    region: v.optional(v.string()),
    sizeSqm: v.optional(v.number()),
    category: v.optional(v.string()),
    status: v.optional(v.string()),
    managerId: v.optional(v.id("users")),
    metadata: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Verify property belongs to user's organization
    const property = await ctx.db.get(args.propertyId);
    if (!property) {
      throw new Error("Property not found");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user || property.organizationId !== user.organizationId) {
      throw new Error("Unauthorized");
    }

    const { propertyId, ...updates } = args;
    await ctx.db.patch(propertyId, updates);

    return propertyId;
  },
});

// Create property area
export const createPropertyArea = mutation({
  args: {
    propertyId: v.id("properties"),
    name: v.string(),
    type: v.string(),
    size: v.optional(v.number()),
    cleaningFrequency: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Verify property belongs to user's organization
    const property = await ctx.db.get(args.propertyId);
    if (!property) {
      throw new Error("Property not found");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user || property.organizationId !== user.organizationId) {
      throw new Error("Unauthorized");
    }

    const areaId = await ctx.db.insert("propertyAreas", {
      ...args,
      organizationId: property.organizationId,
    });

    return areaId;
  },
});

// List property areas
export const listPropertyAreas = query({
  args: { propertyId: v.id("properties") },
  handler: async (ctx, args) => {
    const areas = await ctx.db
      .query("propertyAreas")
      .withIndex("by_property", (q) => q.eq("propertyId", args.propertyId))
      .collect();

    return areas;
  },
});

// Get property metrics
export const getPropertyMetrics = query({
  args: {
    propertyId: v.id("properties"),
    period: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("dashboardMetrics")
      .withIndex("by_property", (q) => q.eq("propertyId", args.propertyId));

    if (args.period) {
      query = query.filter((q) => q.eq(q.field("period"), args.period));
    }

    const metrics = await query.collect();
    return metrics;
  },
});