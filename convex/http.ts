import { httpRouter } from "convex/server";
import { httpAction } from "./_generated/server";
import { internal } from "./_generated/api";
import { Webhook } from "svix";

const http = httpRouter();

// Clerk webhook handler
http.route({
  path: "/clerk-webhook",
  method: "POST",
  handler: httpAction(async (ctx, request) => {
    const webhookSecret = process.env.CLERK_WEBHOOK_SECRET;
    if (!webhookSecret) {
      return new Response("Missing webhook secret", { status: 500 });
    }

    const svix_id = request.headers.get("svix-id");
    const svix_timestamp = request.headers.get("svix-timestamp");
    const svix_signature = request.headers.get("svix-signature");

    if (!svix_id || !svix_timestamp || !svix_signature) {
      return new Response("Missing svix headers", { status: 400 });
    }

    const body = await request.text();

    const wh = new Webhook(webhookSecret);
    let evt: any;

    try {
      evt = wh.verify(body, {
        "svix-id": svix_id,
        "svix-timestamp": svix_timestamp,
        "svix-signature": svix_signature,
      });
    } catch (err) {
      console.error("Error verifying webhook:", err);
      return new Response("Invalid signature", { status: 400 });
    }

    const eventType = evt.type;
    console.log(`Received webhook: ${eventType}`, evt.data);

    switch (eventType) {
      case "user.created":
      case "user.updated":
        await ctx.runMutation(internal.users.upsertUser, {
          clerkUserId: evt.data.id,
          email: evt.data.email_addresses[0]?.email_address || "",
          name: `${evt.data.first_name || ""} ${evt.data.last_name || ""}`.trim() || "Anonymous",
          imageUrl: evt.data.image_url,
        });
        break;

      case "organization.created":
      case "organization.updated":
        await ctx.runMutation(internal.organizations.syncOrganization, {
          clerkOrgId: evt.data.id,
          name: evt.data.name,
          slug: evt.data.slug,
          imageUrl: evt.data.image_url,
          publicMetadata: evt.data.public_metadata,
          privateMetadata: evt.data.private_metadata,
          maxAllowedMemberships: evt.data.max_allowed_memberships,
          adminDeleteEnabled: evt.data.admin_delete_enabled,
        });
        break;

      case "organizationMembership.created":
      case "organizationMembership.updated":
        await ctx.runMutation(internal.organizations.syncOrganizationMember, {
          clerkUserId: evt.data.public_user_data.user_id,
          clerkOrgId: evt.data.organization.id,
          role: evt.data.role,
          publicMetadata: evt.data.public_metadata,
        });
        break;

      case "organizationMembership.deleted":
        await ctx.runMutation(internal.organizations.removeOrganizationMember, {
          clerkUserId: evt.data.public_user_data.user_id,
          clerkOrgId: evt.data.organization.id,
        });
        break;

      case "organization.deleted":
        await ctx.runMutation(internal.organizations.deleteOrganization, {
          clerkOrgId: evt.data.id,
        });
        break;
    }

    return new Response("Webhook processed", { status: 200 });
  }),
});

export default http;