import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { Id } from "./_generated/dataModel";

// Create cleaning task
export const createCleaningTask = mutation({
  args: {
    name: v.string(),
    description: v.optional(v.string()),
    areaType: v.string(),
    frequency: v.string(),
    estimatedDuration: v.optional(v.number()),
    requiredEquipment: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Get user's organization
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user || !user.organizationId) {
      throw new Error("User must belong to an organization");
    }

    const taskId = await ctx.db.insert("cleaningTasks", {
      ...args,
      organizationId: user.organizationId,
    });

    return taskId;
  },
});

// List cleaning tasks
export const listCleaningTasks = query({
  args: {
    areaType: v.optional(v.string()),
    frequency: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    // Get user's organization
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user || !user.organizationId) {
      return [];
    }

    let query = ctx.db
      .query("cleaningTasks")
      .withIndex("by_org", (q) => q.eq("organizationId", user.organizationId!));

    // Apply filters
    if (args.areaType) {
      query = query.filter((q) => q.eq(q.field("areaType"), args.areaType));
    }
    if (args.frequency) {
      query = query.filter((q) => q.eq(q.field("frequency"), args.frequency));
    }

    return await query.collect();
  },
});

// Get cleaning task by ID
export const getCleaningTask = query({
  args: { taskId: v.id("cleaningTasks") },
  handler: async (ctx, args) => {
    const task = await ctx.db.get(args.taskId);
    return task;
  },
});

// Update cleaning task
export const updateCleaningTask = mutation({
  args: {
    taskId: v.id("cleaningTasks"),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    areaType: v.optional(v.string()),
    frequency: v.optional(v.string()),
    estimatedDuration: v.optional(v.number()),
    requiredEquipment: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Verify task belongs to user's organization
    const task = await ctx.db.get(args.taskId);
    if (!task) {
      throw new Error("Task not found");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user || task.organizationId !== user.organizationId) {
      throw new Error("Unauthorized");
    }

    const { taskId, ...updates } = args;
    await ctx.db.patch(taskId, updates);

    return taskId;
  },
});

// Schedule a cleaning task
export const scheduleCleaningTask = mutation({
  args: {
    taskId: v.id("cleaningTasks"),
    propertyAreaId: v.id("propertyAreas"),
    scheduledDate: v.number(),
    assignedToId: v.optional(v.id("users")),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Verify task and area belong to user's organization
    const [task, area] = await Promise.all([
      ctx.db.get(args.taskId),
      ctx.db.get(args.propertyAreaId),
    ]);

    if (!task || !area) {
      throw new Error("Task or area not found");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user || task.organizationId !== user.organizationId || area.organizationId !== user.organizationId) {
      throw new Error("Unauthorized");
    }

    const scheduledTaskId = await ctx.db.insert("scheduledTasks", {
      ...args,
      status: "scheduled",
      organizationId: user.organizationId,
    });

    return scheduledTaskId;
  },
});

// List scheduled tasks
export const listScheduledTasks = query({
  args: {
    propertyAreaId: v.optional(v.id("propertyAreas")),
    assignedToId: v.optional(v.id("users")),
    status: v.optional(v.string()),
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    // Get user's organization
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user || !user.organizationId) {
      return [];
    }

    let query = ctx.db
      .query("scheduledTasks")
      .withIndex("by_org", (q) => q.eq("organizationId", user.organizationId!));

    // Apply filters
    if (args.propertyAreaId) {
      query = query.filter((q) => q.eq(q.field("propertyAreaId"), args.propertyAreaId));
    }
    if (args.assignedToId) {
      query = query.filter((q) => q.eq(q.field("assignedToId"), args.assignedToId));
    }
    if (args.status) {
      query = query.filter((q) => q.eq(q.field("status"), args.status));
    }
    if (args.startDate) {
      query = query.filter((q) => q.gte(q.field("scheduledDate"), args.startDate!));
    }
    if (args.endDate) {
      query = query.filter((q) => q.lte(q.field("scheduledDate"), args.endDate!));
    }

    const scheduledTasks = await query.collect();

    // Get related data
    const tasksWithDetails = await Promise.all(
      scheduledTasks.map(async (scheduledTask) => {
        const [task, area, assignedTo] = await Promise.all([
          ctx.db.get(scheduledTask.taskId),
          ctx.db.get(scheduledTask.propertyAreaId),
          scheduledTask.assignedToId ? ctx.db.get(scheduledTask.assignedToId) : null,
        ]);

        return {
          ...scheduledTask,
          task,
          area,
          assignedTo,
        };
      })
    );

    return tasksWithDetails;
  },
});

// Update scheduled task status
export const updateScheduledTask = mutation({
  args: {
    scheduledTaskId: v.id("scheduledTasks"),
    status: v.optional(v.string()),
    assignedToId: v.optional(v.id("users")),
    completedDate: v.optional(v.number()),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Verify scheduled task belongs to user's organization
    const scheduledTask = await ctx.db.get(args.scheduledTaskId);
    if (!scheduledTask) {
      throw new Error("Scheduled task not found");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user || scheduledTask.organizationId !== user.organizationId) {
      throw new Error("Unauthorized");
    }

    const { scheduledTaskId, ...updates } = args;
    await ctx.db.patch(scheduledTaskId, updates);

    return scheduledTaskId;
  },
});

// Get cleaning tasks by area type
export const getTasksByAreaType = query({
  args: { areaType: v.string() },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    // Get user's organization
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user || !user.organizationId) {
      return [];
    }

    const tasks = await ctx.db
      .query("cleaningTasks")
      .withIndex("by_area_type", (q) => q.eq("areaType", args.areaType))
      .filter((q) => q.eq(q.field("organizationId"), user.organizationId!))
      .collect();

    return tasks;
  },
});

// Get scheduled tasks for a specific date range
export const getTasksForDateRange = query({
  args: {
    startDate: v.number(),
    endDate: v.number(),
    assignedToId: v.optional(v.id("users")),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    // Get user's organization
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user || !user.organizationId) {
      return [];
    }

    let query = ctx.db
      .query("scheduledTasks")
      .withIndex("by_date", (q) => 
        q.gte("scheduledDate", args.startDate).lte("scheduledDate", args.endDate)
      )
      .filter((q) => q.eq(q.field("organizationId"), user.organizationId!));

    if (args.assignedToId) {
      query = query.filter((q) => q.eq(q.field("assignedToId"), args.assignedToId));
    }

    const scheduledTasks = await query.collect();

    // Get related data
    const tasksWithDetails = await Promise.all(
      scheduledTasks.map(async (scheduledTask) => {
        const [task, area, assignedTo] = await Promise.all([
          ctx.db.get(scheduledTask.taskId),
          ctx.db.get(scheduledTask.propertyAreaId),
          scheduledTask.assignedToId ? ctx.db.get(scheduledTask.assignedToId) : null,
        ]);

        return {
          ...scheduledTask,
          task,
          area,
          assignedTo,
        };
      })
    );

    return tasksWithDetails;
  },
});

// Delete cleaning task
export const deleteCleaningTask = mutation({
  args: { taskId: v.id("cleaningTasks") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Verify task belongs to user's organization
    const task = await ctx.db.get(args.taskId);
    if (!task) {
      throw new Error("Task not found");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user || task.organizationId !== user.organizationId) {
      throw new Error("Unauthorized");
    }

    // Check if task is being used in scheduled tasks
    const scheduledTasks = await ctx.db
      .query("scheduledTasks")
      .filter((q) => q.eq(q.field("taskId"), args.taskId))
      .collect();

    if (scheduledTasks.length > 0) {
      throw new Error("Cannot delete task that has scheduled instances");
    }

    await ctx.db.delete(args.taskId);
    return args.taskId;
  },
});