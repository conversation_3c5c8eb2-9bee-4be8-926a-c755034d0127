import { v } from "convex/values";
import { mutation, query, internalMutation } from "./_generated/server";
import { Id } from "./_generated/dataModel";

// Sync organization from Clerk webhook (internal)
export const syncOrganization = internalMutation({
  args: {
    clerkOrgId: v.string(),
    name: v.string(),
    slug: v.string(),
    imageUrl: v.optional(v.string()),
    publicMetadata: v.optional(v.any()),
    privateMetadata: v.optional(v.any()),
    maxAllowedMemberships: v.optional(v.number()),
    adminDeleteEnabled: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    // Check if organization exists
    const existingOrg = await ctx.db
      .query("organizations")
      .withIndex("by_clerk_id", (q) => q.eq("clerkOrgId", args.clerkOrgId))
      .first();

    const now = Date.now();

    if (existingOrg) {
      // Update existing organization
      await ctx.db.patch(existingOrg._id, {
        ...args,
        updatedAt: now,
      });
      return existingOrg._id;
    } else {
      // Create new organization
      const orgId = await ctx.db.insert("organizations", {
        ...args,
        createdAt: now,
        updatedAt: now,
      });
      return orgId;
    }
  },
});

// Sync organization member from Clerk webhook (internal)
export const syncOrganizationMember = internalMutation({
  args: {
    clerkUserId: v.string(),
    clerkOrgId: v.string(),
    role: v.string(),
    publicMetadata: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    // Get organization
    const organization = await ctx.db
      .query("organizations")
      .withIndex("by_clerk_id", (q) => q.eq("clerkOrgId", args.clerkOrgId))
      .first();

    if (!organization) {
      throw new Error("Organization not found");
    }

    // Check if membership exists
    const existingMembership = await ctx.db
      .query("organizationMembers")
      .withIndex("by_user", (q) => q.eq("clerkUserId", args.clerkUserId))
      .filter((q) => q.eq(q.field("organizationId"), organization._id))
      .first();

    const now = Date.now();

    if (existingMembership) {
      // Update existing membership
      await ctx.db.patch(existingMembership._id, {
        role: args.role,
        publicMetadata: args.publicMetadata,
        updatedAt: now,
      });
      return existingMembership._id;
    } else {
      // Create new membership
      const membershipId = await ctx.db.insert("organizationMembers", {
        organizationId: organization._id,
        clerkUserId: args.clerkUserId,
        clerkOrgId: args.clerkOrgId,
        role: args.role,
        publicMetadata: args.publicMetadata,
        createdAt: now,
        updatedAt: now,
      });

      // Update user's organization
      const user = await ctx.db
        .query("users")
        .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", args.clerkUserId))
        .first();

      if (user) {
        await ctx.db.patch(user._id, {
          organizationId: organization._id,
        });
      }

      return membershipId;
    }
  },
});

// Remove organization member (internal)
export const removeOrganizationMember = internalMutation({
  args: {
    clerkUserId: v.string(),
    clerkOrgId: v.string(),
  },
  handler: async (ctx, args) => {
    // Find and delete membership
    const membership = await ctx.db
      .query("organizationMembers")
      .withIndex("by_user", (q) => q.eq("clerkUserId", args.clerkUserId))
      .filter((q) => q.eq(q.field("clerkOrgId"), args.clerkOrgId))
      .first();

    if (membership) {
      await ctx.db.delete(membership._id);

      // Remove organization from user
      const user = await ctx.db
        .query("users")
        .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", args.clerkUserId))
        .first();

      if (user && user.organizationId === membership.organizationId) {
        await ctx.db.patch(user._id, {
          organizationId: undefined,
        });
      }
    }
  },
});

// Get current organization
export const getCurrentOrganization = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }

    // Get user's organization membership
    const membership = await ctx.db
      .query("organizationMembers")
      .withIndex("by_user", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!membership) {
      return null;
    }

    const organization = await ctx.db.get(membership.organizationId);
    return organization;
  },
});

// Get organization by ID
export const getOrganization = query({
  args: { organizationId: v.id("organizations") },
  handler: async (ctx, args) => {
    const organization = await ctx.db.get(args.organizationId);
    if (!organization) {
      return null;
    }

    // Get member count
    const members = await ctx.db
      .query("organizationMembers")
      .withIndex("by_org", (q) => q.eq("organizationId", args.organizationId))
      .collect();

    return {
      ...organization,
      memberCount: members.length,
    };
  },
});

// List organization members
export const listOrganizationMembers = query({
  args: { organizationId: v.id("organizations") },
  handler: async (ctx, args) => {
    const members = await ctx.db
      .query("organizationMembers")
      .withIndex("by_org", (q) => q.eq("organizationId", args.organizationId))
      .collect();

    // Get user details for each member
    const membersWithUsers = await Promise.all(
      members.map(async (member) => {
        const user = await ctx.db
          .query("users")
          .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", member.clerkUserId))
          .first();

        return {
          ...member,
          user,
        };
      })
    );

    return membersWithUsers;
  },
});

// Delete organization (Clerk webhook - internal)
export const deleteOrganization = internalMutation({
  args: {
    clerkOrgId: v.string(),
  },
  handler: async (ctx, args) => {
    const organization = await ctx.db
      .query("organizations")
      .withIndex("by_clerk_id", (q) => q.eq("clerkOrgId", args.clerkOrgId))
      .first();

    if (!organization) {
      return;
    }

    // Delete all memberships
    const memberships = await ctx.db
      .query("organizationMembers")
      .withIndex("by_org", (q) => q.eq("organizationId", organization._id))
      .collect();

    for (const membership of memberships) {
      await ctx.db.delete(membership._id);
    }

    // Note: In production, you'd want to handle cascading deletes for all
    // organization data (properties, inspections, etc.) or implement soft deletes

    // Delete the organization
    await ctx.db.delete(organization._id);
  },
});