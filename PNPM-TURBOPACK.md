# Using pnpm and Turbopack with Ask ARA

This project is configured to use pnpm as the package manager and Turbopack for faster development builds.

## Prerequisites

- Node.js 18.x or later
- pnpm 8.x or later

If you don't have pnpm installed, you can install it using:

```bash
npm install -g pnpm
```

## Getting Started

1. **Install dependencies**

```bash
pnpm install
```

2. **Start the development server with Turbopack**

```bash
pnpm dev
```

This will start the development server with Turbopack, which provides faster refresh times compared to webpack.

## Available Scripts

- `pnpm dev` - Start the development server with Turbopack
- `pnpm build` - Build the application for production
- `pnpm start` - Start the production server
- `pnpm lint` - Run ESLint
- `pnpm clean` - Remove node_modules, .next, and .turbo directories
- `pnpm clean:cache` - Clean the pnpm store cache
- `pnpm reset` - Reset the project (clean + clean cache + install)

## Database Scripts

- `pnpm db:push` - Push schema changes to the database
- `pnpm db:studio` - Open Drizzle Studio
- `pnpm db:generate` - Generate database schema
- `pnpm db:check` - Check database connection
- `pnpm db:verify` - Verify database schema
- `pnpm db:test` - Test Drizzle ORM
- `pnpm db:migrate` - Run database migrations
- `pnpm db:seed` - Seed the database
- `pnpm db:setup` - Set up the database
- `pnpm db:mcp` - Start the Neon MCP server
- `pnpm db:test-mcp` - Test the MCP connection
- `pnpm db:push-mcp` - Push schema using MCP
- `pnpm test:auth` - Test authentication store

## Troubleshooting

If you encounter any issues with dependencies or caching, try resetting the project:

```bash
pnpm reset
```

This will clean the project, clear the pnpm cache, and reinstall all dependencies.

## Turbopack Configuration

Turbopack configuration is defined in:

1. `next.config.mjs` - Contains Turbopack-specific settings in the `experimental.turbo` section
2. `turbo.json` - Defines the Turborepo pipeline configuration

## pnpm Configuration

pnpm-specific configuration is defined in:

1. `.npmrc` - Contains pnpm behavior settings
2. `package.json` - Contains pnpm-specific settings in the `pnpm` section
