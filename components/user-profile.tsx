"use client"

import { useUserProfile } from "@/hooks/use-user-profile"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { LogOut, Settings, User } from "lucide-react"
import { useClerk } from "@clerk/nextjs"

interface UserProfileProps {
  variant?: "avatar-only" | "full" | "compact"
}

export function UserProfile({ variant = "full" }: UserProfileProps) {
  const { profile, isLoading } = useUserProfile()
  const { signOut } = useClerk()

  if (isLoading || !profile) return null

  // Get initials from name
  const initials = profile.name
    .split(" ")
    .map(n => n[0])
    .join("")
    .toUpperCase()
    .substring(0, 2)

  // Get department/state abbreviation
  const deptAbbr = profile.department || "N/A"

  const handleLogout = () => {
    signOut()
  }

  if (variant === "avatar-only") {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="relative h-8 w-8 rounded-full">
            <Avatar className="h-8 w-8">
              <AvatarImage src={profile.avatar || ""} alt={profile.name} />
              <AvatarFallback>{initials}</AvatarFallback>
            </Avatar>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-medium leading-none">{profile.name}</p>
              <p className="text-xs leading-none text-muted-foreground">{profile.email}</p>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem>
            <User className="mr-2 h-4 w-4" />
            <span>Profile</span>
          </DropdownMenuItem>
          <DropdownMenuItem>
            <Settings className="mr-2 h-4 w-4" />
            <span>Settings</span>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={handleLogout}>
            <LogOut className="mr-2 h-4 w-4" />
            <span>Log out</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }

  if (variant === "compact") {
    return (
      <div className="flex items-center space-x-2">
        <Avatar className="h-8 w-8">
          <AvatarImage src={profile.avatar || ""} alt={profile.name} />
          <AvatarFallback>{initials}</AvatarFallback>
        </Avatar>
        <div className="space-y-0.5">
          <p className="text-sm font-medium leading-none">{profile.name}</p>
          <p className="text-xs leading-none text-muted-foreground">{profile.role}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex items-center space-x-4">
      <Avatar className="h-10 w-10">
        <AvatarImage src={profile.avatar || ""} alt={profile.name} />
        <AvatarFallback>{initials}</AvatarFallback>
      </Avatar>
      <div className="space-y-1">
        <p className="text-sm font-medium leading-none">{profile.name}</p>
        <p className="text-xs leading-none text-muted-foreground">{profile.role}</p>
        <div className="flex items-center text-xs text-muted-foreground">
          <span className="bg-[#A4D321] text-black font-medium px-1.5 py-0.5 rounded text-[10px] mr-1.5">
            {deptAbbr}
          </span>
          {profile.email}
        </div>
      </div>
    </div>
  )
}
