"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { Download, X } from "lucide-react"

interface InstallPromptProps {
  forceShow?: boolean
}

export function InstallPrompt({ forceShow = false }: InstallPromptProps) {
  const [showPrompt, setShowPrompt] = useState(false)
  const [deferredPrompt, setDeferredPrompt] = useState<Event | null>(null) // Use Event | null
  const [isIOS, setIsIOS] = useState(false)
  const [isInstalled, setIsInstalled] = useState(false)

  useEffect(() => {
    // Check if the app is already installed
    if (window.matchMedia("(display-mode: standalone)").matches) {
      setIsInstalled(true)
      return
    }

    // Detect iOS device
    const isIOSDevice = /iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as any).MSStream
    setIsIOS(isIOSDevice)

    // Listen for the beforeinstallprompt event
    window.addEventListener("beforeinstallprompt", (e) => {
      // Prevent Chrome 67 and earlier from automatically showing the prompt
      e.preventDefault()
      // Stash the event so it can be triggered later
      setDeferredPrompt(e)

      // Only show if explicitly requested via props
      if (forceShow) {
        setShowPrompt(true)
      }
    })

    // Hide the prompt if the app is installed
    window.addEventListener("appinstalled", () => {
      setShowPrompt(false)
      setIsInstalled(true)
    })

    return () => {
      window.removeEventListener("beforeinstallprompt", () => {})
      window.removeEventListener("appinstalled", () => {})
    }
  }, [forceShow])

  const handleInstallClick = async () => {
    if (!deferredPrompt) return

    // Show the install prompt
    deferredPrompt.prompt()
    // Wait for the user to respond to the prompt
    // @ts-expect-error - userChoice exists on BeforeInstallPromptEvent but TS doesn't know
    const { outcome } = await deferredPrompt.userChoice;
    console.log(`User response to the install prompt: ${outcome}`)
    // We've used the prompt, and can't use it again, throw it away
    setDeferredPrompt(null)
    setShowPrompt(false)
  }

  const closePrompt = () => {
    setShowPrompt(false)

    // Store in localStorage that the user dismissed the prompt
    localStorage.setItem("installPromptDismissed", "true")
  }

  if (isInstalled || (!showPrompt && !forceShow)) return null

  return (
    <motion.div
      className="fixed bottom-20 left-4 right-4 z-50 bg-zinc-900 border border-zinc-800 rounded-lg shadow-lg overflow-hidden"
      initial={{ y: 100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      exit={{ y: 100, opacity: 0 }}
      transition={{ type: "spring", damping: 20, stiffness: 300 }}
    >
      <div className="flex items-start p-4">
        <div className="flex-1">
          <h3 className="text-white text-base font-medium mb-1">Install AskARA</h3>
          {isIOS ? (
            <p className="text-zinc-400 text-sm">
              Tap{" "}
              <span className="inline-flex items-center">
                <svg className="w-4 h-4 mx-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M12 4V20M20 12L4 12"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </span>
              then &quot;Add to Home Screen&quot; to install
            </p>
          ) : (
            <p className="text-zinc-400 text-sm">Install this app on your device for quick access</p>
          )}
        </div>

        <button onClick={closePrompt} className="text-zinc-500 hover:text-white transition-colors" aria-label="Close">
          <X className="w-5 h-5" />
        </button>
      </div>

      {!isIOS && (
        <div className="px-4 pb-4">
          <button
            onClick={handleInstallClick}
            className="w-full py-2.5 bg-gradient-to-r from-[#3D4D61] to-[#A4D321] text-white font-medium rounded-md flex items-center justify-center"
          >
            <Download className="w-4 h-4 mr-2" />
            Install App
          </button>
        </div>
      )}
    </motion.div>
  )
}
