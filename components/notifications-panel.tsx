"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Bell, X, Check, AlertTriangle, Info } from "lucide-react"

interface Notification {
  id: string
  title: string
  message: string
  timestamp: string
  isRead: boolean
  priority: "low" | "medium" | "high"
}

interface NotificationsPanelProps {
  isOpen: boolean
  onClose: () => void
}

export function NotificationsPanel({ isOpen, onClose }: NotificationsPanelProps) {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (isOpen) {
      fetchNotifications()
    }
  }, [isOpen])

  const fetchNotifications = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch("/api/notifications")

      if (!response.ok) {
        throw new Error("Failed to fetch notifications")
      }

      const data = await response.json()
      setNotifications(data.notifications)
    } catch (err) {
      console.error("Error fetching notifications:", err)
      setError("Failed to load notifications. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const markAsRead = async (notificationId: string) => {
    try {
      await fetch("/api/notifications", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ notificationId }),
      })

      // Update local state
      setNotifications((prev) =>
        prev.map((notification) =>
          notification.id === notificationId ? { ...notification, isRead: true } : notification,
        ),
      )
    } catch (err) {
      console.error("Error marking notification as read:", err)
    }
  }

  const getPriorityIcon = (priority: string) => {
    switch (priority.toLowerCase()) {
      case "high":
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      case "medium":
        return <AlertTriangle className="h-4 w-4 text-amber-500" />
      default:
        return <Info className="h-4 w-4 text-blue-500" />
    }
  }

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()

    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 1) {
      return "Just now"
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      return `${diffInDays}d ago`
    }
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          <motion.div
            className="fixed inset-0 bg-black/70 backdrop-blur-sm z-40"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />

          <motion.div
            className="fixed top-16 right-4 w-[90%] max-w-md bg-[#121624] rounded-xl overflow-hidden z-50 max-h-[80vh] flex flex-col"
            initial={{ opacity: 0, y: -20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -20, scale: 0.95 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
          >
            <div className="p-4 border-b border-[#ffffff]/10 flex items-center justify-between">
              <div className="flex items-center">
                <Bell className="h-5 w-5 text-[#A4D321] mr-2" />
                <h2 className="text-white text-lg font-medium">Notifications</h2>
              </div>
              <motion.button
                className="w-8 h-8 rounded-full bg-[#ffffff]/10 flex items-center justify-center"
                whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.2)" }}
                onClick={onClose}
              >
                <X className="h-4 w-4 text-white" />
              </motion.button>
            </div>

            <div className="flex-1 overflow-y-auto">
              {isLoading ? (
                <div className="flex items-center justify-center p-8">
                  <div className="w-6 h-6 border-2 border-[#A4D321] border-t-transparent rounded-full animate-spin" />
                </div>
              ) : error ? (
                <div className="p-6 text-center">
                  <p className="text-red-400 mb-4">{error}</p>
                  <button className="px-4 py-2 bg-[#3D4D61] text-white rounded-md text-sm" onClick={fetchNotifications}>
                    Try Again
                  </button>
                </div>
              ) : notifications.length === 0 ? (
                <div className="p-6 text-center">
                  <p className="text-[#ffffffb2]">No notifications</p>
                </div>
              ) : (
                <div className="divide-y divide-[#ffffff]/10">
                  {notifications.map((notification) => (
                    <motion.div
                      key={notification.id}
                      className={`p-4 ${notification.isRead ? "bg-transparent" : "bg-[#ffffff]/5"}`}
                      whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
                    >
                      <div className="flex">
                        <div className="mr-3 mt-1">{getPriorityIcon(notification.priority)}</div>
                        <div className="flex-1">
                          <div className="flex justify-between items-start">
                            <h3 className="text-white text-sm font-medium">{notification.title}</h3>
                            <span className="text-[#ffffffb2] text-xs">{formatTimestamp(notification.timestamp)}</span>
                          </div>
                          <p className="text-[#ffffffb2] text-xs mt-1">{notification.message}</p>

                          {!notification.isRead && (
                            <motion.button
                              className="mt-2 text-[#A4D321] text-xs flex items-center"
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              onClick={() => markAsRead(notification.id)}
                            >
                              <Check className="h-3 w-3 mr-1" />
                              Mark as read
                            </motion.button>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}
