'use client'

// Removed unused useVoiceAssistant
import Message from './Message'
import TextAnimation from './TextAnimation'
import { type Role, useConversation } from '@11labs/react'
// Removed unused useParams
import React, { useCallback, useEffect, useState } from 'react' // Added React import
import { X } from 'react-feather' // Removed unused GitHub
import { toast } from 'sonner'

export default function AssistantChat() { // Renamed component
  // const { slug } = useParams() // Commenting out slug usage for now
  const [currentText, setCurrentText] = useState('')
  const [messages, setMessages] = useState<unknown[]>([]) // Use unknown[]
  const [isTranscriptOpen, setIsTranscriptOpen] = useState(false)
  // --- Client-side only logic starts here ---

  // Simplified loadConversation - assuming no slug needed for now
  const loadConversation = () => {
    // fetch(`/api/c?id=${slug}`) // Removed slug
    fetch(`/api/c`) // Updated API path
      .then((res) => res.json())
      .then((res) => {
        if (res.length > 0) {
          setMessages(
            res.map((i: unknown) => ({ // Use unknown
              ...(i as Record<string, unknown>), // Cast to record for spreading
              formatted: {
                text: i.content_transcript,
                transcript: i.content_transcript,
              },
            })),
          )
        }
      })
  }

  const conversation = useConversation({
    onError: (error: string) => { toast(error) },
    onConnect: () => { toast('Connected to ElevenLabs.') },
    onMessage: (props: { message: string; source: Role }) => {
      const { message, source } = props
      if (source === 'ai') setCurrentText(message)
      fetch('/api/c', { // API path is correct
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          // id: slug, // Removed slug
          item: {
            type: 'message',
            status: 'completed',
            object: 'realtime.item',
            id: 'item_' + Math.random(),
            role: source === 'ai' ? 'assistant' : 'user',
            content: [{ type: 'text', transcript: message }],
          },
        }),
      }).then(loadConversation)
    },
  })

  const connectConversation = useCallback(async () => {
    toast('Setting up ElevenLabs...')
    if (typeof window !== 'undefined') {
      try {
        await navigator.mediaDevices.getUserMedia({ audio: true })
        const response = await fetch('/api/i', { // API path is correct
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
        })
        const data = await response.json()
        if (data.error) return toast(data.error)
        await conversation.startSession({ signedUrl: data.apiKey })
      } catch (_error) { // Prefix unused variable
        toast('Failed to set up ElevenLabs client :/')
      }
    } else {
      console.warn('navigator.mediaDevices is not available in this environment.')
      toast('Microphone access is not available in this environment.')
      // Optionally set an error state or handle appropriately
    }
  }, [conversation])

  const disconnectConversation = useCallback(async () => {
    await conversation.endSession()
  }, [conversation])

  const handleStartListening = () => {
    if (conversation.status !== 'connected') connectConversation()
  }

  const handleStopListening = () => {
    if (conversation.status === 'connected') disconnectConversation()
  }

  useEffect(() => {
    // Initial load without slug dependency
    loadConversation();

    return () => {
      disconnectConversation()
    }
    // }, [slug]) // Removed slug dependency
  }, [disconnectConversation]) // Add disconnectConversation dependency

  // --- Client-side only logic ends here ---

  // The return statement is now part of the client-side logic
  return (
    <>
      {/* Removed GitHub link and Neon/ElevenLabs attribution for now */}
      {/* ... (rest of the commented out JSX remains the same) ... */}
      <TextAnimation currentText={currentText} isAudioPlaying={conversation.isSpeaking} onStopListening={handleStopListening} onStartListening={handleStartListening} />
      {messages.length > 0 && (
        <button className="text-sm fixed top-2 right-4 underline" onClick={() => setIsTranscriptOpen(!isTranscriptOpen)}>
          Show Transcript
        </button>
      )}
      {isTranscriptOpen && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
          <div className="bg-white text-black p-4 rounded shadow-lg max-w-[90%] max-h-[90%] overflow-y-scroll">
            <div className="flex flex-row items-center justify-between">
              <span>Transcript</span>
              <button onClick={() => setIsTranscriptOpen(false)}>
                <X />
              </button>
            </div>
            <div className="border-t py-4 mt-4 flex flex-col gap-y-4">
              {messages.map((conversationItem) => (
                <Message key={conversationItem.id} conversationItem={conversationItem} />
              ))}
            </div>
          </div>
        </div>
      )}
    </>
  )
}