'use client'

import { useUser } from '@/contexts/user-context'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Loader2 } from 'lucide-react'

export function AuthTest() {
  const { user, isLoading, error, logout, refreshUserProfile, isSignedIn } = useUser()

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Authentication Test</CardTitle>
        <CardDescription>Testing Clerk integration with our database</CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center py-4">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
          </div>
        ) : error ? (
          <div className="p-4 bg-destructive/10 text-destructive rounded-md">
            <p>Error: {error}</p>
          </div>
        ) : !isSignedIn ? (
          <div className="p-4 bg-muted rounded-md">
            <p>You are not signed in.</p>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="p-4 bg-muted rounded-md">
              <h3 className="font-medium">User Profile</h3>
              <pre className="mt-2 text-sm overflow-auto p-2 bg-background rounded">
                {JSON.stringify(user, null, 2)}
              </pre>
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={refreshUserProfile}
          disabled={isLoading || !isSignedIn}
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Loading...
            </>
          ) : (
            'Refresh Profile'
          )}
        </Button>
        {isSignedIn && (
          <Button 
            variant="destructive" 
            onClick={logout}
            disabled={isLoading}
          >
            Sign Out
          </Button>
        )}
      </CardFooter>
    </Card>
  )
}
