"use client"

import { useState, useEffect } from "react"
import { useVoiceAssistant } from "@/components/voice-assistant/voice-assistant-provider"
import { useUser } from "@/contexts/user-context"
import { SettingsScreenContent } from "@/components/screens/settings-screen"
import { ProfileScreen } from "@/components/screens/profile-screen"
import { InspectionDashboard } from "@/components/screens/inspection-dashboard"
import { ChatScreen } from "@/components/screens/chat-screen"
import LoginScreen from "@/components/screens/login-screen"
import SignUpScreen from "@/components/screens/signup-screen"
import ForgotPasswordScreen from "@/components/screens/forgot-password-screen"
import { AppHeader } from "@/components/ui/app-header"
import { StatusBar } from "@/components/ui/status-bar"
import { NavigationBar } from "@/components/ui/navigation-bar"
import { HomeIndicator } from "@/components/ui/home-indicator"
import { SwipeableContainer } from "@/components/ui/swipeable-container"
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'

import { DashboardScreen } from "@/components/screens/dashboard-screen"
import { TasksScreen } from "@/components/screens/tasks-screen"
import { KnowledgeBaseScreen } from "@/components/screens/knowledge-base-screen"
import { ScheduleScreen } from "@/components/screens/schedule-screen"
import { TeamScreen } from "@/components/screens/team-screen"
import { ReportsScreen } from "@/components/screens/reports-screen"
import { HelpScreen } from "@/components/screens/help-screen"
import { MoreMenuScreen } from "@/components/screens/more-menu-screen"
import { InspectionFormScreen } from "@/components/screens/inspection-form-screen"
import { InspectionReportScreen } from "@/components/screens/inspection-report-screen"
import { CorrectiveActionsScreen } from "@/components/screens/corrective-actions-screen"

// Voice Assistant Screen (Left Panel)
function VoiceAssistantScreen() {
  const {
    state,
    messages,
    currentMessage,
    isAudioPlaying,
    startListening,
    stopListening,
    toggleListening,
    clearConversation
  } = useVoiceAssistant()

  return (
    <div className="flex flex-col h-full w-full bg-gray-900 text-white">
      <div className="p-4 border-b border-gray-800 flex justify-between items-center">
        <h2 className="text-xl font-semibold">Voice Assistant</h2>
        <div className="flex space-x-2">
          <button
            onClick={clearConversation}
            className="p-2 rounded-full hover:bg-gray-800 transition-colors"
            aria-label="Clear conversation"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M3 6h18"></path>
              <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
              <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
            </svg>
          </button>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-gray-400">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"></path>
              <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
              <line x1="12" x2="12" y1="19" y2="22"></line>
            </svg>
            <p className="mt-4">Tap the microphone button to start a conversation</p>
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              className={`p-3 rounded-lg ${message.role === 'user' ? 'bg-blue-900/30 ml-8' : 'bg-gray-800 mr-8'}`}
            >
              <p className="text-sm font-medium mb-1">
                {message.role === 'user' ? 'You' : 'Assistant'}
              </p>
              <p>{message.content}</p>
            </div>
          ))
        )}
        {currentMessage && state === 'speaking' && (
          <div className="p-3 rounded-lg bg-gray-800 mr-8 border border-blue-500 animate-pulse">
            <p className="text-sm font-medium mb-1">Assistant</p>
            <p>{currentMessage}</p>
          </div>
        )}
      </div>

      <div className="p-4 border-t border-gray-800">
        <div className="flex justify-center">
          <button
            onClick={toggleListening}
            className={`p-4 rounded-full ${state === 'idle' || state === 'error' ? 'bg-blue-600 hover:bg-blue-700' : 'bg-red-600 hover:bg-red-700'} transition-colors`}
            disabled={state === 'connecting'}
            aria-label={state === 'idle' || state === 'error' ? 'Start listening' : 'Stop listening'}
          >
            {state === 'connecting' ? (
              <svg className="animate-spin h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            ) : state === 'idle' || state === 'error' ? (
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"></path>
                <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                <line x1="12" x2="12" y1="19" y2="22"></line>
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            )}
          </button>
        </div>
        <div className="text-center mt-2 text-sm text-gray-400">
          {state === 'idle' && 'Tap to speak'}
          {state === 'connecting' && 'Connecting...'}
          {state === 'listening' && 'Listening...'}
          {state === 'processing' && 'Processing...'}
          {state === 'speaking' && 'Speaking...'}
          {state === 'error' && 'Error. Tap to retry.'}
        </div>
      </div>
    </div>
  )
}

export function MobileAIInterface() {
  const { user, isLoading, isSignedIn, logout, refreshUserProfile } = useUser()
  const [currentScreen, setCurrentScreen] = useState("dashboard")
  const [apiKey, setApiKey] = useState("")
  const { toggleListening, state, currentMessage } = useVoiceAssistant()
  const isRecording = state === 'listening' || state === 'processing' || state === 'speaking'
  const [swipeIndex, setSwipeIndex] = useState(1)
  const router = useRouter()

  // Effect to check authentication and refresh user profile
  useEffect(() => {
    const savedApiKey = localStorage.getItem("apiKey")
    if (savedApiKey) {
      setApiKey(savedApiKey)
    }
    
    // If user is signed in via Clerk but we don't have their profile yet
    if (isSignedIn && !isLoading) {
      // Try to refresh user profile from our database
      refreshUserProfile().catch(error => {
        console.error("Failed to fetch user profile:", error)
        toast.error("Failed to load user profile")
      })
    }
  }, [isSignedIn, isLoading, refreshUserProfile])

  // Handle navigation
  const handleNavigate = (screen: string) => {
    if (screen === "logout") {
      handleLogout()
    } else if (screen === "chat") {
      setSwipeIndex(2)
      setCurrentScreen("chat")
    } else if (screen === "dashboard") {
      setSwipeIndex(1)
      setCurrentScreen("dashboard")
    } else if (screen === "stream-assistant") {
      setSwipeIndex(0)
      setCurrentScreen("stream-assistant")
    } else if (screen === "signup") {
      router.push('/sign-up')
    } else if (screen === "signin" || screen === "login") {
      router.push('/sign-in')
    } else if (screen === "forgot-password") {
      router.push('/forgot-password')
    } else {
      setCurrentScreen(screen)
    }
  }

  const handleLogout = async () => {
    setApiKey("")
    localStorage.removeItem("apiKey")
    
    try {
      await logout()
      router.push('/sign-in')
    } catch (error) {
      console.error("Logout failed:", error)
      toast.error("Logout failed")
    }
  }

  const toggleMicrophone = async () => {
    await toggleListening()
  }

  // Create the content for each drawer
  const settingsContent = (
    <SettingsScreenContent onNavigate={handleNavigate} />
  )

  const profileContent = <ProfileScreen onNavigate={handleNavigate} userData={{
    name: user?.name || "",
    role: user?.role || "",
    department: user?.department || ""
  }} isDrawer={true} />

  const notificationsContent = (
    <div className="p-4 h-full overflow-y-auto">
      <div className="space-y-4">
        <div className="p-6 text-center">
          <p className="text-[#ffffffb2]">No notifications</p>
        </div>
      </div>
    </div>
  )

  // Loading state while checking authentication
  if (isLoading) {
    return (
      <div className="w-full h-screen flex items-center justify-center bg-black">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#A4D321] mx-auto mb-4"></div>
          <p className="text-white">Loading...</p>
        </div>
      </div>
    )
  }

  // If user is not signed in, redirect to sign-in page
  if (!isSignedIn) {
    router.push('/sign-in')
    return null
  }

  // User is authenticated, show main app UI
  return (
    <div className="w-full h-screen min-h-screen bg-black overflow-hidden flex flex-col">
      <StatusBar />

      <AppHeader
        userName={user?.name || ""}
        userRole={user?.role || ""}
        settingsContent={settingsContent}
        profileContent={profileContent}
        notificationsContent={notificationsContent}
      />

      <div className="flex-1 overflow-hidden pt-[68px]">
        {(currentScreen === "dashboard" || currentScreen === "chat" || currentScreen === "stream-assistant") ? (
          <SwipeableContainer
            activeIndex={swipeIndex}
            onIndexChange={setSwipeIndex}
          >
            <VoiceAssistantScreen />

            <DashboardScreen
              onNavigate={handleNavigate}
              userData={{
                name: user?.name || "",
                role: user?.role || "",
                department: user?.department || ""
              }}
            />

            <div className="flex flex-col h-full">
              <ChatScreen apiKey={apiKey} onNavigate={handleNavigate} userData={{
                name: user?.name || "",
                role: user?.role || "",
                department: user?.department || ""
              }} />
            </div>
          </SwipeableContainer>
        ) : currentScreen === "tasks" ? (
          <TasksScreen onNavigate={handleNavigate} userData={{
            name: user?.name || "",
            role: user?.role || "",
            department: user?.department || ""
          }} />
        ) : currentScreen === "knowledge" ? (
          <KnowledgeBaseScreen onNavigate={handleNavigate} userData={{
            name: user?.name || "",
            role: user?.role || "",
            department: user?.department || ""
          }} />
        ) : currentScreen === "schedule" ? (
          <ScheduleScreen onNavigate={handleNavigate} userData={{
            name: user?.name || "",
            role: user?.role || "",
            department: user?.department || ""
          }} />
        ) : currentScreen === "team" ? (
          <TeamScreen onNavigate={handleNavigate} userData={{
            name: user?.name || "",
            role: user?.role || "",
            department: user?.department || ""
          }} />
        ) : currentScreen === "reports" ? (
          <ReportsScreen onNavigate={handleNavigate} userData={{
            name: user?.name || "",
            role: user?.role || "",
            department: user?.department || ""
          }} />
        ) : currentScreen === "help" ? (
          <HelpScreen onNavigate={handleNavigate} userData={{
            name: user?.name || "",
            role: user?.role || "",
            department: user?.department || ""
          }} />
        ) : currentScreen === "more" ? (
          <MoreMenuScreen onNavigate={handleNavigate} userData={{
            name: user?.name || "",
            role: user?.role || "",
            department: user?.department || ""
          }} />
        ) : currentScreen === "inspection-form" ? (
          <InspectionFormScreen onNavigate={handleNavigate} userData={{
            name: user?.name || "",
            role: user?.role || "",
            department: user?.department || ""
          }} />
        ) : currentScreen === "inspection-report" ? (
          <InspectionReportScreen onNavigate={handleNavigate} userData={{
            name: user?.name || "",
            role: user?.role || "",
            department: user?.department || ""
          }} />
        ) : currentScreen === "corrective-actions" ? (
          <CorrectiveActionsScreen onNavigate={handleNavigate} userData={{
            name: user?.name || "",
            role: user?.role || "",
            department: user?.department || ""
          }} />
        ) : (
          <InspectionDashboard onNavigate={handleNavigate} userData={{
            name: user?.name || "",
            role: user?.role || "",
            department: user?.department || ""
          }} />
        )}
      </div>

      <NavigationBar
        activeScreen={currentScreen}
        onNavigate={handleNavigate}
        isRecording={isRecording}
        onVoiceToggle={toggleMicrophone}
        currentTranscription={currentMessage}
      />
      <HomeIndicator />
    </div>
  )
}
