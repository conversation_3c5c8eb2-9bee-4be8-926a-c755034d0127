"use client"

import { useState } from "react"
import { LogoButton } from "@/components/ui/logo-button"
import { useVoiceAssistant } from "@/components/voice-assistant/voice-assistant-provider"

export function TestAskARA() {
  const { toggleListening, state, messages, currentMessage } = useVoiceAssistant()
  const isRecording = state === 'listening' || state === 'processing' || state === 'speaking'
  // Removed unused 'transcription' state
  const [_transcription, setTranscription] = useState("") // Keep structure for potential use

  const toggleMicrophone = async () => {
    await toggleListening()
    if (state === 'idle' || state === 'error') {
      setTranscription("")
    }
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-black p-4">
      <h1 className="text-2xl font-bold text-white mb-8">AskARA Test Page</h1>
      
      <div className="mb-8">
        <LogoButton onClick={toggleMicrophone} isActive={isRecording} size={80} />
        <p className="text-white mt-4 text-center">
          {isRecording ? "Listening..." : "Click to speak with AskARA"}
        </p>
      </div>

      {currentMessage && (
        <div className="bg-zinc-800 p-4 rounded-lg max-w-md w-full">
          <p className="text-white">{currentMessage}</p>
        </div>
      )}

      {messages.length > 0 && (
        <div className="mt-8 w-full max-w-md">
          <h2 className="text-xl font-bold text-white mb-4">Conversation History</h2>
          <div className="space-y-4">
            {messages.map((message) => (
              <div 
                key={message.id} 
                className={`p-3 rounded-lg ${
                  message.role === 'assistant' 
                    ? 'bg-[#A4D321]/20 text-white' 
                    : 'bg-zinc-800 text-white'
                }`}
              >
                <p className="text-xs text-zinc-400 mb-1">
                  {message.role === 'assistant' ? 'AskARA' : 'You'}
                </p>
                <p>{message.content}</p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
