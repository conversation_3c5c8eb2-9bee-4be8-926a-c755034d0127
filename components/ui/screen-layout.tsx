"use client"

import type React from "react"
import { StatusBar } from "@/components/ui/status-bar"
import { NavigationBar } from "@/components/ui/navigation-bar"
import { HomeIndicator } from "@/components/ui/home-indicator"
import { AppHeader } from "@/components/ui/app-header"
import { ScrollArea } from "@/components/ui/scroll-area"

interface ScreenLayoutProps {
  title: string
  subtitle?: string
  children: React.ReactNode
  onNavigate: (screen: string) => void
  activeScreen: string
  showControls?: boolean
  headerRightContent?: React.ReactNode
  fullScreen?: boolean
  padded?: boolean
  hideHeader?: boolean
}

export function ScreenLayout({
  title,
  subtitle,
  children,
  onNavigate,
  activeScreen,
  showControls = true,
  // Removed unused headerRightContent
  fullScreen = false,
  padded = true,
  hideHeader = false,
}: ScreenLayoutProps) {
  return (
    <div className="flex flex-col w-full h-full min-h-screen relative">
      <StatusBar />

      {!hideHeader && (
        <AppHeader
          userName={title}
          userRole={subtitle}
          showControls={showControls}
        />
      )}

      {fullScreen ? (
        children
      ) : (
        <ScrollArea className="flex-1 w-full pb-20 pt-[68px]">
          <div className={padded ? "p-6" : ""}>{children}</div>
        </ScrollArea>
      )}

      <NavigationBar activeScreen={activeScreen} onNavigate={onNavigate} />
      <HomeIndicator />
    </div>
  )
}
