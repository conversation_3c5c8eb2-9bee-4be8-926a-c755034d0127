"use client"
import { useState, useEffect, useRef } from "react"
import { motion } from "framer-motion"
import { LogoButton } from "@/components/ui/logo-button"
import { Send, Home, Bell, Settings, CheckSquare, ClipboardList, FileText, MoreHorizontal, BarChart3, Users } from "lucide-react" // Removed unused Bot
import { SignedIn, SignedOut, UserButton } from "@clerk/nextjs";
import Link from "next/link";

interface NavigationBarProps {
  activeScreen: string
  onNavigate: (screen: string) => void
  isRecording?: boolean
  onVoiceToggle?: () => void
  currentTranscription?: string
  audioData?: number[]
}

export function NavigationBar({
  activeScreen,
  onNavigate,
  isRecording = false,
  onVoiceToggle,
  currentTranscription = "",
  audioData = []
}: NavigationBarProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [isCursorVisible, setIsCursorVisible] = useState(true)
  const navbarRef = useRef<HTMLDivElement>(null)

  // Ensure uniform icon sizes (e.g., w-5 h-5)
  const iconSize = "w-5 h-5"; // Define uniform size
  const navigationItems = [
    { id: "dashboard", icon: <Home className={iconSize} />, label: "Home" },
    { id: "schedule", icon: <ClipboardList className={iconSize} />, label: "Schedule" },
    { id: "tasks", icon: <CheckSquare className={iconSize} />, label: "Tasks" },
    { id: "reports", icon: <BarChart3 className={iconSize} />, label: "Reports" },
    { id: "contacts", icon: <Users className={iconSize} />, label: "Contacts" },
    { id: "team", icon: <Users className={iconSize} />, label: "Team" },
    { id: "knowledge", icon: <FileText className={iconSize} />, label: "Knowledge" },
    { id: "more", icon: <MoreHorizontal className={iconSize} />, label: "More" },
  ]

  // Start cursor blinking
  useEffect(() => {
    const cursorInterval = setInterval(() => {
      setIsCursorVisible((prev) => !prev)
    }, 500)

    return () => clearInterval(cursorInterval)
  }, [])

  const handleAssistantToggle = () => {
    setIsExpanded(!isExpanded)

    if (isExpanded && onVoiceToggle && isRecording) {
      onVoiceToggle()
    }
  }

  const handleVoiceToggle = () => {
    if (onVoiceToggle) {
      onVoiceToggle()
    }
  }

  const handleSendTranscription = () => {
    if (currentTranscription.trim()) {
      console.log("Sending transcription:", currentTranscription)
      setIsExpanded(false)
    }
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50">
      {/* Floating Navigation Bar with Chat Interface */}
      <div
        ref={navbarRef}
        className="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-[95%] max-w-[500px] transition-all duration-300 ease-in-out"
        style={{ height: isExpanded ? "350px" : "60px" }}
      >
        <motion.div
          className="w-full h-full bg-black/40 backdrop-blur-lg rounded-2xl border border-white/10 shadow-lg overflow-hidden flex flex-col"
          initial={{ borderRadius: 20 }}
          animate={{ borderRadius: 20 }}
        >
          {/* Chat Area (visible when expanded) */}
          {isExpanded && (
            <div className="flex-1 flex flex-col overflow-hidden">
              {/* Header */}
              <div className="flex items-center p-3 border-b border-[#ffffff]/10">
                <div className="flex items-center">
                  <LogoButton size={32} onClick={handleVoiceToggle} isActive={isRecording} />
                  <div className="ml-2">
                    <p className="text-[#ffffffb2] text-xs">{isRecording ? "Listening..." : "Tap to speak"}</p>
                  </div>
                </div>
                <div className="ml-auto flex items-center gap-2">
                  <button className="p-1.5 rounded-full hover:bg-[#ffffff]/10 text-[#ffffffb2]">
                    <Bell className="h-4 w-4" />
                  </button>
                  <button className="p-1.5 rounded-full hover:bg-[#ffffff]/10 text-[#ffffffb2]">
                    <Settings className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* Response Area - Make this flex-1 to fill available space */}
              <div className="flex-1 p-3 overflow-y-auto">
                <div className="space-y-2">
                  {/* Sample response cards would go here */}
                  <div className="bg-black/30 rounded-lg p-3 border border-white/10">
                    <p className="text-white text-sm">How can I help you today?</p>
                  </div>
                </div>
              </div>

              {/* Input Area - Fixed at bottom */}
              <div className="p-3 border-t border-[#ffffff]/10">
                <div className="flex items-center space-x-2">
                  <motion.div initial={{ x: 0 }} animate={{ x: 0 }} className="flex-shrink-0">
                    <LogoButton size={32} onClick={handleVoiceToggle} isActive={isRecording} />
                  </motion.div>

                  <div className="flex-1 bg-black/30 rounded-lg border border-white/20 px-3 py-2 min-h-[36px]">
                    <p className="text-white text-sm">
                      {currentTranscription}
                      {isCursorVisible && <span className="inline-block w-1 h-3.5 bg-[#A4D321] ml-1 animate-pulse" />}
                    </p>
                  </div>

                  <motion.button
                    className="p-2 rounded-full bg-gradient-to-br from-black to-[#A4D321] text-white shadow-sm"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={handleSendTranscription}
                  >
                    <Send className="h-4 w-4" />
                  </motion.button>
                </div>
              </div>
            </div>
          )}

          {/* Navigation Bar - Adjust padding */}
          {/* Navigation Bar - Conditionally render based on auth state */}
          <div className="grid grid-cols-9 w-full h-[60px] items-center px-2">
            <SignedIn>
              {/* Render navigation items for signed-in users */}
              {navigationItems.map((item, index) => {
                 // Skip rendering the 'more' item to make space for UserButton
                 if (item.id === 'more') return null;

                 const isActive = item.id === activeScreen
                 // Original grid logic: 4 items, gap (col 5), 4 items (cols 6-9)
                 // We'll use cols 1-4 for the first 4 items, skip 5, use 6-8 for next 3 items
                 const gridColumn = index < 4 ? index + 1 : index + 2; // index 4 -> col 6, index 5 -> col 7, index 6 -> col 8
                 const commonClasses = `relative h-full cursor-pointer flex items-center justify-center p-2`
                 const iconClasses = `
                   ${
                     isActive
                       ? "text-[#A4D321] drop-shadow-[0_0_3px_rgba(164,211,33,0.7)]"
                       : "text-[#ffffffb2]"
                   }
                 `
                 // Only render items up to index 6 (knowledge) to fit UserButton in col 9
                 if (index < 7) {
                   return (
                     <div
                       key={item.id}
                       className={commonClasses}
                       style={{ gridColumnStart: gridColumn }}
                       onClick={() => onNavigate(item.id)}
                     >
                       <div className={iconClasses}>{item.icon}</div>
                     </div>
                   )
                 }
                 return null; // Don't render items beyond index 6 for now
              })}
              {/* UserButton - Place it in the last column */}
              <div className="relative h-full flex items-center justify-center p-2" style={{ gridColumnStart: 9 }}>
                <UserButton afterSignOutUrl="/" />
              </div>
            </SignedIn>
            <SignedOut>
              {/* Render sign-in/sign-up links for signed-out users */}
              {/* Place links roughly in the middle columns */}
              <div className="flex justify-center items-center space-x-4" style={{ gridColumnStart: 4, gridColumnEnd: 7 }}>
                 <Link href="/sign-in" className="text-[#ffffffb2] hover:text-[#A4D321] text-sm font-medium">
                   Sign In
                 </Link>
                 <Link href="/sign-up" className="text-[#ffffffb2] hover:text-[#A4D321] text-sm font-medium">
                   Sign Up
                 </Link>
              </div>
            </SignedOut>
          </div>
        </motion.div>
      </div>

      {/* Center Button - LogoButton */}
      <div className="fixed bottom-[28px] left-1/2 transform -translate-x-1/2 z-50">
        <LogoButton onClick={handleAssistantToggle} isActive={isRecording} size={50} />
      </div>

      {/* Audio Visualizer */}
      {isRecording && (
        <div className="fixed bottom-[60px] left-0 right-0 h-24 pointer-events-none z-40">
          <svg width="100%" height="100%" preserveAspectRatio="none">
            <defs>
              <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#000000" />
                <stop offset="100%" stopColor="#A4D321" />
              </linearGradient>
            </defs>
            <motion.path
              fill="none"
              stroke="url(#gradient)"
              strokeWidth="2"
              strokeLinecap="round"
              initial={{ opacity: 0 }}
              animate={{
                opacity: 1,
                d: generateWaveformPath(audioData)
              }}
              transition={{
                opacity: { duration: 0.2 }
              }}
            />
          </svg>
        </div>
      )}
    </div>
  )
}

function generateWaveformPath(audioData: number[], width = 400, height = 50): string {
  if (!audioData || audioData.length === 0) {
    return `M0,${height/2} L${width},${height/2}`;
  }
  const step = width / (audioData.length - 1);
  let path = `M0,${height / 2 + (audioData[0] || 0) * (height / 2)}`;
  for (let i = 1; i < audioData.length; i++) {
    const x = i * step;
    const y = height / 2 + (audioData[i] || 0) * (height / 2);
    path += ` L${x.toFixed(2)},${y.toFixed(2)}`;
  }
  return path;
}
