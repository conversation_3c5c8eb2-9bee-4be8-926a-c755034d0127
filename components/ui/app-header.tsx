"use client"

import { useState } from "react"
import type React from "react"
import { Avatar } from "@/components/ui/avatar"
import Image from "next/image"
import { Settings, Bell, Users, Building, ChevronDown } from "lucide-react"
import { useUser } from "@/contexts/user-context"
import { UserProfile } from "@/components/user-profile"
import { motion, AnimatePresence } from "framer-motion"
import { useOrganization, OrganizationSwitcher } from "@clerk/nextjs"

interface AppHeaderProps {
  userName?: string
  userRole?: string
  showControls?: boolean
  settingsContent?: React.ReactNode
  profileContent?: React.ReactNode
  notificationsContent?: React.ReactNode
  onContactsClick?: () => void
}

export function AppHeader({
  userName = "Ask ARA",
  userRole = "Virtual Assistant",
  showControls = true,
  settingsContent,
  profileContent,
  notificationsContent,
  onContactsClick,
}: AppHeaderProps) {
  const { user } = useUser()
  const { organization, isLoaded: isOrgLoaded } = useOrganization()
  const [showSettings, setShowSettings] = useState(false)
  const [showProfile, setShowProfile] = useState(false)
  const [showNotifications, setShowNotifications] = useState(false)
  const [showOrgSwitcher, setShowOrgSwitcher] = useState(false)

  // Use user data from context if available
  const displayName = user?.name || userName
  const displayRole = user?.role || userRole
  const orgName = organization?.name || "ARA Property Services"

  const handleSettingsClick = () => {
    if (!showSettings) {
      setShowProfile(false)
      setShowNotifications(false)
      setShowOrgSwitcher(false)
    }
    setShowSettings(!showSettings)
  }

  const handleProfileClick = () => {
    if (!showProfile) {
      setShowSettings(false)
      setShowNotifications(false)
      setShowOrgSwitcher(false)
    }
    setShowProfile(!showProfile)
  }

  const handleNotificationsClick = () => {
    if (!showNotifications) {
      setShowSettings(false)
      setShowProfile(false)
      setShowOrgSwitcher(false)
    }
    setShowNotifications(!showNotifications)
  }

  const handleOrgSwitcherClick = () => {
    if (!showOrgSwitcher) {
      setShowSettings(false)
      setShowProfile(false)
      setShowNotifications(false)
    }
    setShowOrgSwitcher(!showOrgSwitcher)
  }

  const slideDownVariants = {
    hidden: { opacity: 0, height: 0, y: -20 },
    visible: {
      opacity: 1,
      height: "auto",
      y: 0,
      transition: { duration: 0.3, ease: "easeInOut" },
    },
    exit: {
      opacity: 0,
      height: 0,
      y: -20,
      transition: { duration: 0.2, ease: "easeInOut" },
    },
  }

  const headerHeight = "68px"

  return (
    <>
      <div
        className="w-full bg-black/40 backdrop-blur-lg border-b border-white/10 overflow-hidden z-30 fixed top-0 left-0 right-0 px-4 py-3"
        style={{ height: headerHeight }}
      >
        <div className="flex items-center justify-between h-full">
          <div className="flex items-center space-x-2">
            {/* Organization Switcher */}
            <button
              onClick={handleOrgSwitcherClick}
              className="flex items-center text-white bg-black/30 backdrop-blur-lg border border-white/10 rounded-full px-3 py-1.5 text-sm"
            >
              {organization?.imageUrl ? (
                <Image
                  src={organization.imageUrl}
                  alt={organization.name || "Organization"}
                  width={20}
                  height={20}
                  className="w-5 h-5 rounded-full mr-2"
                />
              ) : (
                <Building className="w-4 h-4 mr-2" />
              )}
              <span className="truncate max-w-[100px]">{orgName}</span>
              <ChevronDown className="w-4 h-4 ml-1" />
            </button>

            {/* User Profile */}
            <div
              className="flex items-center cursor-pointer"
              onClick={handleProfileClick}
            >
              {user ? (
                <Avatar className="w-10 h-10 rounded-full bg-white border-2 border-white/20 mr-3 relative overflow-hidden">
                  {user.avatar ? (
                    <Image src={user.avatar} alt="User avatar" width={40} height={40} className="w-full h-full object-cover" />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-[#A4D321] text-black font-bold">
                      {(user.name || '?').charAt(0)}
                    </div>
                  )}
                </Avatar>
              ) : (
                <Avatar className="w-10 h-10 rounded-full bg-white border-2 border-white/20 mr-3">
                  <svg viewBox="0 0 24 24" className="w-6 h-6 text-[#A4D321]">
                    <path
                      d="M6 7L10 3L18 3L18 11L14 15"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                    />
                    <path
                      d="M14 9L6 9L6 17L14 17"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                    />
                    <path d="M10 13L10 21" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                    <path d="M14 13L18 17" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                  </svg>
                </Avatar>
              )}

              <div className="flex flex-col">
                <h1 className="text-white text-base font-medium">{displayName}</h1>
                <p className="text-white/60 text-xs">{displayRole}</p>
                {user && user.department && (
                  <div className="flex items-center">
                    <span className="bg-[#A4D321] text-black text-[10px] px-1 py-0.5 rounded mr-1">
                      {user.department}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {showControls && (
            <div className="flex items-center space-x-3">
              <button
                className="w-9 h-9 rounded-full bg-black/30 backdrop-blur-lg border border-white/10 flex items-center justify-center shadow-sm overflow-hidden"
                onClick={handleNotificationsClick}
              >
                <Bell className="w-4 h-4 text-white" />
              </button>

              {onContactsClick && (
                <button
                  className="w-9 h-9 rounded-full bg-black/30 backdrop-blur-lg border border-white/10 flex items-center justify-center shadow-sm overflow-hidden"
                  onClick={onContactsClick}
                >
                  <Users className="w-4 h-4 text-white" />
                </button>
              )}

              <button
                className="w-9 h-9 rounded-full bg-black/30 backdrop-blur-lg border border-white/10 flex items-center justify-center shadow-sm overflow-hidden"
                onClick={handleSettingsClick}
              >
                <Settings className="w-4 h-4 text-white" />
              </button>
            </div>
          )}
        </div>
      </div>

      <div
        className="fixed left-0 right-0 w-full z-20 overflow-hidden"
        style={{ top: headerHeight }}
      >
        <AnimatePresence>
          {showSettings && settingsContent && (
            <motion.div
              key="settings"
              initial="hidden"
              animate="visible"
              exit="exit"
              variants={slideDownVariants}
              className="bg-black/80 backdrop-blur-md border-b border-white/10 p-4 overflow-hidden"
              style={{ width: "100%" }}
            >
              {settingsContent}
            </motion.div>
          )}
        </AnimatePresence>

        <AnimatePresence>
          {showProfile && profileContent && (
            <motion.div
              key="profile"
              initial="hidden"
              animate="visible"
              exit="exit"
              variants={slideDownVariants}
              className="bg-black/80 backdrop-blur-md border-b border-white/10 p-4 overflow-hidden"
              style={{ width: "100%" }}
            >
              {user ? (
                <div className="flex flex-col space-y-4">
                  <UserProfile variant="full" />
                  {profileContent}
                </div>
              ) : (
                profileContent
              )}
            </motion.div>
          )}
        </AnimatePresence>

        <AnimatePresence>
          {showNotifications && notificationsContent && (
            <motion.div
              key="notifications"
              initial="hidden"
              animate="visible"
              exit="exit"
              variants={slideDownVariants}
              className="bg-black/80 backdrop-blur-md border-b border-white/10 p-4 overflow-hidden"
              style={{ width: "100%" }}
            >
              {notificationsContent}
            </motion.div>
          )}
        </AnimatePresence>

        <AnimatePresence>
          {showOrgSwitcher && (
            <motion.div
              key="orgSwitcher"
              initial="hidden"
              animate="visible"
              exit="exit"
              variants={slideDownVariants}
              className="bg-black/80 backdrop-blur-md border-b border-white/10 p-4 overflow-hidden"
              style={{ width: "100%" }}
            >
              <div className="p-2">
                <h3 className="text-lg font-medium text-white mb-4">Organization</h3>
                <OrganizationSwitcher
                  appearance={{
                    elements: {
                      rootBox: "w-full",
                      organizationSwitcherTrigger: 
                        "w-full bg-zinc-800 border border-zinc-700 text-white p-2 rounded-lg hover:bg-zinc-700"
                    }
                  }}
                />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </>
  )
}
