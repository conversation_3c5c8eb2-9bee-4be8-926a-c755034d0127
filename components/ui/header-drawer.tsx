"use client"

import type { ReactNode } from "react"
import { Drawer } from "vaul"
import { X } from "lucide-react"

interface HeaderDrawerProps {
  isOpen: boolean
  onClose: () => void
  title: string
  children: ReactNode
}

export function HeaderDrawer({ isOpen, onClose, title, children }: HeaderDrawerProps) {
  return (
    <Drawer.Root direction="top" open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <Drawer.Portal>
        <Drawer.Overlay className="fixed inset-0 bg-black/40 backdrop-blur-sm z-40" />
        <Drawer.Content className="bg-black/90 backdrop-blur-md flex flex-col rounded-b-[10px] max-h-[90vh] fixed top-0 left-0 right-0 z-50 border-b border-white/10">
          <div className="p-4 pt-[72px] rounded-b-[10px] flex-1 flex flex-col">
            {/* Header */}
            <div className="flex items-center justify-between mb-4 px-2">
              <h2 className="text-white text-lg font-medium">{title}</h2>
              <button
                onClick={onClose}
                className="w-8 h-8 rounded-full bg-[#121624]/60 flex items-center justify-center"
              >
                <X className="w-4 h-4 text-white" />
              </button>
            </div>

            {/* Content */}
            <div className="flex-1 overflow-y-auto">{children}</div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  )
}
