"use client"

import { useState, useRef, useEffect } from "react"
import { motion, AnimatePresence, type PanInfo } from "framer-motion"
import type React from "react"

interface SwipeableContainerProps {
  children: React.ReactNode[]
  initialIndex?: number
  activeIndex?: number // Allow external control
  onIndexChange?: (index: number) => void
}

export function SwipeableContainer({
  children,
  initialIndex = 0,
  activeIndex, // Added
  onIndexChange,
}: SwipeableContainerProps) {
  // Initialize with activeIndex if provided (controlled), otherwise use initialIndex (uncontrolled)
  const [currentIndex, setCurrentIndex] = useState(activeIndex ?? initialIndex)
  const [direction, setDirection] = useState(0)
  const containerRef = useRef<HTMLDivElement>(null)

  // Effect to sync internal state with external prop if provided and changed
  useEffect(() => {
    if (activeIndex !== undefined && activeIndex !== currentIndex) {
      // Determine direction for animation based on change
      setDirection(activeIndex > currentIndex ? 1 : -1)
      setCurrentIndex(activeIndex)
    }
    // Only depend on activeIndex to react to external changes from the parent
    // Also include currentIndex to potentially fix the exhaustive-deps warning, though it might cause loops if not handled carefully
  }, [activeIndex, currentIndex]) 
  
  // Effect to notify parent when internal state changes (relevant for both modes)
  useEffect(() => {
    if (onIndexChange) {
      onIndexChange(currentIndex)
    }
  }, [currentIndex, onIndexChange])

  useEffect(() => {
    if (onIndexChange) {
      onIndexChange(currentIndex)
    }
  }, [currentIndex, onIndexChange])

  const handleDragEnd = (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    const threshold = 100 // minimum distance required for a swipe
    let newIndex = currentIndex

    if (info.offset.x > threshold && currentIndex > 0) {
      // Attempted swipe right
      newIndex = currentIndex - 1
      setDirection(-1) // Set direction for potential animation if state updates
    } else if (info.offset.x < -threshold && currentIndex < children.length - 1) {
      // Attempted swipe left
      newIndex = currentIndex + 1
      setDirection(1) // Set direction for potential animation if state updates
    }

    // If the index actually changed based on the swipe gesture
    if (newIndex !== currentIndex) {
      if (activeIndex !== undefined && onIndexChange) {
        // Controlled mode: Notify parent via callback. Parent updates its state,
        // which flows back down via activeIndex prop, triggering the useEffect above.
        onIndexChange(newIndex)
      } else if (activeIndex === undefined) {
        // Uncontrolled mode: Update internal state directly.
        setCurrentIndex(newIndex)
      }
    }
  }

  const variants = {
    enter: (direction: number) => ({
      x: direction > 0 ? "100%" : "-100%",
      opacity: 0,
    }),
    center: {
      x: 0,
      opacity: 1,
    },
    exit: (direction: number) => ({
      x: direction < 0 ? "100%" : "-100%",
      opacity: 0,
    }),
  }

  return (
    <div ref={containerRef} className="w-full h-full overflow-hidden">
      <AnimatePresence initial={false} custom={direction} mode="wait">
        <motion.div
          key={currentIndex} // Keyed by internal state for animation trigger
          custom={direction}
          variants={variants}
          initial="enter"
          animate="center"
          exit="exit"
          transition={{
            x: { type: "spring", stiffness: 300, damping: 30 },
            opacity: { duration: 0.2 },
          }}
          drag="x"
          dragConstraints={{ left: 0, right: 0 }}
          dragElastic={0.7}
          onDragEnd={handleDragEnd}
          className="w-full h-full"
        >
          {/* Render child based on the internal currentIndex state */}
          {children[currentIndex]}
        </motion.div>
      </AnimatePresence>
    </div>
  )
}
