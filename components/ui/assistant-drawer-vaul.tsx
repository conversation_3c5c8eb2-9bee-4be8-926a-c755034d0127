"use client"

import { motion } from "framer-motion"
import { LogoButton } from "@/components/ui/logo-button"
import { Drawer } from "vaul"

interface AssistantDrawerProps {
  isOpen: boolean
  onClose: () => void
  isRecording: boolean
  onVoiceToggle: () => void
  transcription: string
}

export function AssistantDrawerVaul({
  isOpen,
  onClose,
  isRecording,
  onVoiceToggle,
  transcription,
}: AssistantDrawerProps) {
  return (
    <Drawer.Root open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <Drawer.Portal>
        <Drawer.Overlay className="fixed inset-0 bg-black/40 z-40" />
        <Drawer.Content className="bg-black/90 backdrop-blur-md flex flex-col rounded-t-[10px] h-[96%] mt-24 fixed bottom-0 left-0 right-0 z-50">
          <div className="p-4 rounded-t-[10px] flex-1 flex flex-col">
            <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-white/20 mb-4" />

            {/* Assistant Header */}
            <div className="flex items-center px-2 pb-4">
              <motion.div initial={{ x: 0 }} animate={{ x: 0 }} className="flex items-center">
                <LogoButton size={40} onClick={onVoiceToggle} isActive={isRecording} />
                <div className="ml-3">
                  <h3 className="text-white font-medium">AI Assistant</h3>
                  <p className="text-[#ffffffb2] text-sm">{isRecording ? "Listening..." : "Tap to speak"}</p>
                </div>
              </motion.div>
            </div>

            {/* Transcription Area */}
            <div className="flex-1 px-2 py-4 overflow-y-auto">
              <div className="bg-[#121624]/60 rounded-xl p-4 min-h-[100px] border border-[#ffffff]/10">
                <p className="text-white text-lg">
                  {transcription}
                  {isRecording && <span className="inline-block w-2 h-5 bg-[#A4D321] ml-1 animate-pulse" />}
                </p>
              </div>
            </div>

            {/* Input Area at Bottom */}
            <div className="px-2 py-4 border-t border-[#ffffff]/10">
              <div className="flex items-center space-x-3">
                <motion.div initial={{ x: 0 }} animate={{ x: 0 }} className="flex-shrink-0">
                  <LogoButton size={40} onClick={onVoiceToggle} isActive={isRecording} />
                </motion.div>

                <div className="flex-1 bg-[#121624]/60 rounded-xl border border-[#ffffff]/10 px-4 py-2 min-h-[50px]">
                  <p className="text-white text-lg">
                    {isRecording && <span className="inline-block w-2 h-5 bg-[#A4D321] ml-1 animate-pulse" />}
                  </p>
                </div>
              </div>
            </div>

            {/* Suggestions */}
            <div className="px-2 py-4">
              <h4 className="text-[#ffffffb2] mb-3">Suggestions</h4>
              <div className="flex flex-wrap gap-2">
                <motion.button
                  className="px-4 py-2 rounded-full bg-[#121624]/60 border border-[#ffffff]/10 text-white text-sm"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  What&apos;s the weather today?
                </motion.button>
                <motion.button
                  className="px-4 py-2 rounded-full bg-[#121624]/60 border border-[#ffffff]/10 text-white text-sm"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Set a timer for 5 minutes
                </motion.button>
                <motion.button
                  className="px-4 py-2 rounded-full bg-[#121624]/60 border border-[#ffffff]/10 text-white text-sm"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Tell me a joke
                </motion.button>
              </div>
            </div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  )
}
