"use client"

import { motion } from "framer-motion"

interface LoadingProps {
  size?: "small" | "medium" | "large"
  color?: "primary" | "white" | "accent"
  text?: string
}

export function Loading({ size = "medium", color = "primary", text }: LoadingProps) {
  const sizeMap = {
    small: "w-4 h-4",
    medium: "w-8 h-8",
    large: "w-12 h-12",
  }

  const colorMap = {
    primary: "border-[#A4D321] border-t-transparent",
    white: "border-white border-t-transparent",
    accent: "border-[#3D4D61] border-t-transparent",
  }

  return (
    <div className="flex flex-col items-center justify-center">
      <motion.div
        className={`${sizeMap[size]} border-2 ${colorMap[color]} rounded-full animate-spin`}
        animate={{ rotate: 360 }}
        transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: "linear" }}
      />
      {text && <p className="mt-2 text-sm text-[#ffffffb2]">{text}</p>}
    </div>
  )
}
