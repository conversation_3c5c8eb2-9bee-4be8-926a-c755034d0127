"use client";

import React, { useState, useEffect } from 'react'
import dynamic from 'next/dynamic'
import { ThemeProvider } from '@/components/theme-provider'
import { UserProvider } from '@/contexts/user-context'
import { ConvexClientProvider } from '@/components/convex-provider'

// Dynamically import the provider with ssr: false INSIDE a client component
const VoiceAssistantProvider = dynamic(
  () => import('@/components/voice-assistant/voice-assistant-provider').then(mod => {
    return mod.VoiceAssistantProvider
  }),
  { ssr: false }
)

interface ClientProvidersProps {
  children: React.ReactNode;
}

export function ClientProviders({ children }: ClientProvidersProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    // This effect runs only on the client after the component mounts
    setIsClient(true);
  }, []); // Empty dependency array ensures this runs only once on mount

  // Create a safe rendering structure
  return (
    <ConvexClientProvider>
      <ThemeProvider attribute="class" defaultTheme="dark" enableSystem>
        <UserProvider>
          {/* Conditionally render the voice assistant provider only on the client side */}
          {isClient ? (
            <VoiceAssistantProvider>
              {children}
            </VoiceAssistantProvider>
          ) : (
            // Simple fallback when not on client side
            <>{children}</>
          )}
        </UserProvider>
      </ThemeProvider>
    </ConvexClientProvider>
  );
}