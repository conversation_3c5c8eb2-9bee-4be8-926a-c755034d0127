import { Cpu, User } from 'lucide-react'
import React from 'react'; // Import React

interface ConversationItemProps {
  conversationItem: {
    role: string;
    formatted: {
      transcript: string;
    };
  };
}

function GlobalMessage({ conversationItem }: ConversationItemProps) { // Rename function
  return (
    <div className="flex flex-row items-start gap-x-3 flex-wrap max-w-full my-3">
    </div>
  )
}

GlobalMessage.displayName = 'GlobalMessage'; // Add display name

export default GlobalMessage; // Add default export
