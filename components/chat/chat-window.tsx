'use client'

// Removed dynamic import for AssistantChat
// import { useVoiceAssistant } from '@/components/voice-assistant/voice-assistant-provider'
// import { useTypingEffect } from '@/components/voice-assistant/use-typing-effect'
// import { LogoButton } from '@/components/ui/logo-button'

export function ChatWindow() {
  // const {
  //   state,
  //   messages,
  //   currentMessage,
  //   toggleListening
  // } = useVoiceAssistant()
  
  // const animatedText = useTypingEffect(currentMessage, 20)

  return (
    <div className="flex flex-col h-full">
      {/* Large comment block removed for debugging */}
      <iframe
        src="http://localhost:3001/c/default"
        title="Assistant Chat"
        className="w-full h-full border-0"
      />
    </div>
  )
}
