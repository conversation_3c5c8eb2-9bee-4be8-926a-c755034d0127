"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Search, MessageSquare, Phone } from "lucide-react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"

interface TeamScreenProps {
  onNavigate: (screen: string) => void
  userData: {
    name: string
    role: string
    department: string
  }
}

interface TeamMember {
  id: string
  name: string
  role: string
  status: "online" | "offline" | "busy"
  avatar?: string
  assigned: boolean
}

// Removed unused props: onNavigate, userData
export function TeamScreen({ }: TeamScreenProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Fetch team members from API
    fetchTeamMembers()
  }, [])

  const fetchTeamMembers = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // In a real app, this would be an API call
      // const response = await fetch('/api/team')
      // const data = await response.json()
      // setTeamMembers(data.members)

      // For now, just set empty array
      setTeamMembers([])
    } catch (err: unknown) { // Type err as unknown
      console.error("Error fetching team members:", err)
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage || "Failed to load team members")
    } finally {
      setIsLoading(false)
    }
  }

  // Removed unused function 'getStatusColor'

  // Define filterTeamMembers function
  const filterTeamMembers = (filter: string): TeamMember[] => {
    if (filter === "all") {
      return teamMembers
    } else if (filter === "online") {
      return teamMembers.filter((member) => member.status === "online" || member.status === "busy")
    } else if (filter === "assigned") {
      return teamMembers.filter((member) => member.assigned)
    }
    return teamMembers
  }

  return (
    <ScrollArea className="flex-1 w-full h-full">
      <div className="p-6">
        {/* Search Bar */}
        <div className="relative mb-6">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-[#ffffffb2]" />
          <input
            type="text"
            placeholder="Search team members..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-3 rounded-lg bg-black/40 backdrop-blur-sm border border-white/10 text-white text-sm focus:outline-none focus:ring-2 focus:ring-[#A4D321]/50"
          />
        </div>

        {/* Team Filters */}
        <Tabs defaultValue="all" className="w-full mb-6">
          <TabsList className="w-full grid grid-cols-3 bg-black/40 backdrop-blur-sm mb-6">
            <TabsTrigger value="all" className="data-[state=active]:text-[#A4D321]">
              All
            </TabsTrigger>
            <TabsTrigger value="online" className="data-[state=active]:text-[#A4D321]">
              Online
            </TabsTrigger>
            <TabsTrigger value="assigned" className="data-[state=active]:text-[#A4D321]">
              Assigned
            </TabsTrigger>
          </TabsList>

          {isLoading ? (
            <div className="flex items-center justify-center p-12">
              <div className="w-8 h-8 border-2 border-[#A4D321] border-t-transparent rounded-full animate-spin" />
            </div>
          ) : error ? (
            <div className="p-6 text-center">
              <p className="text-red-400 mb-4">{error}</p>
              <button className="px-4 py-2 bg-[#3D4D61] text-white rounded-md text-sm" onClick={fetchTeamMembers}>
                Try Again
              </button>
            </div>
          ) : teamMembers.length === 0 ? (
            <div className="p-6 text-center">
              <p className="text-[#ffffffb2]">No team members found</p>
            </div>
          ) : (
            <>
              <TabsContent value="all" className="space-y-4">
                {filterTeamMembers("all").map((member) => (
                  <TeamMemberCard key={member.id} member={member} />
                ))}
              </TabsContent>

              <TabsContent value="online" className="space-y-4">
                {filterTeamMembers("online").map((member) => (
                  <TeamMemberCard key={member.id} member={member} />
                ))}
              </TabsContent>

              <TabsContent value="assigned" className="space-y-4">
                {filterTeamMembers("assigned").map((member) => (
                  <TeamMemberCard key={member.id} member={member} />
                ))}
              </TabsContent>
            </>
          )}
        </Tabs>
      </div>
    </ScrollArea>
  )
}

interface TeamMemberCardProps {
  member: TeamMember
}

function TeamMemberCard({ member }: TeamMemberCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "online":
        return "bg-green-500"
      case "busy":
        return "bg-amber-500"
      case "offline":
        return "bg-gray-500"
      default:
        return "bg-gray-500"
    }
  }

  return (
    <motion.div
      className="bg-black/30 backdrop-blur-lg rounded-xl p-4 border border-white/10 shadow-sm"
      whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <div className="relative mr-3">
            <Avatar className="h-12 w-12 border-2 border-white/20">
              <AvatarImage src={`/placeholder.svg?height=48&width=48`} alt={member.name} />
              <AvatarFallback className="bg-[#3D4D61] text-white">
                {member.name
                  .split(" ")
                  .map((n) => n[0])
                  .join("")}
              </AvatarFallback>
            </Avatar>
            <div
              className={`absolute bottom-0 right-0 h-3 w-3 rounded-full border-2 border-black ${getStatusColor(
                member.status,
              )}`}
            />
          </div>
          <div>
            <h3 className="text-white text-base font-medium">{member.name}</h3>
            <p className="text-[#ffffffb2] text-xs">{member.role}</p>
          </div>
        </div>

        <div className="flex gap-2">
          <motion.button
            className="w-9 h-9 rounded-full bg-black/40 backdrop-blur-sm border border-white/10 flex items-center justify-center"
            whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.1)" }}
          >
            <MessageSquare className="h-4 w-4 text-[#A4D321]" />
          </motion.button>
          <motion.button
            className="w-9 h-9 rounded-full bg-black/40 backdrop-blur-sm border border-white/10 flex items-center justify-center"
            whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.1)" }}
          >
            <Phone className="h-4 w-4 text-[#A4D321]" />
          </motion.button>
        </div>
      </div>
    </motion.div>
  )
}
