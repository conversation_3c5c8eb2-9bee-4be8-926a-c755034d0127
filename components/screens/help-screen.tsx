"use client"

import { useState } from "react"
// Removed unused motion
import { ScrollArea } from "@/components/ui/scroll-area"
// Removed unused icons: Bot, ChevronRight, ChevronDown, Phone, Mail, Play
// Removed unused Button
import { ChevronDown, ChevronRight } from "lucide-react" // Re-import needed icons

interface HelpScreenProps {
  onNavigate: (screen: string) => void
  userData: {
    name: string
    role: string
    department: string
  }
}

// Removed unused props: onNavigate, userData
export function HelpScreen({ }: HelpScreenProps) {
  const [expandedFaq, setExpandedFaq] = useState<string | null>(null) // Complete useState call
  // Sample FAQs
  // Removed unused 'faqs' state variable
  const sampleFaqs = [
    {
      id: "1",
      question: "How do I create a new task?",
      answer:
        "To create a new task, navigate to the Tasks screen and click the + button in the top right corner. Fill in the task details and click Save.",
    },
    {
      id: "2",
      question: "How do I update my profile information?",
      answer:
        "You can update your profile by going to Settings > Profile Information. Click on the edit button and update your details as needed.",
    },
    {
      id: "3",
      question: "Can I access documents offline?",
      answer:
        "Yes, you can enable offline access in Settings > Data Management. Toggle on the Documents switch to make them available offline.",
    },
    {
      id: "4",
      question: "How do I contact support?",
      answer:
        "You can contact support via <NAME_EMAIL> or call our support line at +1 (800) 555-1234.",
    },
  ]

  // Sample tutorial videos
  // Removed unused 'tutorials' state variable
  const sampleTutorials = [
    {
      id: "1",
      title: "Getting Started with ARA",
      duration: "5:32",
      type: "Tutorial",
    },
    {
      id: "2",
      title: "Task Management Features",
      duration: "8:47",
      type: "Guide",
    },
    {
      id: "3",
      title: "Using the Knowledge Base",
      duration: "3:15",
      type: "Tutorial",
    },
  ]

  // Removed unused 'toggleFaq' function
  // const toggleFaq = (id: string) => {
  //   if (expandedFaq === id) {
  //     setExpandedFaq(null)
  //   } else {
  //     setExpandedFaq(id)
  //   }
  // }

  // Correct return statement
  const toggleFaq = (id: string) => { // Re-define toggleFaq function
    if (expandedFaq === id) {
      setExpandedFaq(null)
    } else {
      setExpandedFaq(id)
    }
  }

  return (
    <ScrollArea className="flex-1 w-full h-full">
      <div className="p-6">
        {/* Placeholder Content for Help & Support */}
        <div className="text-center py-20">
          <h2 className="text-xl font-semibold text-white mb-2">Help & Support</h2>
          <p className="text-[#ffffffb2]">FAQs, documentation, and contact options will be here.</p>
        </div>
      </div>
    </ScrollArea>
  )
}
