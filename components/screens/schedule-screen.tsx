"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Calendar, Clock, MapPin } from "lucide-react" // Removed unused Plus
import { format, addDays, isToday, isTomorrow, isSameDay } from "date-fns"
import { cn } from "@/lib/utils"

interface ScheduleScreenProps {
  onNavigate: (screen: string) => void
  userData: {
    name: string
    role: string
    department: string
  }
}

interface ScheduleItem {
  id: string
  title: string
  time: string
  location: string
  date: Date
}

// Removed unused props: onNavigate, userData
export function ScheduleScreen({ }: ScheduleScreenProps) {
  const today = new Date()
  const [selectedDate, setSelectedDate] = useState(today)
  const [scheduleItems, setScheduleItems] = useState<ScheduleItem[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Generate dates for the next 14 days for the horizontal scroll
  const dates = Array.from({ length: 14 }, (_, i) => addDays(today, i))

  useEffect(() => {
    // Fetch schedule items from API
    fetchScheduleItems()
  }, [selectedDate])

  const fetchScheduleItems = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // In a real app, this would be an API call with the selected date
      // const response = await fetch(`/api/schedule?date=${format(selectedDate, 'yyyy-MM-dd')}`)
      // const data = await response.json()
      // setScheduleItems(data.items)

      // For now, just set empty array
      setScheduleItems([])
    } catch (err: unknown) { // Type err as unknown
      console.error("Error fetching schedule items:", err)
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage || "Failed to load schedule")
    } finally {
      setIsLoading(false)
    }
  }

  // Filter schedule items for the selected date
  const filteredItems = scheduleItems.filter((item) => isSameDay(item.date, selectedDate))

  const formatDateLabel = (date: Date) => {
    if (isToday(date)) return "Today"
    if (isTomorrow(date)) return "Tomorrow"
    return format(date, "EEE, MMM d")
  }

  return (
    <ScrollArea className="flex-1 w-full h-full">
      <div className="p-6">
        {/* Date Selector */}
        <div className="mb-6 -mx-6 px-6">
          <div className="flex overflow-x-auto pb-2 scrollbar-hide">
            <div className="flex gap-2">
              {dates.map((date) => (
                <motion.button
                  key={date.toString()}
                  className={cn(
                    "flex flex-col items-center justify-center min-w-16 h-20 rounded-xl border",
                    isSameDay(date, selectedDate)
                      ? "bg-[#A4D321] border-[#A4D321] text-black"
                      : "bg-black/30 border-white/10 text-white",
                  )}
                  onClick={() => setSelectedDate(date)}
                  whileHover={!isSameDay(date, selectedDate) ? { backgroundColor: "rgba(255, 255, 255, 0.1)" } : {}}
                  whileTap={{ scale: 0.95 }}
                >
                  <p className="text-xs font-medium">{format(date, "EEE")}</p>
                  <p className="text-xl font-bold">{format(date, "d")}</p>
                </motion.button>
              ))}
            </div>
          </div>
        </div>

        {/* Schedule Items */}
        <div>
          <h2 className="text-white text-lg font-medium mb-4">{formatDateLabel(selectedDate)}</h2>

          {isLoading ? (
            <div className="flex items-center justify-center p-12">
              <div className="w-8 h-8 border-2 border-[#A4D321] border-t-transparent rounded-full animate-spin" />
            </div>
          ) : error ? (
            <div className="p-6 text-center">
              <p className="text-red-400 mb-4">{error}</p>
              <button className="px-4 py-2 bg-[#3D4D61] text-white rounded-md text-sm" onClick={fetchScheduleItems}>
                Try Again
              </button>
            </div>
          ) : filteredItems.length > 0 ? (
            <div className="space-y-4">
              {filteredItems.map((item) => (
                <motion.div
                  key={item.id}
                  className="bg-black/30 backdrop-blur-lg rounded-xl p-4 border border-white/10 shadow-sm"
                  whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
                >
                  <div className="flex items-start gap-3">
                    <div className="w-10 h-10 rounded-full bg-[#3D4D61]/40 flex items-center justify-center flex-shrink-0">
                      <Calendar className="h-5 w-5 text-[#A4D321]" />
                    </div>
                    <div>
                      <h3 className="text-white text-base font-medium">{item.title}</h3>
                      <div className="flex items-center gap-1 mt-1">
                        <Clock className="h-3.5 w-3.5 text-[#ffffffb2]" />
                        <p className="text-[#ffffffb2] text-xs">{item.time}</p>
                      </div>
                      <div className="flex items-center gap-1 mt-1">
                        <MapPin className="h-3.5 w-3.5 text-[#ffffffb2]" />
                        <p className="text-[#ffffffb2] text-xs">{item.location}</p>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-10">
              <Calendar className="h-16 w-16 text-[#ffffffb2] mb-4" />
              <p className="text-white text-base font-medium">No events scheduled</p>
              <p className="text-[#ffffffb2] text-sm mt-1">Your schedule is clear for this day</p>
            </div>
          )}
        </div>
      </div>
    </ScrollArea>
  )
}
