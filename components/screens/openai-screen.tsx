"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { LogoButton } from "@/components/ui/logo-button"
import { NavigationBar } from "@/components/ui/navigation-bar"
import { ScreenLayout } from "@/components/ui/screen-layout"

interface OpenAIScreenProps {
  onNavigate: (screen: string) => void
  userData: {
    name: string
    role: string
    department: string
  }
}


interface CallInfo {
  callId: string;
  userId: string;
}

// Removed unused 'userData' prop
export function OpenAIScreen({ onNavigate }: OpenAIScreenProps) {
  const [isRecording, setIsRecording] = useState(false)
  const [transcription, setTranscription] = useState("")
  const [callInfo, setCallInfo] = useState<CallInfo | null>(null); // Use specific interface
  const [isConnecting, setIsConnecting] = useState(false)
  const [isConnected, setIsConnected] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Initialize the OpenAI connection when the component mounts
  useEffect(() => {
    initializeOpenAI()
  }, [])

  const initializeOpenAI = async () => {
    try {
      setIsConnecting(true)
      setError(null)

      // Get credentials from the API
      const response = await fetch('/api/openai/credentials')
      const data = await response.json()

      if (data.error) {
        throw new Error(data.error)
      }

      setCallInfo(data)

      // Connect to the OpenAI agent
      const connectResponse = await fetch('/api/openai/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          callType: data.callType,
          callId: data.callId,
        }),
      })

      const connectData = await connectResponse.json()

      if (connectData.error) {
        throw new Error(connectData.error)
      }

      setIsConnected(true)
      setIsConnecting(false)
    } catch (err) {
      console.error('Error initializing OpenAI:', err)
      setError(err instanceof Error ? err.message : 'Failed to initialize OpenAI')
      setIsConnecting(false)
    }
  }

  const toggleMicrophone = () => {
    setIsRecording(!isRecording)

    // In a real app, this would trigger actual voice recording
    // and send the audio to a speech-to-text service
    if (!isRecording) {
      setTranscription("")

      // Simulate recording for 5 seconds
      setTimeout(() => {
        setIsRecording(false)
        setTranscription("This is a simulated transcription from OpenAI.")
      }, 5000)
    } else {
      setIsRecording(false)
    }
  }

  if (!isConnected && !isConnecting && !error) {
     // Optionally render loading state or nothing until initialized
     return <div>Connecting...</div>; // Return connecting state
  }
  return (
      // Remove stray brace from line below
    <ScreenLayout
      title="OpenAI Assistant"
      subtitle="Voice-powered AI"
      onNavigate={onNavigate}
      activeScreen="openai"
    >
      <div className="flex-1 overflow-y-auto p-4">
        <div className="mb-6">
          <h1 className="text-2xl font-bold mb-2">OpenAI Assistant</h1>
          <p className="text-[#ffffffb2]">Voice-powered AI assistant using Stream and OpenAI</p>
        </div>

        {/* Connection Status */}
        <div className="mb-6 p-4 rounded-xl bg-[#121624]/60 border border-[#ffffff]/10">
          <div className="flex items-center mb-2">
            <div className={`w-3 h-3 rounded-full mr-2 ${
              isConnected ? "bg-green-500" : isConnecting ? "bg-yellow-500" : "bg-red-500"
            }`} />
            <span className="font-medium">
              {isConnected ? "Connected" : isConnecting ? "Connecting..." : "Disconnected"}
            </span>
          </div>
          {error && (
            <div className="mt-2 p-2 bg-red-500/20 border border-red-500/50 rounded-lg">
              <p className="text-white text-sm">{error}</p>
            </div>
          )}
          {callInfo && (
            <div className="mt-2">
              <p className="text-sm text-[#ffffffb2]">Call ID: {callInfo.callId}</p>
              <p className="text-sm text-[#ffffffb2]">User ID: {callInfo.userId}</p>
            </div>
          )}
        </div>

        {/* Voice Interface */}
        <div className="mb-6">
          <div className="flex items-center mb-4">
            <LogoButton size={40} onClick={toggleMicrophone} isActive={isRecording} />
            <div className="ml-3">
              <h3 className="font-medium">Voice Control</h3>
              <p className="text-[#ffffffb2] text-sm">
                {!isConnected 
                  ? "Connect to use voice" 
                  : isRecording 
                    ? "Listening..." 
                    : "Tap to speak"}
              </p>
            </div>
          </div>

          {/* Transcription Area */}
          <div className="bg-[#121624]/60 rounded-xl p-4 min-h-[100px] border border-[#ffffff]/10">
            <p className="text-white">
              {transcription || (isConnected ? "Say something..." : "Waiting for connection...")}
              {isRecording && <span className="inline-block w-2 h-5 bg-[#A4D321] ml-1 animate-pulse" />}
            </p>
          </div>
        </div>

        {/* Suggestions */}
        <div className="mb-6">
          <h3 className="font-medium mb-3">Try saying...</h3>
          <div className="flex flex-wrap gap-2">
            <motion.button
              className="px-4 py-2 rounded-full bg-[#121624]/60 border border-[#ffffff]/10 text-white text-sm"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              disabled={!isConnected}
            >
              &quot;What&apos;s the weather today?&quot;
            </motion.button>
            <motion.button
              className="px-4 py-2 rounded-full bg-[#121624]/60 border border-[#ffffff]/10 text-white text-sm"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              disabled={!isConnected}
            >
              &quot;Tell me about Stream Video&quot;
            </motion.button>
            <motion.button
              className="px-4 py-2 rounded-full bg-[#121624]/60 border border-[#ffffff]/10 text-white text-sm"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              disabled={!isConnected}
            >
              &quot;How does OpenAI work?&quot;
            </motion.button>
          </div>
        </div>

        {/* Instructions */}
        <div className="mb-6 p-4 rounded-xl bg-[#121624]/60 border border-[#ffffff]/10">
          <h3 className="font-medium mb-2">How to use</h3>
          <ul className="list-disc list-inside text-sm text-[#ffffffb2] space-y-1">
            <li>Tap the logo button to start speaking</li>
            <li>Ask questions or give commands</li>
            <li>Swipe right anywhere to open the assistant drawer</li>
            <li>Try the suggested phrases above</li>
          </ul>
        </div>
      </div>
      {/* Navigation Bar */}
      <NavigationBar
        onNavigate={onNavigate}
        isRecording={isRecording}
        onVoiceToggle={toggleMicrophone}
        currentTranscription={transcription}
        activeScreen="openai" // Added prop correctly here
      />
    </ScreenLayout>
  )
}
