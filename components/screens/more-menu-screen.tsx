"use client"

import { ScrollArea } from "@/components/ui/scroll-area"
import { Settings, FileText, BarChart3, Users, HelpCircle, LogOut, ChevronRight } from "lucide-react"

interface MoreMenuScreenProps {
  onNavigate: (screen: string) => void
  userData: { name: string; role: string; department: string }
}

// Removed unused 'userData' prop
export function MoreMenuScreen({ onNavigate }: MoreMenuScreenProps) {
  const menuItems = [
    { id: "settings", label: "Settings", icon: <Settings className="h-5 w-5 text-[#A4D321]" /> },
    { id: "knowledge", label: "Knowledge Base", icon: <FileText className="h-5 w-5 text-[#A4D321]" /> },
    { id: "reports", label: "Reports", icon: <BarChart3 className="h-5 w-5 text-[#A4D321]" /> },
    { id: "team", label: "Team Management", icon: <Users className="h-5 w-5 text-[#A4D321]" /> },
    { id: "help", label: "Help & Support", icon: <HelpCircle className="h-5 w-5 text-[#A4D321]" /> },
  ]

  return (
    <ScrollArea className="flex-1 w-full h-full">
      <div className="p-6">
        <div className="space-y-3">
          {menuItems.map((item) => (
            <button
              key={item.id}
              onClick={() => onNavigate(item.id)}
              className="w-full flex items-center justify-between p-4 bg-[#121624]/80 rounded-lg border border-[#ffffff]/10 shadow-sm hover:bg-white/5 transition-colors"
            >
              <div className="flex items-center">
                {item.icon}
                <span className="text-white text-sm font-medium ml-3">{item.label}</span>
              </div>
              <ChevronRight className="w-5 h-5 text-[#ffffffb2]" />
            </button>
          ))}
        </div>

        <button
          onClick={() => onNavigate("logout")}
          className="w-full mt-8 flex items-center justify-center p-3 bg-red-500/20 rounded-lg border border-red-500/30 shadow-sm text-red-500 hover:bg-red-500/30 transition-colors"
        >
          <LogOut className="w-5 h-5 mr-2" />
          <span className="text-sm font-medium">Logout</span>
        </button>
      </div>
    </ScrollArea>
  )
}
