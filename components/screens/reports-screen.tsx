"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { ScrollArea } from "@/components/ui/scroll-area"
import { ArrowUp, ArrowDown, Download, FileText, Calendar, Filter } from "lucide-react"
// Removed unused Select components
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select" // Re-import if needed
interface ReportsScreenProps {
  onNavigate: (screen: string) => void
  userData: {
    name: string
    role: string
    department: string
  }
}

interface KPI {
  title: string
  value: string
  trend: string
  isPositive: boolean
}

interface InspectionReport {
  id: string
  title: string
  date: string
  score: number
  location: string
}

// Removed unused 'userData' prop
export function ReportsScreen({ onNavigate }: ReportsScreenProps) {
  // Removed unused state setter 'setPeriod'
  const [period, _setPeriod] = useState("monthly")
  const [summaryKPIs, setSummaryKPIs] = useState<KPI[]>([])
  const [inspectionReports, setInspectionReports] = useState<InspectionReport[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Fetch reports data from API
    fetchReportsData()
  }, [period])

  const fetchReportsData = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // In a real app, these would be API calls with the selected period
      // const kpiResponse = await fetch(`/api/kpis?period=${period}`)
      // const kpiData = await kpiResponse.json()
      // setSummaryKPIs(kpiData.kpis)

      // const reportsResponse = await fetch(`/api/inspection-reports?period=${period}`)
      // const reportsData = await reportsResponse.json()
      // setInspectionReports(reportsData.reports)
      // For now, just set empty arrays
      setSummaryKPIs([])
      setInspectionReports([])
    } catch (err: unknown) { // Type err as unknown
      console.error("Error fetching reports data:", err)
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage || "Failed to load reports")
      setError(err.message || "Failed to load reports")
    } finally {
      setIsLoading(false)
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-500"
    if (score >= 70) return "text-amber-500"
    return "text-red-500"
  }

  return (
    <ScrollArea className="flex-1 w-full h-full">
      <div className="p-6">
        {isLoading ? (
          <div className="flex items-center justify-center p-12">
            <div className="w-8 h-8 border-2 border-[#A4D321] border-t-transparent rounded-full animate-spin" />
          </div>
        ) : error ? (
          <div className="p-6 text-center">
            <p className="text-red-400 mb-4">{error}</p>
            <button className="px-4 py-2 bg-[#3D4D61] text-white rounded-md text-sm" onClick={fetchReportsData}>
              Try Again
            </button>
          </div>
        ) : (
          <>
            {/* Summary KPIs */}
            {summaryKPIs.length > 0 ? (
              <div className="grid grid-cols-2 gap-4 mb-6">
                {summaryKPIs.map((kpi, index) => (
                  <motion.div
                    key={index}
                    className="bg-black/30 backdrop-blur-lg rounded-xl p-4 border border-white/10 shadow-sm"
                    whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
                  >
                    <p className="text-[#ffffffb2] text-sm mb-1">{kpi.title}</p>
                    <div className="flex items-end justify-between">
                      <p className="text-white text-2xl font-bold">{kpi.value}</p>
                      <div
                        className={`flex items-center ${
                          kpi.isPositive ? "text-green-500" : "text-red-500"
                        } text-sm font-medium`}
                      >
                        {kpi.isPositive ? (
                          <ArrowUp className="h-3.5 w-3.5 mr-0.5" />
                        ) : (
                          <ArrowDown className="h-3.5 w-3.5 mr-0.5" />
                        )}
                        <span>{kpi.trend}</span>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="bg-black/30 backdrop-blur-lg rounded-xl p-4 border border-white/10 shadow-sm mb-6">
                <p className="text-center text-[#ffffffb2]">No KPI data available</p>
              </div>
            )}

            {/* Recent Inspection Reports */}
            <div className="bg-black/30 backdrop-blur-lg rounded-xl p-4 border border-white/10 shadow-sm mb-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-white text-lg font-medium">Recent Inspections</h2>
                <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full bg-black/40 border border-white/10">
                  <Filter className="h-4 w-4 text-white" />
                </Button>
              </div>

              {inspectionReports.length > 0 ? (
                <div className="space-y-3">
                  {inspectionReports.map((report) => (
                    <motion.div
                      key={report.id}
                      className="bg-black/40 rounded-lg p-3 border border-white/10 flex items-center justify-between"
                      whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
                      onClick={() => onNavigate("inspection-report")}
                    >
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-[#3D4D61]/40 flex items-center justify-center flex-shrink-0">
                          <FileText className="h-5 w-5 text-[#A4D321]" />
                        </div>
                        <div>
                          <h3 className="text-white text-sm font-medium">{report.title}</h3>
                          <div className="flex items-center gap-2 mt-1">
                            <Calendar className="h-3 w-3 text-[#ffffffb2]" />
                            <p className="text-[#ffffffb2] text-xs">{report.date}</p>
                            <span className="w-1 h-1 bg-[#ffffffb2] rounded-full"></span>
                            <p className="text-[#ffffffb2] text-xs">{report.location}</p>
                          </div>
                        </div>
                      </div>
                      <div className={`text-lg font-bold ${getScoreColor(report.score)}`}>{report.score}%</div>
                    </motion.div>
                  ))}
                </div>
              ) : (
                <div className="p-6 text-center">
                  <p className="text-[#ffffffb2]">No inspection reports available</p>
                </div>
              )}

              <Button
                variant="outline"
                className="w-full mt-4 bg-black/40 border-white/10 text-white"
                onClick={() => onNavigate("inspection-report")}
              >
                View All Reports
              </Button>
            </div>

            {/* Task Completion Chart */}
            <div className="bg-black/30 backdrop-blur-lg rounded-xl p-4 border border-white/10 shadow-sm mb-6">
              <h2 className="text-white text-lg font-medium mb-4">Task Completion</h2>
              <div className="h-60 w-full flex items-center justify-center">
                <p className="text-[#ffffffb2]">Chart data will be populated from API</p>
              </div>
            </div>

            {/* Export Button */}
            <Button className="w-full bg-[#A4D321] text-black hover:bg-[#A4D321]/80">
              <Download className="h-4 w-4 mr-2" /> Export Report
            </Button>
          </>
        )}
      </div>
    </ScrollArea>
  )
}
