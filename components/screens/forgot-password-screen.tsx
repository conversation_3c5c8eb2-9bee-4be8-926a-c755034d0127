"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardFooter } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { Input } from "@/components/ui/input"
import { useState } from "react"
import Image from "next/image"
import { validateEmail, validateRequired } from "@/lib/validation"
import type React from "react"

interface ForgotPasswordScreenProps {
  onSubmit?: (email: string) => void
  onLoginClick?: () => void
}

export default function ForgotPasswordScreen({ onSubmit, onLoginClick }: ForgotPasswordScreenProps) {
  const [email, setEmail] = useState("")
  const [error, setError] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const validateForm = () => {
    if (!validateRequired(email)) {
      setError("Email is required")
      return false
    }

    if (!validateEmail(email)) {
      setError("Please enter a valid email address")
      return false
    }

    setError("")
    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsLoading(true)

    try {
      // In a real app, you would call your API here
      await new Promise((resolve) => setTimeout(resolve, 1000)) // Simulate API call

      if (onSubmit) {
        onSubmit(email)
      }

      setIsSubmitted(true)
    } catch (error) {
      console.error("Password reset error:", error)
      setError("An error occurred. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleResend = () => {
    setIsSubmitted(false)
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-black p-4">
      <Card
        className={cn(
          "w-full max-w-[400px] mx-auto rounded-3xl",
          "bg-zinc-900 dark:bg-black",
          "border-2 border-zinc-800/50",
          "shadow-[0_24px_48px_-12px] shadow-black/30",
        )}
      >
        <CardHeader className="space-y-4 px-8 pt-10">
          <div className="space-y-2 text-center">
            <div className="flex justify-center mb-4">
              <Image
                src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/aralogolanding-gK5iNztr5YiH0xaz2bWRDGFAQqVyxe.png"
                alt="ARA Property Services"
                width={150}
                height={60}
                priority
                className="h-auto"
              />
            </div>
            <CardDescription className="text-base text-zinc-400">
              {isSubmitted ? "Check your email" : "Reset your password"}
            </CardDescription>
          </div>
        </CardHeader>

        <CardContent className="p-8 pt-4 space-y-6">
          {isSubmitted ? (
            <div className="space-y-6">
              <p className="text-zinc-300 text-center">We've sent password reset instructions to:</p>
              <p className="text-white font-medium text-center">{email}</p>
              <p className="text-zinc-400 text-sm text-center">
                Didn't receive the email? Check your spam folder or try again.
              </p>
              <Button
                type="button"
                onClick={handleResend}
                className="w-full h-12 relative group overflow-hidden
                  bg-gradient-to-r from-[#3D4D61] to-[#A4D321]
                  text-white font-medium
                  shadow-lg shadow-black/20
                  hover:from-[#A4D321] hover:to-[#3D4D61]
                  transition-all duration-300"
              >
                Resend Email
              </Button>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-4">
                <p className="text-zinc-400 text-sm">
                  Enter your email address and we'll send you instructions to reset your password.
                </p>
                <div>
                  <Input
                    type="email"
                    placeholder="Email address"
                    className={cn("h-12 bg-zinc-800/50 border-zinc-700/50 text-white", error && "border-red-500")}
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    disabled={isLoading}
                  />
                  {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
                </div>

                <Button
                  type="submit"
                  className="w-full h-12 relative group overflow-hidden
                    bg-gradient-to-r from-[#3D4D61] to-[#A4D321]
                    text-white font-medium
                    shadow-lg shadow-black/20
                    hover:from-[#A4D321] hover:to-[#3D4D61]
                    transition-all duration-300"
                  disabled={isLoading}
                >
                  {isLoading ? "Sending..." : "Send Reset Instructions"}
                </Button>
              </div>
            </form>
          )}

          <div className="text-center">
            <button
              type="button"
              onClick={onLoginClick}
              className="text-[#A4D321] hover:text-[#8fb81d] text-sm
                transition-colors underline underline-offset-4 decoration-zinc-700
                font-medium"
            >
              Back to Sign In
            </button>
          </div>
        </CardContent>

        <CardFooter className="px-8 pb-10 pt-2">
          <div className="text-center text-sm text-zinc-500 w-full">
            By continuing, you agree to our{" "}
            <a
              href="#"
              className="text-[#A4D321] hover:text-[#8fb81d]
                transition-colors underline underline-offset-4 decoration-zinc-700
                font-medium"
            >
              Terms of Service
            </a>{" "}
            and{" "}
            <a
              href="#"
              className="text-[#A4D321] hover:text-[#8fb81d]
                transition-colors underline underline-offset-4 decoration-zinc-700
                font-medium"
            >
              Privacy Policy
            </a>
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}
