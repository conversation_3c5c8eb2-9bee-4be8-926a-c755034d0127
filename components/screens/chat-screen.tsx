"use client"

import TextAnimation from '@/components/TextAnimation'
import Message from '@/components/Message'
import { useParams } from 'next/navigation'
import React, { useCallback, useEffect, useState } from 'react' // Added React import for clarity, though often implicit
import { useConversation } from '@11labs/react'
import { toast } from 'sonner'
import { X } from 'lucide-react'
import { ChatWindow } from '../chat/chat-window'

interface UserData {
  name: string
  role: string
  department: string
}

interface ChatScreenProps {
  apiKey?: string
  onNavigate?: (screen: string) => void
  userData?: UserData
}

// Removed unused props: apiKey, onNavigate, userData
export function ChatScreen({ }: ChatScreenProps) {
  const [isClient, setIsClient] = useState(false); // Added isClient state
  const [currentText, setCurrentText] = useState('')
  const [messages, setMessages] = useState<unknown[]>([]) // Use unknown[]
  const [isTranscriptOpen, setIsTranscriptOpen] = useState(false)
  const conversationId = `askaraConv_${performance.now()}_${Math.random()}`

  useEffect(() => { // Added useEffect to set isClient
    setIsClient(true);
  }, []);
  const loadConversation = useCallback(() => {
    fetch(`/api/c?id=${conversationId}`)
      .then((res) => res.json())
      .then((res) => {
        if (res.length > 0) {
          setMessages(
            res.map((i: unknown) => ({ // Use unknown
              ...(i as Record<string, unknown>), // Cast to record for spreading
              formatted: {
                text: i.content_transcript,
                transcript: i.content_transcript,
              },
            })),
          )
        }
      })
      .catch(err => console.error("Error loading conversation:", err))
  }, [conversationId])
  
  const conversation = useConversation({
    onError: (error: string) => { 
      toast(error) 
      console.error("ElevenLabs Error:", error)
    },
    onConnect: () => { 
      toast('Connected to ElevenLabs voice assistant.')
    },
    onMessage: (props: { message: string; source: 'ai' | 'user' }) => {
      const { message, source } = props
      if (source === 'ai') setCurrentText(message)
      
      fetch('/api/c', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: conversationId,
          item: {
            type: 'message',
            status: 'completed',
            object: 'realtime.item',
            id: 'item_' + Math.random(),
            role: source === 'ai' ? 'assistant' : 'user',
            content: [{ type: 'text', transcript: message }],
          },
        }),
      })
      .then(loadConversation)
      .catch(err => console.error("Error saving message:", err))
    },
  })
  
  const connectConversation = useCallback(async () => {
    toast('Setting up ElevenLabs voice assistant...')
    try {
      await navigator.mediaDevices.getUserMedia({ audio: true })
      const response = await fetch('/api/i', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      })
      const data = await response.json()
      if (data.error) return toast(data.error)
      await conversation.startSession({ signedUrl: data.apiKey })
    } catch (error) {
      toast('Failed to set up ElevenLabs voice assistant')
      console.error("Error connecting to ElevenLabs:", error)
    }
  }, [conversation])
  
  const disconnectConversation = useCallback(async () => {
    await conversation.endSession()
  }, [conversation])
  
  const handleStartListening = () => {
    if (conversation.status !== 'connected') connectConversation()
  }
  
  const handleStopListening = () => {
    if (conversation.status === 'connected') disconnectConversation()
  }
  
  useEffect(() => {
    return () => {
      disconnectConversation()
    }
  }, [disconnectConversation])
  
  return (
    <div className="flex flex-col h-full w-full">
      <TextAnimation 
        currentText={currentText}
        isAudioPlaying={conversation.isSpeaking}
        onStartListening={handleStartListening}
        onStopListening={handleStopListening}
      />
      
      {isClient ? <ChatWindow /> : null /* Or a loading indicator */}
      
      {messages.length > 0 && (
        <button className="text-sm fixed top-2 right-4 underline" onClick={() => setIsTranscriptOpen(!isTranscriptOpen)}>
          Show Conversation
        </button>
      )}
      
      {isTranscriptOpen && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
          <div className="bg-[#121624] text-white p-4 rounded shadow-lg max-w-[90%] max-h-[90%] overflow-y-scroll">
            <div className="flex flex-row items-center justify-between">
              <span>Conversation History</span>
              <button onClick={() => setIsTranscriptOpen(false)}>
                <X />
              </button>
            </div>
            <div className="border-t border-white/10 py-4 mt-4 flex flex-col gap-y-4">
              {messages.map((conversationItem) => (
                <Message key={conversationItem.id} conversationItem={conversationItem} />
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
