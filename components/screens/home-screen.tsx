"use client"

import { useState, useRef, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { streamText } from "ai"
import { openai } from "@ai-sdk/openai"
import { Loading } from "@/components/ui/loading"
import { useToast } from "@/components/ui/toast-context"

interface Message {
  id: string
  content: string
  role: "user" | "assistant"
  isStreaming?: boolean
}

interface UserData {
  name: string
  role: string
  department: string
}

interface HomeScreenProps {
  apiKey: string
  onNavigate: (screen: string) => void
  userData: UserData
}

// Removed unused props: onNavigate, userData
export function HomeScreen({ apiKey }: HomeScreenProps) {
  const [messages, setMessages] = useState<Message[]>([])
  const [input, setInput] = useState("")
  const [isRecording, setIsRecording] = useState(false)
  const [isConnected, setIsConnected] = useState(!!apiKey)
  const [isProcessing, setIsProcessing] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const { showToast } = useToast()

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [messages])

  useEffect(() => {
    setIsConnected(!!apiKey)
  }, [apiKey])

  const handleSendMessage = async () => {
    if (!input.trim() || !isConnected || isProcessing) return

    const userMessage: Message = {
      id: Date.now().toString(),
      content: input,
      role: "user",
    }

    setMessages((prev) => [...prev, userMessage])
    setInput("")
    setIsProcessing(true)

    const assistantMessage: Message = {
      id: (Date.now() + 1).toString(),
      content: "",
      role: "assistant",
      isStreaming: true,
    }

    setMessages((prev) => [...prev, assistantMessage])

    try {
      const result = await streamText({
        model: openai("gpt-4o"),
        prompt: input,
        apiKey: apiKey,
        onChunk: ({ chunk }) => {
          if (chunk.type === "text-delta") {
            setMessages((prev) =>
              prev.map((msg) => (msg.id === assistantMessage.id ? { ...msg, content: msg.content + chunk.text } : msg)),
            )
          }
        },
      })

      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === assistantMessage.id ? { ...msg, content: result.text, isStreaming: false } : msg,
        ),
      )
    } catch (error: unknown) { // Type error as unknown
      console.error("Error generating response:", error)
      const errorMessage = error instanceof Error ? error.message : String(error);
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === assistantMessage.id
            ? { ...msg, content: "Sorry, I encountered an error. Please try again.", isStreaming: false }
            : msg,
        ),
      )
      showToast(`Error: ${errorMessage || "Failed to generate response"}`, "error")
    } finally {
      setIsProcessing(false)
    }
  }

  const toggleMicrophone = async () => {
    if (!isConnected || isProcessing) return

    setIsRecording(!isRecording)

    if (!isRecording) {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
        const mediaRecorder = new MediaRecorder(stream)
        const audioChunks: Blob[] = []

        mediaRecorder.addEventListener("dataavailable", (event) => {
          audioChunks.push(event.data)
        })

        mediaRecorder.addEventListener("stop", async () => {
          const audioBlob = new Blob(audioChunks, { type: "audio/webm" })
          setIsProcessing(true)

          try {
            // Create form data for the API
            const formData = new FormData()
            formData.append("file", audioBlob, "audio.webm")
            formData.append("apiKey", apiKey)

            // Call our voice API
            const response = await fetch("/api/voice", {
              method: "POST",
              body: formData,
            })

            if (!response.ok) {
              const errorData = await response.json()
              throw new Error(errorData.error || "Failed to transcribe audio")
            }

            const result = await response.json()
            setInput(result.text)

            // Stop all tracks
            // Stop all tracks
            stream.getTracks().forEach((track) => track.stop())
          } catch (error: unknown) { // Type error as unknown
            console.error("Error processing audio:", error)
            const errorMessage = error instanceof Error ? error.message : String(error);
            showToast(`Error: ${errorMessage || "Failed to process audio"}`, "error")
            setIsRecording(false)
            setIsProcessing(false)
          }
        })

        mediaRecorder.start()
        setTimeout(() => mediaRecorder.stop(), 5000) // Record for 5 seconds
        setTimeout(() => mediaRecorder.stop(), 5000) // Record for 5 seconds
      } catch (error: unknown) { // Type error as unknown
        console.error("Error recording audio:", error)
        const errorMessage = error instanceof Error ? error.message : String(error);
        showToast(`Error: ${errorMessage || "Failed to access microphone"}`, "error")
      }
    } else {
      setIsRecording(false)
    }
  }

  return (
    <div className="flex-1 flex flex-col w-full overflow-hidden [background:linear-gradient(180deg,rgba(18,22,36,0)_0%,rgba(18,22,36,1)_100%)]">
      {/* Chat Area */}
      <div className="flex-1 w-full overflow-y-auto p-4 space-y-4 pb-20">
        <AnimatePresence>
          {messages.map((message) => (
            <motion.div
              key={message.id}
              className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <div
                className={`relative max-w-[80%] rounded-lg p-2.5 ${
                  message.role === "user"
                    ? "bg-gradient-to-br from-[#3D4D61]/20 to-[#3D4D61]/10 border border-[#3D4D61]/30 text-white"
                    : "bg-gradient-to-br from-[#121624]/80 to-[#121624]/40 border border-[#ffffff]/10 text-white"
                } shadow-sm`}
              >
                <motion.div
                  className="absolute -inset-px rounded-lg z-0 opacity-0 pointer-events-none"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: message.role === "user" ? 0.5 : 0.3 }}
                  style={{
                    background:
                      message.role === "user"
                        ? "radial-gradient(circle, rgba(61,77,97,0.3) 0%, rgba(61,77,97,0.1) 50%, rgba(61,77,97,0) 100%)"
                        : "radial-gradient(circle, rgba(164,211,33,0.3) 0%, rgba(164,211,33,0.1) 50%, rgba(164,211,33,0) 100%)",
                  }}
                />
                <p className="relative z-10 text-sm break-words">
                  {message.content}
                  {message.isStreaming && <span className="ml-1 inline-block w-1.5 h-3.5 bg-white animate-pulse" />}
                </p>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>

        {isProcessing && !messages.some((m) => m.isStreaming) && (
          <div className="flex justify-center my-4">
            <Loading size="small" color="primary" text="Processing..." />
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="absolute bottom-16 left-0 right-0 p-4 bg-gradient-to-t from-[#121624] to-transparent">
        <div className="flex items-center space-x-2">
          <input
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={(e) => e.key === "Enter" && handleSendMessage()}
            placeholder="Type your message..."
            className="flex-1 px-4 py-2 rounded-xl bg-[#121624]/80 backdrop-blur-sm border border-[#ffffff]/10 focus:outline-none focus:ring-2 focus:ring-[#A4D321]/50 text-white"
            disabled={!isConnected || isProcessing}
          />
          <motion.button
            className={`p-2 rounded-full ${
              isRecording ? "bg-red-500 text-white" : "bg-[#121624]/80 backdrop-blur-sm border border-[#ffffff]/10"
            }`}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={toggleMicrophone}
            disabled={!isConnected || isProcessing}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z" />
              <path d="M19 10v2a7 7 0 0 1-14 0v-2" />
              <line x1="12" x2="12" y1="19" y2="22" />
            </svg>
          </motion.button>
          <motion.button
            className="p-2 rounded-full bg-[#A4D321] text-black"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={handleSendMessage}
            disabled={!isConnected || isProcessing || !input.trim()}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="m22 2-7 20-4-9-9-4Z" />
              <path d="M22 2 11 13" />
            </svg>
          </motion.button>
        </div>
      </div>
    </div>
  )
}
