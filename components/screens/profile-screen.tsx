"use client"

import { motion } from "framer-motion"
import { StatusBar } from "@/components/ui/status-bar"
import { NavigationBar } from "@/components/ui/navigation-bar"
import { HomeIndicator } from "@/components/ui/home-indicator"
import { AppHeader } from "@/components/ui/app-header"
// Removed unused Badge
import { Mail, Phone, MapPin, Settings, HelpCircle, LogOut, Linkedin, Twitter, Globe } from "lucide-react"
// Removed unused Avatar, AvatarFallback, AvatarImage
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar" // Re-import if used later
interface UserData {
  name: string
  role: string
  department: string
  email?: string
  phone?: string
  location?: string
  linkedin?: string
  twitter?: string
  website?: string
}

export function ProfileScreen({
  onNavigate,
  userData,
  isDrawer = false,
}: {
  onNavigate: (screen: string) => void
  userData?: UserData
  isDrawer?: boolean
}) {
  return (
    <div className={`flex flex-col w-full ${isDrawer ? "h-full" : "h-full min-h-screen"} relative`}>
      {!isDrawer && (
        <>
          <StatusBar />
          <AppHeader userName="Profile" userRole="Property Services" onContactsClick={() => onNavigate('contacts')} />
        </>
      )}

      {/* Profile Content */}
      <ScrollArea className="flex-1 w-full">
        <div className="p-6 space-y-6">
          {/* Contact Info */}
          <div className="space-y-2 bg-black/40 rounded-lg p-4 border border-[#ffffff]/10 shadow-sm">
            {userData?.email && <div className="flex items-center">
              <Mail className="h-4 w-4 mr-2 text-[#A4D321]" />
              <span className="text-sm text-white">{userData.email}</span>
            </div>}
            {userData?.phone && <div className="flex items-center">
              <Phone className="h-4 w-4 mr-2 text-[#A4D321]" />
              <span className="text-sm text-white">{userData.phone}</span>
            </div>}
            {userData?.location && <div className="flex items-center">
              <MapPin className="h-4 w-4 mr-2 text-[#A4D321]" />
              <span className="text-sm text-white">{userData.location}</span>
            </div>}
          </div>

          {/* Social Links */}
          <div className="space-y-2 bg-black/40 rounded-lg p-4 border border-[#ffffff]/10 shadow-sm">
            <p className="text-sm font-semibold mb-2 text-white">Social Links</p>
            {userData?.linkedin && <div className="flex items-center">
              <Linkedin className="h-4 w-4 mr-2 text-[#A4D321]" />
              <a href={userData.linkedin} target="_blank" rel="noopener noreferrer" className="text-sm text-white hover:underline">LinkedIn Profile</a>
            </div>}
            {userData?.twitter && <div className="flex items-center">
              <Twitter className="h-4 w-4 mr-2 text-[#A4D321]" />
              <a href={userData.twitter} target="_blank" rel="noopener noreferrer" className="text-sm text-white hover:underline">Twitter Profile</a>
            </div>}
            {userData?.website && <div className="flex items-center">
              <Globe className="h-4 w-4 mr-2 text-[#A4D321]" />
              <a href={userData.website} target="_blank" rel="noopener noreferrer" className="text-sm text-white hover:underline">Personal Website</a>
            </div>}
            {!(userData?.linkedin || userData?.twitter || userData?.website) && <p className="text-sm text-[#ffffffb2]">No social links provided.</p>}
          </div>

          {/* Action Buttons */}
          <div className="space-y-2">
            <motion.button
              className="w-full py-2 rounded-lg bg-[#121624]/80 text-white text-sm font-medium border border-[#ffffff]/10 flex items-center justify-center"
              whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.1)" }}
              onClick={() => onNavigate("settings")}
            >
              <Settings className="mr-2 h-4 w-4 text-[#A4D321]" />
              Settings
            </motion.button>
            <motion.button
              className="w-full py-2 rounded-lg bg-[#121624]/80 text-white text-sm font-medium border border-[#ffffff]/10 flex items-center justify-center"
              whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.1)" }}
            >
              <HelpCircle className="mr-2 h-4 w-4 text-[#A4D321]" />
              Help & Support
            </motion.button>
            <motion.button
              className="w-full py-2 rounded-lg bg-red-500/20 text-red-500 text-sm font-medium border border-red-500/30 flex items-center justify-center"
              whileHover={{ backgroundColor: "rgba(239, 68, 68, 0.3)" }}
              onClick={() => onNavigate("logout")}
            >
              <LogOut className="mr-2 h-4 w-4" />
              Log Out
            </motion.button>
          </div>
        </div>
      </ScrollArea>

      {!isDrawer && (
        <>
          <NavigationBar activeScreen="profile" onNavigate={onNavigate} />
          <HomeIndicator />
        </>
      )}
    </div>
  )
}
