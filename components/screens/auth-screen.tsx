"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { Download } from "lucide-react"
import { StatusBar } from "@/components/ui/status-bar"
import { HomeIndicator } from "@/components/ui/home-indicator"
import { <PERSON><PERSON> } from "@/components/ui/button"
// Input removed as form is gone
import { toast } from "sonner"
import { useRouter } from 'next/navigation'
// useUser removed as setUser is no longer needed

interface AuthScreenProps {
  screen: 'login' | 'signup' | 'forgot'
  onNavigate: (screen: 'login' | 'signup' | 'forgot' | 'chat' | 'dashboard') => void
  // onLogin removed
}

// Removed unused 'onNavigate' prop
export function AuthScreen({ screen }: AuthScreenProps) {
  // setUser removed
  // email, password, name, loading state removed (already commented out)
  // Removed unused 'deferredInstallPrompt' state
  const router = useRouter();

  // --- PWA Install Logic (unchanged) ---
  useEffect(() => {
    const handler = (e: Event) => {
      e.preventDefault()
      setDeferredInstallPrompt(e)
      if (!window.matchMedia('(display-mode: standalone)').matches) {
        console.log("`beforeinstallprompt` event fired. Prompt stored.")
        showInstallToast(e)
      }
    }
    window.addEventListener('beforeinstallprompt', handler)
    return () => {
      window.removeEventListener('beforeinstallprompt', handler)
    }
  }, [showInstallToast]) // Add showInstallToast dependency
  
  const handleInstallPWA = async (promptEvent: Event | null) => {
    if (!promptEvent) {
      console.log("Install prompt not available.")
      toast.error("Installation is not available or the app might already be installed.")
      return
    }
    console.log("Triggering PWA install prompt...")
    // @ts-expect-error - prompt() exists on BeforeInstallPromptEvent but TS doesn't know
    promptEvent.prompt()
    // @ts-expect-error - userChoice exists on BeforeInstallPromptEvent but TS doesn't know
    const { outcome } = await promptEvent.userChoice
    console.log(`User response to the install prompt: ${outcome}`)
    setDeferredInstallPrompt(null)
  }

  const showInstallToast = (promptEvent: Event) => {
    toast("Install AskARA?", {
      description: "Add to your Home Screen for easy access.",
      icon: <Download className="w-4 h-4" />,
      action: {
        label: "Install",
        onClick: () => handleInstallPWA(promptEvent),
      },
      cancel: {
        label: "Later",
        onClick: () => console.log("User dismissed install prompt."),
      },
      duration: Infinity,
      id: 'pwa-install-toast',
    })
  }
  // --- End PWA Install Logic ---

  // handleLogin removed
  // handleSignup removed
  // handleForgotPassword removed
  // handleSubmit removed


  const getTitle = () => {
    switch (screen) {
      case "signup":
        return "Create Account"
      case "forgot":
        return "Reset Password"
      case "login":
      default:
        return "Welcome Back"
    }
  }

  const getSubtitle = () => {
    switch (screen) {
      case "signup":
        return "Enter your details to get started."
      case "forgot":
        return "Enter your email to reset your password."
      case "login":
      default:
        return "Login to your AskARA account."
    }
  }

  return (
    <>
      <StatusBar />

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="flex flex-col items-center justify-center min-h-screen w-full p-8 bg-gradient-to-br from-gray-900 via-black to-gray-900 text-white"
      >
        <div className="w-full max-w-sm text-center">
          <div className="w-16 h-16 rounded-full bg-gradient-to-r from-[#3D4D61] to-[#A4D321] mx-auto mb-6 flex items-center justify-center">
            {/* Consider adding a dynamic icon or initial based on app name */}
            <span className="text-2xl font-bold">A</span>
          </div>

          <h1 className="text-2xl font-bold mb-1">{getTitle()}</h1>
          <p className="text-sm text-gray-400 mb-8">{getSubtitle()}</p>

          {/* Form removed as authentication is handled by Clerk pages */}
          {/* <form onSubmit={handleSubmit} className="space-y-4"> ... </form> */}
          <div className="space-y-4 text-sm text-gray-500">
             <p>Authentication is handled via dedicated pages.</p>
             {/* Add buttons to navigate to Clerk pages */}
             {screen === 'login' && <Button onClick={() => router.push('/sign-in')} className="w-full bg-gradient-to-r from-[#3D4D61] to-[#A4D321] hover:opacity-90 text-white font-semibold rounded-md transition duration-200">Go to Sign In</Button>}
             {screen === 'signup' && <Button onClick={() => router.push('/sign-up')} className="w-full bg-gradient-to-r from-[#3D4D61] to-[#A4D321] hover:opacity-90 text-white font-semibold rounded-md transition duration-200">Go to Sign Up</Button>}
             {/* Consider adding a forgot password link/button if Clerk provides one */}
          </div>

          <div className="mt-6 text-center">
            {/* Links adjusted to use router.push to Clerk pages */}
            {screen === "login" && (
              <>
                {/* Forgot password link might need to point to Clerk's flow if available */}
                {/* <button onClick={() => router.push('/forgot-password')} className="text-xs text-gray-400 hover:text-[#A4D321] transition duration-200">
                  Forgot Password?
                </button> */}
                <p className="mt-4 text-xs text-gray-400">
                  Don&apos;t have an account?{" "}
                  <button onClick={() => router.push('/sign-up')} className="font-semibold text-[#A4D321] hover:underline">
                    Sign Up
                  </button>
                </p>
              </>
            )}
            {(screen === "signup" || screen === "forgot") && (
              <p className="mt-4 text-xs text-gray-400">
                Already have an account?{" "}
                <button onClick={() => router.push('/sign-in')} className="font-semibold text-[#A4D321] hover:underline">
                  Log In
                </button>
              </p>
            )}
          </div>
        </div>
      </motion.div>

      <HomeIndicator />
    </>
  )
}
