"use client"

import { SignIn } from '@clerk/nextjs'
import { useRouter } from 'next/navigation'

interface LoginScreenProps {
  onLogin?: (email: string, password: string) => void
  onForgotPassword?: () => void
  onSignUp?: () => void
}

export default function LoginScreen({ onForgotPassword, onSignUp }: LoginScreenProps) {
  const router = useRouter()

  return (
    <div className="flex items-center justify-center min-h-screen bg-black p-4">
      <div className="w-full max-w-md">
        <SignIn 
          redirectUrl="/"
          signUpUrl="/sign-up"
          appearance={{
            elements: {
              formButtonPrimary: 'bg-gradient-to-r from-[#3D4D61] to-[#A4D321] hover:from-[#A4D321] hover:to-[#3D4D61] text-sm normal-case',
              card: 'bg-zinc-900 border-2 border-zinc-800/50 rounded-3xl shadow-[0_24px_48px_-12px] shadow-black/30',
              headerTitle: 'text-white',
              headerSubtitle: 'text-zinc-400',
              formFieldInput: 'h-12 bg-zinc-800/50 border-zinc-700/50 text-white',
              footerActionLink: 'text-[#A4D321] hover:text-[#8fb81d]',
              socialButtonsIconButton: 'bg-zinc-800/50 hover:bg-zinc-700/50 border-zinc-700/50 hover:border-zinc-600/50 text-white',
              socialButtonsBlockButton: 'bg-zinc-800/50 hover:bg-zinc-700/50 border-zinc-700/50 hover:border-zinc-600/50 text-white',
            }
          }}
        />

        {/* Legacy custom navigation buttons */}
        <div className="mt-4 text-center space-y-2 hidden">
          {onForgotPassword && (
            <button
              onClick={onForgotPassword}
              className="text-[#A4D321] hover:text-[#8fb81d] text-sm
                transition-colors underline underline-offset-4 decoration-zinc-700
                font-medium"
            >
              Forgot your password?
            </button>
          )}

          {onSignUp && (
            <div>
              <button
                onClick={onSignUp}
                className="text-[#A4D321] hover:text-[#8fb81d] text-sm
                  transition-colors underline underline-offset-4 decoration-zinc-700
                  font-medium"
              >
                Don't have an account? Sign up
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
