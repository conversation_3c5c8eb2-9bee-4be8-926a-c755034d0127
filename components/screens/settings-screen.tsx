"use client"

import { useState, useEffect } from "react"
import { useTheme } from "next-themes"
import { motion } from "framer-motion"
import { Switch } from "@/components/ui/switch"
// Removed unused Button
import {
  User, Lock, Bell, Moon, Trash2, HelpCircle, Download, Info, LogOut, ChevronRight
  // Removed unused MessageSquare
} from "lucide-react"
interface SettingsScreenProps {
  onNavigate: (screen: string, params?: any) => void;
  // Add props for initial state if needed, e.g., initialNotificationsEnabled
}

// Helper component for settings list items
const SettingsItem = ({
  icon: Icon,
  label,
  onClick,
  control,
  iconColor,
}: {
  icon: React.ElementType; // Corrected type definition placement
  label: string;
  onClick?: () => void;
  control?: React.ReactNode;
  iconColor?: string;
}) => (
  <div
    className={`flex items-center justify-between p-4 bg-[#121624]/80 rounded-lg border border-[#ffffff]/10 ${onClick ? 'cursor-pointer hover:bg-white/5' : ''}`}
    onClick={onClick}
  >
    <div className="flex items-center">
      <Icon className={`w-5 h-5 mr-3 ${iconColor}`} />
      <span className="text-white text-sm font-medium">{label}</span>
    </div>
    {control ? control : (onClick && <ChevronRight className="w-5 h-5 text-[#ffffffb2]" />)}
  </div>
);

// This component now only renders the settings content
export function SettingsScreenContent({ onNavigate }: SettingsScreenProps) {
  const { theme, setTheme } = useTheme()
  const [notificationsEnabled, setNotificationsEnabled] = useState(true)
  const [deferredInstallPrompt, setDeferredInstallPrompt] = useState<Event | null>(null)
  const [isPWAInstallable, setIsPWAInstallable] = useState(false)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    // PWA Install prompt handling
    const handler = (e: Event) => {
      e.preventDefault();
      setDeferredInstallPrompt(e);
      // Show install button only if not already installed
      if (!window.matchMedia('(display-mode: standalone)').matches) {
        setIsPWAInstallable(true);
        console.log("`beforeinstallprompt` event fired.");
      } else {
        console.log("App is already installed, hiding install button.");
        setIsPWAInstallable(false);
      }
    };
    window.addEventListener('beforeinstallprompt', handler);
    // Initial check in case the event fired before the listener was added
    if (!window.matchMedia('(display-mode: standalone)').matches) {
       // Check if the prompt is already available
       // This part is tricky as the event might have been missed
       // A common approach is to assume not installable initially
       // unless the beforeinstallprompt event fires.
    } else {
        setIsPWAInstallable(false);
    }

    return () => {
      window.removeEventListener('beforeinstallprompt', handler);
    };
  }, []);

  const handleInstallPWA = async () => {
    if (!deferredInstallPrompt) {
      console.log("Install prompt not available.");
      return;
    }
    // @ts-expect-error - prompt() exists on BeforeInstallPromptEvent
    deferredInstallPrompt.prompt();
    // @ts-expect-error - userChoice exists on BeforeInstallPromptEvent
    const { outcome } = await deferredInstallPrompt.userChoice;
    console.log(`User response to the install prompt: ${outcome}`);
    setDeferredInstallPrompt(null);
    setIsPWAInstallable(false);
  };

  const handleClearHistory = () => {
    console.log("Clear Chat History clicked");
    alert("Chat history clearing functionality not yet implemented.");
  }

  const appVersion = process.env.NEXT_PUBLIC_APP_VERSION || "1.0.0";

  // Only show the UI after mounting to avoid hydration mismatch for theme
  if (!mounted) {
    return null
  }

  const isDarkMode = theme === "dark";

  // Return only the settings content structure
  return (
     <div className="p-4 space-y-8"> {/* Adjusted padding, removed scroll */}
        {/* Account Section */}
        <section>
          <h2 className="text-xs font-semibold uppercase text-[#ffffffb2] mb-3">Account</h2>
          <div className="space-y-2">
            <SettingsItem
              icon={User}
              label="Edit Profile"
              onClick={() => onNavigate('edit-profile')}
            />
            <SettingsItem
              icon={Lock}
              label="Change Password"
              onClick={() => onNavigate('change-password')}
            />
          </div>
        </section>

        {/* Preferences Section */}
        <section>
          <h2 className="text-xs font-semibold uppercase text-[#ffffffb2] mb-3">Preferences</h2>
          <div className="space-y-2">
            <SettingsItem
              icon={Bell}
              label="Notifications"
              control={
                <Switch
                  checked={notificationsEnabled}
                  onCheckedChange={setNotificationsEnabled}
                  className="data-[state=checked]:bg-[#A4D321] data-[state=unchecked]:bg-gray-600"
                />
              }
            />
            <SettingsItem
              icon={Moon}
              label="Dark Mode"
              control={
                <Switch
                  checked={isDarkMode}
                  onCheckedChange={(checked) => setTheme(checked ? 'dark' : 'light')}
                  className="data-[state=checked]:bg-[#A4D321] data-[state=unchecked]:bg-gray-600"
                />
              }
            />
            <SettingsItem
              icon={Trash2} // Changed icon to Trash2 for consistency
              label="Clear Chat History"
              onClick={handleClearHistory}
            />
          </div>
        </section>

        {/* Support Section */}
        <section>
          <h2 className="text-xs font-semibold uppercase text-[#ffffffb2] mb-3">Support</h2>
          <div className="space-y-2">
            <SettingsItem
              icon={HelpCircle}
              label="Help & Support"
              onClick={() => onNavigate('help-support')}
            />
            {isPWAInstallable && (
               <SettingsItem
                  icon={Download}
                  label="Install App"
                  onClick={handleInstallPWA}
               />
            )}
            <SettingsItem
              icon={Info}
              label="About AskARA"
              onClick={() => onNavigate('about')}
            />
          </div>
        </section>

        {/* Logout Button */}
        <motion.button
          className="w-full mt-6 py-2.5 rounded-lg bg-red-500/20 text-red-500 text-sm font-medium border border-red-500/30 flex items-center justify-center"
          whileHover={{ backgroundColor: "rgba(239, 68, 68, 0.3)" }}
          onClick={() => onNavigate("logout")}
        >
          <LogOut className="mr-2 h-4 w-4" />
          Log Out
        </motion.button>

         {/* Version Info */}
         <p className="text-center text-xs text-[#ffffffb2] mt-6">Version {appVersion}</p>
      </div>
  );
}
