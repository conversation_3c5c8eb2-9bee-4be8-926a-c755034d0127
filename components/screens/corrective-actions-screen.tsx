"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
// Removed unused ScreenLayout
import { AlertTriangle, CheckCircle, ChevronDown, ChevronUp, Clock, Filter, Search, User } from "lucide-react" // Removed unused Plus
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger, TabsContent } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { cn } from "@/lib/utils"
import { ScrollArea } from "@/components/ui/scroll-area"

interface CorrectiveActionsScreenProps {
  onNavigate: (screen: string) => void
  userData: {
    name: string
    role: string
    department: string
  }
}

interface CorrectiveAction {
  id: string
  title: string
  description: string
  status: "open" | "in-progress" | "completed" | "overdue"
  priority: "high" | "medium" | "low"
  dueDate: string
  assignee: {
    name: string
    avatar?: string
  }
  location: string
  inspectionId: string
  expanded?: boolean
}

// Removed unused props: onNavigate, userData
export function CorrectiveActionsScreen({ }: CorrectiveActionsScreenProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [actions, setActions] = useState<CorrectiveAction[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Fetch corrective actions from API
    fetchActions()
  }, [])

  const fetchActions = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // In a real app, this would be an API call
      // const response = await fetch('/api/corrective-actions')
      // const data = await response.json()
      // setActions(data.actions)

      // For now, just set empty array
      // For now, just set empty array
      setActions([])
    } catch (err: unknown) { // Type err as unknown
      console.error("Error fetching corrective actions:", err)
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage || "Failed to load corrective actions")
      setIsLoading(false)
    }
  }

  const toggleActionExpand = (actionId: string) => {
    setActions((prevActions) =>
      prevActions.map((action) => (action.id === actionId ? { ...action, expanded: !action.expanded } : action)),
    )
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "border-green-500"
      case "in-progress":
        return "border-blue-500"
      case "open":
        return "border-amber-500"
      case "overdue":
        return "border-red-500"
      default:
        return ""
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case "in-progress":
        return <Clock className="h-5 w-5 text-blue-500" />
      case "open":
        return <AlertTriangle className="h-5 w-5 text-amber-500" />
      case "overdue":
        return <AlertTriangle className="h-5 w-5 text-red-500" />
      default:
        return null
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "text-red-500"
      case "medium":
        return "text-amber-500"
      case "low":
        return "text-green-500"
      default:
        return "text-white"
    }
  }

  const filterActions = (filter: string) => {
    if (filter === "all") {
      return actions
    }
    return actions.filter((action) => action.status === filter)
  }

  return (
    <ScrollArea className="flex-1 w-full h-full">
      <div className="p-6">
        {/* Search and Filter */}
        <div className="flex gap-2 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[#ffffffb2]" />
            <input
              type="text"
              placeholder="Search actions..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-9 pr-4 py-2 rounded-lg bg-black/40 backdrop-blur-sm border border-white/10 text-white text-sm focus:outline-none focus:ring-2 focus:ring-[#A4D321]/50"
            />
          </div>
          <Button variant="outline" size="icon" className="h-10 w-10 rounded-lg bg-black/40 border-white/10">
            <Filter className="h-4 w-4 text-white" />
          </Button>
        </div>

        {/* Tabs */}
        <Tabs defaultValue="all" className="w-full">
          <TabsList className="w-full grid grid-cols-4 bg-black/40 backdrop-blur-sm mb-6">
            <TabsTrigger value="all" className="data-[state=active]:text-[#A4D321]">
              All
            </TabsTrigger>
            <TabsTrigger value="open" className="data-[state=active]:text-[#A4D321]">
              Open
            </TabsTrigger>
            <TabsTrigger value="in-progress" className="data-[state=active]:text-[#A4D321]">
              In Progress
            </TabsTrigger>
            <TabsTrigger value="completed" className="data-[state=active]:text-[#A4D321]">
              Completed
            </TabsTrigger>
          </TabsList>

          {isLoading ? (
            <div className="flex items-center justify-center p-12">
              <div className="w-8 h-8 border-2 border-[#A4D321] border-t-transparent rounded-full animate-spin" />
            </div>
          ) : error ? (
            <div className="p-6 text-center">
              <p className="text-red-400 mb-4">{error}</p>
              <Button onClick={fetchActions}>Try Again</Button>
            </div>
          ) : actions.length === 0 ? (
            <div className="p-6 text-center">
              <p className="text-[#ffffffb2]">No corrective actions found</p>
            </div>
          ) : (
            <>
              <TabsContent value="all" className="space-y-4">
                {filterActions("all").map((action) => (
                  <ActionCard
                    key={action.id}
                    action={action}
                    toggleExpand={toggleActionExpand}
                    getStatusColor={getStatusColor}
                    getStatusIcon={getStatusIcon}
                    getPriorityColor={getPriorityColor}
                  />
                ))}
              </TabsContent>

              <TabsContent value="open" className="space-y-4">
                {filterActions("open").map((action) => (
                  <ActionCard
                    key={action.id}
                    action={action}
                    toggleExpand={toggleActionExpand}
                    getStatusColor={getStatusColor}
                    getStatusIcon={getStatusIcon}
                    getPriorityColor={getPriorityColor}
                  />
                ))}
              </TabsContent>

              <TabsContent value="in-progress" className="space-y-4">
                {filterActions("in-progress").map((action) => (
                  <ActionCard
                    key={action.id}
                    action={action}
                    toggleExpand={toggleActionExpand}
                    getStatusColor={getStatusColor}
                    getStatusIcon={getStatusIcon}
                    getPriorityColor={getPriorityColor}
                  />
                ))}
              </TabsContent>

              <TabsContent value="completed" className="space-y-4">
                {filterActions("completed").map((action) => (
                  <ActionCard
                    key={action.id}
                    action={action}
                    toggleExpand={toggleActionExpand}
                    getStatusColor={getStatusColor}
                    getStatusIcon={getStatusIcon}
                    getPriorityColor={getPriorityColor}
                  />
                ))}
              </TabsContent>
            </>
          )}
        </Tabs>
      </div>
    </ScrollArea>
  )
}

interface ActionCardProps {
  action: CorrectiveAction
  toggleExpand: (actionId: string) => void
  getStatusColor: (status: string) => string
  getStatusIcon: (status: string) => React.ReactNode
  getPriorityColor: (priority: string) => string
}

function ActionCard({ action, toggleExpand, getStatusColor, getStatusIcon, getPriorityColor }: ActionCardProps) {
  return (
    <motion.div
      className={cn(
        "bg-black/30 backdrop-blur-lg rounded-xl overflow-hidden border border-white/10 shadow-sm",
        `border-l-4 ${getStatusColor(action.status)}`,
      )}
      animate={{ height: action.expanded ? "auto" : "auto" }}
      transition={{ duration: 0.3 }}
    >
      <div className="p-4">
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-3">
            <div>{getStatusIcon(action.status)}</div>
            <div>
              <h3 className="text-white text-base font-medium">{action.title}</h3>
              <div className="flex items-center gap-2 mt-1">
                <span className={`text-xs font-medium ${getPriorityColor(action.priority)}`}>
                  {action.priority.charAt(0).toUpperCase() + action.priority.slice(1)}
                </span>
                <span className="w-1 h-1 bg-[#ffffffb2] rounded-full"></span>
                <span className="text-[#ffffffb2] text-xs">Due {action.dueDate}</span>
              </div>
            </div>
          </div>
          <motion.button
            onClick={() => toggleExpand(action.id)}
            whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.1)" }}
            className="w-8 h-8 flex items-center justify-center rounded-full"
          >
            {action.expanded ? (
              <ChevronUp className="h-5 w-5 text-white" />
            ) : (
              <ChevronDown className="h-5 w-5 text-white" />
            )}
          </motion.button>
        </div>

        {action.expanded && (
          <div className="mt-4 pt-4 border-t border-white/10">
            <p className="text-white text-sm mb-4">{action.description}</p>

            <div className="flex items-center gap-3 mb-3">
              <User className="h-4 w-4 text-[#ffffffb2]" />
              <div className="flex items-center gap-2">
                <Avatar className="h-5 w-5">
                  <AvatarImage src={`/placeholder.svg?height=20&width=20`} alt={action.assignee.name} />
                  <AvatarFallback className="bg-[#3D4D61] text-white text-xs">
                    {action.assignee.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </AvatarFallback>
                </Avatar>
                <p className="text-white text-sm">{action.assignee.name}</p>
              </div>
            </div>

            <div className="flex gap-2 mt-4">
              {action.status !== "completed" && (
                <Button className="flex-1 bg-[#A4D321] text-black hover:bg-[#A4D321]/80">
                  {action.status === "open" ? "Start Work" : "Mark Complete"}
                </Button>
              )}
              <Button variant="outline" className="flex-1 bg-white/5 text-white border-white/20 hover:bg-white/10">
                View Details
              </Button>
            </div>
          </div>
        )}
      </div>
    </motion.div>
  )
}
