"use client"

import type React from "react"

import { useState } from "react"
import { motion } from "framer-motion"
import { StatusBar } from "@/components/ui/status-bar"
import { NavigationBar } from "@/components/ui/navigation-bar"
import { HomeIndicator } from "@/components/ui/home-indicator"
import { Search, Phone, Mail, MapPin, Star, Plus, List, LayoutGrid } from "lucide-react"
import { AppHeader } from "@/components/ui/app-header"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Building, Briefcase, User } from "lucide-react"
import { cn } from "@/lib/utils"

interface Contact {
  id: string
  name: string
  role: string
  company: string
  phone: string
  email: string
  location: string
  department: string
  avatarColor: string
  isFavorite: boolean
}

interface UserData {
  name: string
  role: string
  department: string
}

// Sample contact data based on the images
const sampleContacts: Contact[] = [
  { id: "1", name: "<PERSON>", role: "Property Manager", company: "ARA Properties", phone: "+61 4XX XXX XXX", email: "<EMAIL>", location: "Sydney Office", department: "Facilities Management", avatarColor: "bg-green-500", isFavorite: true },
  { id: "2", name: "Michael Chen", role: "Maintenance Supervisor", company: "ARA Properties", phone: "+61 4XX XXX XXX", email: "<EMAIL>", location: "Melbourne Office", department: "Technical Services", avatarColor: "bg-blue-500", isFavorite: true },
  { id: "3", name: "Emma Wilson", role: "Inspection Coordinator", company: "ARA Properties", phone: "+61 4XX XXX XXX", email: "<EMAIL>", location: "Brisbane Office", department: "Quality Assurance", avatarColor: "bg-purple-500", isFavorite: false },
  { id: "4", name: "David Thompson", role: "Facilities Director", company: "ARA Properties", phone: "+61 4XX XXX XXX", email: "<EMAIL>", location: "Perth Office", department: "Executive Management", avatarColor: "bg-orange-500", isFavorite: false },
  { id: "5", name: "Jessica Lee", role: "Quality Assurance", company: "ARA Properties", phone: "+61 4XX XXX XXX", email: "<EMAIL>", location: "Adelaide Office", department: "Quality Control", avatarColor: "bg-pink-500", isFavorite: false },
  { id: "6", name: "Robert Garcia", role: "Safety Coordinator", company: "ARA Properties", phone: "+61 4XX XXX XXX", email: "<EMAIL>", location: "Hobart Office", department: "Health & Safety", avatarColor: "bg-teal-500", isFavorite: false },
  { id: "7", name: "Ling Zhang", role: "Compliance Officer", company: "ARA Properties", phone: "+61 4XX XXX XXX", email: "<EMAIL>", location: "Darwin Office", department: "Regulatory Affairs", avatarColor: "bg-yellow-500", isFavorite: true },
]


// Contact Card Component (for Card View)
function ContactCard({ contact, onNavigate }: { contact: Contact; onNavigate: (screen: string, params?: Record<string, unknown> | undefined) => void }) { // Type params
  const initials = contact.name.split(' ').map(n => n[0]).join('');
  return (
    <motion.div // Add missing opening tag
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`bg-black/40 rounded-lg p-4 border border-[#ffffff]/10 shadow-sm flex flex-col space-y-3 border-l-4 ${contact.avatarColor.replace('bg-', 'border-')}`} // Use border color for accent
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Avatar className={`h-10 w-10 ${contact.avatarColor}`}>
            <AvatarFallback className="text-white font-bold">{initials}</AvatarFallback>
          </Avatar>
          <div>
            <p className="text-base font-semibold text-white">{contact.name}</p>
            <p className="text-xs text-[#ffffffb2]">{contact.role}</p>
            <p className="text-xs text-[#ffffffb2] flex items-center"><Building className="w-3 h-3 mr-1"/>{contact.company}</p>
          </div>
        </div>
        {contact.isFavorite && <Star className="h-5 w-5 text-yellow-400 fill-yellow-400" />}
      </div>

      <div className="space-y-1.5 text-sm text-white">
        <div className="flex items-center"><Phone className="w-3.5 h-3.5 mr-2 text-[#A4D321]"/>{contact.phone}</div>
        <div className="flex items-center"><Mail className="w-3.5 h-3.5 mr-2 text-[#A4D321]"/>{contact.email}</div>
        <div className="flex items-center"><MapPin className="w-3.5 h-3.5 mr-2 text-[#A4D321]"/>{contact.location}</div>
        <div className="flex items-center"><Briefcase className="w-3.5 h-3.5 mr-2 text-[#A4D321]"/>{contact.department}</div>
      </div>

      <div className="flex justify-between pt-2 border-t border-white/10">
        <Button variant="ghost" size="sm" className="text-xs text-[#A4D321] hover:bg-white/10">
          <Phone className="w-3.5 h-3.5 mr-1.5"/>Call
        </Button>
        <Button variant="ghost" size="sm" className="text-xs text-[#A4D321] hover:bg-white/10">
          <Mail className="w-3.5 h-3.5 mr-1.5"/>Email
        </Button>
        <Button variant="ghost" size="sm" className="text-xs text-[#A4D321] hover:bg-white/10" onClick={() => onNavigate('profile', { userId: contact.id })}>
          <User className="w-3.5 h-3.5 mr-1.5"/>Profile
        </Button>
      </div>
    </motion.div>
  );
}


// Contact List Item Component (for List View)
function ContactListItem({ contact, onNavigate }: { contact: Contact; onNavigate: (screen: string, params?: Record<string, unknown> | undefined) => void }) { // Type params
    const initials = contact.name.split(' ').map(n => n[0]).join('');
    return (
      <motion.div // Add missing opening tag
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
          className="flex items-center justify-between p-3 border-b border-white/10 hover:bg-white/5 cursor-pointer"
          onClick={() => onNavigate('profile', { userId: contact.id })} // Navigate on click
        >
            <div className="flex items-center space-x-3 flex-1 min-w-0">
                <Avatar className={`h-9 w-9 ${contact.avatarColor}`}>
                  <AvatarFallback className="text-white font-bold text-sm">{initials}</AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                    <div className="flex items-center">
                        <p className="text-sm font-medium text-white truncate mr-2">{contact.name}</p>
                        {contact.isFavorite && <Star className="h-4 w-4 text-yellow-400 fill-yellow-400 flex-shrink-0" />}
                    </div>
                    <p className="text-xs text-[#ffffffb2] truncate">{contact.role} • {contact.department}</p>
                     <p className="text-xs text-[#ffffffb2] flex items-center truncate"><Building className="w-3 h-3 mr-1 flex-shrink-0"/>{contact.company}</p>
                </div>
            </div>
            {/* Optionally add truncated contact info or action icons for list view */}
            <div className="hidden md:flex items-center space-x-3 text-xs text-[#ffffffb2] ml-4">
                 <span className="flex items-center"><Phone className="w-3 h-3 mr-1"/>{contact.phone}</span>
                 <span className="flex items-center"><Mail className="w-3 h-3 mr-1"/>{contact.email}</span>
                 <span className="flex items-center"><MapPin className="w-3 h-3 mr-1"/>{contact.location}</span>
            </div>
        </motion.div>
    );
}

interface ContactsScreenProps {
  onNavigate: (screen: string, params?: Record<string, unknown> | undefined) => void; // Type params
  userData: UserData;
}

// Removed unused 'userData' prop
export function ContactsScreen({ onNavigate }: ContactsScreenProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [viewMode, setViewMode] = useState<'card' | 'list'>('card')

  const filteredContacts = sampleContacts.filter(contact =>
    contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contact.role.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contact.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contact.email.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="flex flex-col w-full h-full min-h-screen">
      <StatusBar />
      <AppHeader userName="Contacts" userRole="Company Directory" onContactsClick={() => onNavigate('contacts')} />

      <div className="flex-1 flex flex-col pt-[68px]">
        <div className="p-4 bg-black/40 border-b border-white/10 sticky top-[68px] z-10">
          <div className="relative mb-3">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="text"
              placeholder="Search contacts..."
              className="w-full pl-10 pr-4 py-2 bg-[#121624]/80 border border-white/10 rounded-md focus:ring-[#A4D321] focus:border-[#A4D321] text-sm"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex justify-end space-x-1">
             <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className={cn(
                      "text-xs px-2 py-1 h-auto",
                      viewMode === 'list' ? 'bg-white/20 text-white' : 'text-[#ffffffb2] hover:bg-white/10 hover:text-white'
                  )}
              >
                  <List className="w-3.5 h-3.5 mr-1.5"/> List View
              </Button>
               <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setViewMode('card')}
                  className={cn(
                      "text-xs px-2 py-1 h-auto",
                      viewMode === 'card' ? 'bg-white/20 text-white' : 'text-[#ffffffb2] hover:bg-white/10 hover:text-white'
                  )}
              >
                   <LayoutGrid className="w-3.5 h-3.5 mr-1.5"/> Card View
              </Button>
          </div>
        </div>

        <ScrollArea className="flex-1">
          <div className={cn("p-4", viewMode === 'card' ? 'space-y-4' : '')}>
            {filteredContacts.length === 0 && (
              <p className="text-center text-[#ffffffb2] mt-10">No contacts found.</p>
            )}

            {viewMode === 'card' ? (
              filteredContacts.map(contact => (
                <ContactCard key={contact.id} contact={contact} onNavigate={onNavigate} />
              ))
            ) : (
               <div className="bg-black/40 rounded-lg border border-[#ffffff]/10 shadow-sm overflow-hidden">
                   {filteredContacts.map(contact => (
                      <ContactListItem key={contact.id} contact={contact} onNavigate={onNavigate} />
                   ))}
               </div>
            )}
          </div>
        </ScrollArea>

        <div className="absolute bottom-24 right-6">
          <motion.button
            className="w-12 h-12 rounded-full bg-gradient-to-r from-[#3D4D61] to-[#A4D321] flex items-center justify-center shadow-lg"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Plus className="h-5 w-5 text-white" />
          </motion.button>
        </div>
      </div>

      <NavigationBar
        activeScreen="contacts"
        onNavigate={onNavigate}
        isRecording={false}
        onVoiceToggle={() => {}}
      />
      <HomeIndicator />
    </div>
  )
}
