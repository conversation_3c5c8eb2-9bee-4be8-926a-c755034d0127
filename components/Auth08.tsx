"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardFooter } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { Input } from "@/components/ui/input"
import { useState } from "react"
// Removed unused Image import
import type React from "react"

interface AuthCardProps extends React.HTMLAttributes<HTMLDivElement> {
  showApple?: boolean
  showGoogle?: boolean
  onSubmit?: (email: string, password: string) => void
}

export default function Auth08({ showApple = true, showGoogle = true, className, onSubmit, ...props }: AuthCardProps) {
  const [email, setEmail] = useState("<EMAIL>")
  const [password, setPassword] = useState("ARAPaulPS!")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (onSubmit) {
      onSubmit(email, password)
    }
  }

  return (
    <Card
      className={cn(
        "w-[400px] mx-auto rounded-3xl",
        "bg-zinc-900 dark:bg-black",
        "border-2 border-zinc-800/50",
        "shadow-[0_24px_48px_-12px] shadow-black/30",
        className,
      )}
      {...props}
    >
      <form onSubmit={handleSubmit}>
        <CardHeader className="space-y-4 px-8 pt-10">
          <div className="space-y-2 text-center">
            <div className="flex justify-center mb-4">
              <div className="text-center">
                <h1 className="text-2xl font-bold text-white mb-1">ARA</h1>
                <h2 className="text-lg font-semibold text-[#A4D321]">Property Services</h2>
              </div>
            </div>
            <CardDescription className="text-base text-zinc-400">Property Services Staff Portal</CardDescription>
            <p className="text-xs text-[#A4D321] mt-1">Pre-filled with Paul McCann&apos;s credentials</p>
          </div>
        </CardHeader>

        <CardContent className="p-8 pt-4 space-y-6">
          <div className="grid grid-cols-1 gap-4">
            <div className="space-y-4">
              <Input
                type="email"
                placeholder="Email address"
                className="h-12 bg-zinc-800/50 border-zinc-700/50 text-white"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
              <Input
                type="password"
                placeholder="Password"
                className="h-12 bg-zinc-800/50 border-zinc-700/50 text-white"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
              <Button
                type="submit"
                className="w-full h-12 relative group overflow-hidden
                  bg-gradient-to-r from-[#3D4D61] to-[#A4D321]
                  text-white font-medium
                  shadow-lg shadow-black/20
                  hover:from-[#A4D321] hover:to-[#3D4D61]
                  transition-all duration-300"
              >
                Sign in
              </Button>
            </div>

            <div className="relative my-2">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t border-zinc-800/50" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-zinc-900 dark:bg-black px-4 text-zinc-500 font-medium">Or continue with</span>
              </div>
            </div>

            {showGoogle && (
              <Button
                type="button"
                variant="outline"
                className="h-12 relative group
                  bg-zinc-800/50
                  hover:bg-zinc-700/50
                  border-zinc-700/50
                  hover:border-zinc-600/50
                  text-white
                  transition duration-200"
              >
                <svg className="mr-2 h-5 w-5" viewBox="0 0 24 24">
                  <path
                    d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    fill="#4285F4"
                  />
                  <path
                    d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    fill="#34A853"
                  />
                  <path
                    d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    fill="#FBBC05"
                  />
                  <path
                    d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    fill="#EA4335"
                  />
                </svg>
                <span className="font-medium text-zinc-300">Continue with Google</span>
              </Button>
            )}

            {showApple && (
              <Button
                type="button"
                variant="outline"
                className="h-12 relative group
                  bg-zinc-800/50
                  hover:bg-zinc-700/50
                  border-zinc-700/50
                  hover:border-zinc-600/50
                  text-white
                  transition duration-200"
              >
                <svg className="mr-2 h-5 w-5" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12.152 6.896c-.948 0-2.415-1.078-3.96-1.04-2.04.027-3.91 1.183-4.961 3.014-2.117 3.675-.546 9.103 1.519 12.09 1.013 1.454 2.208 3.09 3.792 3.039 1.52-.065 2.09-.987 3.935-.987 1.831 0 2.35.987 3.96.948 1.637-.026 2.676-1.48 3.676-2.948 1.156-1.688 1.636-3.325 1.662-3.415-.039-.013-3.182-1.221-3.22-4.857-.026-3.04 2.48-4.494 2.597-4.559-1.429-2.09-3.623-2.324-4.39-2.376-2-.156-3.675 1.09-4.61 1.09z" />
                </svg>
                <span className="font-medium text-zinc-300">Continue with Apple</span>
              </Button>
            )}
          </div>
        </CardContent>

        <CardFooter className="px-8 pb-10 pt-2">
          <div className="text-center text-sm text-zinc-500">
            By continuing, you agree to our{" "}
            <a
              href="#"
              className="text-[#A4D321] hover:text-[#8fb81d]
                transition-colors underline underline-offset-4 decoration-zinc-700
                font-medium"
            >
              Terms of Service
            </a>{" "}
            and{" "}
            <a
              href="#"
              className="text-[#A4D321] hover:text-[#8fb81d]
                transition-colors underline underline-offset-4 decoration-zinc-700
                font-medium"
            >
              Privacy Policy
            </a>
          </div>
        </CardFooter>
      </form>
    </Card>
  )
}
