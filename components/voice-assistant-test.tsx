'use client'

import { useState, useCallback } from 'react'
import { useConversation } from '@11labs/react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Loader2, Mic, MicOff, Volume2, VolumeX } from 'lucide-react'
import { toast } from 'sonner'

type Role = 'ai' | 'user'

export function VoiceAssistantTest() {
  const [state, setState] = useState<'idle' | 'connecting' | 'listening' | 'processing' | 'speaking' | 'error'>('idle')
  const [messages, setMessages] = useState<{ id: string; role: 'user' | 'assistant'; content: string; timestamp: Date }[]>([])
  const [currentMessage, setCurrentMessage] = useState('')

  const addMessage = useCallback((message: { id: string; role: 'user' | 'assistant'; content: string; timestamp: Date }) => {
    setMessages(prev => [...prev, message])
  }, [])

  const conversation = useConversation({
    onError: (error: string) => {
      toast.error(error)
      setState('error')
    },
    onConnect: () => {
      toast.success('Connected to ElevenLabs voice assistant.')
      setState('listening')
    },
    onMessage: (props: { message: string; source: Role }) => {
      const { message, source } = props

      if (source === 'ai') {
        setCurrentMessage(message)
        if (message && message.trim().length > 0) {
          setState('speaking')
        }

        // Add message when it's complete
        addMessage({
          id: `ai-${Date.now()}`,
          role: 'assistant',
          content: message,
          timestamp: new Date()
        })

        if (state === 'speaking') {
          setState('listening')
        }
      } else if (source === 'user') {
        addMessage({
          id: `user-${Date.now()}`,
          role: 'user',
          content: message,
          timestamp: new Date()
        })
        setState('processing')
      }
    },
  })

  const startListening = useCallback(async () => {
    if (state === 'idle' || state === 'error') {
      setState('connecting')
      if (typeof window !== 'undefined') {
        try {
          await navigator.mediaDevices.getUserMedia({ audio: true })
          const response = await fetch('/api/i', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
          })
          const data = await response.json()
          if (data.error) throw new Error(data.error)
          await conversation.startSession({ signedUrl: data.apiKey })
        } catch (error) {
          console.error('Failed to initialize:', error)
          toast.error('Failed to initialize voice assistant')
          setState('error')
        }
      }
    }
  }, [conversation, state])

  const stopListening = useCallback(async () => {
    if (state !== 'idle' && state !== 'error') {
      await conversation.endSession()
      setState('idle')
    }
  }, [conversation, state])

  const toggleListening = useCallback(() => {
    if (state === 'idle' || state === 'error') {
      startListening()
    } else {
      stopListening()
    }
  }, [state, startListening, stopListening])

  const clearConversation = useCallback(() => {
    setMessages([])
  }, [])

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Voice Assistant Test</CardTitle>
        <CardDescription>Testing ElevenLabs voice assistant integration</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="p-4 bg-muted rounded-md">
            <h3 className="font-medium">Status: {state}</h3>
            <p className="text-sm text-muted-foreground mt-1">
              {state === 'idle' && 'Click the microphone button to start.'}
              {state === 'connecting' && 'Connecting to ElevenLabs...'}
              {state === 'listening' && 'Listening for your voice...'}
              {state === 'processing' && 'Processing your request...'}
              {state === 'speaking' && 'Assistant is speaking...'}
              {state === 'error' && 'Error connecting to ElevenLabs.'}
            </p>
          </div>

          <div className="border rounded-md p-4 h-64 overflow-y-auto">
            {messages.length === 0 ? (
              <p className="text-center text-muted-foreground">No messages yet</p>
            ) : (
              <div className="space-y-4">
                {messages.map(message => (
                  <div 
                    key={message.id} 
                    className={`p-3 rounded-lg ${
                      message.role === 'user' ? 'bg-primary/10 ml-8' : 'bg-muted mr-8'
                    }`}
                  >
                    <p className="text-sm font-medium mb-1">
                      {message.role === 'user' ? 'You' : 'Assistant'}
                    </p>
                    <p>{message.content}</p>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={clearConversation}
          disabled={messages.length === 0}
        >
          Clear Conversation
        </Button>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            size="icon"
            disabled={state !== 'speaking'}
            onClick={() => conversation.stopSpeaking()}
          >
            {conversation.isSpeaking ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
          </Button>
          <Button 
            variant={state === 'idle' || state === 'error' ? 'default' : 'secondary'}
            size="icon"
            onClick={toggleListening}
            disabled={state === 'connecting'}
          >
            {state === 'connecting' ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : state === 'idle' || state === 'error' ? (
              <Mic className="h-4 w-4" />
            ) : (
              <MicOff className="h-4 w-4" />
            )}
          </Button>
        </div>
      </CardFooter>
    </Card>
  )
}
