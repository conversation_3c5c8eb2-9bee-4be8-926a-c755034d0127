'use client'

import { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react'
import { useConversation, type Role } from '@11labs/react'
import { toast } from 'sonner'

interface ConversationMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
}

interface VoiceAssistantContextType {
  state: 'idle' | 'connecting' | 'listening' | 'processing' | 'speaking' | 'error'
  messages: ConversationMessage[]
  currentMessage: string
  isAudioPlaying: boolean
  startListening: () => Promise<void>
  stopListening: () => Promise<void>
  toggleListening: () => Promise<void>
  clearConversation: () => void
}

// Create a default context value to avoid undefined errors
const defaultContextValue: VoiceAssistantContextType = {
  state: 'idle',
  messages: [],
  currentMessage: '',
  isAudioPlaying: false,
  startListening: async () => {},
  stopListening: async () => {},
  toggleListening: async () => {},
  clearConversation: () => {},
}

const VoiceAssistantContext = createContext<VoiceAssistantContextType>(defaultContextValue)

export function VoiceAssistantProvider({ children }: { children: ReactNode }) {
  const [isBrowser, setIsBrowser] = useState(false)
  const [state, setState] = useState<'idle' | 'connecting' | 'listening' | 'processing' | 'speaking' | 'error'>('idle')
  const [messages, setMessages] = useState<ConversationMessage[]>([])
  const [currentMessage, setCurrentMessage] = useState('')
  const [isSpeaking, setIsSpeaking] = useState(false)

  // Handle SSR - only initialize browser-specific logic after mount
  useEffect(() => {
    setIsBrowser(true)
  }, [])

  // Safe conversation object with fallbacks
  const conversation = isBrowser 
    ? useConversation({
        onError: (error: string) => {
          toast(error)
          setState('error')
        },
        onConnect: () => {
          toast('Connected to ElevenLabs voice assistant.')
          setState('listening')
        },
        onMessage: (props: { message: string; source: Role }) => {
          const { message, source } = props

          if (source === 'ai') {
            setCurrentMessage(message)
            if (message && message.trim().length > 0) {
              setState('speaking')
              setIsSpeaking(true)
            }

            // Add message when it's complete
            addMessage({
              id: `ai-${Date.now()}`,
              role: 'assistant',
              content: message,
              timestamp: new Date()
            })

            if (state === 'speaking') {
              setState('listening')
              setIsSpeaking(false)
            }
          } else if (source === 'user') {
            addMessage({
              id: `user-${Date.now()}`,
              role: 'user',
              content: message,
              timestamp: new Date()
            })
            setState('processing')
          }
        },
      })
    : null

  const addMessage = useCallback((message: ConversationMessage) => {
    setMessages(prev => [...prev, message])

    // Only attempt to save if we're in a browser environment
    if (isBrowser) {
      fetch('/api/c', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          item: {
            type: 'message',
            status: 'completed',
            object: 'realtime.item',
            id: message.id,
            role: message.role,
            content: [{ type: 'text', transcript: message.content }],
          },
        }),
      }).catch(console.error)
    }
  }, [isBrowser])

  const startListening = useCallback(async () => {
    if (!isBrowser) return
    
    if (state === 'idle' || state === 'error') {
      setState('connecting')
      try {
        await navigator.mediaDevices.getUserMedia({ audio: true })
        const response = await fetch('/api/i', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
        })
        const data = await response.json()
        if (data.error) throw new Error(data.error)
        await conversation?.startSession({ signedUrl: data.apiKey })
      } catch (error) {
        console.error('Failed to initialize:', error)
        toast('Failed to initialize voice assistant')
        setState('error')
      }
    }
  }, [conversation, state, isBrowser])

  const stopListening = useCallback(async () => {
    if (!isBrowser) return
    
    if (state !== 'idle' && state !== 'error') {
      await conversation?.endSession()
      setState('idle')
    }
  }, [conversation, state, isBrowser])

  const toggleListening = useCallback(async () => {
    if (!isBrowser) return
    
    if (state === 'idle' || state === 'error') {
      await startListening()
    } else {
      await stopListening()
    }
  }, [state, startListening, stopListening, isBrowser])

  const clearConversation = useCallback(() => {
    setMessages([])
  }, [])

  useEffect(() => {
    if (!isBrowser) return
    
    return () => {
      conversation?.endSession().catch(console.error)
    }
  }, [conversation, isBrowser])

  // Context value with proper typing
  const contextValue: VoiceAssistantContextType = {
    state,
    messages,
    currentMessage,
    isAudioPlaying: isSpeaking,
    startListening,
    stopListening,
    toggleListening,
    clearConversation,
  }

  return (
    <VoiceAssistantContext.Provider value={contextValue}>
      {children}
    </VoiceAssistantContext.Provider>
  )
}

export function useVoiceAssistant() {
  const context = useContext(VoiceAssistantContext)
  if (!context) {
    throw new Error('useVoiceAssistant must be used within VoiceAssistantProvider')
  }
  return context
}