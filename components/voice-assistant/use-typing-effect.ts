'use client'

import { useState, useEffect, useRef } from 'react'

/**
 * Custom hook that creates a typing animation effect
 * Optimized for React 19 with proper ref usage to prevent unnecessary re-renders
 * 
 * @param text - The text to animate
 * @param typingSpeed - Speed of typing in milliseconds per character
 * @returns Animated text output
 */
export function useTypingEffect(text: string, typingSpeed: number = 30) {
  const [displayText, setDisplayText] = useState('')
  const textRef = useRef(text)
  const indexRef = useRef(0)
  const intervalRef = useRef<ReturnType<typeof setInterval> | null>(null)
  
  useEffect(() => {
    // Reset on text change
    textRef.current = text
    indexRef.current = 0
    setDisplayText('')
    
    // Clean up any existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }
    
    if (!text) return
    
    // Set up typing interval
    intervalRef.current = setInterval(() => {
      if (indexRef.current < textRef.current.length) {
        setDisplayText(prev => prev + textRef.current.charAt(indexRef.current))
        indexRef.current += 1
      } else {
        // Clean up when done
        if (intervalRef.current) {
          clearInterval(intervalRef.current)
          intervalRef.current = null
        }
      }
    }, typingSpeed)
    
    // Clean up on unmount or text change
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }
  }, [text, typingSpeed])
  
  return displayText
}
