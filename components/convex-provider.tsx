"use client";

import { Convex<PERSON>rovider, ConvexReactClient } from "convex/react";
import { <PERSON><PERSON><PERSON><PERSON>, useAuth } from "@clerk/nextjs";
import { ReactNode } from "react";

const convex = new ConvexReactClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

export function ConvexClientProvider({ children }: { children: ReactNode }) {
  return (
    <ConvexProvider client={convex}>
      {children}
    </ConvexProvider>
  );
}