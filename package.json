{"name": "askara-property-services", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest", "ci": "pnpm lint", "clean": "rm -rf node_modules .next .turbo dist", "clean:cache": "pnpm store prune", "reset": "./scripts/reset-pnpm.sh", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:check": "node scripts/check-db.js", "db:verify": "node scripts/verify-schema.js", "db:test": "node scripts/test-drizzle.js", "db:seed": "tsx -r tsconfig-paths/register seed_script.ts", "db:seed-contracts": "node scripts/seed-contract-specs.js", "test:clerk": "node scripts/test-clerk-integration.js", "test:neon": "node --loader ts-node/esm scripts/test-neon-connection.js", "example:drizzle": "npx tsx examples/drizzle-neon-example.ts", "db:verify-setup": "node --loader ts-node/esm scripts/verify-db-setup.js", "db:migrate-and-seed": "node scripts/run-migrations-and-seed.js", "validate:env": "node scripts/validation/env-validator.js", "validate:deployment": "node scripts/validation/deployment-validator.js", "validate:all": "pnpm validate:env && pnpm validate:deployment", "postinstall": "([ \"$CI\" != \"true\" ] && pnpm db:migrate) || exit 0"}, "dependencies": {"@11labs/react": "^0.1.3", "@ai-sdk/openai": "latest", "@clerk/nextjs": "^5.1.2", "@clerk/themes": "^2.2.34", "@convex-dev/auth": "^0.0.87", "@emotion/is-prop-valid": "latest", "@hookform/resolvers": "^3.9.1", "@neondatabase/serverless": "0.10.4", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "latest", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "latest", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "latest", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "latest", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@vercel/blob": "latest", "ai": "latest", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "convex": "^1.25.4", "convex-helpers": "^0.1.100", "date-fns": "latest", "drizzle-orm": "^0.42.0", "drizzle-seed": "^0.3.1", "embla-carousel-react": "8.5.1", "eventsource": "^3.0.6", "express": "^5.1.0", "framer-motion": "^12.7.2", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "next": "15.3.1", "next-themes": "^0.4.0", "react": "^18.2.0", "react-day-picker": "^9.6.7", "react-dom": "^18.2.0", "react-feather": "^2.0.10", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.1", "svix": "^1.20.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.1", "vaul": "latest", "zod": "^3.24.1"}, "devDependencies": {"@swc/core": "^1.3.100", "@swc/helpers": "^0.5.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/node": "^22", "@types/pg": "^8.11.13", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^9.0.8", "@vitejs/plugin-react": "^4.4.1", "@vitest/coverage-v8": "3.1.1", "chalk": "^5.3.0", "dotenv": "^16.4.5", "drizzle-kit": "^0.31.0", "eslint": "^9.25.0", "eslint-config-next": "^15.3.1", "jsdom": "^26.1.0", "node-fetch": "^3.3.2", "pg": "^8.14.1", "postcss": "^8", "tailwindcss": "^3.4.17", "task-master-ai": "^0.11.1", "tsx": "^4.19.3", "typescript": "^5", "vite-tsconfig-paths": "^4.3.2", "vitest": "^3.1.1"}, "pnpm": {"onlyBuiltDependencies": ["@clerk/shared", "@swc/core", "es5-ext", "esbuild", "sharp", "unrs-resolver"], "peerDependencyRules": {"allowedVersions": {"react": "18", "react-dom": "18"}}}, "trustedDependencies": ["@clerk/shared", "@swc/core", "es5-ext", "esbuild", "sharp", "unrs-resolver"]}