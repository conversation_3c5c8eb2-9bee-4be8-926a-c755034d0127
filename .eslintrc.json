{
  "extends": [
    "next/core-web-vitals",
    "next/typescript"
  ],
  "ignorePatterns": ["*.js", "**/*.js"],
  "rules": {
    // Unused variables prefixed with _ are ignored, catch errors too
    // Temporarily disable unused-vars lint errors globally
    "@typescript-eslint/no-unused-vars": "off",
    "@typescript-eslint/no-explicit-any": "off",
    // Disable display-name and anonymous default export rules
    "react/display-name": "off",
    "import/no-anonymous-default-export": "off",
    // Allow unescaped entities in JSX
    "react/no-unescaped-entities": "off"
  }
}
