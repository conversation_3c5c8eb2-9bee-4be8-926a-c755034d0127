# Database Migration to Drizzle ORM with Neon

This document outlines the process of migrating the database to Drizzle ORM with Neon PostgreSQL.

## Prerequisites

- Node.js installed
- Access to a Neon PostgreSQL database
- DATABASE_URL environment variable set in `.env.local` or `.env`

## Migration Scripts

The following scripts have been added to help with the migration process:

### 1. Check Database Connection

```bash
npm run db:check
```

This script checks the database connection and lists all existing tables in the database.

### 2. Run Complete Migration

```bash
npm run db:migrate
```

This script runs the complete migration process, including:
- Checking the database connection
- Generating and running migrations
- Verifying the schema
- Testing the Drizzle ORM functionality

### 3. Individual Steps

If you prefer to run the steps individually:

```bash
# Generate migration files
npm run db:generate

# Push schema to database
npm run db:push

# Verify schema after migration
npm run db:verify

# Test Drizzle ORM functionality
npm run db:test

# Open Drizzle Studio to view and manage data
npm run db:studio
```

## Schema

The database schema is defined in `app/db/schema.ts` and includes the following tables:

- `users`: User accounts and profiles
- `messages`: Chat messages
- `contacts`: Contact information
- `chat_sessions`: Chat session metadata
- `inspection_reports`: Property inspection reports
- `inspection_actions`: Actions related to inspection reports

## Database Access

The Drizzle ORM client is initialized in `app/db/index.ts` and can be imported and used in your application:

```typescript
import { db } from '@/app/db';
import { users } from '@/app/db/schema';
import { eq } from 'drizzle-orm';

// Example: Find a user by email
const user = await db.query.users.findFirst({
  where: eq(users.email, '<EMAIL>'),
});
```

## Troubleshooting

If you encounter issues during the migration:

1. Check that your DATABASE_URL is correct and accessible
2. Ensure you have the necessary permissions to create and modify tables
3. Check for any error messages in the console output
4. Try running the individual steps to isolate the issue

## Additional Resources

- [Drizzle ORM Documentation](https://orm.drizzle.team/docs/overview)
- [Neon PostgreSQL Documentation](https://neon.tech/docs/introduction)
