import { loadEnvConfig } from '@next/env';
import { neon } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-http';
import { eq } from 'drizzle-orm';
import crypto from 'crypto';

// Import schema
import * as schema from '../app/db/schema';

// Load environment variables
loadEnvConfig(process.cwd());

// Check if DATABASE_URL is defined
if (!process.env.DATABASE_URL) {
  throw new Error('DATABASE_URL environment variable is not defined');
}

// Create a Neon client
const sql = neon(process.env.DATABASE_URL);

// Create a Drizzle client
const db = drizzle(sql, { schema });

/**
 * Example function to demonstrate CRUD operations with Drizzle and Neon
 */
async function drizzleNeonExample() {
  console.log('🚀 Drizzle + Neon Example');
  
  try {
    // CREATE: Insert a new user
    console.log('\n--- CREATE ---');
    const newUser = {
      id: crypto.randomUUID(),
      name: 'Example User',
      role: 'Tester',
      department: 'IT',
      email: `example.user.${Date.now()}@example.com`, // Ensure unique email
      password: 'password123',
      preferences: { theme: 'dark', notifications: true },
      created_at: new Date(),
    };
    
    const insertResult = await db.insert(schema.users).values(newUser).returning();
    console.log('Inserted user:', insertResult[0]);
    
    // READ: Query users
    console.log('\n--- READ ---');
    const users = await db.select().from(schema.users).limit(5);
    console.log(`Found ${users.length} users. First few:`);
    users.slice(0, 3).forEach(user => {
      console.log(`- ${user.name} (${user.email})`);
    });
    
    // READ: Query a specific user
    const user = await db.select().from(schema.users).where(eq(schema.users.id, newUser.id)).limit(1);
    console.log('\nQueried specific user:', user[0]?.name);
    
    // UPDATE: Update the user
    console.log('\n--- UPDATE ---');
    const updateResult = await db.update(schema.users)
      .set({ name: 'Updated Example User', role: 'Senior Tester' })
      .where(eq(schema.users.id, newUser.id))
      .returning();
    console.log('Updated user:', updateResult[0]);
    
    // DELETE: Delete the user
    console.log('\n--- DELETE ---');
    const deleteResult = await db.delete(schema.users)
      .where(eq(schema.users.id, newUser.id))
      .returning();
    console.log('Deleted user:', deleteResult[0]);
    
    // ADVANCED: Transaction example
    console.log('\n--- TRANSACTION EXAMPLE ---');
    // Note: For HTTP connections, transactions are simulated by batching queries
    const transactionUser = {
      id: crypto.randomUUID(),
      name: 'Transaction User',
      role: 'Tester',
      department: 'IT',
      email: `transaction.user.${Date.now()}@example.com`,
      password: 'password123',
      preferences: { theme: 'light', notifications: false },
      created_at: new Date(),
    };
    
    // Create a property that will be managed by this user
    const transactionProperty = {
      id: crypto.randomUUID(),
      name: 'Transaction Property',
      address: '123 Test Street',
      suburb: 'Testville',
      state: 'NSW',
      postcode: '2000',
      type: 'Test',
      tier: 3,
      region: 'Test Region',
      category: 'Test Category',
      status: 'active',
      manager_id: transactionUser.id,
      created_at: new Date(),
    };
    
    // Execute multiple operations
    await db.batch([
      db.insert(schema.users).values(transactionUser),
      db.insert(schema.properties).values(transactionProperty),
    ]);
    
    // Verify the transaction
    const verifyUser = await db.select().from(schema.users).where(eq(schema.users.id, transactionUser.id));
    const verifyProperty = await db.select().from(schema.properties).where(eq(schema.properties.id, transactionProperty.id));
    
    console.log('Transaction user created:', verifyUser[0]?.name);
    console.log('Transaction property created:', verifyProperty[0]?.name);
    
    // Clean up transaction data
    await db.batch([
      db.delete(schema.properties).where(eq(schema.properties.id, transactionProperty.id)),
      db.delete(schema.users).where(eq(schema.users.id, transactionUser.id)),
    ]);
    
    console.log('\n✅ Drizzle + Neon example completed successfully!');
  } catch (error) {
    console.error('\n❌ Error in Drizzle + Neon example:', error);
  }
}

// Run the example
drizzleNeonExample();
