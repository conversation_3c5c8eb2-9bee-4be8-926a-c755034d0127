'use client';

import { useState, useEffect } from 'react';
import { useUser as useClerkUser } from '@clerk/nextjs';

export interface UserProfile {
  id: string;
  name: string;
  email: string;
  role: string;
  department: string;
  avatar?: string;
  phone?: string;
}

export function useUserProfile() {
  const { user: clerkUser, isLoaded: isClerkLoaded, isSignedIn } = useClerkUser();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isClerkLoaded && isSignedIn && clerkUser) {
      setIsLoading(true);
      setError(null);

      fetch('/api/user/profile')
        .then(res => {
          if (!res.ok) {
            throw new Error('Failed to fetch user profile');
          }
          return res.json();
        })
        .then(data => {
          setProfile(data.user);
        })
        .catch(err => {
          console.error('Error fetching user profile:', err);
          setError(err.message);
        })
        .finally(() => {
          setIsLoading(false);
        });
    } else if (isClerkLoaded && !isSignedIn) {
      setProfile(null);
      setIsLoading(false);
    }
  }, [isClerkLoaded, isSignedIn, clerkUser]);

  return { profile, isLoading, error, isSignedIn };
}
