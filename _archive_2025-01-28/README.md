# Archive - 2025-01-28

This archive contains files removed during project cleanup and migration from Neon/Drizzle to Convex.

## Archive Structure

- `__archive__/` - Original archive directory from the project
- `duplicate_files/` - JavaScript files that have TypeScript equivalents
- `neon_drizzle/` - Database-related files for Neon and Drizzle
- `test_scripts/` - Various test and utility scripts
- `seed_data/` - Original seed data files
- `reference_implementations/` - Reference code and examples

## Why These Were Archived

### Migration-Related
1. **Duplicate Files**: JS files where TS versions exist (violates single source of truth)
2. **Old Implementation**: Neon/Drizzle code being replaced by Convex
3. **Test Scripts**: Ad-hoc scripts that should be organized or removed
4. **Generated Data**: Mock and seed data being replaced with validated data
5. **Reference Code**: Example implementations that aren't part of the active codebase

### Cleanup-Related
6. **Legacy Documentation**: Outdated documentation files moved to maintain history
7. **Deprecated Scripts**: Old migration and setup scripts no longer needed
8. **Unused Dependencies**: Package files and configurations for removed dependencies

## Contents Overview

### `__archive__/`
Original project archive containing:
- Old documentation files
- Reference implementations
- Legacy configuration files
- Historical project artifacts

### `duplicate_files/`
JavaScript versions of files that have TypeScript equivalents:
- Component files (.js → .tsx)
- Utility functions (.js → .ts)
- Configuration files with JS/TS duplicates

### `neon_drizzle/`
Database implementation files for the old Neon/Drizzle stack:
- Drizzle configuration and migrations
- Neon-specific connection code
- MCP server implementations
- Database models and schemas

### `test_scripts/`
Various testing and utility scripts:
- Database connection tests
- User management scripts
- Migration utilities
- Development helpers

### `seed_data/`
Original seed data and conversion scripts:
- CSV data files
- Data conversion utilities
- Sample data generators

### `reference_implementations/`
Reference code and examples:
- Component examples
- Implementation patterns
- Code snippets for reference

## Restoration

If you need to restore any of these files:

### Single File
```bash
cp _archive_2025-01-28/<path_to_file> <destination>
```

### Entire Directory
```bash
cp -r _archive_2025-01-28/<directory> <destination>
```

### Specific Examples
```bash
# Restore a specific component
cp _archive_2025-01-28/duplicate_files/components/example.tsx components/

# Restore database configuration
cp -r _archive_2025-01-28/neon_drizzle/drizzle ./

# Restore test scripts
cp -r _archive_2025-01-28/test_scripts/specific-test.js scripts/testing/
```

## Important Notes

- **Do not restore** without understanding the current project structure
- **Check compatibility** with current dependencies and architecture
- **Review changes** made since archival before restoration
- **Test thoroughly** after any restoration

## Archive Date
Created: January 28, 2025
Reason: Project cleanup and Convex migration