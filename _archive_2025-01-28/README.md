# Archive - 2025-01-28

This archive contains files removed during the migration from Neon/Drizzle to Convex.

## Archive Structure

- `__archive__/` - Original archive directory from the project
- `duplicate_files/` - JavaScript files that have TypeScript equivalents
- `neon_drizzle/` - Database-related files for Neon and Drizzle
- `test_scripts/` - Various test and utility scripts
- `seed_data/` - Original seed data files
- `reference_implementations/` - Reference code and examples

## Why These Were Archived

1. **Duplicate Files**: JS files where TS versions exist (violates single source of truth)
2. **Old Implementation**: Neon/Drizzle code being replaced by Convex
3. **Test Scripts**: Ad-hoc scripts that should be organized or removed
4. **Generated Data**: Mock and seed data being replaced with validated data
5. **Reference Code**: Example implementations that aren't part of the active codebase

## Restoration

If you need to restore any of these files:
```bash
cp -r _archive_2025-01-28/<path_to_file> <destination>
```