# Database Migration Re-Enabled

## Current Status

The database migration to Neon Postgres has been re-enabled. You can now make schema changes and apply them to the database.

## What Has Been Changed

1. Removed the `scripts/migration-disabled.js` file that was preventing migrations
2. Restored the original functionality of the migration scripts

## How to Use Migrations

You can use the following npm scripts to manage your database:

```bash
# Generate migration files based on schema changes
npm run db:generate

# Push schema changes to the database
npm run db:push

# Open Drizzle Studio to view and manage data
npm run db:studio

# Run the complete migration process
npm run db:migrate

# Seed the database with test data
npm run db:seed

# Set up the database (generate, push, verify, seed)
npm run db:setup
```

## Database Information

The Neon Postgres database is configured and accessible. The connection string is stored in the `.env` or `.env.local` file as `DATABASE_URL`.

The database schema is defined in `app/db/schema.ts` and includes tables for users, properties, inspection templates, and more.

## Making Schema Changes

To make changes to the database schema:

1. Update the schema definition in `app/db/schema.ts`
2. Generate migration files with `npm run db:generate`
3. Push the changes to the database with `npm run db:push`
4. Verify the changes with `npm run db:verify`

## Viewing Database Data

You can view the current data in the database using Drizzle Studio:

```bash
npm run db:studio
```

This will open a web interface where you can browse and manage the data in your Neon Postgres database.
