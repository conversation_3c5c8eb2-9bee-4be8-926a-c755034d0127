"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"

interface ErrorBoundaryProps {
  error: Error
  reset: () => void
}

export function ErrorBoundary({ error, reset }: ErrorBoundaryProps) {
  const [errorDetails, setErrorDetails] = useState<string>("")

  useEffect(() => {
    // Log the error to an error reporting service
    console.error("Application error:", error)

    // Format error details for display
    setErrorDetails(error.message || "An unknown error occurred")
  }, [error])

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-6 bg-gradient-to-b from-black to-zinc-900">
      <div className="w-20 h-20 rounded-2xl bg-zinc-900 flex items-center justify-center mb-6 shadow-lg">
        <svg viewBox="0 0 24 24" className="w-12 h-12">
          <path d="M12 8L12 12" stroke="#A4D321" strokeWidth="2" strokeLinecap="round" />
          <path d="M12 16L12 16.01" stroke="#A4D321" strokeWidth="2" strokeLinecap="round" />
          <circle cx="12" cy="12" r="9" stroke="#3D4D61" strokeWidth="2" fill="none" />
        </svg>
      </div>

      <h1 className="text-white text-2xl font-bold mb-4 text-center">Something went wrong</h1>
      <p className="text-zinc-400 text-center mb-8 max-w-xs">{errorDetails}</p>

      <motion.button
        className="py-3 px-8 bg-gradient-to-r from-[#3D4D61] to-[#A4D321] text-white font-medium rounded-md"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={reset}
      >
        Try Again
      </motion.button>
    </div>
  )
}
