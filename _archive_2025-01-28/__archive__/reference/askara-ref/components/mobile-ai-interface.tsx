"use client"

import { useState, useEffect } from "react"
import { SettingsScreen } from "@/components/screens/settings-screen"
import { ProfileScreen } from "@/components/screens/profile-screen"
import { InspectionDashboard } from "@/components/screens/inspection-dashboard"
import { ChatScreen } from "@/components/screens/chat-screen"
import LoginScreen from "@/components/screens/login-screen"
import SignUpScreen from "@/components/screens/signup-screen"
import ForgotPasswordScreen from "@/components/screens/forgot-password-screen"
import { AppHeader } from "@/components/ui/app-header"
import { StatusBar } from "@/components/ui/status-bar"
import { NavigationBar } from "@/components/ui/navigation-bar"
import { HomeIndicator } from "@/components/ui/home-indicator"
import { SwipeableContainer } from "@/components/ui/swipeable-container"
import { SwipeDetector } from "@/components/ui/swipe-detector"

import { DashboardScreen } from "@/components/screens/dashboard-screen"
import { TasksScreen } from "@/components/screens/tasks-screen"
import { KnowledgeBaseScreen } from "@/components/screens/knowledge-base-screen"
import { ScheduleScreen } from "@/components/screens/schedule-screen"
import { TeamScreen } from "@/components/screens/team-screen"
import { ReportsScreen } from "@/components/screens/reports-screen"
import { HelpScreen } from "@/components/screens/help-screen"
import { MoreMenuScreen } from "@/components/screens/more-menu-screen"
import { InspectionFormScreen } from "@/components/screens/inspection-form-screen"
import { InspectionReportScreen } from "@/components/screens/inspection-report-screen"
import { CorrectiveActionsScreen } from "@/components/screens/corrective-actions-screen"
import { AssistantDrawerVaul } from "@/components/ui/assistant-drawer-vaul"

export function MobileAIInterface() {
  const [currentScreen, setCurrentScreen] = useState("login")
  const [apiKey, setApiKey] = useState("")
  const [isRecording, setIsRecording] = useState(false)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [swipeIndex, setSwipeIndex] = useState(0)
  const [isAssistantOpen, setIsAssistantOpen] = useState(false)
  const [transcription, setTranscription] = useState("")
  const [userData, setUserData] = useState({
    name: "",
    role: "",
    department: "",
  })

  useEffect(() => {
    const savedApiKey = localStorage.getItem("apiKey")
    if (savedApiKey) {
      setApiKey(savedApiKey)

      // In a real app, you would fetch user data here
      // For now, we'll just set authenticated state
      setIsAuthenticated(true)
      setCurrentScreen("dashboard")
    }
  }, [])

  const handleLogin = (email: string, password: string) => {
    // In a real app, you would validate credentials here
    // and fetch user data from the server
    setApiKey("demo-api-key")
    localStorage.setItem("apiKey", "demo-api-key")

    // Set user data based on login - this would come from your API
    setUserData({
      name: "User",
      role: "Role",
      department: "Department",
    })

    setIsAuthenticated(true)
    setCurrentScreen("dashboard")
  }

  const handleSignUp = (email: string, password: string, name: string) => {
    // In a real app, you would create a new user account here
    setApiKey("demo-api-key")
    localStorage.setItem("apiKey", "demo-api-key")

    setUserData({
      name: name,
      role: "Role",
      department: "Department",
    })

    setIsAuthenticated(true)
    setCurrentScreen("dashboard")
  }

  const handleForgotPassword = (email: string) => {
    // In a real app, you would send a password reset email here
    console.log("Password reset requested for:", email)
    // After sending the email, you might want to show a confirmation message
    // or redirect the user to a confirmation page
  }

  const handleLogout = () => {
    setApiKey("")
    localStorage.removeItem("apiKey")
    setIsAuthenticated(false)
    setCurrentScreen("login")
  }

  const handleNavigate = (screen: string) => {
    if (screen === "logout") {
      handleLogout()
    } else if (screen === "chat") {
      setSwipeIndex(1)
    } else if (screen === "dashboard") {
      setSwipeIndex(0)
    } else if (screen === "signup") {
      setCurrentScreen("signup")
    } else if (screen === "forgot-password") {
      setCurrentScreen("forgot-password")
    } else if (
      screen === "tasks" ||
      screen === "knowledge" ||
      screen === "schedule" ||
      screen === "team" ||
      screen === "reports" ||
      screen === "help" ||
      screen === "more" ||
      screen === "inspection-form" ||
      screen === "inspection-report" ||
      screen === "corrective-actions"
    ) {
      setCurrentScreen(screen)
    } else {
      setCurrentScreen(screen)
    }
  }

  const toggleMicrophone = () => {
    setIsRecording(!isRecording)

    // In a real app, this would trigger actual voice recording
    // and send the audio to a speech-to-text service
    if (!isRecording) {
      setTranscription("")

      // Clear transcription when recording stops
      setTimeout(() => {
        setIsRecording(false)
      }, 5000) // Simulate 5 seconds of recording
    }
  }

  // Create the content for each drawer
  const settingsContent = (
    <SettingsScreen
      apiKey={apiKey}
      setApiKey={setApiKey}
      onNavigate={handleNavigate}
      onLogout={handleLogout}
      userData={userData}
      isDrawer={true}
    />
  )

  const profileContent = <ProfileScreen onNavigate={handleNavigate} userData={userData} isDrawer={true} />

  const notificationsContent = (
    <div className="p-4 h-full overflow-y-auto">
      <div className="space-y-4">
        {/* Notifications will be populated from the API */}
        <div className="p-6 text-center">
          <p className="text-[#ffffffb2]">No notifications</p>
        </div>
      </div>
    </div>
  )

  const handleSwipeLeft = () => {
    setIsAssistantOpen(true)
  }

  const handleSwipeRight = () => {
    // Handle swipe right if needed
  }

  return (
    <div className="w-full h-screen min-h-screen bg-black overflow-hidden flex flex-col">
      {!isAuthenticated ? (
        currentScreen === "login" ? (
          <LoginScreen
            onLogin={handleLogin}
            onForgotPassword={() => handleNavigate("forgot-password")}
            onSignUp={() => handleNavigate("signup")}
          />
        ) : currentScreen === "signup" ? (
          <SignUpScreen onSignUp={handleSignUp} onLoginClick={() => handleNavigate("login")} />
        ) : currentScreen === "forgot-password" ? (
          <ForgotPasswordScreen onSubmit={handleForgotPassword} onLoginClick={() => handleNavigate("login")} />
        ) : (
          <LoginScreen onLogin={handleLogin} />
        )
      ) : (
        <>
          <StatusBar />

          <AppHeader
            userName={userData.name}
            userRole={userData.role}
            userDepartment={userData.department}
            settingsContent={settingsContent}
            profileContent={profileContent}
            notificationsContent={notificationsContent}
          />

          <SwipeDetector onSwipeLeft={handleSwipeLeft} onSwipeRight={handleSwipeRight}>
            <div className="flex-1 overflow-hidden">
              {currentScreen === "dashboard" || currentScreen === "chat" ? (
                <SwipeableContainer initialIndex={swipeIndex} onIndexChange={setSwipeIndex}>
                  {/* Dashboard Screen (Main Home) */}
                  <DashboardScreen onNavigate={handleNavigate} userData={userData} />

                  {/* Chat Screen (Swipe Right) */}
                  <div className="flex flex-col h-full">
                    <ChatScreen apiKey={apiKey} onNavigate={handleNavigate} userData={userData} />
                  </div>
                </SwipeableContainer>
              ) : currentScreen === "tasks" ? (
                <TasksScreen onNavigate={handleNavigate} userData={userData} />
              ) : currentScreen === "knowledge" ? (
                <KnowledgeBaseScreen onNavigate={handleNavigate} userData={userData} />
              ) : currentScreen === "schedule" ? (
                <ScheduleScreen onNavigate={handleNavigate} userData={userData} />
              ) : currentScreen === "team" ? (
                <TeamScreen onNavigate={handleNavigate} userData={userData} />
              ) : currentScreen === "reports" ? (
                <ReportsScreen onNavigate={handleNavigate} userData={userData} />
              ) : currentScreen === "help" ? (
                <HelpScreen onNavigate={handleNavigate} userData={userData} />
              ) : currentScreen === "more" ? (
                <MoreMenuScreen onNavigate={handleNavigate} userData={userData} />
              ) : currentScreen === "inspection-form" ? (
                <InspectionFormScreen onNavigate={handleNavigate} userData={userData} />
              ) : currentScreen === "inspection-report" ? (
                <InspectionReportScreen onNavigate={handleNavigate} userData={userData} />
              ) : currentScreen === "corrective-actions" ? (
                <CorrectiveActionsScreen onNavigate={handleNavigate} userData={userData} />
              ) : (
                <InspectionDashboard onNavigate={handleNavigate} userData={userData} />
              )}
            </div>
          </SwipeDetector>

          <AssistantDrawerVaul
            isOpen={isAssistantOpen}
            onClose={() => setIsAssistantOpen(false)}
            isRecording={isRecording}
            onVoiceToggle={toggleMicrophone}
            transcription={transcription}
          />

          <NavigationBar
            activeScreen={
              currentScreen === "dashboard" ||
              (swipeIndex === 0 &&
                ![
                  "tasks",
                  "knowledge",
                  "schedule",
                  "team",
                  "reports",
                  "help",
                  "inspection-form",
                  "inspection-report",
                  "corrective-actions",
                ].includes(currentScreen))
                ? "dashboard"
                : currentScreen === "chat" || swipeIndex === 1
                  ? "chat"
                  : currentScreen
            }
            onNavigate={handleNavigate}
            isRecording={isRecording}
            onVoiceToggle={toggleMicrophone}
          />

          <HomeIndicator />
        </>
      )}
    </div>
  )
}
