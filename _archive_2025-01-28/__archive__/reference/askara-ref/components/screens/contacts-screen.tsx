"use client"

import type React from "react"

import { useState } from "react"
import { motion } from "framer-motion"
import { StatusBar } from "@/components/ui/status-bar"
import { NavigationBar } from "@/components/ui/navigation-bar"
import { HomeIndicator } from "@/components/ui/home-indicator"
import { Search, Phone, Mail, MapPin, Star, Plus } from "lucide-react"
import { AppHeader } from "@/components/ui/app-header"

interface Contact {
  id: string
  name: string
  role: string
  company: string
  phone: string
  email: string
  location: string
  isFavorite: boolean
  avatar: string
}

interface UserData {
  name: string
  role: string
  department: string
}

export function ContactsScreen({ onNavigate, userData }: { onNavigate: (screen: string) => void; userData: UserData }) {
  const [searchQuery, setSearchQuery] = useState("")
  const [isRecording, setIsRecording] = useState(false)
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null)
  const [showContactDetails, setShowContactDetails] = useState(false)

  // <PERSON><PERSON> contacts data
  const contacts: Contact[] = [
    {
      id: "1",
      name: "<PERSON>",
      role: "Property Manager",
      company: "ARA Properties",
      phone: "+61 4XX XXX XXX",
      email: "<EMAIL>",
      location: "Sydney Office",
      isFavorite: true,
      avatar: "SJ",
    },
    {
      id: "2",
      name: "Michael Chen",
      role: "Maintenance Supervisor",
      company: "ARA Properties",
      phone: "+61 4XX XXX XXX",
      email: "<EMAIL>",
      location: "Melbourne Office",
      isFavorite: true,
      avatar: "MC",
    },
    {
      id: "3",
      name: "Emma Wilson",
      role: "Inspection Coordinator",
      company: "ARA Properties",
      phone: "+61 4XX XXX XXX",
      email: "<EMAIL>",
      location: "Brisbane Office",
      isFavorite: false,
      avatar: "EW",
    },
    {
      id: "4",
      name: "David Thompson",
      role: "Facilities Director",
      company: "ARA Properties",
      phone: "+61 4XX XXX XXX",
      email: "<EMAIL>",
      location: "Perth Office",
      isFavorite: false,
      avatar: "DT",
    },
    {
      id: "5",
      name: "Jessica Lee",
      role: "Quality Assurance",
      company: "ARA Properties",
      phone: "+61 4XX XXX XXX",
      email: "<EMAIL>",
      location: "Adelaide Office",
      isFavorite: false,
      avatar: "JL",
    },
  ]

  const filteredContacts = contacts.filter(
    (contact) =>
      contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      contact.role.toLowerCase().includes(searchQuery.toLowerCase()) ||
      contact.company.toLowerCase().includes(searchQuery.toLowerCase()) ||
      contact.location.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const toggleMicrophone = () => {
    setIsRecording(!isRecording)

    // Simulate stopping recording after 5 seconds
    if (!isRecording) {
      setTimeout(() => {
        setIsRecording(false)
      }, 5000)
    }
  }

  const handleContactClick = (contact: Contact) => {
    setSelectedContact(contact)
    setShowContactDetails(true)
  }

  const closeContactDetails = () => {
    setShowContactDetails(false)
    setTimeout(() => setSelectedContact(null), 300) // Clear after animation completes
  }

  const toggleFavorite = (contactId: string, event: React.MouseEvent) => {
    event.stopPropagation()
    // In a real app, you would update the contact in your database
    console.log(`Toggle favorite for contact ${contactId}`)
  }

  return (
    <div className="flex flex-col w-full h-full min-h-screen relative">
      <StatusBar />

      <AppHeader
        userName={userData.name}
        userRole={userData.role}
        userDepartment={userData.department}
        onSettingsClick={() => onNavigate("settings")}
      />

      {/* Contacts Content */}
      <div className="flex-1 flex flex-col w-full mt-4 overflow-hidden">
        {/* Search Bar */}
        <div className="w-full p-3 border-b border-[#ffffff]/10 px-6">
          <div className="relative">
            <Search className="absolute left-2.5 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-[#ffffffb2]" />
            <input
              type="text"
              placeholder="Search contacts..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-8 pr-3 py-1.5 rounded-lg bg-black/80 backdrop-blur-sm border border-[#ffffff]/10 focus:outline-none focus:ring-1 focus:ring-[#3D4D61]/50 text-white text-xs"
            />
          </div>
        </div>

        {/* Contacts List */}
        <div className="flex-1 w-full overflow-y-auto pb-24">
          {filteredContacts.length > 0 ? (
            <div className="divide-y divide-[#ffffff]/10 px-6">
              {filteredContacts.map((contact) => (
                <motion.div
                  key={contact.id}
                  className="p-3 hover:bg-[#ffffff]/5 cursor-pointer"
                  whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
                  onClick={() => handleContactClick(contact)}
                >
                  <div className="flex items-center">
                    <div className="w-10 h-10 rounded-full bg-[#3D4D61] flex items-center justify-center text-white font-medium mr-3">
                      {contact.avatar}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h3 className="text-white text-sm font-medium">{contact.name}</h3>
                        <motion.button
                          className="text-[#ffffffb2] hover:text-[#f59e0b]"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          onClick={(e) => toggleFavorite(contact.id, e)}
                        >
                          <Star className={`h-4 w-4 ${contact.isFavorite ? "fill-[#f59e0b] text-[#f59e0b]" : ""}`} />
                        </motion.button>
                      </div>
                      <p className="text-[#ffffffb2] text-xs">{contact.role}</p>
                      <p className="text-[#ffffffb2] text-xs">{contact.company}</p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-full p-4 text-center">
              <div className="text-[#ffffffb2] text-4xl mb-3">􀉭</div>
              <p className="text-[#ffffffb2] text-xs mb-1.5">No contacts found</p>
              <p className="text-[#ffffffb2] text-[10px]">Try a different search term or add a new contact</p>
            </div>
          )}
        </div>

        {/* Add Contact Button */}
        <div className="absolute bottom-24 right-6">
          <motion.button
            className="w-12 h-12 rounded-full bg-gradient-to-r from-[#3D4D61] to-[#A4D321] flex items-center justify-center shadow-lg"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Plus className="h-5 w-5 text-white" />
          </motion.button>
        </div>
      </div>

      {/* Contact Details Modal */}
      <motion.div
        className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: showContactDetails ? 1 : 0 }}
        exit={{ opacity: 0 }}
        style={{ pointerEvents: showContactDetails ? "auto" : "none" }}
      >
        <motion.div
          className="w-[90%] max-w-md bg-[#121624] rounded-xl overflow-hidden"
          initial={{ scale: 0.9, y: 20 }}
          animate={{
            scale: showContactDetails ? 1 : 0.9,
            y: showContactDetails ? 0 : 20,
          }}
        >
          {selectedContact && (
            <>
              <div className="p-4 border-b border-[#ffffff]/10">
                <div className="flex items-center justify-between">
                  <h2 className="text-white text-lg font-medium">Contact Details</h2>
                  <motion.button
                    className="w-8 h-8 rounded-full bg-[#ffffff]/10 flex items-center justify-center"
                    whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.2)" }}
                    onClick={closeContactDetails}
                  >
                    <span className="text-white">✕</span>
                  </motion.button>
                </div>
              </div>

              <div className="p-6">
                <div className="flex flex-col items-center mb-6">
                  <div className="w-20 h-20 rounded-full bg-[#3D4D61] flex items-center justify-center text-white text-xl font-medium mb-3">
                    {selectedContact.avatar}
                  </div>
                  <h3 className="text-white text-xl font-medium">{selectedContact.name}</h3>
                  <p className="text-[#ffffffb2] text-sm">{selectedContact.role}</p>
                  <p className="text-[#ffffffb2] text-sm">{selectedContact.company}</p>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center">
                    <div className="w-10 h-10 rounded-full bg-[#ffffff]/10 flex items-center justify-center mr-3">
                      <Phone className="h-5 w-5 text-[#A4D321]" />
                    </div>
                    <div>
                      <p className="text-[#ffffffb2] text-xs">Phone</p>
                      <p className="text-white text-sm">{selectedContact.phone}</p>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <div className="w-10 h-10 rounded-full bg-[#ffffff]/10 flex items-center justify-center mr-3">
                      <Mail className="h-5 w-5 text-[#A4D321]" />
                    </div>
                    <div>
                      <p className="text-[#ffffffb2] text-xs">Email</p>
                      <p className="text-white text-sm">{selectedContact.email}</p>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <div className="w-10 h-10 rounded-full bg-[#ffffff]/10 flex items-center justify-center mr-3">
                      <MapPin className="h-5 w-5 text-[#A4D321]" />
                    </div>
                    <div>
                      <p className="text-[#ffffffb2] text-xs">Location</p>
                      <p className="text-white text-sm">{selectedContact.location}</p>
                    </div>
                  </div>
                </div>

                <div className="flex space-x-3 mt-6">
                  <motion.button
                    className="flex-1 py-2.5 rounded-lg bg-[#3D4D61] text-white text-sm font-medium"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Call
                  </motion.button>
                  <motion.button
                    className="flex-1 py-2.5 rounded-lg bg-[#A4D321] text-black text-sm font-medium"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Message
                  </motion.button>
                </div>
              </div>
            </>
          )}
        </motion.div>
      </motion.div>

      <NavigationBar
        activeScreen="contacts"
        onNavigate={onNavigate}
        isRecording={isRecording}
        onVoiceToggle={toggleMicrophone}
      />
      <HomeIndicator />
    </div>
  )
}
