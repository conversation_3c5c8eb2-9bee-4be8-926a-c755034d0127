"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { ScreenLayout } from "@/components/ui/screen-layout"
import {
  Plus,
  ChevronDown,
  ChevronUp,
  CheckCircle,
  Circle,
  Clock,
  AlertCircle,
  ClipboardList,
  <PERSON><PERSON><PERSON><PERSON>gle,
} from "lucide-react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"

interface TasksScreenProps {
  onNavigate: (screen: string) => void
  userData: {
    name: string
    role: string
    department: string
  }
}

interface Task {
  id: string
  title: string
  status: "pending" | "in-progress" | "completed"
  due: string
  assignee: string
  details: string
  expanded?: boolean
  type?: "inspection" | "maintenance" | "corrective"
}

// Utility functions for task status and type
const getStatusColor = (status: string) => {
  switch (status) {
    case "completed":
      return "border-green-500"
    case "in-progress":
      return "border-amber-500"
    case "pending":
      return "border-blue-500"
    default:
      return ""
  }
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case "completed":
      return <CheckCircle className="h-5 w-5 text-green-500" />
    case "in-progress":
      return <Clock className="h-5 w-5 text-amber-500" />
    case "pending":
      return <Circle className="h-5 w-5 text-blue-500" />
    default:
      return <AlertCircle className="h-5 w-5 text-red-500" />
  }
}

const getTypeIcon = (type?: string) => {
  switch (type) {
    case "inspection":
      return <ClipboardList className="h-4 w-4 text-blue-500" />
    case "corrective":
      return <AlertTriangle className="h-4 w-4 text-amber-500" />
    default:
      return <Circle className="h-4 w-4 text-white" />
  }
}

export function TasksScreen({ onNavigate, userData }: TasksScreenProps) {
  // Initialize with empty array - will be populated from API
  const [tasks, setTasks] = useState<Task[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Fetch tasks from API
    fetchTasks()
  }, [])

  const fetchTasks = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // In a real app, this would be an API call
      // const response = await fetch('/api/tasks')
      // const data = await response.json()
      // setTasks(data.tasks)

      // For now, just set empty array
      setTasks([])
    } catch (err: any) {
      console.error("Error fetching tasks:", err)
      setError(err.message || "Failed to load tasks")
    } finally {
      setIsLoading(false)
    }
  }

  const toggleTaskExpand = (taskId: string) => {
    setTasks((prevTasks) =>
      prevTasks.map((task) => (task.id === taskId ? { ...task, expanded: !task.expanded } : task)),
    )
  }

  const filterTasks = (filter: string) => {
    if (filter === "all") {
      return tasks
    }
    return tasks.filter((task) => task.status === filter)
  }

  const handleTaskAction = (task: Task) => {
    if (task.type === "inspection") {
      onNavigate("inspection-form")
    } else if (task.type === "corrective") {
      onNavigate("corrective-actions")
    }
  }

  return (
    <ScreenLayout
      title="Tasks"
      subtitle="Tasks Dashboard"
      onNavigate={onNavigate}
      activeScreen="tasks"
      headerRightContent={
        <div className="flex gap-2">
          <motion.button
            className="w-8 h-8 rounded-full bg-[#A4D321] flex items-center justify-center"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <Plus className="h-5 w-5 text-black" />
          </motion.button>
        </div>
      }
    >
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-white text-lg font-medium">Task Management</h2>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            className="bg-black/40 border-white/10 text-white"
            onClick={() => onNavigate("inspection-form")}
          >
            <ClipboardList className="h-4 w-4 mr-1" /> Inspections
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="bg-black/40 border-white/10 text-white"
            onClick={() => onNavigate("corrective-actions")}
          >
            <AlertTriangle className="h-4 w-4 mr-1" /> Actions
          </Button>
        </div>
      </div>

      <Tabs defaultValue="all" className="w-full">
        <TabsList className="w-full grid grid-cols-4 bg-black/40 backdrop-blur-sm mb-6">
          <TabsTrigger value="all" className="data-[state=active]:text-[#A4D321]">
            All
          </TabsTrigger>
          <TabsTrigger value="pending" className="data-[state=active]:text-[#A4D321]">
            Pending
          </TabsTrigger>
          <TabsTrigger value="in-progress" className="data-[state=active]:text-[#A4D321]">
            In Progress
          </TabsTrigger>
          <TabsTrigger value="completed" className="data-[state=active]:text-[#A4D321]">
            Completed
          </TabsTrigger>
        </TabsList>

        {isLoading ? (
          <div className="flex items-center justify-center p-12">
            <div className="w-8 h-8 border-2 border-[#A4D321] border-t-transparent rounded-full animate-spin" />
          </div>
        ) : error ? (
          <div className="p-6 text-center">
            <p className="text-red-400 mb-4">{error}</p>
            <Button onClick={fetchTasks}>Try Again</Button>
          </div>
        ) : tasks.length === 0 ? (
          <div className="p-6 text-center">
            <p className="text-[#ffffffb2]">No tasks found</p>
          </div>
        ) : (
          <>
            <TabsContent value="all" className="space-y-4">
              {filterTasks("all").map((task) => (
                <TaskCard key={task.id} task={task} toggleExpand={toggleTaskExpand} onAction={handleTaskAction} />
              ))}
            </TabsContent>

            <TabsContent value="pending" className="space-y-4">
              {filterTasks("pending").map((task) => (
                <TaskCard key={task.id} task={task} toggleExpand={toggleTaskExpand} onAction={handleTaskAction} />
              ))}
            </TabsContent>

            <TabsContent value="in-progress" className="space-y-4">
              {filterTasks("in-progress").map((task) => (
                <TaskCard key={task.id} task={task} toggleExpand={toggleTaskExpand} onAction={handleTaskAction} />
              ))}
            </TabsContent>

            <TabsContent value="completed" className="space-y-4">
              {filterTasks("completed").map((task) => (
                <TaskCard key={task.id} task={task} toggleExpand={toggleTaskExpand} onAction={handleTaskAction} />
              ))}
            </TabsContent>
          </>
        )}
      </Tabs>
    </ScreenLayout>
  )
}

interface TaskCardProps {
  task: Task
  toggleExpand: (taskId: string) => void
  onAction: (task: Task) => void
}

function TaskCard({ task, toggleExpand, onAction }: TaskCardProps) {
  return (
    <motion.div
      className={cn(
        "bg-black/30 backdrop-blur-lg rounded-xl overflow-hidden border border-white/10 shadow-sm",
        `border-l-4 ${getStatusColor(task.status)}`,
      )}
      animate={{ height: task.expanded ? "auto" : "auto" }}
      transition={{ duration: 0.3 }}
    >
      <div className="p-4">
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-3">
            <div>{getStatusIcon(task.status)}</div>
            <div>
              <div className="flex items-center gap-2">
                <h3 className="text-white text-base font-medium">{task.title}</h3>
                {task.type && <div className="flex items-center">{getTypeIcon(task.type)}</div>}
              </div>
              <p className="text-[#ffffffb2] text-xs">
                Due {task.due} • {task.assignee}
              </p>
            </div>
          </div>
          <motion.button
            onClick={() => toggleExpand(task.id)}
            whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.1)" }}
            className="w-8 h-8 flex items-center justify-center rounded-full"
          >
            {task.expanded ? (
              <ChevronUp className="h-5 w-5 text-white" />
            ) : (
              <ChevronDown className="h-5 w-5 text-white" />
            )}
          </motion.button>
        </div>

        {task.expanded && (
          <div className="mt-4 pt-4 border-t border-white/10">
            <p className="text-white text-sm mb-4">{task.details}</p>
            <div className="flex gap-2">
              {task.status !== "completed" && (
                <Button className="flex-1 bg-[#A4D321] text-black hover:bg-[#A4D321]/80" onClick={() => onAction(task)}>
                  {task.type === "inspection"
                    ? "Start Inspection"
                    : task.type === "corrective"
                      ? "View Actions"
                      : "Mark Complete"}
                </Button>
              )}
              <Button
                variant="outline"
                className="flex-1 bg-white/5 text-white border-white/20 hover:bg-white/10"
                onClick={() => onAction(task)}
              >
                View Details
              </Button>
            </div>
          </div>
        )}
      </div>
    </motion.div>
  )
}
