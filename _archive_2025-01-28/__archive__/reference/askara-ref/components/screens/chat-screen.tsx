"use client"

import { useState, useRef, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { streamText } from "ai"
import { openai } from "@ai-sdk/openai"
import { Loading } from "@/components/ui/loading"
import { useToast } from "@/components/ui/toast-context"
import { Ta<PERSON>, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Search, Send } from "lucide-react"

interface Message {
  id: string
  content: string
  role: "user" | "assistant"
  isStreaming?: boolean
  timestamp?: Date
}

interface UserData {
  name: string
  role: string
  department: string
}

interface ChatScreenProps {
  apiKey: string
  onNavigate: (screen: string) => void
  userData: UserData
}

export function ChatScreen({ apiKey, onNavigate, userData }: ChatScreenProps) {
  const [messages, setMessages] = useState<Message[]>([])
  const [input, setInput] = useState("")
  const [isRecording, setIsRecording] = useState(false)
  const [isConnected, setIsConnected] = useState(!!apiKey)
  const [isProcessing, setIsProcessing] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const { showToast } = useToast()

  // Sample chat history data
  const chatHistory = [
    {
      id: "1",
      title: "Project Planning",
      preview: "Let's create a timeline for the new mobile app launch...",
      date: "Today, 2:30 PM",
      isStarred: true,
    },
    {
      id: "2",
      title: "Travel Recommendations",
      preview: "I'm looking for places to visit in Japan during spring...",
      date: "Yesterday, 10:15 AM",
      isStarred: true,
    },
    {
      id: "3",
      title: "Recipe Ideas",
      preview: "Can you suggest some vegetarian dinner recipes?",
      date: "Mar 15, 2023",
      isStarred: false,
    },
    {
      id: "4",
      title: "Coding Help",
      preview: "How do I implement authentication in my React app?",
      date: "Mar 12, 2023",
      isStarred: false,
    },
    {
      id: "5",
      title: "Book Recommendations",
      preview: "I'm looking for science fiction novels similar to Dune...",
      date: "Mar 10, 2023",
      isStarred: false,
    },
  ]

  const filteredHistory = chatHistory.filter(
    (chat) =>
      chat.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      chat.preview.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [messages])

  useEffect(() => {
    setIsConnected(!!apiKey)
  }, [apiKey])

  const handleSendMessage = async () => {
    if (!input.trim() || !isConnected || isProcessing) return

    const userMessage: Message = {
      id: Date.now().toString(),
      content: input,
      role: "user",
      timestamp: new Date(),
    }

    setMessages((prev) => [...prev, userMessage])
    setInput("")
    setIsProcessing(true)

    const assistantMessage: Message = {
      id: (Date.now() + 1).toString(),
      content: "",
      role: "assistant",
      isStreaming: true,
      timestamp: new Date(),
    }

    setMessages((prev) => [...prev, assistantMessage])

    try {
      const result = await streamText({
        model: openai("gpt-4o"),
        prompt: input,
        apiKey: apiKey,
        onChunk: ({ chunk }) => {
          if (chunk.type === "text-delta") {
            setMessages((prev) =>
              prev.map((msg) => (msg.id === assistantMessage.id ? { ...msg, content: msg.content + chunk.text } : msg)),
            )
          }
        },
      })

      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === assistantMessage.id ? { ...msg, content: result.text, isStreaming: false } : msg,
        ),
      )
    } catch (error: any) {
      console.error("Error generating response:", error)
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === assistantMessage.id
            ? { ...msg, content: "Sorry, I encountered an error. Please try again.", isStreaming: false }
            : msg,
        ),
      )
      showToast(`Error: ${error.message || "Failed to generate response"}`, "error")
    } finally {
      setIsProcessing(false)
    }
  }

  const toggleMicrophone = async () => {
    if (!isConnected || isProcessing) return

    setIsRecording(!isRecording)

    if (!isRecording) {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
        const mediaRecorder = new MediaRecorder(stream)
        const audioChunks: Blob[] = []

        mediaRecorder.addEventListener("dataavailable", (event) => {
          audioChunks.push(event.data)
        })

        mediaRecorder.addEventListener("stop", async () => {
          const audioBlob = new Blob(audioChunks, { type: "audio/webm" })
          setIsProcessing(true)

          try {
            // Create form data for the API
            const formData = new FormData()
            formData.append("file", audioBlob, "audio.webm")
            formData.append("apiKey", apiKey)

            // Call our voice API
            const response = await fetch("/api/voice", {
              method: "POST",
              body: formData,
            })

            if (!response.ok) {
              const errorData = await response.json()
              throw new Error(errorData.error || "Failed to transcribe audio")
            }

            const result = await response.json()
            setInput(result.text)

            // Stop all tracks
            stream.getTracks().forEach((track) => track.stop())
          } catch (error: any) {
            console.error("Error processing audio:", error)
            showToast(`Error: ${error.message || "Failed to process audio"}`, "error")
          } finally {
            setIsRecording(false)
            setIsProcessing(false)
          }
        })

        mediaRecorder.start()
        setTimeout(() => mediaRecorder.stop(), 5000) // Record for 5 seconds
      } catch (error: any) {
        console.error("Error recording audio:", error)
        showToast(`Error: ${error.message || "Failed to access microphone"}`, "error")
        setIsRecording(false)
      }
    } else {
      setIsRecording(false)
    }
  }

  return (
    <div className="flex flex-col h-full w-full">
      <Tabs defaultValue="tasks" className="w-full h-full flex flex-col">
        <TabsList className="w-full flex justify-around bg-[#121624]/40 backdrop-blur-sm border-b border-[#ffffff]/10 px-4 py-2">
          <TabsTrigger value="tasks" className="flex items-center gap-1 data-[state=active]:text-[#A4D321]">
            <svg
              className="w-4 h-4"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M12 2v4M12 18v4M4.93 4.93l2.83 2.83M16.24 16.24l2.83 2.83M2 12h4M18 12h4M4.93 19.07l2.83-2.83M16.24 7.76l2.83-2.83" />
            </svg>
            <span>Tasks</span>
          </TabsTrigger>
          <TabsTrigger value="inspections" className="flex items-center gap-1 data-[state=active]:text-[#A4D321]">
            <svg
              className="w-4 h-4"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M9 11a4 4 0 1 0 0-8 4 4 0 0 0 0 8z" />
              <path d="M2 21v-2a4 4 0 0 1 4-4h6a4 4 0 0 1 4 4v2" />
              <path d="M16 3.13a4 4 0 0 1 0 7.75" />
              <path d="M21 20.94c.5-.09 1-.21 1.36-.36a2 2 0 0 0 1-1.7V17a2 2 0 0 0-1-1.7c-1.07-.55-2.43-.55-3.5 0a2 2 0 0 0-1 1.7v1.88a2 2 0 0 0 1 1.7c.5.26 1.1.4 1.73.48" />
              <path d="M15 8a4 4 0 0 0 4.5-4.89" />
            </svg>
            <span>Inspections</span>
          </TabsTrigger>
          <TabsTrigger value="corrective" className="flex items-center gap-1 data-[state=active]:text-[#A4D321]">
            <svg
              className="w-4 h-4"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z" />
              <path d="m9 12 2 2 4-4" />
            </svg>
            <span>Corrective</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent
          value="tasks"
          className="flex-1 flex flex-col w-full overflow-hidden [background:linear-gradient(180deg,rgba(18,22,36,0)_0%,rgba(18,22,36,1)_100%)]"
        >
          {/* Chat Area */}
          <div className="flex-1 w-full overflow-y-auto p-4 space-y-4 pb-20">
            <AnimatePresence>
              {messages.map((message) => (
                <motion.div
                  key={message.id}
                  className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <div
                    className={`relative max-w-[80%] rounded-lg p-2.5 ${
                      message.role === "user"
                        ? "bg-gradient-to-br from-[#3D4D61]/20 to-[#3D4D61]/10 border border-[#3D4D61]/30 text-white"
                        : "bg-gradient-to-br from-[#121624]/80 to-[#121624]/40 border border-[#ffffff]/10 text-white"
                    } shadow-sm`}
                  >
                    <motion.div
                      className="absolute -inset-px rounded-lg z-0 opacity-0 pointer-events-none"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: message.role === "user" ? 0.5 : 0.3 }}
                      style={{
                        background:
                          message.role === "user"
                            ? "radial-gradient(circle, rgba(61,77,97,0.3) 0%, rgba(61,77,97,0.1) 50%, rgba(61,77,97,0) 100%)"
                            : "radial-gradient(circle, rgba(164,211,33,0.3) 0%, rgba(164,211,33,0.1) 50%, rgba(164,211,33,0) 100%)",
                      }}
                    />
                    <p className="relative z-10 text-sm break-words">
                      {message.content}
                      {message.isStreaming && <span className="ml-1 inline-block w-1.5 h-3.5 bg-white animate-pulse" />}
                    </p>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>

            {isProcessing && !messages.some((m) => m.isStreaming) && (
              <div className="flex justify-center my-4">
                <Loading size="small" color="primary" text="Processing..." />
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Input Area */}
          <div className="absolute bottom-16 left-0 right-0 p-4 bg-gradient-to-t from-[#121624] to-transparent">
            <div className="flex items-center space-x-2">
              <input
                type="text"
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && handleSendMessage()}
                placeholder="Type your message..."
                className="flex-1 px-4 py-2 rounded-xl bg-[#121624]/80 backdrop-blur-sm border border-[#ffffff]/10 focus:outline-none focus:ring-2 focus:ring-[#A4D321]/50 text-white"
                disabled={!isConnected || isProcessing}
              />
              <motion.button
                className={`p-2 rounded-full ${
                  isRecording ? "bg-red-500 text-white" : "bg-[#121624]/80 backdrop-blur-sm border border-[#ffffff]/10"
                }`}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={toggleMicrophone}
                disabled={!isConnected || isProcessing}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z" />
                  <path d="M19 10v2a7 7 0 0 1-14 0v-2" />
                  <line x1="12" x2="12" y1="19" y2="22" />
                </svg>
              </motion.button>
              <motion.button
                className="p-2 rounded-full bg-[#A4D321] text-black"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={handleSendMessage}
                disabled={!isConnected || isProcessing || !input.trim()}
              >
                <Send className="w-5 h-5" />
              </motion.button>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="inspections" className="flex-1 flex flex-col w-full overflow-hidden">
          {/* Search Bar */}
          <div className="w-full p-3 border-b border-[#ffffff]/10 px-6">
            <div className="relative">
              <Search className="absolute left-2.5 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-[#ffffffb2]" />
              <input
                type="text"
                placeholder="Search conversations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-8 pr-3 py-1.5 rounded-lg bg-black/80 backdrop-blur-sm border border-[#ffffff]/10 focus:outline-none focus:ring-1 focus:ring-[#3D4D61]/50 text-white text-xs"
              />
            </div>
          </div>

          {/* Chat Sessions List */}
          <div className="flex-1 w-full overflow-y-auto pb-24">
            {filteredHistory.length > 0 ? (
              <div className="divide-y divide-[#ffffff]/10 px-6">
                {filteredHistory.map((session) => (
                  <motion.div
                    key={session.id}
                    className="p-3 hover:bg-[#ffffff]/5 cursor-pointer"
                    whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
                    onClick={() => {}}
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center">
                          <h3 className="text-white text-xs font-medium">{session.title}</h3>
                          {session.isStarred && <div className="ml-1.5 text-[#f59e0b] text-[10px]">★</div>}
                        </div>
                        <p className="text-[#ffffffb2] text-[10px] line-clamp-1 mt-0.5">{session.preview}</p>
                      </div>
                      <span className="text-[#ffffffb2] text-[9px]">{session.date}</span>
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-full p-4 text-center">
                <div className="text-[#ffffffb2] text-4xl mb-3">🔍</div>
                <p className="text-[#ffffffb2] text-xs mb-1.5">No conversations found</p>
                <p className="text-[#ffffffb2] text-[10px]">Try a different search term or start a new chat</p>
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="corrective" className="flex-1 flex flex-col w-full overflow-hidden">
          <div className="p-6">
            <h2 className="text-white text-lg font-medium mb-4">Chat Analytics</h2>

            <div className="space-y-6">
              <div className="bg-[#121624]/40 backdrop-blur-sm rounded-xl p-4 border border-[#ffffff]/10">
                <h3 className="text-white text-sm font-medium mb-3">Usage Statistics</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-[#121624]/60 rounded-lg p-3 border border-[#ffffff]/10">
                    <p className="text-[#ffffffb2] text-xs">Total Messages</p>
                    <p className="text-white text-xl font-bold">1,243</p>
                  </div>
                  <div className="bg-[#121624]/60 rounded-lg p-3 border border-[#ffffff]/10">
                    <p className="text-[#ffffffb2] text-xs">Conversations</p>
                    <p className="text-white text-xl font-bold">87</p>
                  </div>
                  <div className="bg-[#121624]/60 rounded-lg p-3 border border-[#ffffff]/10">
                    <p className="text-[#ffffffb2] text-xs">Avg. Response Time</p>
                    <p className="text-white text-xl font-bold">1.2s</p>
                  </div>
                  <div className="bg-[#121624]/60 rounded-lg p-3 border border-[#ffffff]/10">
                    <p className="text-[#ffffffb2] text-xs">Tokens Used</p>
                    <p className="text-white text-xl font-bold">325K</p>
                  </div>
                </div>
              </div>

              <div className="bg-[#121624]/40 backdrop-blur-sm rounded-xl p-4 border border-[#ffffff]/10">
                <h3 className="text-white text-sm font-medium mb-3">Recent Activity</h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center text-xs">
                    <span className="text-[#ffffffb2]">Today</span>
                    <span className="text-white">23 messages</span>
                  </div>
                  <div className="flex justify-between items-center text-xs">
                    <span className="text-[#ffffffb2]">Yesterday</span>
                    <span className="text-white">45 messages</span>
                  </div>
                  <div className="flex justify-between items-center text-xs">
                    <span className="text-[#ffffffb2]">Last 7 days</span>
                    <span className="text-white">189 messages</span>
                  </div>
                  <div className="flex justify-between items-center text-xs">
                    <span className="text-[#ffffffb2]">Last 30 days</span>
                    <span className="text-white">512 messages</span>
                  </div>
                </div>
              </div>

              <div className="bg-[#121624]/40 backdrop-blur-sm rounded-xl p-4 border border-[#ffffff]/10">
                <h3 className="text-white text-sm font-medium mb-3">Top Categories</h3>
                <div className="space-y-2">
                  <div className="w-full bg-[#121624]/60 rounded-full h-2.5">
                    <div className="bg-[#A4D321] h-2.5 rounded-full" style={{ width: "70%" }}></div>
                  </div>
                  <div className="flex justify-between text-xs">
                    <span className="text-[#ffffffb2]">Property Inspections</span>
                    <span className="text-white">70%</span>
                  </div>

                  <div className="w-full bg-[#121624]/60 rounded-full h-2.5 mt-3">
                    <div className="bg-[#3D4D61] h-2.5 rounded-full" style={{ width: "45%" }}></div>
                  </div>
                  <div className="flex justify-between text-xs">
                    <span className="text-[#ffffffb2]">Maintenance Requests</span>
                    <span className="text-white">45%</span>
                  </div>

                  <div className="w-full bg-[#121624]/60 rounded-full h-2.5 mt-3">
                    <div className="bg-[#f59e0b] h-2.5 rounded-full" style={{ width: "30%" }}></div>
                  </div>
                  <div className="flex justify-between text-xs">
                    <span className="text-[#ffffffb2]">Scheduling</span>
                    <span className="text-white">30%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
