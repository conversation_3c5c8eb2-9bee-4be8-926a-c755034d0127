"use client"

import type React from "react"

import { motion } from "framer-motion"
import { ScreenLayout } from "@/components/ui/screen-layout"
import { Card, CardContent } from "@/components/ui/card"
import { CheckSquare, ClipboardList, Clock, Droplet, Camera, Search, Upload, FileText, Download } from "lucide-react"
import { cn } from "@/lib/utils"

interface DashboardScreenProps {
  onNavigate: (screen: string) => void
  userData: {
    name: string
    role: string
    department: string
  }
}

export function DashboardScreen({ onNavigate, userData }: DashboardScreenProps) {
  // Sample data for the dashboard
  const currentJob = {
    title: "Quarterly Maintenance",
    client: "TechCorp Headquarters",
    progress: 75,
    timeRemaining: "1h 45m",
    tasksCompleted: 18,
    tasksTotal: 24,
  }

  const kpis = [
    { title: "Tasks Completed", value: "18", icon: <CheckSquare className="h-5 w-5 text-green-500" /> },
    { title: "Tasks Remaining", value: "6", icon: <ClipboardList className="h-5 w-5 text-amber-500" /> },
    { title: "Time on Site", value: "6:15", icon: <Clock className="h-5 w-5 text-blue-500" /> },
    { title: "Supplies Used", value: "12", icon: <Droplet className="h-5 w-5 text-indigo-500" /> },
  ]

  const recentDocuments = [
    {
      title: "TechCorp Maintenance Report.pdf",
      date: "Today, 2:30 PM",
      size: "1.2 MB",
      icon: <FileText className="h-10 w-10 text-[#A4D321]" />,
    },
    {
      title: "Building Inspection Checklist.docx",
      date: "Yesterday, 10:15 AM",
      size: "845 KB",
      icon: <FileText className="h-10 w-10 text-blue-500" />,
    },
    {
      title: "Electrical Systems Guide.pdf",
      date: "Mar 15, 2025",
      size: "3.7 MB",
      icon: <FileText className="h-10 w-10 text-amber-500" />,
    },
  ]

  const QuickActionButton = ({ icon, label, color }: { icon: React.ReactNode; label: string; color: string }) => (
    <motion.button
      className={cn(
        "flex flex-col items-center justify-center p-3 rounded-xl bg-black/40 backdrop-blur-sm border border-white/10 shadow-sm",
        "hover:bg-black/60 transition-colors",
      )}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      <div className={`w-12 h-12 rounded-full ${color} flex items-center justify-center mb-2`}>{icon}</div>
      <span className="text-white text-sm">{label}</span>
    </motion.button>
  )

  return (
    <ScreenLayout title={userData.name} subtitle={userData.role} onNavigate={onNavigate} activeScreen="dashboard">
      {/* Current Job Card */}
      <Card className="w-full bg-black/30 backdrop-blur-lg rounded-xl border border-white/10 shadow-sm mb-6">
        <CardContent className="p-4">
          <div className="flex items-start gap-4">
            <div className="relative flex-shrink-0 size-16">
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-white text-sm font-medium">{currentJob.progress}%</span>
              </div>
              <svg className="w-full h-full" viewBox="0 0 36 36">
                <circle cx="18" cy="18" r="16" fill="none" stroke="rgba(255, 255, 255, 0.1)" strokeWidth="2" />
                <circle
                  cx="18"
                  cy="18"
                  r="16"
                  fill="none"
                  stroke="#A4D321"
                  strokeWidth="2"
                  strokeDasharray={`${(currentJob.progress / 100) * 100} 100`}
                  transform="rotate(-90 18 18)"
                />
              </svg>
            </div>

            <div className="flex-1">
              <h3 className="text-white text-lg font-medium">{currentJob.title}</h3>
              <p className="text-[#ffffffb2] text-sm mb-2">{currentJob.client}</p>
              <div className="flex justify-between">
                <div>
                  <p className="text-[#ffffffb2] text-xs">Time Remaining</p>
                  <p className="text-white text-sm font-medium">{currentJob.timeRemaining}</p>
                </div>
                <div>
                  <p className="text-[#ffffffb2] text-xs">Tasks</p>
                  <p className="text-white text-sm font-medium">
                    {currentJob.tasksCompleted}/{currentJob.tasksTotal}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* KPI Cards */}
      <div className="grid grid-cols-2 gap-3 mb-6">
        {kpis.map((kpi, index) => (
          <motion.div
            key={index}
            className="bg-black/30 backdrop-blur-lg rounded-xl p-4 border border-white/10 shadow-sm"
            whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
          >
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-full bg-white/10 flex items-center justify-center">{kpi.icon}</div>
              <div>
                <p className="text-[#ffffffb2] text-xs">{kpi.title}</p>
                <p className="text-white text-lg font-bold">{kpi.value}</p>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Quick Actions Section */}
      <div className="mb-6">
        <h2 className="text-white text-lg font-medium mb-3">Quick Actions</h2>
        <div className="grid grid-cols-3 gap-3">
          <QuickActionButton icon={<Camera className="h-6 w-6 text-white" />} label="Capture" color="bg-[#3D4D61]/50" />
          <QuickActionButton icon={<Search className="h-6 w-6 text-white" />} label="Scan" color="bg-[#A4D321]/50" />
          <QuickActionButton icon={<Upload className="h-6 w-6 text-white" />} label="Upload" color="bg-blue-500/50" />
        </div>
      </div>

      {/* Recent Documents Section */}
      <div>
        <div className="flex justify-between items-center mb-3">
          <h2 className="text-white text-lg font-medium">Recent Documents</h2>
          <motion.button
            className="text-[#A4D321] text-sm"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => onNavigate("knowledge")}
          >
            View All
          </motion.button>
        </div>

        <div className="space-y-3">
          {recentDocuments.map((doc, index) => (
            <motion.div
              key={index}
              className="flex items-center justify-between bg-black/30 backdrop-blur-lg rounded-xl p-3 border border-white/10 shadow-sm"
              whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
            >
              <div className="flex items-center gap-3">
                <div className="flex-shrink-0">{doc.icon}</div>
                <div>
                  <h3 className="text-white text-sm font-medium">{doc.title}</h3>
                  <p className="text-[#ffffffb2] text-xs">
                    {doc.date} • {doc.size}
                  </p>
                </div>
              </div>
              <motion.button
                className="flex items-center justify-center w-8 h-8 rounded-full bg-white/10"
                whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.2)" }}
              >
                <Download className="h-4 w-4 text-[#A4D321]" />
              </motion.button>
            </motion.div>
          ))}
        </div>
      </div>
    </ScreenLayout>
  )
}
