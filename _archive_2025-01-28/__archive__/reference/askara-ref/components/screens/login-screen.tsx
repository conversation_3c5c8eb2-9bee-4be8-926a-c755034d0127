"use client"

import Auth08 from "@/components/Auth08"
import { useState } from "react"

interface LoginScreenProps {
  onLogin: (email: string, password: string) => void
  onForgotPassword?: () => void
  onSignUp?: () => void
}

export default function LoginScreen({ onLogin, onForgotPassword, onSignUp }: LoginScreenProps) {
  const [isLoading, setIsLoading] = useState(false)

  const handleLogin = async (email: string, password: string) => {
    setIsLoading(true)
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))
      onLogin(email, password)
    } catch (error) {
      console.error("Login error:", error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-black p-4">
      <div className="w-full max-w-md">
        <Auth08 onSubmit={handleLogin} />

        <div className="mt-4 text-center space-y-2">
          {onForgotPassword && (
            <button
              onClick={onForgotPassword}
              className="text-[#A4D321] hover:text-[#8fb81d] text-sm
                transition-colors underline underline-offset-4 decoration-zinc-700
                font-medium"
            >
              Forgot your password?
            </button>
          )}

          {onSignUp && (
            <div>
              <button
                onClick={onSignUp}
                className="text-[#A4D321] hover:text-[#8fb81d] text-sm
                  transition-colors underline underline-offset-4 decoration-zinc-700
                  font-medium"
              >
                Don't have an account? Sign up
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
