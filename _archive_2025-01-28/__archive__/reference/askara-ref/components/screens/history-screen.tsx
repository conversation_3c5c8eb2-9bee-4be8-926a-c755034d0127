"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { StatusBar } from "@/components/ui/status-bar"
import { NavigationBar } from "@/components/ui/navigation-bar"
import { HomeIndicator } from "@/components/ui/home-indicator"
import { Search } from "lucide-react"
import { AppHeader } from "@/components/ui/app-header"

interface ChatSession {
  id: string
  title: string
  preview: string
  date: string
  isStarred: boolean
}

export function HistoryScreen({ onNavigate }: { onNavigate: (screen: string) => void }) {
  const [searchQuery, setSearchQuery] = useState("")

  // Sample chat history data
  const chatSessions: ChatSession[] = [
    {
      id: "1",
      title: "Project Planning",
      preview: "Let's create a timeline for the new mobile app launch...",
      date: "Today, 2:30 PM",
      isStarred: true,
    },
    {
      id: "2",
      title: "Travel Recommendations",
      preview: "I'm looking for places to visit in Japan during spring...",
      date: "Yesterday, 10:15 AM",
      isStarred: true,
    },
    {
      id: "3",
      title: "Recipe Ideas",
      preview: "Can you suggest some vegetarian dinner recipes?",
      date: "Mar 15, 2023",
      isStarred: false,
    },
    {
      id: "4",
      title: "Coding Help",
      preview: "How do I implement authentication in my React app?",
      date: "Mar 12, 2023",
      isStarred: false,
    },
    {
      id: "5",
      title: "Book Recommendations",
      preview: "I'm looking for science fiction novels similar to Dune...",
      date: "Mar 10, 2023",
      isStarred: false,
    },
  ]

  const filteredSessions = chatSessions.filter(
    (session) =>
      session.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      session.preview.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  return (
    <div className="flex flex-col w-full h-full min-h-screen relative">
      <StatusBar />

      <AppHeader userName="Chat History" userRole="Property Services" onSettingsClick={() => onNavigate("home")} />

      {/* History Content */}
      <div className="flex-1 flex flex-col w-full mt-4 overflow-hidden">
        {/* Search Bar */}
        <div className="w-full p-3 border-b border-[#ffffff]/10 px-6">
          <div className="relative">
            <Search className="absolute left-2.5 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-[#ffffffb2]" />
            <input
              type="text"
              placeholder="Search conversations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-8 pr-3 py-1.5 rounded-lg bg-black/80 backdrop-blur-sm border border-[#ffffff]/10 focus:outline-none focus:ring-1 focus:ring-[#3D4D61]/50 text-white text-xs"
            />
          </div>
        </div>

        {/* Chat Sessions List */}
        <div className="flex-1 w-full overflow-y-auto pb-24">
          {filteredSessions.length > 0 ? (
            <div className="divide-y divide-[#ffffff]/10 px-6">
              {filteredSessions.map((session) => (
                <motion.div
                  key={session.id}
                  className="p-3 hover:bg-[#ffffff]/5 cursor-pointer"
                  whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
                  onClick={() => onNavigate("home")}
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center">
                        <h3 className="text-white text-xs font-medium">{session.title}</h3>
                        {session.isStarred && <div className="ml-1.5 text-[#f59e0b] text-[10px]">􀋂</div>}
                      </div>
                      <p className="text-[#ffffffb2] text-[10px] line-clamp-1 mt-0.5">{session.preview}</p>
                    </div>
                    <span className="text-[#ffffffb2] text-[9px]">{session.date}</span>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-full p-4 text-center">
              <div className="text-[#ffffffb2] text-4xl mb-3">􀉭</div>
              <p className="text-[#ffffffb2] text-xs mb-1.5">No conversations found</p>
              <p className="text-[#ffffffb2] text-[10px]">Try a different search term or start a new chat</p>
            </div>
          )}
        </div>
      </div>

      <NavigationBar activeScreen="history" onNavigate={onNavigate} />
      <HomeIndicator />
    </div>
  )
}
