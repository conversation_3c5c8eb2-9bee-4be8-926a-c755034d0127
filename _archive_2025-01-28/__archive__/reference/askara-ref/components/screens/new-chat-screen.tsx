"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { StatusBar } from "@/components/ui/status-bar"
import { NavigationBar } from "@/components/ui/navigation-bar"
import { HomeIndicator } from "@/components/ui/home-indicator"
import { Label } from "@/components/ui/label"
import { Sparkles, Zap, Brain, Lightbulb, Code, BookOpen } from "lucide-react"
import { AppHeader } from "@/components/ui/app-header"

export function NewChatScreen({ onNavigate }: { onNavigate: (screen: string) => void }) {
  const [selectedModel, setSelectedModel] = useState("gpt-4o")
  const [temperature, setTemperature] = useState(0.7)
  const [maxTokens, setMaxTokens] = useState(2000)
  const [selectedTemplate, setSelectedTemplate] = useState("")

  // Sample templates
  const templates = [
    { id: "creative", name: "Creative Writing", icon: <Sparkles className="h-5 w-5" />, color: "#f59e0b" },
    { id: "coding", name: "Code Assistant", icon: <Code className="h-5 w-5" />, color: "#10b981" },
    { id: "research", name: "Research Helper", icon: <BookOpen className="h-5 w-5" />, color: "#3b82f6" },
    { id: "brainstorm", name: "Brainstorming", icon: <Brain className="h-5 w-5" />, color: "#8b5cf6" },
    { id: "tutor", name: "Learning Tutor", icon: <Lightbulb className="h-5 w-5" />, color: "#ec4899" },
    { id: "quick", name: "Quick Chat", icon: <Zap className="h-5 w-5" />, color: "#ef4444" },
  ]

  const startNewChat = () => {
    // Here you would typically set up a new chat with the selected parameters
    onNavigate("home")
  }

  return (
    <div className="flex flex-col w-full h-full min-h-screen relative">
      <StatusBar />

      <AppHeader userName="New Chat" userRole="Property Services" />

      {/* New Chat Content */}
      <div className="flex-1 flex flex-col w-full mt-4 px-6 overflow-y-auto pb-24">
        {/* Templates */}
        <div className="space-y-3">
          <h2 className="text-white text-sm font-medium">Templates</h2>
          <div className="grid grid-cols-2 gap-2.5">
            {templates.map((template) => (
              <motion.div
                key={template.id}
                className={`p-3 rounded-lg border ${
                  selectedTemplate === template.id
                    ? `border-[${template.color}] bg-[${template.color}]/10`
                    : "border-[#ffffff]/10 bg-black/40"
                } cursor-pointer shadow-sm`}
                whileHover={{
                  scale: 1.02,
                  backgroundColor: `rgba(${Number.parseInt(template.color.slice(1, 3), 16)}, ${Number.parseInt(template.color.slice(3, 5), 16)}, ${Number.parseInt(template.color.slice(5, 7), 16)}, 0.1)`,
                }}
                whileTap={{ scale: 0.98 }}
                onClick={() => setSelectedTemplate(template.id)}
              >
                <div className="flex flex-col items-center text-center">
                  <div className="mb-1.5" style={{ color: template.color }}>
                    {template.icon}
                  </div>
                  <span className="text-white text-xs">{template.name}</span>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Model Selection */}
        <div className="space-y-1.5 mt-6">
          <Label htmlFor="model" className="text-[#ffffffb2] text-xs">
            AI Model
          </Label>
          <select
            id="model"
            value={selectedModel}
            onChange={(e) => setSelectedModel(e.target.value)}
            className="w-full px-2.5 py-1.5 rounded-md bg-black/80 border border-[#ffffff]/10 text-white text-xs"
          >
            <option value="gpt-4o">GPT-4o</option>
            <option value="gpt-4-turbo">GPT-4 Turbo</option>
            <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
          </select>
        </div>

        {/* Advanced Settings */}
        <div className="space-y-3 mt-6">
          <h2 className="text-white text-sm font-medium">Advanced Settings</h2>

          <div className="space-y-1.5">
            <div className="flex justify-between items-center">
              <Label htmlFor="temperature" className="text-[#ffffffb2] text-xs">
                Temperature: {temperature}
              </Label>
              <span className="text-[9px] text-[#ffffffb2]">{temperature}</span>
            </div>
            <input
              id="temperature"
              type="range"
              min="0"
              max="2"
              step="0.1"
              value={temperature}
              onChange={(e) => setTemperature(Number.parseFloat(e.target.value))}
              className="w-full"
            />
            <p className="text-[9px] text-[#ffffffb2]">
              Higher values make output more random, lower values more deterministic.
            </p>
          </div>
        </div>

        {/* System Instructions */}
        <div className="space-y-2 mt-6">
          <Label htmlFor="system-instructions" className="text-[#ffffffb2]">
            System Instructions
          </Label>
          <textarea
            id="system-instructions"
            rows={4}
            placeholder="Enter system instructions for the AI..."
            defaultValue={
              selectedTemplate === "creative"
                ? "You are a creative writing assistant. Help the user craft engaging stories, poems, and other creative content."
                : selectedTemplate === "coding"
                  ? "You are a coding assistant. Help the user write, debug, and optimize code."
                  : selectedTemplate === "research"
                    ? "You are a research assistant. Help the user find information, summarize articles, and organize research."
                    : selectedTemplate === "brainstorm"
                      ? "You are a brainstorming partner. Help the user generate and refine ideas."
                      : selectedTemplate === "tutor"
                        ? "You are a learning tutor. Help the user understand concepts and learn new skills."
                        : selectedTemplate === "quick"
                          ? "You are a helpful assistant. Provide concise and accurate responses."
                          : "You are a helpful assistant."
            }
            className="w-full px-3 py-2 rounded-lg bg-black/80 border border-[#ffffff]/10 text-white"
          />
        </div>

        {/* Start Button */}
        <motion.button
          className="w-full py-2 rounded-md bg-gradient-to-r from-[#3D4D61] to-[#A4D321] text-white text-xs font-medium shadow-md mt-6"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={startNewChat}
        >
          Start New Chat
        </motion.button>
      </div>

      <NavigationBar activeScreen="new" onNavigate={onNavigate} />
      <HomeIndicator />
    </div>
  )
}
