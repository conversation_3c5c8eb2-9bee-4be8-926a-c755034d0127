"use client"

import { motion } from "framer-motion"
import { ScreenLayout } from "@/components/ui/screen-layout"
import { Calendar, Users, BarChart2, Settings, HelpCircle, LogOut } from "lucide-react"

interface MoreMenuScreenProps {
  onNavigate: (screen: string) => void
  userData: {
    name: string
    role: string
    department: string
  }
}

export function MoreMenuScreen({ onNavigate, userData }: MoreMenuScreenProps) {
  const menuItems = [
    { id: "schedule", icon: <Calendar className="h-6 w-6" />, label: "Schedule" },
    { id: "team", icon: <Users className="h-6 w-6" />, label: "Team" },
    { id: "reports", icon: <BarChart2 className="h-6 w-6" />, label: "Reports" },
    { id: "settings", icon: <Settings className="h-6 w-6" />, label: "Settings" },
    { id: "help", icon: <HelpCircle className="h-6 w-6" />, label: "Help" },
    { id: "logout", icon: <LogOut className="h-6 w-6" />, label: "Log Out" },
  ]

  return (
    <ScreenLayout title="More" subtitle="App Options" onNavigate={onNavigate} activeScreen="more">
      <div className="grid grid-cols-2 gap-4">
        {menuItems.map((item) => (
          <motion.button
            key={item.id}
            className={`flex flex-col items-center justify-center p-6 rounded-xl 
              ${
                item.id === "logout"
                  ? "bg-red-500/10 border border-red-500/30"
                  : "bg-black/30 backdrop-blur-lg border border-white/10"
              } shadow-sm`}
            whileHover={{
              scale: 1.05,
              backgroundColor: item.id === "logout" ? "rgba(239, 68, 68, 0.2)" : "rgba(255, 255, 255, 0.05)",
            }}
            whileTap={{ scale: 0.95 }}
            onClick={() => onNavigate(item.id)}
          >
            <div
              className={`w-14 h-14 rounded-full flex items-center justify-center mb-3 
                ${item.id === "logout" ? "bg-red-500/20" : "bg-[#3D4D61]/40"}`}
            >
              <div className={item.id === "logout" ? "text-red-500" : "text-[#A4D321]"}>{item.icon}</div>
            </div>
            <span className={`text-sm font-medium ${item.id === "logout" ? "text-red-500" : "text-white"}`}>
              {item.label}
            </span>
          </motion.button>
        ))}
      </div>
    </ScreenLayout>
  )
}
