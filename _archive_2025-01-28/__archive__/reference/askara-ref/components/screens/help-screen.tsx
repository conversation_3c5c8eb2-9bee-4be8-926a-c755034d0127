"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { ScreenLayout } from "@/components/ui/screen-layout"
import { Bot, ChevronRight, ChevronDown, Phone, Mail, Play } from "lucide-react"
import { Button } from "@/components/ui/button"

interface HelpScreenProps {
  onNavigate: (screen: string) => void
  userData: {
    name: string
    role: string
    department: string
  }
}

export function HelpScreen({ onNavigate, userData }: HelpScreenProps) {
  const [expandedFaq, setExpandedFaq] = useState<string | null>(null)

  // Sample FAQs
  const faqs = [
    {
      id: "1",
      question: "How do I create a new task?",
      answer:
        "To create a new task, navigate to the Tasks screen and click the + button in the top right corner. Fill in the task details and click Save.",
    },
    {
      id: "2",
      question: "How do I update my profile information?",
      answer:
        "You can update your profile by going to Settings > Profile Information. Click on the edit button and update your details as needed.",
    },
    {
      id: "3",
      question: "Can I access documents offline?",
      answer:
        "Yes, you can enable offline access in Settings > Data Management. Toggle on the Documents switch to make them available offline.",
    },
    {
      id: "4",
      question: "How do I contact support?",
      answer:
        "You can contact support via <NAME_EMAIL> or call our support line at +****************.",
    },
  ]

  // Sample tutorial videos
  const tutorials = [
    {
      id: "1",
      title: "Getting Started with ARA",
      duration: "5:32",
      type: "Tutorial",
    },
    {
      id: "2",
      title: "Task Management Features",
      duration: "8:47",
      type: "Guide",
    },
    {
      id: "3",
      title: "Using the Knowledge Base",
      duration: "3:15",
      type: "Tutorial",
    },
  ]

  const toggleFaq = (id: string) => {
    if (expandedFaq === id) {
      setExpandedFaq(null)
    } else {
      setExpandedFaq(id)
    }
  }

  return (
    <ScreenLayout title="Help" subtitle="Get assistance and support" onNavigate={onNavigate} activeScreen="help">
      {/* AI Assistant Card */}
      <div className="bg-black/30 backdrop-blur-lg rounded-xl p-4 border border-white/10 shadow-sm mb-6">
        <div className="flex items-center gap-3 mb-3">
          <div className="w-10 h-10 rounded-full bg-[#A4D321]/20 flex items-center justify-center">
            <Bot className="h-5 w-5 text-[#A4D321]" />
          </div>
          <div>
            <h3 className="text-white text-base font-medium">AI Assistant</h3>
            <p className="text-[#ffffffb2] text-xs">Get instant answers to your questions</p>
          </div>
        </div>
        <Button className="w-full bg-[#A4D321] text-black hover:bg-[#A4D321]/80">Chat with AI Assistant</Button>
      </div>

      {/* FAQs Section */}
      <div className="mb-6">
        <h2 className="text-white text-lg font-medium mb-3">Frequently Asked Questions</h2>
        <div className="space-y-3">
          {faqs.map((faq) => (
            <motion.div
              key={faq.id}
              className="bg-black/30 backdrop-blur-lg rounded-xl border border-white/10 shadow-sm overflow-hidden"
              animate={{ height: expandedFaq === faq.id ? "auto" : "auto" }}
              transition={{ duration: 0.3 }}
            >
              <button
                className="w-full flex justify-between items-center p-4 text-left"
                onClick={() => toggleFaq(faq.id)}
              >
                <h3 className="text-white text-sm font-medium">{faq.question}</h3>
                {expandedFaq === faq.id ? (
                  <ChevronDown className="h-5 w-5 text-[#ffffffb2]" />
                ) : (
                  <ChevronRight className="h-5 w-5 text-[#ffffffb2]" />
                )}
              </button>
              {expandedFaq === faq.id && (
                <div className="px-4 pb-4 text-[#ffffffb2] text-sm border-t border-white/10">{faq.answer}</div>
              )}
            </motion.div>
          ))}
        </div>
      </div>

      {/* Contact Support Section */}
      <div className="bg-black/30 backdrop-blur-lg rounded-xl p-4 border border-white/10 shadow-sm mb-6">
        <h2 className="text-white text-lg font-medium mb-3">Contact Support</h2>
        <div className="space-y-3">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full bg-white/10 flex items-center justify-center">
              <Phone className="h-5 w-5 text-[#A4D321]" />
            </div>
            <div>
              <p className="text-[#ffffffb2] text-xs">Phone Support</p>
              <p className="text-white text-sm">+****************</p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full bg-white/10 flex items-center justify-center">
              <Mail className="h-5 w-5 text-[#A4D321]" />
            </div>
            <div>
              <p className="text-[#ffffffb2] text-xs">Email Support</p>
              <p className="text-white text-sm"><EMAIL></p>
            </div>
          </div>
        </div>
      </div>

      {/* Tutorial Videos Section */}
      <div>
        <h2 className="text-white text-lg font-medium mb-3">Tutorial Videos</h2>
        <div className="space-y-3">
          {tutorials.map((tutorial) => (
            <motion.div
              key={tutorial.id}
              className="bg-black/30 backdrop-blur-lg rounded-xl p-3 border border-white/10 shadow-sm"
              whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
            >
              <div className="flex items-center gap-3">
                <div className="relative w-20 h-16 bg-black/60 rounded-lg flex-shrink-0">
                  {/* Placeholder for video thumbnail */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-8 h-8 rounded-full bg-[#A4D321] flex items-center justify-center">
                      <Play className="h-4 w-4 text-black" />
                    </div>
                  </div>
                </div>
                <div>
                  <h3 className="text-white text-sm font-medium">{tutorial.title}</h3>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-[#ffffffb2] text-xs">{tutorial.duration}</span>
                    <span className="w-1 h-1 bg-[#ffffffb2] rounded-full"></span>
                    <span className="text-[#ffffffb2] text-xs">{tutorial.type}</span>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </ScreenLayout>
  )
}
