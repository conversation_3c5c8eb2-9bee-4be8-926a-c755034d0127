"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { ScreenLayout } from "@/components/ui/screen-layout"
import { Search, FileText, Book, FileSpreadsheet, FileCode, Download, Bot } from "lucide-react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>ontent } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"

interface KnowledgeBaseScreenProps {
  onNavigate: (screen: string) => void
  userData: {
    name: string
    role: string
    department: string
  }
}

interface Document {
  id: string
  title: string
  type: "manual" | "form" | "guide" | "sop"
  date: string
  size: string
}

const getDocumentIcon = (type: string) => {
  switch (type) {
    case "manual":
      return <Book className="h-10 w-10 text-blue-500" />
    case "form":
      return <FileSpreadsheet className="h-10 w-10 text-green-500" />
    case "guide":
      return <FileText className="h-10 w-10 text-amber-500" />
    case "sop":
      return <FileCode className="h-10 w-10 text-[#A4D321]" />
    default:
      return <FileText className="h-10 w-10 text-gray-500" />
  }
}

export function KnowledgeBaseScreen({ onNavigate, userData }: KnowledgeBaseScreenProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [documents, setDocuments] = useState<Document[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Fetch documents from API
    fetchDocuments()
  }, [])

  const fetchDocuments = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // In a real app, this would be an API call
      // const response = await fetch('/api/documents')
      // const data = await response.json()
      // setDocuments(data.documents)

      // For now, just set empty array
      setDocuments([])
    } catch (err: any) {
      console.error("Error fetching documents:", err)
      setError(err.message || "Failed to load documents")
    } finally {
      setIsLoading(false)
    }
  }

  const filterDocuments = (filter: string) => {
    if (filter === "all") {
      return documents
    }
    return documents.filter((doc) => doc.type === filter)
  }

  return (
    <ScreenLayout
      title="Knowledge Base"
      subtitle="Documents & Resources"
      onNavigate={onNavigate}
      activeScreen="knowledge"
    >
      {/* Search Bar */}
      <div className="relative mb-6">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-[#ffffffb2]" />
        <input
          type="text"
          placeholder="Search documents..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="w-full pl-10 pr-4 py-3 rounded-lg bg-black/40 backdrop-blur-sm border border-white/10 text-white text-sm focus:outline-none focus:ring-2 focus:ring-[#A4D321]/50"
        />
      </div>

      {/* Document Tabs */}
      <Tabs defaultValue="all" className="w-full mb-6">
        <TabsList className="w-full grid grid-cols-4 bg-black/40 backdrop-blur-sm mb-6">
          <TabsTrigger value="all" className="data-[state=active]:text-[#A4D321]">
            All
          </TabsTrigger>
          <TabsTrigger value="manual" className="data-[state=active]:text-[#A4D321]">
            Manuals
          </TabsTrigger>
          <TabsTrigger value="form" className="data-[state=active]:text-[#A4D321]">
            Forms
          </TabsTrigger>
          <TabsTrigger value="guide" className="data-[state=active]:text-[#A4D321]">
            Guides
          </TabsTrigger>
        </TabsList>

        {isLoading ? (
          <div className="flex items-center justify-center p-12">
            <div className="w-8 h-8 border-2 border-[#A4D321] border-t-transparent rounded-full animate-spin" />
          </div>
        ) : error ? (
          <div className="p-6 text-center">
            <p className="text-red-400 mb-4">{error}</p>
            <button className="px-4 py-2 bg-[#3D4D61] text-white rounded-md text-sm" onClick={fetchDocuments}>
              Try Again
            </button>
          </div>
        ) : documents.length === 0 ? (
          <div className="p-6 text-center">
            <p className="text-[#ffffffb2]">No documents found</p>
          </div>
        ) : (
          <>
            <TabsContent value="all" className="space-y-4">
              {filterDocuments("all").map((doc) => (
                <DocumentCard key={doc.id} document={doc} />
              ))}
            </TabsContent>

            <TabsContent value="manual" className="space-y-4">
              {filterDocuments("manual").map((doc) => (
                <DocumentCard key={doc.id} document={doc} />
              ))}
            </TabsContent>

            <TabsContent value="form" className="space-y-4">
              {filterDocuments("form").map((doc) => (
                <DocumentCard key={doc.id} document={doc} />
              ))}
            </TabsContent>

            <TabsContent value="guide" className="space-y-4">
              {filterDocuments("guide").map((doc) => (
                <DocumentCard key={doc.id} document={doc} />
              ))}
            </TabsContent>
          </>
        )}
      </Tabs>

      {/* AI Assisted Search Section */}
      <div className="bg-black/30 backdrop-blur-lg rounded-xl p-4 border border-white/10 shadow-sm">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-10 h-10 rounded-full bg-[#A4D321]/20 flex items-center justify-center">
            <Bot className="h-5 w-5 text-[#A4D321]" />
          </div>
          <div>
            <h3 className="text-white text-base font-medium">AI Assisted Search</h3>
            <p className="text-[#ffffffb2] text-xs">Ask ARA to find specific information</p>
          </div>
        </div>

        <div className="space-y-2">
          <Button variant="outline" className="w-full justify-start text-sm bg-black/30 border-white/10 text-white">
            Find cleaning protocols for office buildings
          </Button>
          <Button variant="outline" className="w-full justify-start text-sm bg-black/30 border-white/10 text-white">
            Show safety procedures for chemical handling
          </Button>
          <Button variant="outline" className="w-full justify-start text-sm bg-black/30 border-white/10 text-white">
            Documents related to TechCorp contract
          </Button>
        </div>
      </div>
    </ScreenLayout>
  )
}

interface DocumentCardProps {
  document: Document
}

function DocumentCard({ document }: DocumentCardProps) {
  return (
    <motion.div
      className="flex items-center justify-between bg-black/30 backdrop-blur-lg rounded-xl p-3 border border-white/10 shadow-sm"
      whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
    >
      <div className="flex items-center gap-3">
        <div className="flex-shrink-0">{getDocumentIcon(document.type)}</div>
        <div>
          <h3 className="text-white text-sm font-medium">{document.title}</h3>
          <p className="text-[#ffffffb2] text-xs">
            {document.date} • {document.size}
          </p>
        </div>
      </div>
      <motion.button
        className="flex items-center justify-center w-8 h-8 rounded-full bg-white/10"
        whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.2)" }}
      >
        <Download className="h-4 w-4 text-[#A4D321]" />
      </motion.button>
    </motion.div>
  )
}
