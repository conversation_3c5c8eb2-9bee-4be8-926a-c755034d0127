"use client"

import { motion } from "framer-motion"
import { StatusBar } from "@/components/ui/status-bar"
import { NavigationBar } from "@/components/ui/navigation-bar"
import { HomeIndicator } from "@/components/ui/home-indicator"
import { Progress } from "@/components/ui/progress"
import { AppHeader } from "@/components/ui/app-header"
import { Badge } from "@/components/ui/badge"
import { Mail, Phone, MapPin, Settings, HelpCircle, LogOut } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ScrollArea } from "@/components/ui/scroll-area"

interface UserData {
  name: string
  role: string
  department: string
}

export function ProfileScreen({
  onNavigate,
  userData,
  isDrawer = false,
}: {
  onNavigate: (screen: string) => void
  userData?: UserData
  isDrawer?: boolean
}) {
  // Sample user stats
  const userStats = {
    messagesExchanged: 1243,
    conversationsStarted: 87,
    daysActive: 42,
    usagePercentage: 65,
    usageLimit: "500,000",
    usageUsed: "325,000",
    subscription: "Pro Plan",
    subscriptionRenewal: "Apr 15, 2023",
    estimates: "250+",
    accuracy: "98%",
    contractValue: "$150M+",
  }

  return (
    <div className={`flex flex-col w-full ${isDrawer ? "h-full" : "h-full min-h-screen"} relative`}>
      {!isDrawer && (
        <>
          <StatusBar />
          <AppHeader userName="Profile" userRole="Property Services" onSettingsClick={() => onNavigate("settings")} />
        </>
      )}

      {/* Profile Content */}
      <ScrollArea className="flex-1 w-full">
        <div className="p-6 space-y-6">
          {/* User Profile */}
          <div className="flex flex-col items-center space-y-4">
            <Avatar className="h-24 w-24">
              <AvatarImage src="/placeholder.svg?height=96&width=96" alt={userData?.name || "User"} />
              <AvatarFallback className="bg-[#3D4D61] text-white text-xl">
                {userData?.name
                  ?.split(" ")
                  .map((n) => n[0])
                  .join("") || "JS"}
              </AvatarFallback>
            </Avatar>
            <div className="text-center">
              <h2 className="text-2xl font-bold text-white">{userData?.name || "John Smith"}</h2>
              <p className="text-[#ffffffb2]">{userData?.role || "Property Manager"}</p>
            </div>
            <Badge className="bg-[#A4D321] hover:bg-[#A4D321]/80 text-black">Expert</Badge>

            <motion.button
              className="px-3.5 py-1.5 rounded-md bg-gradient-to-r from-[#3D4D61] to-[#A4D321] text-white text-xs font-medium shadow-md"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Edit Profile
            </motion.button>
          </div>

          {/* Stats */}
          <div className="flex justify-between text-center bg-black/40 rounded-lg p-4 border border-[#ffffff]/10 shadow-sm">
            <div>
              <p className="text-2xl font-bold text-white">{userStats.estimates}</p>
              <p className="text-sm text-[#ffffffb2]">Estimates</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-white">{userStats.accuracy}</p>
              <p className="text-sm text-[#ffffffb2]">Accuracy</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-white">{userStats.contractValue}</p>
              <p className="text-sm text-[#ffffffb2]">Contract Value</p>
            </div>
          </div>

          {/* Contact Info */}
          <div className="space-y-2 bg-black/40 rounded-lg p-4 border border-[#ffffff]/10 shadow-sm">
            <div className="flex items-center">
              <Mail className="h-4 w-4 mr-2 text-[#A4D321]" />
              <span className="text-sm text-white"><EMAIL></span>
            </div>
            <div className="flex items-center">
              <Phone className="h-4 w-4 mr-2 text-[#A4D321]" />
              <span className="text-sm text-white">(+61) ***********</span>
            </div>
            <div className="flex items-center">
              <MapPin className="h-4 w-4 mr-2 text-[#A4D321]" />
              <span className="text-sm text-white">Melbourne, Australia</span>
            </div>
          </div>

          {/* Expertise */}
          <div className="bg-black/40 rounded-lg p-4 border border-[#ffffff]/10 shadow-sm">
            <p className="text-sm font-semibold mb-2 text-white">Expertise</p>
            <div className="flex flex-wrap gap-2">
              <Badge variant="secondary" className="bg-[#121624]/60 text-white border border-[#ffffff]/10">
                Cost Estimation
              </Badge>
              <Badge variant="secondary" className="bg-[#121624]/60 text-white border border-[#ffffff]/10">
                Commercial Projects
              </Badge>
              <Badge variant="secondary" className="bg-[#121624]/60 text-white border border-[#ffffff]/10">
                Quantity Surveying
              </Badge>
              <Badge variant="secondary" className="bg-[#121624]/60 text-white border border-[#ffffff]/10">
                Risk Assessment
              </Badge>
              <Badge variant="secondary" className="bg-[#121624]/60 text-white border border-[#ffffff]/10">
                Value Engineering
              </Badge>
            </div>
          </div>

          {/* Usage Stats */}
          <div className="space-y-1.5 bg-black/40 rounded-lg p-4 border border-[#ffffff]/10 shadow-sm">
            <div className="flex justify-between items-center">
              <h3 className="text-white text-sm font-medium">Usage</h3>
              <span className="text-[#ffffffb2] text-[10px]">{userStats.usagePercentage}%</span>
            </div>
            <Progress value={userStats.usagePercentage} className="h-1.5 bg-[#ffffff]/10">
              <div className="h-full rounded-full [background:linear-gradient(90deg,rgba(61,77,97,1)_0%,rgba(164,211,33,1)_100%)]" />
            </Progress>
            <div className="flex justify-between items-center text-[9px] text-[#ffffffb2]">
              <span>{userStats.usageUsed} tokens used</span>
              <span>{userStats.usageLimit} tokens limit</span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-2">
            <motion.button
              className="w-full py-2 rounded-lg bg-[#121624]/80 text-white text-sm font-medium border border-[#ffffff]/10 flex items-center justify-center"
              whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.1)" }}
              onClick={() => onNavigate("settings")}
            >
              <Settings className="mr-2 h-4 w-4 text-[#A4D321]" />
              Settings
            </motion.button>
            <motion.button
              className="w-full py-2 rounded-lg bg-[#121624]/80 text-white text-sm font-medium border border-[#ffffff]/10 flex items-center justify-center"
              whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.1)" }}
            >
              <HelpCircle className="mr-2 h-4 w-4 text-[#A4D321]" />
              Help & Support
            </motion.button>
            <motion.button
              className="w-full py-2 rounded-lg bg-red-500/20 text-red-500 text-sm font-medium border border-red-500/30 flex items-center justify-center"
              whileHover={{ backgroundColor: "rgba(239, 68, 68, 0.3)" }}
              onClick={() => onNavigate("logout")}
            >
              <LogOut className="mr-2 h-4 w-4" />
              Log Out
            </motion.button>
          </div>
        </div>
      </ScrollArea>

      {!isDrawer && (
        <>
          <NavigationBar activeScreen="profile" onNavigate={onNavigate} />
          <HomeIndicator />
        </>
      )}
    </div>
  )
}
