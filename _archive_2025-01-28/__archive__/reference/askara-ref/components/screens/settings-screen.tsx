"use client"

import { useState, useEffect } from "react"
import { StatusBar } from "@/components/ui/status-bar"
import { NavigationBar } from "@/components/ui/navigation-bar"
import { HomeIndicator } from "@/components/ui/home-indicator"
import { Switch } from "@/components/ui/switch"
import { ChevronRight, User, Lock, Bell, Moon, MessageSquare, HelpCircle, Info, LogOut, Download } from "lucide-react"
import { useTheme } from "next-themes"
import { InstallPrompt } from "@/components/install-prompt"
import { motion } from "framer-motion"
import { AppHeader } from "@/components/ui/app-header"

export function SettingsScreen({
  apiKey,
  setApiKey,
  onNavigate,
  onLogout,
  userData,
  isDrawer = false,
}: {
  apiKey: string
  setApiKey: (key: string) => void
  onNavigate: (screen: string) => void
  onLogout: () => void
  userData?: { name: string; role: string; department: string }
  isDrawer?: boolean
}) {
  const [notificationsEnabled, setNotificationsEnabled] = useState(true)
  const [isRecording, setIsRecording] = useState(false)
  const [showInstallPrompt, setShowInstallPrompt] = useState(false)
  const { theme, setTheme } = useTheme()
  const [mounted, setMounted] = useState(false)
  const [isStandalone, setIsStandalone] = useState(false)

  // After mounting, we can safely show the UI that depends on the theme
  useEffect(() => {
    setMounted(true)

    // Check if app is already installed
    if (typeof window !== "undefined" && window.matchMedia("(display-mode: standalone)").matches) {
      setIsStandalone(true)
    }
  }, [])

  const toggleMicrophone = () => {
    setIsRecording(!isRecording)

    // Simulate stopping recording after 5 seconds
    if (!isRecording) {
      setTimeout(() => {
        setIsRecording(false)
      }, 5000)
    }
  }

  // Handle dark mode toggle
  const toggleDarkMode = () => {
    setTheme(theme === "dark" ? "light" : "dark")
  }

  // Only show the UI after mounting to avoid hydration mismatch
  if (!mounted) {
    return null
  }

  // Replace the darkMode state with the actual theme value
  const isDarkMode = theme === "dark"

  return (
    <div className={`flex flex-col w-full ${isDrawer ? "h-full" : "h-full min-h-screen"} relative`}>
      {!isDrawer && (
        <>
          <StatusBar />
          <AppHeader
            userName="Settings"
            userRole="Property Services"
            onSettingsClick={() => {}}
            onProfileClick={() => {}}
          />
        </>
      )}

      {/* Settings Content */}
      <div className="flex-1 flex flex-col w-full px-6 mt-2 overflow-y-auto pb-24">
        {/* Account Section */}
        <div className="w-full mb-5">
          <h2 className="text-white text-sm font-medium mb-2">Account</h2>

          <div className="bg-[#121624]/40 backdrop-blur-sm rounded-xl overflow-hidden border border-[#ffffff]/10 shadow-sm">
            <motion.button
              className="w-full flex items-center justify-between p-4 border-b border-[#ffffff]/10"
              whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
            >
              <div className="flex items-center">
                <User className="w-5 h-5 text-[#A4D321] mr-3" />
                <span className="text-white text-sm">Edit Profile</span>
              </div>
              <ChevronRight className="w-5 h-5 text-[#ffffffb2]" />
            </motion.button>

            <motion.button
              className="w-full flex items-center justify-between p-4"
              whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
            >
              <div className="flex items-center">
                <Lock className="w-5 h-5 text-[#A4D321] mr-3" />
                <span className="text-white text-sm">Change Password</span>
              </div>
              <ChevronRight className="w-5 h-5 text-[#ffffffb2]" />
            </motion.button>
          </div>
        </div>

        {/* Preferences Section */}
        <div className="w-full mb-5">
          <h2 className="text-white text-sm font-medium mb-2">Preferences</h2>

          <div className="bg-[#121624]/40 backdrop-blur-sm rounded-xl overflow-hidden border border-[#ffffff]/10 shadow-sm">
            <motion.div
              className="flex items-center justify-between p-4 border-b border-[#ffffff]/10"
              whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
            >
              <div className="flex items-center">
                <Bell className="w-5 h-5 text-[#A4D321] mr-3" />
                <span className="text-white text-sm">Notifications</span>
              </div>
              <Switch
                checked={notificationsEnabled}
                onCheckedChange={setNotificationsEnabled}
                className="data-[state=checked]:bg-[#A4D321]"
              />
            </motion.div>

            <motion.div
              className="flex items-center justify-between p-4 border-b border-[#ffffff]/10"
              whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
            >
              <div className="flex items-center">
                <Moon className="w-5 h-5 text-[#A4D321] mr-3" />
                <span className="text-white text-sm">Dark Mode</span>
              </div>
              <Switch
                checked={isDarkMode}
                onCheckedChange={toggleDarkMode}
                className="data-[state=checked]:bg-[#A4D321]"
              />
            </motion.div>

            <motion.button
              className="w-full flex items-center justify-between p-4"
              whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
            >
              <div className="flex items-center">
                <MessageSquare className="w-5 h-5 text-[#A4D321] mr-3" />
                <span className="text-white text-sm">Clear Chat History</span>
              </div>
              <ChevronRight className="w-5 h-5 text-[#ffffffb2]" />
            </motion.button>
          </div>
        </div>

        {/* Support Section */}
        <div className="w-full mb-5">
          <h2 className="text-white text-sm font-medium mb-2">Support</h2>

          <div className="bg-[#121624]/40 backdrop-blur-sm rounded-xl overflow-hidden border border-[#ffffff]/10 shadow-sm">
            <motion.button
              className="w-full flex items-center justify-between p-4 border-b border-[#ffffff]/10"
              whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
            >
              <div className="flex items-center">
                <HelpCircle className="w-5 h-5 text-[#A4D321] mr-3" />
                <span className="text-white text-sm">Help & Support</span>
              </div>
              <ChevronRight className="w-5 h-5 text-[#ffffffb2]" />
            </motion.button>

            {!isStandalone && (
              <motion.button
                className="w-full flex items-center justify-between p-4 border-b border-[#ffffff]/10"
                onClick={() => setShowInstallPrompt(true)}
                whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
              >
                <div className="flex items-center">
                  <Download className="w-5 h-5 text-[#A4D321] mr-3" />
                  <span className="text-white text-sm">Install App</span>
                </div>
                <ChevronRight className="w-5 h-5 text-[#ffffffb2]" />
              </motion.button>
            )}

            <motion.button
              className="w-full flex items-center justify-between p-4"
              whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
            >
              <div className="flex items-center">
                <Info className="w-5 h-5 text-[#A4D321] mr-3" />
                <span className="text-white text-sm">About AskARA</span>
              </div>
              <ChevronRight className="w-5 h-5 text-[#ffffffb2]" />
            </motion.button>
          </div>
        </div>

        {/* Logout Button */}
        <motion.button
          className="w-full py-3.5 rounded-xl border border-red-500/50 text-red-500 text-base font-medium mb-3 bg-[#121624]/20 backdrop-blur-sm"
          onClick={onLogout}
          whileHover={{ backgroundColor: "rgba(239, 68, 68, 0.1)" }}
          whileTap={{ scale: 0.98 }}
        >
          <div className="flex items-center justify-center">
            <LogOut className="w-5 h-5 mr-2" />
            Logout
          </div>
        </motion.button>

        {/* Version */}
        <p className="text-[#ffffffb2] text-xs text-center w-full mb-4">Version 1.0.0</p>
      </div>

      {!isDrawer && (
        <>
          <NavigationBar
            activeScreen="settings"
            onNavigate={onNavigate}
            isRecording={isRecording}
            onVoiceToggle={toggleMicrophone}
          />
          <HomeIndicator />
        </>
      )}

      {/* Only show install prompt when explicitly requested */}
      {showInstallPrompt && <InstallPrompt forceShow={true} />}
    </div>
  )
}
