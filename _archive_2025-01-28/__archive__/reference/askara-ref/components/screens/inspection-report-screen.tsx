"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { ScreenLayout } from "@/components/ui/screen-layout"
import {
  AlertTriangle,
  CheckCircle,
  ChevronDown,
  ChevronUp,
  Clock,
  Download,
  FileText,
  MapPin,
  Share2,
  X,
  Play,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs"

interface InspectionReportScreenProps {
  onNavigate: (screen: string) => void
  userData: {
    name: string
    role: string
    department: string
  }
  reportId?: string
}

export function InspectionReportScreen({ onNavigate, userData, reportId }: InspectionReportScreenProps) {
  const [expandedSection, setExpandedSection] = useState<string | null>("summary")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [reportData, setReportData] = useState<any>(null)

  useEffect(() => {
    // Fetch report data
    fetchReportData()
  }, [reportId])

  const fetchReportData = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // In a real app, this would be an API call
      // const response = await fetch(`/api/inspection-reports/${reportId || 'latest'}`)
      // const data = await response.json()
      // setReportData(data)

      // For now, just set empty data structure
      setReportData({
        id: reportId || "",
        title: "",
        location: "",
        date: "",
        inspector: "",
        status: "",
        score: 0,
        summary: "",
        sections: {
          safety: {
            score: 0,
            items: [],
          },
          equipment: {
            score: 0,
            items: [],
          },
          compliance: {
            score: 0,
            items: [],
          },
        },
        actions: [],
        photos: 0,
        voiceNotes: 0,
      })
    } catch (err: any) {
      console.error("Error fetching report data:", err)
      setError(err.message || "Failed to load report data")
    } finally {
      setIsLoading(false)
    }
  }

  const toggleSection = (section: string) => {
    if (expandedSection === section) {
      setExpandedSection(null)
    } else {
      setExpandedSection(section)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "compliant":
        return "bg-green-500"
      case "non-compliant":
        return "bg-red-500"
      case "attention":
        return "bg-amber-500"
      default:
        return "bg-gray-500"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "compliant":
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case "non-compliant":
        return <X className="h-5 w-5 text-red-500" />
      case "attention":
        return <Clock className="h-5 w-5 text-amber-500" />
      default:
        return null
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "text-red-500"
      case "medium":
        return "text-amber-500"
      case "low":
        return "text-green-500"
      default:
        return "text-white"
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-500"
    if (score >= 70) return "text-amber-500"
    return "text-red-500"
  }

  if (isLoading) {
    return (
      <ScreenLayout title="Inspection Report" subtitle="Loading..." onNavigate={onNavigate} activeScreen="reports">
        <div className="flex items-center justify-center p-12">
          <div className="w-8 h-8 border-2 border-[#A4D321] border-t-transparent rounded-full animate-spin" />
        </div>
      </ScreenLayout>
    )
  }

  if (error) {
    return (
      <ScreenLayout title="Inspection Report" subtitle="Error" onNavigate={onNavigate} activeScreen="reports">
        <div className="p-6 text-center">
          <p className="text-red-400 mb-4">{error}</p>
          <Button onClick={() => fetchReportData()}>Try Again</Button>
        </div>
      </ScreenLayout>
    )
  }

  if (!reportData) {
    return (
      <ScreenLayout title="Inspection Report" subtitle="No Data" onNavigate={onNavigate} activeScreen="reports">
        <div className="p-6 text-center">
          <p className="text-[#ffffffb2]">No report data available</p>
        </div>
      </ScreenLayout>
    )
  }

  return (
    <ScreenLayout
      title="Inspection Report"
      subtitle={reportData.title}
      onNavigate={onNavigate}
      activeScreen="reports"
      headerRightContent={
        <div className="flex gap-2">
          <Button size="icon" variant="ghost" className="h-8 w-8 rounded-full bg-black/40 border border-white/10">
            <Share2 className="h-4 w-4 text-white" />
          </Button>
          <Button size="icon" variant="ghost" className="h-8 w-8 rounded-full bg-black/40 border border-white/10">
            <Download className="h-4 w-4 text-white" />
          </Button>
        </div>
      }
    >
      {/* Report Header */}
      <div className="bg-black/30 backdrop-blur-lg rounded-xl p-4 border border-white/10 shadow-sm mb-6">
        <div className="flex justify-between items-start mb-3">
          <div>
            <h2 className="text-white text-lg font-medium">{reportData.id || "No ID"}</h2>
            <p className="text-[#ffffffb2] text-sm">{reportData.date || "No date"}</p>
          </div>
          <div className="flex flex-col items-end">
            <div className={`text-lg font-bold ${getScoreColor(reportData.score)}`}>{reportData.score}%</div>
            <p className="text-[#ffffffb2] text-xs">Overall Score</p>
          </div>
        </div>

        <div className="flex items-center gap-3 mb-3">
          <MapPin className="h-4 w-4 text-[#ffffffb2]" />
          <p className="text-white text-sm">{reportData.location || "No location"}</p>
        </div>

        <div className="flex items-center gap-3">
          <FileText className="h-4 w-4 text-[#ffffffb2]" />
          <p className="text-white text-sm">Inspector: {reportData.inspector || "No inspector"}</p>
        </div>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="details" className="w-full mb-6">
        <TabsList className="w-full grid grid-cols-2 bg-black/40 backdrop-blur-sm mb-6">
          <TabsTrigger value="details" className="data-[state=active]:text-[#A4D321]">
            Details
          </TabsTrigger>
          <TabsTrigger value="actions" className="data-[state=active]:text-[#A4D321]">
            Actions
          </TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-4">
          {/* Summary Section */}
          <motion.div
            className="bg-black/30 backdrop-blur-lg rounded-xl overflow-hidden border border-white/10 shadow-sm"
            animate={{ height: expandedSection === "summary" ? "auto" : "auto" }}
            transition={{ duration: 0.3 }}
          >
            <div
              className="p-4 flex justify-between items-center cursor-pointer"
              onClick={() => toggleSection("summary")}
            >
              <h3 className="text-white text-base font-medium">Summary</h3>
              <motion.button>
                {expandedSection === "summary" ? (
                  <ChevronUp className="h-5 w-5 text-white" />
                ) : (
                  <ChevronDown className="h-5 w-5 text-white" />
                )}
              </motion.button>
            </div>

            {expandedSection === "summary" && (
              <div className="px-4 pb-4 border-t border-white/10 pt-4">
                <p className="text-white text-sm">{reportData.summary || "No summary available"}</p>
              </div>
            )}
          </motion.div>

          {/* Safety Section */}
          <motion.div
            className="bg-black/30 backdrop-blur-lg rounded-xl overflow-hidden border border-white/10 shadow-sm"
            animate={{ height: expandedSection === "safety" ? "auto" : "auto" }}
            transition={{ duration: 0.3 }}
          >
            <div
              className="p-4 flex justify-between items-center cursor-pointer"
              onClick={() => toggleSection("safety")}
            >
              <div className="flex items-center gap-2">
                <h3 className="text-white text-base font-medium">Safety</h3>
                <span className={`text-sm font-medium ${getScoreColor(reportData.sections.safety.score)}`}>
                  {reportData.sections.safety.score}%
                </span>
              </div>
              <motion.button>
                {expandedSection === "safety" ? (
                  <ChevronUp className="h-5 w-5 text-white" />
                ) : (
                  <ChevronDown className="h-5 w-5 text-white" />
                )}
              </motion.button>
            </div>

            {expandedSection === "safety" && (
              <div className="px-4 pb-4 border-t border-white/10 pt-4">
                {reportData.sections.safety.items.length > 0 ? (
                  <div className="space-y-4">
                    {reportData.sections.safety.items.map((item: any, index: number) => (
                      <div key={index} className="flex items-start gap-3">
                        <div className="mt-0.5">{getStatusIcon(item.status)}</div>
                        <div>
                          <p className="text-white text-sm">{item.name}</p>
                          {item.notes && <p className="text-[#ffffffb2] text-xs mt-1">{item.notes}</p>}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-[#ffffffb2] text-sm">No safety items available</p>
                )}
              </div>
            )}
          </motion.div>

          {/* Equipment Section */}
          <motion.div
            className="bg-black/30 backdrop-blur-lg rounded-xl overflow-hidden border border-white/10 shadow-sm"
            animate={{ height: expandedSection === "equipment" ? "auto" : "auto" }}
            transition={{ duration: 0.3 }}
          >
            <div
              className="p-4 flex justify-between items-center cursor-pointer"
              onClick={() => toggleSection("equipment")}
            >
              <div className="flex items-center gap-2">
                <h3 className="text-white text-base font-medium">Equipment</h3>
                <span className={`text-sm font-medium ${getScoreColor(reportData.sections.equipment.score)}`}>
                  {reportData.sections.equipment.score}%
                </span>
              </div>
              <motion.button>
                {expandedSection === "equipment" ? (
                  <ChevronUp className="h-5 w-5 text-white" />
                ) : (
                  <ChevronDown className="h-5 w-5 text-white" />
                )}
              </motion.button>
            </div>

            {expandedSection === "equipment" && (
              <div className="px-4 pb-4 border-t border-white/10 pt-4">
                {reportData.sections.equipment.items.length > 0 ? (
                  <div className="space-y-4">
                    {reportData.sections.equipment.items.map((item: any, index: number) => (
                      <div key={index} className="flex items-start gap-3">
                        <div className="mt-0.5">{getStatusIcon(item.status)}</div>
                        <div>
                          <p className="text-white text-sm">{item.name}</p>
                          {item.notes && <p className="text-[#ffffffb2] text-xs mt-1">{item.notes}</p>}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-[#ffffffb2] text-sm">No equipment items available</p>
                )}
              </div>
            )}
          </motion.div>

          {/* Compliance Section */}
          <motion.div
            className="bg-black/30 backdrop-blur-lg rounded-xl overflow-hidden border border-white/10 shadow-sm"
            animate={{ height: expandedSection === "compliance" ? "auto" : "auto" }}
            transition={{ duration: 0.3 }}
          >
            <div
              className="p-4 flex justify-between items-center cursor-pointer"
              onClick={() => toggleSection("compliance")}
            >
              <div className="flex items-center gap-2">
                <h3 className="text-white text-base font-medium">Compliance</h3>
                <span className={`text-sm font-medium ${getScoreColor(reportData.sections.compliance.score)}`}>
                  {reportData.sections.compliance.score}%
                </span>
              </div>
              <motion.button>
                {expandedSection === "compliance" ? (
                  <ChevronUp className="h-5 w-5 text-white" />
                ) : (
                  <ChevronDown className="h-5 w-5 text-white" />
                )}
              </motion.button>
            </div>

            {expandedSection === "compliance" && (
              <div className="px-4 pb-4 border-t border-white/10 pt-4">
                {reportData.sections.compliance.items.length > 0 ? (
                  <div className="space-y-4">
                    {reportData.sections.compliance.items.map((item: any, index: number) => (
                      <div key={index} className="flex items-start gap-3">
                        <div className="mt-0.5">{getStatusIcon(item.status)}</div>
                        <div>
                          <p className="text-white text-sm">{item.name}</p>
                          {item.notes && <p className="text-[#ffffffb2] text-xs mt-1">{item.notes}</p>}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-[#ffffffb2] text-sm">No compliance items available</p>
                )}
              </div>
            )}
          </motion.div>

          {/* Media Section */}
          <motion.div
            className="bg-black/30 backdrop-blur-lg rounded-xl overflow-hidden border border-white/10 shadow-sm"
            animate={{ height: expandedSection === "media" ? "auto" : "auto" }}
            transition={{ duration: 0.3 }}
          >
            <div
              className="p-4 flex justify-between items-center cursor-pointer"
              onClick={() => toggleSection("media")}
            >
              <h3 className="text-white text-base font-medium">Photos & Media</h3>
              <motion.button>
                {expandedSection === "media" ? (
                  <ChevronUp className="h-5 w-5 text-white" />
                ) : (
                  <ChevronDown className="h-5 w-5 text-white" />
                )}
              </motion.button>
            </div>

            {expandedSection === "media" && (
              <div className="px-4 pb-4 border-t border-white/10 pt-4">
                <p className="text-white text-sm mb-3">Photos ({reportData.photos || 0})</p>
                {reportData.photos > 0 ? (
                  <div className="grid grid-cols-3 gap-2 mb-4">
                    {Array.from({ length: reportData.photos }).map((_, index) => (
                      <div key={index} className="aspect-square bg-black/60 rounded-md overflow-hidden relative">
                        {/* Placeholder for photos */}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-[#ffffffb2] text-sm mb-4">No photos available</p>
                )}

                <p className="text-white text-sm mb-3">Voice Notes ({reportData.voiceNotes || 0})</p>
                {reportData.voiceNotes > 0 ? (
                  <div className="space-y-2">
                    {Array.from({ length: reportData.voiceNotes }).map((_, index) => (
                      <div
                        key={index}
                        className="bg-black/40 rounded-lg p-3 border border-white/10 flex items-center justify-between"
                      >
                        <p className="text-white text-sm">Voice Note {index + 1}</p>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Play className="h-4 w-4 text-white" />
                        </Button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-[#ffffffb2] text-sm">No voice notes available</p>
                )}
              </div>
            )}
          </motion.div>
        </TabsContent>

        <TabsContent value="actions" className="space-y-4">
          <div className="bg-black/30 backdrop-blur-lg rounded-xl p-4 border border-white/10 shadow-sm">
            <h3 className="text-white text-base font-medium mb-4">Required Actions</h3>

            {reportData.actions && reportData.actions.length > 0 ? (
              <div className="space-y-4">
                {reportData.actions.map((action: any) => (
                  <div key={action.id} className="bg-black/40 rounded-lg p-3 border border-white/10">
                    <div className="flex items-start gap-3">
                      <div className="mt-0.5">
                        <AlertTriangle className={`h-5 w-5 ${getPriorityColor(action.priority)}`} />
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <p className="text-white text-sm font-medium">{action.title}</p>
                          <span className={`text-xs font-medium ${getPriorityColor(action.priority)}`}>
                            {action.priority.charAt(0).toUpperCase() + action.priority.slice(1)}
                          </span>
                        </div>
                        <p className="text-[#ffffffb2] text-xs mt-1">Assigned to: {action.assignee}</p>
                        <p className="text-[#ffffffb2] text-xs">Due: {action.dueDate}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="p-6 text-center">
                <p className="text-[#ffffffb2]">No actions required</p>
              </div>
            )}
          </div>

          <Button className="w-full bg-[#A4D321] text-black hover:bg-[#A4D321]/80">Create Work Order</Button>
        </TabsContent>
      </Tabs>
    </ScreenLayout>
  )
}
