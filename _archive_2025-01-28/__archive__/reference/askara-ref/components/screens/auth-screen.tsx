"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Eye, EyeOff } from "lucide-react"
import { StatusBar } from "@/components/ui/status-bar"
import { HomeIndicator } from "@/components/ui/home-indicator"

interface AuthScreenProps {
  onLogin: (apiKey: string) => void
}

export function AuthScreen({ onLogin }: AuthScreenProps) {
  const [apiKey, setApiKey] = useState("sk-demo-1234567890abcdefghijklmnopqrstuvwxyz")
  const [showApiKey, setShowApiKey] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleConnect = () => {
    setIsLoading(true)
    // Simulate API connection
    setTimeout(() => {
      setIsLoading(false)
      onLogin(apiKey)
    }, 1500)
  }

  const handleSSOLogin = () => {
    setIsLoading(true)
    // Simulate SSO authentication
    setTimeout(() => {
      setIsLoading(false)
      onLogin("sso-authenticated-session")
    }, 2000)
  }

  return (
    <>
      <StatusBar />

      <div className="relative w-full h-full flex flex-col items-center justify-center bg-gradient-to-br from-black to-zinc-900">
        {/* Logo and Title */}
        <div className="flex flex-col items-center mb-10">
          <div className="w-16 h-16 rounded-xl bg-zinc-800/80 backdrop-blur-md border border-zinc-700/50 flex items-center justify-center mb-5 shadow-lg">
            <svg viewBox="0 0 24 24" className="w-10 h-10">
              <path d="M6 7L10 3L18 3L18 11L14 15" fill="none" stroke="#A4D321" strokeWidth="2" strokeLinecap="round" />
              <path d="M14 9L6 9L6 17L14 17" fill="none" stroke="#A4D321" strokeWidth="2" strokeLinecap="round" />
              <path d="M10 13L10 21" fill="none" stroke="#3D4D61" strokeWidth="2" strokeLinecap="round" />
              <path d="M14 13L18 17" fill="none" stroke="#3D4D61" strokeWidth="2" strokeLinecap="round" />
            </svg>
          </div>
          <h1 className="text-white text-2xl font-bold mb-1">Ask ARA</h1>
          <p className="text-zinc-400 text-xs tracking-wide">Your Property Service Assistant</p>
        </div>

        {/* Login Form */}
        <div className="w-[280px] bg-zinc-900/80 backdrop-blur-md rounded-lg border border-zinc-800/50 p-5 mb-4 shadow-xl">
          <h2 className="text-white text-sm font-medium mb-4">Enter API Credentials</h2>

          <div className="relative mb-4">
            <input
              type={showApiKey ? "text" : "password"}
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder="OpenAI API Key"
              className="w-full bg-zinc-800 text-white text-xs border border-zinc-700 rounded-md px-3 py-2.5 pr-9 focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
            />
            <button
              className="absolute right-2.5 top-1/2 transform -translate-y-1/2 text-zinc-400"
              onClick={() => setShowApiKey(!showApiKey)}
            >
              {showApiKey ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </button>
          </div>

          <motion.button
            className="w-full bg-indigo-600 hover:bg-indigo-700 text-white text-xs font-medium py-2.5 rounded-md mb-4 flex items-center justify-center shadow-md"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleConnect}
            disabled={isLoading}
          >
            {isLoading ? (
              <div className="w-3.5 h-3.5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
            ) : null}
            Connect
          </motion.button>

          <div className="flex items-center my-3">
            <div className="flex-1 h-px bg-zinc-800"></div>
            <span className="px-2 text-zinc-500 text-[10px]">OR</span>
            <div className="flex-1 h-px bg-zinc-800"></div>
          </div>

          <motion.button
            className="w-full bg-[#A4D321] hover:bg-[#8fb81d] text-zinc-900 text-xs font-medium py-2.5 rounded-md flex items-center justify-center shadow-md"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleSSOLogin}
            disabled={isLoading}
          >
            {isLoading ? (
              <div className="w-3.5 h-3.5 border-2 border-zinc-900 border-t-transparent rounded-full animate-spin mr-2"></div>
            ) : null}
            ARA Property Services SSO
          </motion.button>
        </div>

        <p className="text-zinc-500 text-[10px] mt-3 tracking-wide">Version 1.0.0 (Development Build)</p>
      </div>

      <HomeIndicator />
    </>
  )
}
