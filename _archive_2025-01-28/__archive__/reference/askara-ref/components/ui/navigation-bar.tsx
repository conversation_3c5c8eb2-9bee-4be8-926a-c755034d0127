"use client"
import { useState, useEffect, useRef } from "react"
import { motion } from "framer-motion"
import { LogoButton } from "@/components/ui/logo-button"
import { Send, Home, Bell, Settings, CheckCircle, FileText, MoreHorizontal } from "lucide-react"

interface NavigationBarProps {
  activeScreen: string
  onNavigate: (screen: string) => void
  isRecording?: boolean
  onVoiceToggle?: () => void
}

export function NavigationBar({ activeScreen, onNavigate, isRecording = false, onVoiceToggle }: NavigationBarProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [transcription, setTranscription] = useState("")
  const [isCursorVisible, setIsCursorVisible] = useState(true)
  const navbarRef = useRef<HTMLDivElement>(null)

  // Navigation items with Lucide icons
  const navigationItems = [
    { id: "dashboard", icon: <Home className="w-4 h-4" />, label: "Home" },
    { id: "tasks", icon: <CheckCircle className="w-4 h-4" />, label: "Tasks" },
    { id: "new", icon: null, label: "New" }, // Center button is handled separately
    { id: "knowledge", icon: <FileText className="w-4 h-4" />, label: "Knowledge" },
    { id: "more", icon: <MoreHorizontal className="w-4 h-4" />, label: "More" },
  ]

  // Start cursor blinking
  useEffect(() => {
    const cursorInterval = setInterval(() => {
      setIsCursorVisible((prev) => !prev)
    }, 500)

    return () => clearInterval(cursorInterval)
  }, [])

  const handleAssistantToggle = () => {
    setIsExpanded(!isExpanded)

    if (!isExpanded) {
      setTranscription("")
    } else if (onVoiceToggle && isRecording) {
      // Stop recording if it's active when closing
      onVoiceToggle()
    }
  }

  const handleVoiceToggle = () => {
    if (onVoiceToggle) {
      onVoiceToggle()

      if (!isRecording) {
        // Simulate transcription when recording starts
        setTranscription("")
        const phrases = [
          "What's the weather like today?",
          "Tell me a joke",
          "What time is my next meeting?",
          "Remind me to call mom later",
          "How tall is Mount Everest?",
        ]

        const randomPhrase = phrases[Math.floor(Math.random() * phrases.length)]
        let currentText = ""

        const typeInterval = setInterval(() => {
          if (currentText.length < randomPhrase.length) {
            currentText += randomPhrase[currentText.length]
            setTranscription(currentText)
          } else {
            clearInterval(typeInterval)
          }
        }, 100)
      }
    }
  }

  const handleSendTranscription = () => {
    if (transcription.trim()) {
      // Here you would typically process the transcription
      console.log("Sending transcription:", transcription)
      setTranscription("")
      setIsExpanded(false)
    }
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50">
      {/* Floating Navigation Bar with Chat Interface */}
      <div
        ref={navbarRef}
        className="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-[95%] max-w-[500px] transition-all duration-300 ease-in-out"
        style={{ height: isExpanded ? "350px" : "60px" }}
      >
        <motion.div
          className="w-full h-full bg-black/40 backdrop-blur-lg rounded-2xl border border-white/10 shadow-lg overflow-hidden flex flex-col"
          initial={{ borderRadius: 20 }}
          animate={{ borderRadius: 20 }}
        >
          {/* Chat Area (visible when expanded) */}
          {isExpanded && (
            <div className="flex-1 flex flex-col overflow-hidden">
              {/* Header */}
              <div className="flex items-center p-3 border-b border-[#ffffff]/10">
                <div className="flex items-center">
                  <LogoButton size={32} onClick={handleVoiceToggle} isActive={isRecording} />
                  <div className="ml-2">
                    <p className="text-[#ffffffb2] text-xs">{isRecording ? "Listening..." : "Tap to speak"}</p>
                  </div>
                </div>
                <div className="ml-auto flex items-center gap-2">
                  <button className="p-1.5 rounded-full hover:bg-[#ffffff]/10 text-[#ffffffb2]">
                    <Bell className="h-4 w-4" />
                  </button>
                  <button className="p-1.5 rounded-full hover:bg-[#ffffff]/10 text-[#ffffffb2]">
                    <Settings className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* Response Area - Make this flex-1 to fill available space */}
              <div className="flex-1 p-3 overflow-y-auto">
                <div className="space-y-2">
                  {/* Sample response cards would go here */}
                  <div className="bg-black/30 rounded-lg p-3 border border-white/10">
                    <p className="text-white text-sm">How can I help you today?</p>
                  </div>
                </div>
              </div>

              {/* Input Area - Fixed at bottom */}
              <div className="p-3 border-t border-[#ffffff]/10">
                <div className="flex items-center space-x-2">
                  <motion.div initial={{ x: 0 }} animate={{ x: 0 }} className="flex-shrink-0">
                    <LogoButton size={32} onClick={handleVoiceToggle} isActive={isRecording} />
                  </motion.div>

                  <div className="flex-1 bg-black/30 rounded-lg border border-white/20 px-3 py-2 min-h-[36px]">
                    <p className="text-white text-sm">
                      {transcription}
                      {isCursorVisible && <span className="inline-block w-1 h-3.5 bg-[#A4D321] ml-1 animate-pulse" />}
                    </p>
                  </div>

                  <motion.button
                    className="p-2 rounded-full bg-gradient-to-br from-black to-[#A4D321] text-white shadow-sm"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={handleSendTranscription}
                  >
                    <Send className="h-4 w-4" />
                  </motion.button>
                </div>
              </div>
            </div>
          )}

          {/* Navigation Bar */}
          <div className="flex w-full h-[60px] items-center justify-between px-4">
            {navigationItems.map((item, index) => (
              <div
                key={item.id}
                className="relative flex-1 h-8 cursor-pointer flex items-center justify-center"
                onClick={() => item.id !== "new" && onNavigate(item.id)}
              >
                {index !== 2 && (
                  <div
                    className={`
                      ${
                        item.id === activeScreen
                          ? "text-[#A4D321] drop-shadow-[0_0_3px_rgba(164,211,33,0.7)]"
                          : "text-[#ffffffb2]"
                      }
                    `}
                  >
                    {item.icon}
                  </div>
                )}
              </div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Center Button - LogoButton */}
      <div className="fixed bottom-[28px] left-1/2 transform -translate-x-1/2 z-50">
        <LogoButton onClick={handleAssistantToggle} isActive={isRecording} size={50} />
      </div>

      {/* Audio Visualizer */}
      {isRecording && (
        <div className="fixed bottom-[60px] left-0 right-0 h-24 pointer-events-none z-40">
          <svg width="100%" height="100%" preserveAspectRatio="none">
            <defs>
              <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#000000" />
                <stop offset="100%" stopColor="#A4D321" />
              </linearGradient>
            </defs>
            <motion.path
              d="M0,50 Q25,30 50,50 Q75,70 100,50 Q125,30 150,50 Q175,70 200,50 Q225,30 250,50 Q275,70 300,50 Q325,30 350,50 Q375,70 400,50"
              fill="none"
              stroke="url(#gradient)"
              strokeWidth="2"
              strokeLinecap="round"
              initial={{ opacity: 0, pathLength: 0 }}
              animate={{
                opacity: 1,
                pathLength: 1,
                d: [
                  "M0,50 Q25,30 50,50 Q75,70 100,50 Q125,30 150,50 Q175,70 200,50 Q225,30 250,50 Q275,70 300,50 Q325,30 350,50 Q375,70 400,50",
                  "M0,50 Q25,40 50,50 Q75,60 100,50 Q125,40 150,50 Q175,60 200,50 Q225,40 250,50 Q275,60 300,50 Q325,40 350,50 Q375,60 400,50",
                  "M0,50 Q25,20 50,50 Q75,80 100,50 Q125,20 150,50 Q175,80 200,50 Q225,20 250,50 Q275,80 300,50 Q325,20 350,50 Q375,80 400,50",
                ],
              }}
              transition={{
                d: {
                  repeat: Number.POSITIVE_INFINITY,
                  repeatType: "reverse",
                  duration: 2,
                  ease: "easeInOut",
                },
              }}
            />
          </svg>
        </div>
      )}
    </div>
  )
}
