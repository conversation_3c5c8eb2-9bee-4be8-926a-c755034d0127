"use client"
import { useRef, useState, useEffect } from "react"
import type React from "react"

import { motion } from "framer-motion"

interface SwipeDetectorProps {
  onSwipeLeft: () => void
  onSwipeRight: () => void
  children: React.ReactNode
  threshold?: number
}

export function SwipeDetector({ onSwipeLeft, onSwipeRight, children, threshold = 100 }: SwipeDetectorProps) {
  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)
  const constraintsRef = useRef(null)

  // Reset if the touch is canceled
  const handleTouchCancel = () => {
    setTouchStart(null)
    setTouchEnd(null)
  }

  // Handle swipe detection
  useEffect(() => {
    if (!touchStart || !touchEnd) return

    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > threshold
    const isRightSwipe = distance < -threshold

    if (isLeftSwipe) {
      onSwipeLeft()
    }
    if (isRightSwipe) {
      onSwipeRight()
    }

    // Reset
    setTouchStart(null)
    setTouchEnd(null)
  }, [touchEnd, touchStart, onSwipeLeft, onSwipeRight, threshold])

  return (
    <motion.div
      ref={constraintsRef}
      className="w-full h-full"
      onTouchStart={(e) => setTouchStart(e.targetTouches[0].clientX)}
      onTouchMove={(e) => setTouchEnd(e.targetTouches[0].clientX)}
      onTouchEnd={() => setTouchEnd((prev) => prev)}
      onTouchCancel={handleTouchCancel}
    >
      {children}
    </motion.div>
  )
}
