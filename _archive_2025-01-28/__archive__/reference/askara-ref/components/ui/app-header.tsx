"use client"
import { useState } from "react"
import type React from "react"

import { Avatar } from "@/components/ui/avatar"
import { <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react"
import { HeaderDrawer } from "@/components/ui/header-drawer"

interface AppHeaderProps {
  userName?: string
  userRole?: string
  userDepartment?: string
  onSettingsClick?: () => void
  onProfileClick?: () => void
  onNotificationsClick?: () => void
  showControls?: boolean
  settingsContent?: React.ReactNode
  profileContent?: React.ReactNode
  notificationsContent?: React.ReactNode
}

export function AppHeader({
  userName = "Ask ARA",
  userRole = "Virtual Assistant",
  userDepartment = "AI Support",
  onSettingsClick,
  onProfileClick,
  onNotificationsClick,
  showControls = true,
  settingsContent,
  profileContent,
  notificationsContent,
}: AppHeaderProps) {
  const [showSettings, setShowSettings] = useState(false)
  const [showProfile, setShowProfile] = useState(false)
  const [showNotifications, setShowNotifications] = useState(false)

  const handleSettingsClick = () => {
    if (onSettingsClick) {
      onSettingsClick()
    } else {
      setShowSettings(true)
    }
  }

  const handleProfileClick = () => {
    if (onProfileClick) {
      onProfileClick()
    } else {
      setShowProfile(true)
    }
  }

  const handleNotificationsClick = () => {
    if (onNotificationsClick) {
      onNotificationsClick()
    } else {
      setShowNotifications(true)
    }
  }

  return (
    <>
      <div className="w-full bg-black/40 backdrop-blur-lg border-b border-white/10 overflow-hidden z-20 fixed top-0 left-0 right-0 px-4 py-3">
        {/* Header Content */}
        <div className="flex items-center justify-between">
          <div className="flex items-center" onClick={handleProfileClick}>
            <Avatar className="w-10 h-10 rounded-full bg-white border-2 border-white/20 mr-3 cursor-pointer">
              <svg viewBox="0 0 24 24" className="w-6 h-6 text-[#A4D321]">
                <path
                  d="M6 7L10 3L18 3L18 11L14 15"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
                <path
                  d="M14 9L6 9L6 17L14 17"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
                <path d="M10 13L10 21" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                <path d="M14 13L18 17" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
              </svg>
            </Avatar>

            <div className="flex flex-col">
              <h1 className="text-white text-base font-medium">{userName}</h1>
              <p className="text-white/60 text-xs">{userRole}</p>
            </div>
          </div>

          {showControls && (
            <div className="flex items-center space-x-3">
              <button
                className="w-9 h-9 rounded-full bg-black/30 backdrop-blur-lg border border-white/10 flex items-center justify-center shadow-sm overflow-hidden"
                onClick={handleNotificationsClick}
                style={{
                  transform: showNotifications ? "scale(1.05)" : "scale(1)",
                  transition: "transform 0.2s ease-in-out",
                }}
              >
                <Bell className="w-4 h-4 text-white" />
              </button>

              <button
                className="w-9 h-9 rounded-full bg-black/30 backdrop-blur-lg border border-white/10 flex items-center justify-center shadow-sm overflow-hidden"
                onClick={handleSettingsClick}
                style={{
                  transform: showSettings ? "scale(1.05)" : "scale(1)",
                  transition: "transform 0.2s ease-in-out",
                }}
              >
                <Settings className="w-4 h-4 text-white" />
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Spacer to push content below the header */}
      <div className="w-full h-[56px]" />

      {/* Drawers */}
      {settingsContent && (
        <HeaderDrawer isOpen={showSettings} onClose={() => setShowSettings(false)} title="Settings">
          {settingsContent}
        </HeaderDrawer>
      )}

      {profileContent && (
        <HeaderDrawer isOpen={showProfile} onClose={() => setShowProfile(false)} title="Profile">
          {profileContent}
        </HeaderDrawer>
      )}

      {notificationsContent && (
        <HeaderDrawer isOpen={showNotifications} onClose={() => setShowNotifications(false)} title="Notifications">
          {notificationsContent}
        </HeaderDrawer>
      )}
    </>
  )
}
