"use client"

import { useState, useRef, useEffect } from "react"
import { motion, AnimatePresence, type PanInfo } from "framer-motion"
import type React from "react"

interface SwipeableContainerProps {
  children: React.ReactNode[]
  initialIndex?: number
  onIndexChange?: (index: number) => void
}

export function SwipeableContainer({ children, initialIndex = 0, onIndexChange }: SwipeableContainerProps) {
  const [currentIndex, setCurrentIndex] = useState(initialIndex)
  const [direction, setDirection] = useState(0)
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (onIndexChange) {
      onIndexChange(currentIndex)
    }
  }, [currentIndex, onIndexChange])

  const handleDragEnd = (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    const threshold = 100 // minimum distance required for a swipe

    if (info.offset.x > threshold && currentIndex > 0) {
      // Swiped right
      setDirection(-1)
      setCurrentIndex(currentIndex - 1)
    } else if (info.offset.x < -threshold && currentIndex < children.length - 1) {
      // Swiped left
      setDirection(1)
      setCurrentIndex(currentIndex + 1)
    }
  }

  const variants = {
    enter: (direction: number) => ({
      x: direction > 0 ? "100%" : "-100%",
      opacity: 0,
    }),
    center: {
      x: 0,
      opacity: 1,
    },
    exit: (direction: number) => ({
      x: direction < 0 ? "100%" : "-100%",
      opacity: 0,
    }),
  }

  return (
    <div ref={containerRef} className="w-full h-full overflow-hidden">
      <AnimatePresence initial={false} custom={direction} mode="wait">
        <motion.div
          key={currentIndex}
          custom={direction}
          variants={variants}
          initial="enter"
          animate="center"
          exit="exit"
          transition={{
            x: { type: "spring", stiffness: 300, damping: 30 },
            opacity: { duration: 0.2 },
          }}
          drag="x"
          dragConstraints={{ left: 0, right: 0 }}
          dragElastic={0.7}
          onDragEnd={handleDragEnd}
          className="w-full h-full"
        >
          {children[currentIndex]}
        </motion.div>
      </AnimatePresence>
    </div>
  )
}
