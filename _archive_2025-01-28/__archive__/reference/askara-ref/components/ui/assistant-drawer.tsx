"use client"
import { motion, AnimatePresence, type PanInfo } from "framer-motion"
import { LogoButton } from "@/components/ui/logo-button"
import { useRef, useState } from "react"

interface AssistantDrawerProps {
  isOpen: boolean
  onClose: () => void
  isRecording: boolean
  onVoiceToggle: () => void
  transcription: string
}

export function AssistantDrawer({ isOpen, onClose, isRecording, onVoiceToggle, transcription }: AssistantDrawerProps) {
  const constraintsRef = useRef(null)
  const [isDragging, setIsDragging] = useState(false)

  const handleDragEnd = (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    if (info.offset.x > 100) {
      onClose()
    }
    setIsDragging(false)
  }

  return (
    <div ref={constraintsRef} className="absolute inset-0 overflow-hidden pointer-events-none">
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="absolute inset-0 bg-black/50 z-40 pointer-events-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />
        )}
      </AnimatePresence>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="absolute top-0 bottom-0 right-0 bg-black/90 backdrop-blur-md overflow-hidden z-50 flex flex-col pointer-events-auto"
            initial={{ width: 0 }}
            animate={{ width: "85%" }}
            exit={{ width: 0 }}
            transition={{ type: "spring", damping: 30, stiffness: 300 }}
            drag="x"
            dragConstraints={constraintsRef}
            dragElastic={0.2}
            dragDirectionLock
            onDragStart={() => setIsDragging(true)}
            onDragEnd={handleDragEnd}
            dragSnapToOrigin={!isDragging}
          >
            {/* Handle/Pill */}
            <div className="w-full flex justify-center pt-4 pb-4">
              <div className="w-12 h-1 bg-white/20 rounded-full" />
            </div>

            {/* Assistant Header */}
            <div className="flex items-center px-6 pb-4">
              <motion.div initial={{ x: 0 }} animate={{ x: 0 }} className="flex items-center">
                <LogoButton size={40} onClick={onVoiceToggle} isActive={isRecording} />
                <div className="ml-3">
                  <h3 className="text-white font-medium">AI Assistant</h3>
                  <p className="text-[#ffffffb2] text-sm">{isRecording ? "Listening..." : "Tap to speak"}</p>
                </div>
              </motion.div>
            </div>

            {/* Transcription Area */}
            <div className="flex-1 px-6 py-4 overflow-y-auto">
              <div className="bg-[#121624]/60 rounded-xl p-4 min-h-[100px] border border-[#ffffff]/10">
                <p className="text-white text-lg">
                  {transcription}
                  {isRecording && <span className="inline-block w-2 h-5 bg-[#A4D321] ml-1 animate-pulse" />}
                </p>
              </div>
            </div>

            {/* Input Area at Bottom */}
            <div className="px-6 py-4 border-t border-[#ffffff]/10">
              <div className="flex items-center space-x-3">
                <motion.div initial={{ x: 0 }} animate={{ x: 0 }} className="flex-shrink-0">
                  <LogoButton size={40} onClick={onVoiceToggle} isActive={isRecording} />
                </motion.div>

                <div className="flex-1 bg-[#121624]/60 rounded-xl border border-[#ffffff]/10 px-4 py-2 min-h-[50px]">
                  <p className="text-white text-lg">
                    {isRecording && <span className="inline-block w-2 h-5 bg-[#A4D321] ml-1 animate-pulse" />}
                  </p>
                </div>
              </div>
            </div>

            {/* Suggestions */}
            <div className="px-6 py-4">
              <h4 className="text-[#ffffffb2] mb-3">Suggestions</h4>
              <div className="flex flex-wrap gap-2">
                <motion.button
                  className="px-4 py-2 rounded-full bg-[#121624]/60 border border-[#ffffff]/10 text-white text-sm"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  What's the weather today?
                </motion.button>
                <motion.button
                  className="px-4 py-2 rounded-full bg-[#121624]/60 border border-[#ffffff]/10 text-white text-sm"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Set a timer for 5 minutes
                </motion.button>
                <motion.button
                  className="px-4 py-2 rounded-full bg-[#121624]/60 border border-[#ffffff]/10 text-white text-sm"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Tell me a joke
                </motion.button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
