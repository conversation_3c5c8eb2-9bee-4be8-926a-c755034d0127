"use client"

import { useState, useEffect, type ReactNode } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { X } from "lucide-react"

interface DrawerProps {
  isOpen: boolean
  onClose: () => void
  children: ReactNode
  title?: string
}

export function Drawer({ isOpen, onClose, children, title }: DrawerProps) {
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)

    // Prevent body scrolling when drawer is open
    if (isOpen) {
      document.body.style.overflow = "hidden"
    } else {
      document.body.style.overflow = ""
    }

    return () => {
      document.body.style.overflow = ""
    }
  }, [isOpen])

  if (!isMounted) return null

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          <motion.div
            className="fixed inset-0 bg-black/70 backdrop-blur-sm z-40"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />

          <motion.div
            className="fixed top-0 left-0 right-0 bottom-0 z-50 flex flex-col bg-[#121624] overflow-hidden"
            initial={{ y: "-100%" }}
            animate={{ y: 0 }}
            exit={{ y: "-100%" }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
          >
            <div className="flex items-center justify-between p-4 border-b border-[#ffffff]/10">
              {title && <h2 className="text-white text-lg font-medium">{title}</h2>}
              <motion.button
                className="w-8 h-8 rounded-full bg-[#ffffff]/10 flex items-center justify-center ml-auto"
                whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.2)" }}
                onClick={onClose}
              >
                <X className="h-4 w-4 text-white" />
              </motion.button>
            </div>

            <div className="flex-1 overflow-y-auto">{children}</div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}

export const DrawerPortal = () => null
export const DrawerOverlay = () => null
export const DrawerTrigger = () => null
export const DrawerClose = () => null
export const DrawerContent = () => null
export const DrawerHeader = () => null
export const DrawerFooter = () => null
export const DrawerTitle = () => null
export const DrawerDescription = () => null
