var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { NextResponse } from "next/server";
import { openai } from "@ai-sdk/openai";
import { streamText } from "ai";
export function POST(request) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const { message, apiKey, history = [] } = yield request.json();
            if (!message) {
                return NextResponse.json({ error: "Message is required" }, { status: 400 });
            }
            if (!apiKey) {
                return NextResponse.json({ error: "API key is required" }, { status: 400 });
            }
            // Prepare the conversation history for the AI
            const formattedHistory = history.map((msg) => ({
                role: msg.role,
                content: msg.content,
            }));
            // Add the new message to the history
            formattedHistory.push({
                role: "user",
                content: message,
            });
            // Create a stream transformer
            const stream = yield streamText({
                model: openai("gpt-4o"),
                prompt: message,
                apiKey: apiKey,
                messages: formattedHistory,
            });
            return NextResponse.json({
                text: stream.text,
                id: Date.now().toString(),
                role: "assistant",
            });
        }
        catch (error) {
            console.error("Error in chat API:", error);
            return NextResponse.json({ error: `Failed to generate response: ${error.message}` }, { status: 500 });
        }
    });
}
