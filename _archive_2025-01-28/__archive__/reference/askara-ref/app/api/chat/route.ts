import { NextResponse } from "next/server"
import { openai } from "@ai-sdk/openai"
import { streamText } from "ai"

export async function POST(request: Request) {
  try {
    const { message, apiKey, history = [] } = await request.json()

    if (!message) {
      return NextResponse.json({ error: "Message is required" }, { status: 400 })
    }

    if (!apiKey) {
      return NextResponse.json({ error: "API key is required" }, { status: 400 })
    }

    // Prepare the conversation history for the AI
    const formattedHistory = history.map((msg: any) => ({
      role: msg.role,
      content: msg.content,
    }))

    // Add the new message to the history
    formattedHistory.push({
      role: "user",
      content: message,
    })

    // Create a stream transformer
    const stream = await streamText({
      model: openai("gpt-4o"),
      prompt: message,
      apiKey: apiKey,
      messages: formattedHistory,
    })

    return NextResponse.json({
      text: stream.text,
      id: Date.now().toString(),
      role: "assistant",
    })
  } catch (error: any) {
    console.error("Error in chat API:", error)
    return NextResponse.json({ error: `Failed to generate response: ${error.message}` }, { status: 500 })
  }
}
