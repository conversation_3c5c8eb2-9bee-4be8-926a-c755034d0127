var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { NextResponse } from "next/server";
// Replace mock data with empty array
const MOCK_NOTIFICATIONS = [];
export function GET(request) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            // In a real app, you would fetch notifications from a database
            // and filter based on the user's ID from the session
            // Return empty array for now - will be populated with real data
            return NextResponse.json({ notifications: [] });
        }
        catch (error) {
            console.error("Error fetching notifications:", error);
            return NextResponse.json({ error: `Failed to fetch notifications: ${error.message}` }, { status: 500 });
        }
    });
}
export function POST(request) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const { notificationId } = yield request.json();
            if (!notificationId) {
                return NextResponse.json({ error: "Notification ID is required" }, { status: 400 });
            }
            // In a real app, you would update the notification in the database
            // Here we're just simulating a successful update
            return NextResponse.json({ success: true, message: "Notification marked as read" });
        }
        catch (error) {
            console.error("Error updating notification:", error);
            return NextResponse.json({ error: `Failed to update notification: ${error.message}` }, { status: 500 });
        }
    });
}
