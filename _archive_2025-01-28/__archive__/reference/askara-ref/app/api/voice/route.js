var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { NextResponse } from "next/server";
export function POST(request) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a;
        try {
            const formData = yield request.formData();
            const audioFile = formData.get("file");
            const apiKey = formData.get("apiKey");
            if (!audioFile) {
                return NextResponse.json({ error: "Audio file is required" }, { status: 400 });
            }
            if (!apiKey) {
                return NextResponse.json({ error: "API key is required" }, { status: 400 });
            }
            // Create a form to send to OpenAI
            const whisperFormData = new FormData();
            whisperFormData.append("file", audioFile);
            whisperFormData.append("model", "whisper-1");
            // Call OpenAI Whisper API
            const response = yield fetch("https://api.openai.com/v1/audio/transcriptions", {
                method: "POST",
                headers: {
                    Authorization: `Bearer ${apiKey}`,
                },
                body: whisperFormData,
            });
            if (!response.ok) {
                const errorData = yield response.json();
                throw new Error(((_a = errorData.error) === null || _a === void 0 ? void 0 : _a.message) || "Failed to transcribe audio");
            }
            const result = yield response.json();
            return NextResponse.json({ text: result.text });
        }
        catch (error) {
            console.error("Error in voice API:", error);
            return NextResponse.json({ error: `Failed to transcribe audio: ${error.message}` }, { status: 500 });
        }
    });
}
