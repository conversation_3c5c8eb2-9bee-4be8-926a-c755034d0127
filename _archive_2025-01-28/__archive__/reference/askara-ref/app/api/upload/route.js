var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { put } from "@vercel/blob";
import { NextResponse } from "next/server";
export const config = {
    api: {
        bodyParser: false,
        responseLimit: "50mb",
    },
};
export function POST(request) {
    return __awaiter(this, void 0, void 0, function* () {
        const { searchParams } = new URL(request.url);
        const filename = searchParams.get("filename");
        if (!filename) {
            return NextResponse.json({ error: "Filename is required" }, { status: 400 });
        }
        const blobToken = process.env.BLOB_READ_WRITE_TOKEN;
        if (!blobToken) {
            return NextResponse.json({ error: "BLOB_READ_WRITE_TOKEN is not configured" }, { status: 500 });
        }
        try {
            const blob = yield request.blob();
            const { url } = yield put(filename, blob, {
                access: "public",
                token: blobToken,
                addRandomSuffix: true,
            });
            return NextResponse.json({ url });
        }
        catch (error) {
            console.error("Error uploading file:", error);
            return NextResponse.json({ error: "Failed to upload file: " + error.message }, { status: 500 });
        }
    });
}
