import Link from "next/link"
import { StatusBar } from "@/components/ui/status-bar"
import { HomeIndicator } from "@/components/ui/home-indicator"

export default function OfflinePage() {
  return (
    <div className="h-screen flex flex-col items-center justify-center p-6 bg-gradient-to-b from-black to-zinc-900">
      <StatusBar />

      <div className="flex flex-col items-center justify-center flex-1 w-full max-w-md mx-auto">
        <div className="w-20 h-20 rounded-2xl bg-zinc-900 flex items-center justify-center mb-6 shadow-lg">
          <svg viewBox="0 0 24 24" className="w-12 h-12">
            <path d="M6 7L10 3L18 3L18 11L14 15" fill="none" stroke="#A4D321" strokeWidth="2" strokeLinecap="round" />
            <path d="M14 9L6 9L6 17L14 17" fill="none" stroke="#A4D321" strokeWidth="2" strokeLinecap="round" />
            <path d="M10 13L10 21" fill="none" stroke="#3D4D61" strokeWidth="2" strokeLinecap="round" />
            <path d="M14 13L18 17" fill="none" stroke="#3D4D61" strokeWidth="2" strokeLinecap="round" />
          </svg>
        </div>

        <h1 className="text-white text-2xl font-bold mb-4 text-center">You're offline</h1>
        <p className="text-zinc-400 text-center mb-8 max-w-xs">
          It looks like you've lost your internet connection. Check your connection and try again.
        </p>

        <div className="space-y-4 w-full">
          <Link
            href="/"
            className="block w-full py-3 bg-gradient-to-r from-[#3D4D61] to-[#A4D321] text-white font-medium rounded-md text-center"
          >
            Try Again
          </Link>

          <button className="block w-full py-3 bg-zinc-800 text-white font-medium rounded-md">View Cached Data</button>
        </div>

        <div className="mt-8 text-zinc-500 text-sm">
          <p>Some features may be limited while offline</p>
        </div>
      </div>

      <HomeIndicator />
    </div>
  )
}
