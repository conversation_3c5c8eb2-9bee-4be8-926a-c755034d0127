import Link from "next/link"
import { StatusBar } from "@/components/ui/status-bar"
import { HomeIndicator } from "@/components/ui/home-indicator"

export default function NotFound() {
  return (
    <div className="h-screen flex flex-col items-center justify-center p-6 bg-gradient-to-b from-black to-zinc-900">
      <StatusBar />

      <div className="flex flex-col items-center justify-center flex-1 w-full max-w-md mx-auto">
        <div className="w-20 h-20 rounded-2xl bg-zinc-900 flex items-center justify-center mb-6 shadow-lg">
          <svg viewBox="0 0 24 24" className="w-12 h-12">
            <path d="M9 9L15 15" stroke="#A4D321" strokeWidth="2" strokeLinecap="round" />
            <path d="M15 9L9 15" stroke="#A4D321" strokeWidth="2" strokeLinecap="round" />
            <circle cx="12" cy="12" r="9" stroke="#3D4D61" strokeWidth="2" fill="none" />
          </svg>
        </div>

        <h1 className="text-white text-2xl font-bold mb-4 text-center">Page Not Found</h1>
        <p className="text-zinc-400 text-center mb-8 max-w-xs">
          The page you're looking for doesn't exist or has been moved.
        </p>

        <Link
          href="/"
          className="py-3 px-8 bg-gradient-to-r from-[#3D4D61] to-[#A4D321] text-white font-medium rounded-md"
        >
          Go Home
        </Link>
      </div>

      <HomeIndicator />
    </div>
  )
}
