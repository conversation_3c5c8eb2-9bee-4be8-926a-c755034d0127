type FetchOptions = {
  method?: "GET" | "POST" | "PUT" | "DELETE"
  headers?: Record<string, string>
  body?: any
  timeout?: number
}

export async function fetchWithTimeout<T>(url: string, options: FetchOptions = {}) {
  const { timeout = 8000, ...fetchOptions } = options

  // Add default headers
  const headers = {
    "Content-Type": "application/json",
    ...options.headers,
  }

  // Prepare body if it's an object
  const body = options.body && typeof options.body === "object" ? JSON.stringify(options.body) : options.body

  // Create abort controller for timeout
  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), timeout)

  try {
    const response = await fetch(url, {
      method: options.method || "GET",
      headers,
      body,
      signal: controller.signal,
      ...fetchOptions,
    })

    clearTimeout(timeoutId)

    // Handle non-2xx responses
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || `Request failed with status ${response.status}`)
    }

    // Parse JSON response
    const data = await response.json()
    return data
  } catch (error: any) {
    clearTimeout(timeoutId)

    if (error.name === "AbortError") {
      throw new Error("Request timeout. Please try again.")
    }

    throw error
  }
}

export const api = {
  get: <T>(url: string, options: Omit<FetchOptions, 'method' | 'body'> = {}) => 
    fetchWithTimeout<T>(url, options),
    
  post: <T>(url: string, data: any, options: Omit<FetchOptions, 'method'> = {}) => 
    fetchWithTimeout<T>(url, { ...options, method: 'POST', body: data }),
    
  put: <T>(url: string, data: any, options: Omit<FetchOptions, 'method'> = {}) => 
    fetchWithTimeout<T>(url, { ...options, method: 'PUT', body: data }),
    
  delete: <T>(url: string, options: Omit<FetchOptions, 'method' | 'body'> = {}) => 
    fetchWithTimeout<T>(url, { ...options, method: 'DELETE' }),
};
