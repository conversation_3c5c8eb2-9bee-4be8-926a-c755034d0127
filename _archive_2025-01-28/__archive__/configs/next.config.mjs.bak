/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  webpack: (config) => {
    // Fix for WebpackError constructor issue
    config.optimization = {
      ...config.optimization,
      minimize: false // Disable minification to avoid the minify-webpack-plugin error
    };
    
    return config;
  },
  // Disable experimental features that might cause issues
  experimental: {
    webpackBuildWorker: false,
    parallelServerBuildTraces: false,
    parallelServerCompiles: false,
  },
  // Add SWC options
  compiler: {
    styledComponents: true
  }
}

export default nextConfig
