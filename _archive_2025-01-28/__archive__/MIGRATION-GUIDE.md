# Step-by-Step Database Migration Guide

This guide provides detailed instructions for migrating the database to Drizzle ORM with Neon PostgreSQL.

## Prerequisites

- Node.js and npm installed
- Access to a Neon PostgreSQL database
- DATABASE_URL environment variable set in `.env.local` or `.env`

## Step 1: Install Required Dependencies

```bash
# Install Drizzle ORM
npm install drizzle-orm

# Install Drizzle Kit as a dev dependency
npm install -D drizzle-kit
```

## Step 2: Update package.json Scripts

Add the following scripts to your package.json:

```json
"scripts": {
  "db:push": "drizzle-kit push:pg",
  "db:studio": "drizzle-kit studio",
  "db:generate": "drizzle-kit generate:pg"
}
```

## Step 3: Verify Database Connection

Before proceeding with the migration, verify that you can connect to your Neon database:

```bash
# Create a test script
cat > test-connection.js << 'EOF'
import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function testConnection() {
  console.log('Testing database connection...');
  
  const databaseUrl = process.env.DATABASE_URL;
  
  if (!databaseUrl) {
    console.error('DATABASE_URL not found in environment variables');
    return;
  }
  
  try {
    const sql = neon(databaseUrl);
    const result = await sql('SELECT 1 as test');
    
    if (result && result[0] && result[0].test === 1) {
      console.log('Database connection successful');
      
      // List tables in the database
      const tables = await sql(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
      `);
      
      console.log('Tables in the database:');
      tables.forEach(table => {
        console.log(`- ${table.table_name}`);
      });
    } else {
      console.error('Database connection failed: Unexpected response');
    }
  } catch (error) {
    console.error('Database connection failed:', error);
  }
}

testConnection();
EOF

# Run the test script
node test-connection.js
```

## Step 4: Create Drizzle Configuration

Create a `drizzle.config.ts` file in the root of your project:

```typescript
import { defineConfig } from 'drizzle-kit';
import { loadEnvConfig } from '@next/env';

// Load environment variables
loadEnvConfig(process.cwd());

if (!process.env.DATABASE_URL) {
  throw new Error('DATABASE_URL must be a Neon postgres connection string');
}

export default defineConfig({
  dialect: 'postgresql',
  dbCredentials: {
    url: process.env.DATABASE_URL,
  },
  schema: './app/db/schema.ts',
  out: './drizzle',
});
```

## Step 5: Generate Migrations

Generate migration files based on your schema:

```bash
npm run db:generate
```

This will create migration files in the `drizzle` directory.

## Step 6: Push Schema to Database

Push the schema to your Neon database:

```bash
npm run db:push
```

## Step 7: Verify Schema

Verify that the schema was successfully pushed to the database:

```bash
# Create a verification script
cat > verify-schema.js << 'EOF'
import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Expected tables based on schema.ts
const expectedTables = [
  'users',
  'messages',
  'contacts',
  'chat_sessions',
  'inspection_reports',
  'inspection_actions'
];

async function verifySchema() {
  console.log('Verifying database schema...');
  
  const databaseUrl = process.env.DATABASE_URL;
  
  if (!databaseUrl) {
    console.error('DATABASE_URL not found in environment variables');
    return;
  }
  
  try {
    const sql = neon(databaseUrl);
    
    // Get all tables
    const tables = await sql(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `);
    
    const tableNames = tables.map(t => t.table_name);
    
    console.log('Verifying tables:');
    
    for (const expectedTable of expectedTables) {
      if (tableNames.includes(expectedTable)) {
        console.log(`✅ Table '${expectedTable}' exists`);
      } else {
        console.error(`❌ Table '${expectedTable}' is missing`);
      }
    }
  } catch (error) {
    console.error('Database error:', error);
  }
}

verifySchema();
EOF

# Run the verification script
node verify-schema.js
```

## Step 8: Test Drizzle ORM

Test the Drizzle ORM functionality:

```bash
# Create a test script
cat > test-drizzle.js << 'EOF'
import { neon } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-http';
import { eq } from 'drizzle-orm';
import dotenv from 'dotenv';
import crypto from 'crypto';

// Load environment variables
dotenv.config();

// Import schema
import * as schema from './app/db/schema.js';

async function testDrizzle() {
  console.log('Testing Drizzle ORM functionality...');
  
  const databaseUrl = process.env.DATABASE_URL;
  
  if (!databaseUrl) {
    console.error('DATABASE_URL not found in environment variables');
    return;
  }
  
  try {
    // Initialize Neon and Drizzle
    const sql = neon(databaseUrl);
    const db = drizzle(sql, { schema });
    
    // Test user operations
    console.log('Testing user operations:');
    
    // 1. Create a test user
    const testUser = {
      id: crypto.randomUUID(),
      name: 'Test User',
      role: 'Tester',
      department: 'QA',
      email: `test-${Date.now()}@example.com`,
      password: 'test-password',
      preferences: { theme: 'dark', notifications: true }
    };
    
    console.log('Creating test user...');
    const insertResult = await db.insert(schema.users).values(testUser).returning();
    
    if (insertResult && insertResult.length > 0) {
      console.log('User created successfully:', insertResult[0].email);
      
      // 2. Find the user by email
      console.log('Finding user by email...');
      const foundUser = await db.select().from(schema.users).where(eq(schema.users.email, testUser.email)).limit(1);
      
      if (foundUser && foundUser.length > 0) {
        console.log('User found by email:', foundUser[0].email);
        
        // 3. Delete the test user
        console.log('Deleting test user...');
        const deleteResult = await db.delete(schema.users).where(eq(schema.users.id, testUser.id)).returning();
        
        if (deleteResult && deleteResult.length > 0) {
          console.log('User deleted successfully');
        } else {
          console.error('Failed to delete user');
        }
      } else {
        console.error('Failed to find user by email');
      }
    } else {
      console.error('Failed to create user');
    }
  } catch (error) {
    console.error('Drizzle test error:', error);
  }
}

testDrizzle();
EOF

# Run the test script
node test-drizzle.js
```

## Step 9: Use Drizzle Studio (Optional)

Open Drizzle Studio to view and manage your database:

```bash
npm run db:studio
```

## Step 10: Update Application Code

Update your application code to use Drizzle ORM instead of raw SQL queries. Here's an example of how to use Drizzle in your application:

```typescript
// Import the Drizzle client
import { db } from '@/app/db';
import { users } from '@/app/db/schema';
import { eq } from 'drizzle-orm';

// Example: Find a user by email
const user = await db.query.users.findFirst({
  where: eq(users.email, '<EMAIL>'),
});

// Example: Create a new user
const newUser = await db.insert(users).values({
  id: crypto.randomUUID(),
  name: 'New User',
  role: 'User',
  department: 'General',
  email: '<EMAIL>',
  password: 'password123',
}).returning();

// Example: Update a user
const updatedUser = await db.update(users)
  .set({ role: 'Admin' })
  .where(eq(users.id, userId))
  .returning();

// Example: Delete a user
const deletedUser = await db.delete(users)
  .where(eq(users.id, userId))
  .returning();
```

## Troubleshooting

If you encounter issues during the migration:

1. **Database Connection Issues**:
   - Verify that your DATABASE_URL is correct
   - Check that your Neon database is running and accessible
   - Ensure your IP is allowed to access the database

2. **Schema Push Failures**:
   - Check for syntax errors in your schema.ts file
   - Ensure you have the necessary permissions to create and modify tables
   - Look for conflicts with existing tables

3. **Drizzle ORM Errors**:
   - Make sure you're importing the schema correctly
   - Check for type mismatches between your schema and data
   - Verify that you're using the correct Drizzle query syntax

## Additional Resources

- [Drizzle ORM Documentation](https://orm.drizzle.team/docs/overview)
- [Neon PostgreSQL Documentation](https://neon.tech/docs/introduction)
