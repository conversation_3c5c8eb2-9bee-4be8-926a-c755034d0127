# ARA Property Services App: Deployment Instructions

This document provides comprehensive instructions for deploying the ARA Property Services App to Vercel. It covers all the necessary steps, from environment configuration to database migrations and post-deployment validation.

## Prerequisites

Before beginning the deployment process, ensure you have the following:

1. Access to the ARA Property Services GitHub repository
2. Vercel account with appropriate permissions
3. Neon PostgreSQL database credentials
4. Clerk account with organization features enabled
5. Node.js and pnpm installed locally

## Deployment Steps

### 1. Environment Configuration

The following environment variables must be configured in Vercel:

```
# Database Connection
DATABASE_URL=postgres://neondb_owner:password@hostname:port/neondb?sslmode=require

# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...
CLERK_SECRET_KEY=sk_test_...
CLERK_WEBHOOK_SECRET=whsec_...
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/
```

You can use the script to generate the Vercel CLI commands for setting these variables:

```bash
node scripts/generate-vercel-env.js
```

### 2. Deployment Process

#### Option 1: Vercel CLI

```bash
# Login to Vercel
vercel login

# Deploy to production
vercel --prod
```

#### Option 2: GitHub Integration

1. Go to the Vercel dashboard: https://vercel.com/dashboard
2. Click on "Add New" and select "Project"
3. Import the GitHub repository: askara-prod-final
4. Select the "deployment" branch
5. Configure the project with these settings:
   - Framework Preset: Next.js
   - Build Command: pnpm build
   - Install Command: pnpm install --no-frozen-lockfile
   - Output Directory: .next
6. Configure the Environment Variables as listed above
7. Click "Deploy"

### 3. Post-Deployment Tasks

After the deployment completes, run the post-deployment script to ensure all database migrations are applied:

```bash
node scripts/post-deploy.js
```

This script will:
1. Verify the database connection
2. Check if the organizations table exists
3. Run the organization migration if needed
4. Verify that the migration was successful

### 4. Deployment Validation

Run the deployment validation script to ensure everything is properly configured:

```bash
npm run validate:deployment
```

If there are any issues, check the validation output for specific errors and fix them as needed.

### 5. Manual Verification

After deploying, manually verify:

1. The application loads correctly at the production URL
2. Authentication works (sign-in and sign-up)
3. Organization features work (organization switcher, member management)
4. API endpoints are accessible and return expected responses
5. Clerk webhooks are firing correctly

## Troubleshooting

### Deployment Errors

If the deployment fails, check:

1. **Build Errors**: Check the Vercel build logs for details
2. **Lock File Issues**: Try running `pnpm install --no-frozen-lockfile` locally and commit the updated lock file
3. **Environment Variables**: Ensure all required variables are set in Vercel

### Database Issues

If database migrations fail:

1. **Connection Issues**: Verify the `DATABASE_URL` is correct
2. **Migration Errors**: Check the post-deployment script output
3. **Manual Migration**: Connect to the database and run the migrations manually:

```sql
-- Create organizations table
CREATE TABLE IF NOT EXISTS "organizations" (
  "id" TEXT PRIMARY KEY,
  "name" TEXT NOT NULL,
  "slug" TEXT NOT NULL UNIQUE,
  "image_url" TEXT,
  -- ... other fields
);

-- ... other migration steps
```

### Clerk Integration Issues

If Clerk integration fails:

1. **Environment Variables**: Ensure Clerk variables are properly set
2. **Organization Features**: Verify that organization features are enabled in the Clerk dashboard
3. **Webhooks**: Check that webhooks are properly configured in Clerk

## Production URLs

- **Main Production URL**: https://askara-prod-final.vercel.app/
- **Latest Deployment**: https://ask-ara-prod-final-h7d9m71ys-alias-labs.vercel.app

## Contact Information

For deployment issues, contact:
- Dan Humphreys (<EMAIL>) - Project Owner
- Technical Support: <EMAIL>
