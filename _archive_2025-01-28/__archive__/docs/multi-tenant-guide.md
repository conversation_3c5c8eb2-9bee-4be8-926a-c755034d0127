# Multi-Tenant Architecture Guide

## Overview

The ARA Property Services App implements a comprehensive multi-tenant architecture using Clerk for authentication and organization management. This guide provides detailed information about how the multi-tenant features work, how to manage organizations, and how the data isolation between organizations is implemented.

## Table of Contents

1. [Multi-Tenant Architecture](#multi-tenant-architecture)
2. [Organization Management](#organization-management)
3. [Data Isolation](#data-isolation)
4. [User Roles and Permissions](#user-roles-and-permissions)
5. [Organization Switching](#organization-switching)
6. [Webhooks and Data Synchronization](#webhooks-and-data-synchronization)
7. [Frontend Components](#frontend-components)
8. [API Endpoints](#api-endpoints)
9. [Middleware and Route Protection](#middleware-and-route-protection)
10. [Troubleshooting](#troubleshooting)

## Multi-Tenant Architecture

### Architecture Overview

The multi-tenant architecture allows each organization to have its own isolated data while sharing the same application infrastructure. Key components of the architecture include:

- **Clerk Authentication**: Handles user authentication and organization management
- **Organization-Scoped Data**: All data is associated with specific organizations
- **Role-Based Access Control**: Users have different roles and permissions within organizations
- **Webhook Integration**: Keeps the application database in sync with Clerk's authentication data

### Key Benefits

- **Data Isolation**: Each organization's data is securely isolated
- **Resource Efficiency**: Single application instance serves multiple organizations
- **Centralized Management**: Easy administration of multiple organizations
- **Scalability**: Supports growing number of organizations without infrastructure changes

## Organization Management

### Creating Organizations

Organizations can be created in two ways:

1. **Through Clerk Dashboard**:
   - Log in to the Clerk Dashboard
   - Navigate to "Organizations"
   - Click "Create Organization"
   - Fill in organization details (name, slug, etc.)
   - Organization will be synchronized to the application via webhooks

2. **Through the Application**:
   - Log in to the application
   - Click "Create Organization" in the user dropdown
   - Fill in organization details
   - Submit the form to create the organization

### Managing Organization Members

Organization administrators can manage members through these steps:

1. Navigate to "Organization Settings" > "Members"
2. From here, administrators can:
   - View all current members and their roles
   - Invite new members via email
   - Change member roles (Admin, Member)
   - Remove members from the organization

### Organization Settings

The organization settings page allows administrators to:

- Update organization name and profile image
- Manage custom organization metadata
- Configure organization-specific settings
- View organization activity logs

## Data Isolation

### How Data Isolation Works

Data isolation ensures that each organization can only access its own data:

1. **Database Structure**:
   - The `organizations` table stores organization information
   - The `organization_members` table maps users to organizations with roles
   - Tables like `properties` and `contract_specifications` have an `organization_id` foreign key

2. **Query Filtering**:
   - All database queries include an organization filter
   - Example: `WHERE organization_id = current_organization_id`

3. **API Protection**:
   - All API endpoints verify organization membership
   - Data is filtered by organization ID before being returned

### Organization Context

The application maintains the current organization context:

```typescript
// Example organization context
export function useOrganization() {
  const { organization } = useClerk();
  const [orgData, setOrgData] = useState(null);
  
  useEffect(() => {
    // Load organization data from database based on Clerk organization
    if (organization) {
      fetchOrganizationData(organization.id).then(setOrgData);
    }
  }, [organization]);
  
  return {
    currentOrganization: orgData,
    isLoaded: !!orgData,
    isAdmin: orgData?.role === 'admin'
  };
}
```

## User Roles and Permissions

### Available Roles

The application supports the following roles within organizations:

- **Admin**: Full access to all organization settings and data
- **Member**: Standard access to organization data with limited settings control

### Permission Matrix

| Permission | Admin | Member |
|------------|-------|--------|
| View Properties | ✅ | ✅ |
| Create Properties | ✅ | ✅ |
| Delete Properties | ✅ | ❌ |
| Invite Members | ✅ | ❌ |
| Manage Members | ✅ | ❌ |
| Update Organization Settings | ✅ | ❌ |
| Delete Organization | ✅ | ❌ |

### Permission Enforcement

Permissions are enforced at multiple levels:

1. **UI Level**: Components are conditionally rendered based on user role
2. **API Level**: Endpoints check user permissions before processing requests
3. **Database Level**: Queries filter data based on organization membership

## Organization Switching

### How Organization Switching Works

Users can belong to multiple organizations and switch between them:

1. The organization switcher component is available in the app header
2. When a user switches organizations:
   - The Clerk active organization is updated
   - The application loads the new organization context
   - All data is refreshed to show only the current organization's data

### Implementation Details

```typescript
// Example organization switching function
async function switchOrganization(organizationId) {
  try {
    await clerk.setActive({ organization: organizationId });
    // Reload data for the new organization
    router.refresh();
    return true;
  } catch (error) {
    console.error('Error switching organization:', error);
    return false;
  }
}
```

## Webhooks and Data Synchronization

### Webhook Setup

The application uses Clerk webhooks to keep organization data in sync:

1. **Webhook Endpoint**: `/api/webhooks/clerk`
2. **Required Events**:
   - `user.created`, `user.updated`, `user.deleted`
   - `organization.created`, `organization.updated`, `organization.deleted`
   - `organizationMembership.created`, `organizationMembership.updated`, `organizationMembership.deleted`
   - `organizationInvitation.created`, `organizationInvitation.accepted`

### Data Synchronization Process

When webhook events are received:

1. The application verifies the webhook signature
2. It extracts the event data
3. Based on the event type, it updates the local database
4. This ensures the application database is always in sync with Clerk

### Example Webhook Handler

```typescript
// Example webhook handler for organization.created
async function handleOrganizationCreated(data) {
  const { id, name, slug, created_at } = data;
  
  await db.insert(organizations).values({
    id,
    name,
    slug,
    created_at: new Date(created_at),
  });
  
  console.log(`Organization ${name} (${id}) created`);
}
```

## Frontend Components

### OrganizationSwitcher

The organization switcher component allows users to switch between organizations:

```typescript
// Example OrganizationSwitcher implementation
import { OrganizationSwitcher } from '@clerk/nextjs';

export function AppHeader() {
  return (
    <header className="flex items-center justify-between p-4 border-b">
      <div className="flex items-center gap-4">
        <Logo />
        <nav>{/* Navigation links */}</nav>
      </div>
      
      <div className="flex items-center gap-4">
        <OrganizationSwitcher 
          appearance={{
            elements: {
              rootBox: 'relative',
              organizationSwitcherTrigger: 'flex items-center gap-2'
            }
          }}
        />
        <UserButton />
      </div>
    </header>
  );
}
```

### OrganizationSettings

The organization settings component provides an interface for managing organization details:

```typescript
// Example OrganizationSettings implementation
export function OrganizationSettings() {
  const { organization } = useOrganization();
  const [name, setName] = useState(organization?.name || '');
  
  const updateOrganization = async () => {
    await fetch(`/api/organizations/${organization.id}`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ name })
    });
  };
  
  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Organization Settings</h1>
      <form onSubmit={updateOrganization}>
        <div className="mb-4">
          <label>Organization Name</label>
          <input 
            type="text" 
            value={name} 
            onChange={(e) => setName(e.target.value)} 
            className="block w-full p-2 border rounded"
          />
        </div>
        <button type="submit" className="px-4 py-2 bg-blue-500 text-white rounded">
          Save Changes
        </button>
      </form>
    </div>
  );
}
```

## API Endpoints

### Organization Management APIs

The application provides the following API endpoints for organization management:

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/organizations` | GET | List organizations the user belongs to |
| `/api/organizations` | POST | Create a new organization |
| `/api/organizations/[id]` | GET | Get organization details |
| `/api/organizations/[id]` | PATCH | Update organization details |
| `/api/organizations/[id]` | DELETE | Delete an organization |
| `/api/organizations/[id]/members` | GET | List organization members |
| `/api/organizations/[id]/members` | POST | Add a member to the organization |
| `/api/organizations/[id]/members/[userId]` | PATCH | Update member role |
| `/api/organizations/[id]/members/[userId]` | DELETE | Remove a member from the organization |

### Example API Request

```typescript
// Example API request to add a member to an organization
async function inviteUserToOrganization(organizationId, email, role) {
  try {
    const response = await fetch(`/api/organizations/${organizationId}/members`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ email, role })
    });
    
    if (!response.ok) {
      throw new Error('Failed to invite user');
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error inviting user:', error);
    throw error;
  }
}
```

## Middleware and Route Protection

### Middleware Implementation

The application uses Next.js middleware to protect routes and enforce organization membership:

```typescript
// middleware.ts
import { authMiddleware, clerkClient, getAuth } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';

export default authMiddleware({
  publicRoutes: [
    '/',
    '/sign-in(.*)',
    '/sign-up(.*)',
    '/api/webhooks(.*)'
  ],
  async afterAuth(auth, req, evt) {
    // Handle authenticated requests
    if (auth.isPublicRoute) {
      return NextResponse.next();
    }
    
    // Redirect to sign in if not authenticated
    if (!auth.userId) {
      return NextResponse.redirect(new URL('/sign-in', req.url));
    }
    
    // Check organization routes
    if (req.url.includes('/organizations/')) {
      const url = new URL(req.url);
      const parts = url.pathname.split('/');
      const orgSlug = parts[2];
      
      // Find organization by slug
      try {
        const orgs = await clerkClient.users.getOrganizationMembershipList({
          userId: auth.userId
        });
        
        const matchingOrg = orgs.find(org => org.organization.slug === orgSlug);
        
        if (!matchingOrg) {
          // User doesn't belong to this organization
          return NextResponse.redirect(new URL('/', req.url));
        }
      } catch (error) {
        console.error('Error checking organization membership:', error);
        return NextResponse.redirect(new URL('/', req.url));
      }
    }
    
    return NextResponse.next();
  }
});

export const config = {
  matcher: ['/((?!.*\\..*|_next).*)', '/', '/(api|trpc)(.*)'],
};
```

### Route Protection Strategy

The application protects routes at multiple levels:

1. **Public vs. Private Routes**: Basic authentication check in middleware
2. **Organization-Specific Routes**: Verifies organization membership
3. **Role-Specific Access**: Checks user role for sensitive operations

## Troubleshooting

### Common Issues

#### Organization Not Found

**Symptom**: User receives "Organization not found" error when accessing an organization.

**Possible Causes**:
- User is not a member of the organization
- Organization does not exist
- Webhook failed to synchronize organization data

**Solutions**:
1. Check if the user is a member of the organization in Clerk dashboard
2. Verify the organization exists in the database
3. Check webhook logs for any synchronization issues

#### Permission Denied

**Symptom**: User receives "Permission denied" when trying to perform an action.

**Possible Causes**:
- User does not have the required role
- Middleware is blocking access
- Organization membership is not properly synced

**Solutions**:
1. Check user's role in the organization
2. Verify middleware is correctly checking permissions
3. Check that organization membership is properly synchronized

#### Data from Other Organizations Visible

**Symptom**: User can see data from organizations they shouldn't have access to.

**Possible Causes**:
- Missing organization filter in queries
- Organization context is not properly set
- Bug in data isolation implementation

**Solutions**:
1. Check all database queries include organization filters
2. Verify organization context is correctly set when switching organizations
3. Review data isolation implementation for bugs

### Logging and Debugging

To debug multi-tenant issues:

1. **Check Server Logs**: Look for errors in Next.js server logs
2. **Check Webhook Logs**: Review webhook processing logs
3. **Database Inspection**: Verify organization relationships in the database
4. **Organization Context**: Check that the current organization context is correctly set

## Conclusion

The multi-tenant architecture provides a robust foundation for serving multiple organizations within a single application instance. By following the guidelines in this document, you can effectively manage organizations, ensure proper data isolation, and implement secure role-based access control.

For any further questions or issues, please contact the development team.
