# MCP Server Production Deployment Guide

This document outlines the recommended approach for deploying the Neon MCP server in a production environment. The MCP server is a critical component of the ARA Property Services app, providing database access and serving as a communication channel between the frontend and the Neon PostgreSQL database.

## Current Implementation

The current MCP server implementation (`neon-mcp-server.js`) provides basic functionality for:

1. Establishing a connection to the Neon PostgreSQL database
2. Exposing database operations through a Server-Sent Events (SSE) endpoint
3. Handling tool calls for operations like listing tables, running queries, and managing messages

While this implementation works well for development, it requires enhancements for production deployment.

## Production Enhancements

### 1. Connection Pooling

The current implementation creates a new database connection for each query. This approach is inefficient for production environments with higher traffic. Implementing connection pooling will:

- Reduce connection overhead
- Better handle concurrent requests
- Improve query performance

**Implementation:**

```javascript
import pg from 'pg';

// Create connection pool
const pool = new pg.Pool({
  connectionString: process.env.DATABASE_URL,
  min: 2,          // Minimum number of connections
  max: 10,         // Maximum number of connections
  idleTimeoutMillis: 30000  // How long a connection can be idle before being closed
});

// Use pool for queries
async function runQuery(query, params = []) {
  const client = await pool.connect();
  try {
    return await client.query(query, params);
  } finally {
    client.release();  // Return connection to pool
  }
}

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('SIGTERM signal received: closing pool');
  await pool.end();
  process.exit(0);
});
```

### 2. Error Handling with Retry Mechanism

The current implementation lacks robust error handling and retry mechanisms. Enhancing error handling will:

- Automatically recover from transient database errors
- Provide clearer error messages to clients
- Log errors for monitoring and debugging

**Implementation:**

```javascript
async function executeWithRetry(operation, maxRetries = 3, delay = 1000) {
  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      // Check if error is retryable
      if (!isRetryableError(error) || attempt === maxRetries) {
        break;
      }
      
      console.warn(`Attempt ${attempt} failed, retrying in ${delay}ms: ${error.message}`);
      await new Promise(resolve => setTimeout(resolve, delay));
      
      // Exponential backoff
      delay *= 2;
    }
  }
  
  throw lastError;
}

// Determine if error is retryable
function isRetryableError(error) {
  // PostgreSQL error codes that are typically transient
  const retryableCodes = [
    '08000', // Connection exception
    '08006', // Connection failure
    '08001', // SQL client unable to establish SQL connection
    '08004', // SQL server rejected SQL connection
    '57P01', // Admin shutdown
    '57P02', // Crash shutdown
    '57P03', // Cannot connect now
    '40001', // Serialization failure
    '40P01'  // Deadlock detected
  ];
  
  return error.code && retryableCodes.includes(error.code);
}
```

### 3. Structured Logging

Implementing structured logging will improve monitoring and debugging capabilities:

**Implementation:**

```javascript
import winston from 'winston';

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'neon-mcp-server' },
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ 
      filename: 'logs/neon-mcp-error.log', 
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5
    }),
    new winston.transports.File({ 
      filename: 'logs/neon-mcp-combined.log',
      maxsize: 5242880, // 5MB
      maxFiles: 5
    })
  ]
});

// Use logging
logger.info('Server started', { port: PORT });
logger.error('Database connection failed', { error: err.message, code: err.code });
```

### 4. Health Check Endpoint

Adding a health check endpoint allows monitoring systems to verify that the MCP server is running:

**Implementation:**

```javascript
// Add to server routes
if (pathname === '/health') {
  res.writeHead(200, { 'Content-Type': 'application/json' });
  
  try {
    // Test database connection
    const result = await sql`SELECT 1`;
    
    res.end(JSON.stringify({
      status: 'ok',
      timestamp: new Date().toISOString(),
      database: 'connected',
      uptime: process.uptime()
    }));
  } catch (error) {
    res.writeHead(500, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      status: 'error',
      timestamp: new Date().toISOString(),
      database: 'disconnected',
      error: error.message
    }));
  }
  
  return;
}
```

### 5. Authentication and Security

The current server lacks authentication, which is required for production:

**Implementation:**

```javascript
// Middleware function to check authentication
function authenticateRequest(req, res) {
  const apiKey = req.headers['x-api-key'];
  
  if (!apiKey || apiKey !== process.env.MCP_API_KEY) {
    res.writeHead(401, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ error: 'Unauthorized' }));
    return false;
  }
  
  return true;
}

// Apply to routes
if (pathname === '/sse') {
  if (!authenticateRequest(req, res)) {
    return;
  }
  
  // Existing SSE handler
}
```

### 6. Rate Limiting

Implementing rate limiting will protect the server from abuse:

**Implementation:**

```javascript
// Simple in-memory rate limiter
const rateLimiter = {
  clients: new Map(),
  windowMs: 60000, // 1 minute
  maxRequests: 100, // 100 requests per minute
  
  check(clientId) {
    const now = Date.now();
    const clientData = this.clients.get(clientId) || { count: 0, resetTime: now + this.windowMs };
    
    // Reset if window has passed
    if (now > clientData.resetTime) {
      clientData.count = 1;
      clientData.resetTime = now + this.windowMs;
      this.clients.set(clientId, clientData);
      return true;
    }
    
    // Increment and check
    clientData.count++;
    this.clients.set(clientId, clientData);
    
    return clientData.count <= this.maxRequests;
  }
};

// Use rate limiter
const clientId = req.headers['x-forwarded-for'] || req.socket.remoteAddress;
if (!rateLimiter.check(clientId)) {
  res.writeHead(429, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify({ error: 'Too many requests' }));
  return;
}
```

## Deployment Configuration

### Environment Variables

Production deployment should use these environment variables:

```
# Database Connection
DATABASE_URL=****************************************************************

# Server Configuration
MCP_PORT=3005
MCP_HOST=0.0.0.0
MCP_API_KEY=your_secure_api_key

# Logging
LOG_LEVEL=info
```

### Process Management with PM2

Use PM2 to manage the server process for reliability:

```bash
# Install PM2
npm install -g pm2

# Start server
pm2 start neon-mcp-server.js --name "neon-mcp" --time

# Set up auto restart
pm2 startup
pm2 save

# Monitor logs
pm2 logs neon-mcp
```

### Monitoring Setup

Configure monitoring to track server health:

```javascript
// Metrics endpoint for Prometheus
if (pathname === '/metrics') {
  res.writeHead(200, { 'Content-Type': 'text/plain' });
  
  const metrics = [
    `# HELP mcp_up MCP server is up`,
    `# TYPE mcp_up gauge`,
    `mcp_up 1`,
    
    `# HELP mcp_connections_total Total number of connections`,
    `# TYPE mcp_connections_total counter`,
    `mcp_connections_total ${totalConnections}`,
    
    `# HELP mcp_active_connections Current active connections`,
    `# TYPE mcp_active_connections gauge`,
    `mcp_active_connections ${activeConnections}`,
    
    `# HELP mcp_queries_total Total number of queries executed`,
    `# TYPE mcp_queries_total counter`,
    `mcp_queries_total ${queryCounter}`
  ].join('\n');
  
  res.end(metrics);
  return;
}
```

## Implementation Strategy

1. Create a production version of the MCP server with the enhancements above
2. Test locally with simulated load
3. Deploy to staging environment
4. Verify functionality and monitor performance
5. Deploy to production

## Production Readiness Checklist

- [ ] Connection pooling implemented
- [ ] Error handling with retry mechanism
- [ ] Structured logging configured
- [ ] Health check endpoint added
- [ ] Authentication implemented
- [ ] Rate limiting added
- [ ] Environment variables documented
- [ ] Process management configured
- [ ] Monitoring set up
- [ ] Load testing completed

## Conclusion

By implementing these enhancements, the Neon MCP server will be ready for production deployment with improved reliability, performance, and security. These changes will ensure the MCP server can handle the demands of the multi-tenant ARA Property Services app in a production environment.
