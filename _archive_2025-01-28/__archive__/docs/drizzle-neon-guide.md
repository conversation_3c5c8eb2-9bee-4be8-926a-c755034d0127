# Using Drizzle ORM with Neon Postgres

This guide explains how to use Drizzle ORM with Neon Postgres in the ARA Property Services application.

## Overview

The application uses:
- [Drizzle ORM](https://orm.drizzle.team/) - A TypeScript ORM for SQL databases
- [Neon](https://neon.tech/) - A serverless Postgres database
- [@neondatabase/serverless](https://www.npmjs.com/package/@neondatabase/serverless) - The Neon serverless driver

## Database Connection

The database connection is set up in `app/db/index.ts`:

```typescript
import { loadEnvConfig } from '@next/env';
import { neon } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-http';

// Import the schema definitions
import * as schema from './schema';

// Load environment variables
if (typeof process !== 'undefined' && process.versions && process.versions.node) {
  loadEnvConfig(process.cwd());
}

// Check if the database URL is set
if (!process.env.DATABASE_URL) {
  throw new Error('DATABASE_URL must be a Neon postgres connection string');
}

// Initialize the Neon serverless driver
const sql = neon(process.env.DATABASE_URL);

// Initialize Drizzle ORM with the Neon driver and the schema
export const db = drizzle(sql, { schema });

// Export schema for use in other files
export * from './schema';
```

## Environment Variables

The application uses the following environment variables:

```
DATABASE_URL="****************************************************************"
```

This should be set in your `.env` file.

## Database Schema

The database schema is defined in `app/db/schema.ts`. This file contains all the table definitions and relationships.

## Drizzle Configuration

The Drizzle configuration is in `drizzle.config.ts`:

```typescript
import { defineConfig } from 'drizzle-kit';
import { loadEnvConfig } from '@next/env';

// Load environment variables
loadEnvConfig(process.cwd());

if (!process.env.DATABASE_URL) {
  throw new Error('DATABASE_URL must be a Neon postgres connection string');
}

export default defineConfig({
  schema: './app/db/schema.ts', // Point to the schema file
  out: './drizzle', // Specify the output directory for migrations
  driver: 'pg', // Use 'pg' as the driver
  dbCredentials: {
    connectionString: process.env.DATABASE_URL, // Use the Neon connection string
  },
});
```

## Database Scripts

The application includes several scripts for working with the database:

- `npm run db:push` - Push schema changes to the database
- `npm run db:studio` - Open Drizzle Studio to view and edit data
- `npm run db:generate` - Generate SQL migrations
- `npm run db:seed` - Seed the database with initial data
- `npm run db:setup` - Set up the database (generate migrations, push schema, verify, seed)
- `npm run test:neon` - Test the Neon database connection
- `npm run example:drizzle` - Run the Drizzle + Neon example

## Example Usage

Here's an example of how to use Drizzle with Neon in your code:

```typescript
import { db } from '@/app/db';
import { users } from '@/app/db/schema';
import { eq } from 'drizzle-orm';

// Query all users
const allUsers = await db.select().from(users);

// Query a specific user
const user = await db.select().from(users).where(eq(users.email, '<EMAIL>')).limit(1);

// Insert a new user
const newUser = await db.insert(users).values({
  id: 'user-id',
  name: 'Example User',
  role: 'User',
  department: 'General',
  email: '<EMAIL>',
  password: 'password123',
  created_at: new Date(),
}).returning();

// Update a user
const updatedUser = await db.update(users)
  .set({ name: 'Updated Name' })
  .where(eq(users.id, 'user-id'))
  .returning();

// Delete a user
const deletedUser = await db.delete(users)
  .where(eq(users.id, 'user-id'))
  .returning();
```

## Transactions

For HTTP connections, transactions are simulated by batching queries:

```typescript
// Execute multiple operations in a batch
await db.batch([
  db.insert(users).values(user),
  db.insert(properties).values(property),
]);
```

## Migrations

The application uses Drizzle Kit for migrations:

1. Generate migrations:
   ```
   npm run db:generate
   ```

2. Push schema changes to the database:
   ```
   npm run db:push
   ```

## Troubleshooting

If you encounter issues with the database connection:

1. Check that your `.env` file contains the correct `DATABASE_URL`
2. Run `npm run test:neon` to test the database connection
3. Check that the Neon database is running and accessible
4. Verify that your IP address is allowed in the Neon database settings

## Additional Resources

- [Drizzle ORM Documentation](https://orm.drizzle.team/docs/overview)
- [Neon Documentation](https://neon.tech/docs/introduction)
- [Drizzle + Neon Example](../examples/drizzle-neon-example.ts)
