# Deploying to Vercel

This guide provides instructions for deploying the application to Vercel, including both the 11Labs voice assistant and the OpenAI assistant.

## Prerequisites

1. A Vercel account (https://vercel.com)
2. ElevenLabs API credentials:
   - Agent ID from https://elevenlabs.io/app/conversational-ai
   - API Key from https://elevenlabs.io/app/settings/api-keys
3. OpenAI API key with access to the Realtime API
4. Stream account with API key and secret
5. Neon database connection string from https://console.neon.tech

## Deployment Steps

### 1. Set up Environment Variables in Vercel

Before deploying, you need to set up environment variables in your Vercel project:

1. Go to your Vercel dashboard
2. Select your project (or create a new one)
3. Go to "Settings" > "Environment Variables"
4. Add the following environment variables:
   - `OPENAI_API_KEY`: Your OpenAI API key
   - `STREAM_API_KEY`: Your Stream API key
   - `STREAM_API_SECRET`: Your Stream API secret
   - `AGENT_ID`: Your ElevenLabs agent ID
   - `XI_API_KEY`: Your ElevenLabs API key
   - `DATABASE_URL`: Your Neon database connection string

### 2. Deploy with Vercel CLI

You can deploy using the Vercel CLI:

```bash
# Install Vercel CLI if you haven't already
npm install -g vercel

# Deploy to Vercel
vercel

# Follow the prompts to link to your Vercel project
```

### 3. Deploy from GitHub

Alternatively, you can deploy directly from GitHub:

1. Push your code to GitHub
2. In Vercel, create a new project
3. Import your repository
4. Configure the project:
   - Set the framework preset to Next.js
   - Set the build command to `npm run build`
   - Set the output directory to `.next`
5. Deploy

## Vercel Configuration

The `vercel.json` file in the root directory configures the deployment. It specifies:

- The build settings for Next.js
- The environment variable references

## Testing the Deployment

After deployment, you can test the application by:

1. Opening the deployed URL in your browser
2. Logging in to the application
3. Swiping left to access the 11Labs voice assistant
4. Swiping right to access the OpenAI assistant

## Troubleshooting

If you encounter issues with the deployment:

1. Check the Vercel deployment logs
2. Verify that all environment variables are correctly set
3. Ensure your Neon database is accessible from Vercel's servers
4. Check that your API credentials are valid

## Updating the Deployment

To update your deployment after making changes:

```bash
# Deploy the updates
vercel --prod
```

Or simply push changes to your GitHub repository if you've set up automatic deployments.
