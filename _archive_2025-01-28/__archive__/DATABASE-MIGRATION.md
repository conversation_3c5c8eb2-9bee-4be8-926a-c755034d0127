# Database Migration and Seeding Guide

This document provides instructions for migrating and seeding the database for the ARA Property Services application.

## Overview

The application uses Neon PostgreSQL as the database and Drizzle ORM for database operations. The database schema includes tables for:

- Users and authentication
- Properties and property areas
- Inspection templates and reports
- Contacts and companies
- Chat sessions and messages
- Contract specifications and cleaning tasks

## Migration Scripts

The following scripts are available for database migration:

- `db:migrate`: Runs the complete migration process
- `db:migrate-contracts`: Migrates the contract specifications tables
- `db:check`: Checks the database connection and lists tables
- `db:verify`: Verifies the database schema
- `db:test`: Tests the Drizzle ORM functionality

## Seeding Scripts

The following scripts are available for seeding the database:

- `db:seed`: Seeds the database with basic data (users, properties, inspection templates, contacts)
- `db:seed-contracts`: Seeds the contract specifications tables (cleaning areas, tasks, frequencies, tiers)
- `db:setup`: Sets up the database (generate, push, verify, seed)

## Running Migrations

To run the complete migration process:

```bash
pnpm run db:migrate
```

To migrate only the contract specifications tables:

```bash
pnpm run db:migrate-contracts
```

## Seeding the Database

To seed the database with basic data:

```bash
pnpm run db:seed
```

To seed the contract specifications tables:

```bash
pnpm run db:seed-contracts
```

## Troubleshooting

If you encounter issues with the migration or seeding process:

1. Check the database connection by running `pnpm run db:check`
2. Verify that the DATABASE_URL environment variable is correctly set in .env or .env.local
3. Check the console output for specific error messages
4. If you encounter module-related errors, make sure your package.json has `"type": "module"`

## Contract Specifications Schema

The contract specifications schema includes the following tables:

- `cleaning_areas`: Stores information about different areas that require cleaning
- `cleaning_tasks`: Stores information about specific cleaning tasks performed within areas
- `frequency_types`: Stores information about different cleaning frequencies
- `tier_specifications`: Stores information about different facility tiers with specific cleaning requirements
- `contract_specifications`: Stores information about service contracts with clients
- `facility_cleaning_specifications`: Maps cleaning tasks to facilities with frequencies based on tier
- `periodical_services`: Stores information about scheduled specialized cleaning services
- `scheduled_tasks`: Stores information about scheduled cleaning tasks
- `retail_cleaning_scope`: Stores information about retail-specific cleaning requirements

For more details on the contract specifications schema, see the [CONTRACT-SPECIFICATIONS.md](CONTRACT-SPECIFICATIONS.md) file.

## Database Schema

The database schema is defined in the following files:

- `app/db/schema.ts`: Main schema file
- `app/db/schema/contract-specifications.ts`: Contract specifications schema

## Last Updated

2025-04-20
