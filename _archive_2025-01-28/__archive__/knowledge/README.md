# Project Knowledge Base: ARA Property Services Internal App

This folder serves as the central repository for all documentation, context, and knowledge related to the ARA Property Services Internal App project.

## Folder Structure

- **/business_context**: Information about the client (ARA), stakeholders, project goals, and relevant business processes.
  - `company_profile.md`: High-level description, mission, vision.
  - `stakeholders.md`: Key contacts, roles, responsibilities.
  - `goals_objectives.md`: Project goals, KPIs.
  - `processes.md`: Key business processes.
- **/technical_design**: Details about the application's architecture, technology stack, and integrations.
  - `architecture.md`: System architecture diagrams and descriptions.
  - `tech_stack.md`: Technologies, frameworks, libraries used.
  - `integration_points.md`: How different systems connect.
- **/data_models**: Database schema information, data dictionaries, and sample data.
  - `database_schema.md`: Description of database tables, relationships.
  - `data_dictionary.md`: Definitions of key data fields.
  - `sample_data/`: Folder for sample data files (e.g., CSV, JSON).
- **/user_information**: Details about the application's users and requirements.
  - `user_personas.md`: Descriptions of target user types.
  - `user_stories.md`: User requirements and scenarios.
- **/project_management**: Meeting notes, decision logs, and other project management artifacts.
  - `meeting_notes/`: Subfolder for meeting summaries.
  - `decisions_log.md`: Record of key project decisions.
- **/external_references**: Documentation for external APIs, links to useful resources, etc.
  - `api_documentation/`: Docs for external APIs used.
  - `useful_links.md`: Links to relevant external resources.
- **/design_system**: Branding guidelines, UI components, and design assets.
  - `brand_guidelines.md`: Logo usage, color palette, typography, and brand voice.
  - `ui_components.md`: Detailed specifications for UI elements.
  - `design_assets.md`: Information on logos, icons, fonts, and other resources.

## Related Directories

### Scratchpad

In addition to the knowledge folder, there is a `scratchpad` directory at the project root level that contains:
- Reference materials from previous or related projects
- Work-in-progress content that isn't ready for integration
- Context transfer documents from other systems or applications
- Experimental ideas and designs

Currently, the scratchpad contains:
- `AskARA_Mobile_AI_Assistant_Context.md`: Documentation from a previous mobile version of the ARA Property Services app

## Maintenance

Please keep this knowledge base up-to-date as the project evolves. Add new files and update existing ones following the established structure. Periodically review the scratchpad directory to integrate valuable information into the appropriate knowledge folders.
