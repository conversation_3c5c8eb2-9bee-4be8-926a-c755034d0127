# ARA Property Services: UI Component Guide

This document outlines the standard UI components to be used in the ARA Property Services Internal App, ensuring consistency across all screens and features.

## Button Styles

### Primary Button
- Background: ARA Blue (#003366)
- Text: White (#FFFFFF)
- Border: None
- Border Radius: 4px
- Padding: 10px 16px
- Font: Helvetica Neue Bold, 14px
- Hover State: Slightly darker blue (#002855)
- Disabled State: Grey (#CCCCCC)

### Secondary Button
- Background: White (#FFFFFF)
- Text: ARA Blue (#003366)
- Border: 1px solid ARA Blue (#003366)
- Border Radius: 4px
- Padding: 10px 16px
- Font: Helvetica Neue Medium, 14px
- Hover State: Light blue background (#F0F7FF)
- Disabled State: Grey text and border (#CCCCCC)

### Action Button
- Background: ARA Light Blue (#0099CC)
- Text: White (#FFFFFF)
- Border: None
- Border Radius: 4px
- Padding: 10px 16px
- Font: Helvetica Neue Bold, 14px
- Hover State: Slightly darker blue (#0088B6)
- Disabled State: Grey (#CCCCCC)

## Form Elements

### Text Input
- Border: 1px solid ARA Grey (#666666)
- Border Radius: 4px
- Background: White (#FFFFFF)
- Padding: 8px 12px
- Font: Helvetica Neue Regular, 14px
- Focus State: Border color ARA Light Blue (#0099CC)
- Error State: Border color ARA Red (#CC3333)
- Disabled State: Light grey background (#F5F5F5)

### Dropdown
- Same styling as Text Input
- Dropdown Icon: Simple down arrow in ARA Grey (#666666)
- Selected Option: Helvetica Neue Regular, 14px
- Options List: Same styling as parent dropdown
- Hover State for Options: Light grey background (#F5F5F5)

### Checkbox
- Size: 16px × 16px
- Border: 1px solid ARA Grey (#666666)
- Border Radius: 2px
- Checked State: ARA Blue (#003366) background with white checkmark
- Hover State: Border color ARA Light Blue (#0099CC)
- Disabled State: Light grey background (#F5F5F5)

### Radio Button
- Size: 16px × 16px
- Border: 1px solid ARA Grey (#666666)
- Border Radius: 50%
- Selected State: White circle with ARA Blue (#003366) outer ring
- Hover State: Border color ARA Light Blue (#0099CC)
- Disabled State: Light grey background (#F5F5F5)

## Data Display

### Tables
- Header Background: ARA Blue (#003366)
- Header Text: White (#FFFFFF)
- Row Background (even): White (#FFFFFF)
- Row Background (odd): Light grey (#F9F9F9)
- Border: 1px solid light grey (#EEEEEE)
- Cell Padding: 10px 16px
- Hover State: Light blue background (#F0F7FF)
- Selected State: Light blue background (#E5F1FF)

### Cards
- Background: White (#FFFFFF)
- Border: 1px solid light grey (#EEEEEE)
- Border Radius: 6px
- Padding: 16px
- Shadow: 0 2px 4px rgba(0,0,0,0.1)
- Header Font: Helvetica Neue Bold, 16px
- Body Font: Helvetica Neue Regular, 14px

### Status Indicators
- Success: ARA Green (#00CC66)
- Warning: Orange (#FF9900)
- Error: ARA Red (#CC3333)
- Neutral: ARA Grey (#666666)
- Pending: ARA Light Blue (#0099CC)

## Navigation

### Main Navigation
- Background: ARA Blue (#003366)
- Text: White (#FFFFFF)
- Active Item: White background, ARA Blue (#003366) text
- Hover State: Slightly lighter blue (#004480)
- Icon Size: 20px × 20px

### Secondary Navigation
- Background: White (#FFFFFF)
- Text: ARA Grey (#666666)
- Active Item: ARA Light Blue (#0099CC) text, bottom border
- Hover State: Darker grey text (#444444)

### Breadcrumbs
- Font: Helvetica Neue Regular, 12px
- Text Color: ARA Grey (#666666)
- Separator: Light grey forward slash
- Current Page: ARA Blue (#003366)

## Feedback Elements

### Alerts
- Success: Light green background (#E6F9F0), ARA Green (#00CC66) border
- Warning: Light orange background (#FFF6E6), Orange (#FF9900) border
- Error: Light red background (#FFEBEB), ARA Red (#CC3333) border
- Info: Light blue background (#E6F4F9), ARA Light Blue (#0099CC) border
- Border Radius: 4px
- Padding: 12px 16px
- Icon: Corresponding color, 16px × 16px

### Tooltips
- Background: Dark grey (#333333)
- Text: White (#FFFFFF)
- Border Radius: 4px
- Padding: 6px 10px
- Font: Helvetica Neue Regular, 12px
- Arrow: Same color as background

### Loaders
- Primary Color: ARA Light Blue (#0099CC)
- Secondary Color: Light grey (#EEEEEE)
- Size Options: Small (16px), Medium (24px), Large (32px)

## Responsive Breakpoints

- Mobile: < 768px
- Tablet: 768px - 1023px
- Desktop: 1024px - 1439px
- Large Desktop: ≥ 1440px

## Accessibility Guidelines

- Contrast Ratio: Minimum 4.5:1 for normal text, 3:1 for large text
- Focus States: Clearly visible for all interactive elements
- Text Size: Minimum 14px for body text
- Touch Targets: Minimum 44px × 44px for mobile
- Screen Reader Support: All images have alt text, forms have labels

## Last Updated
2025-04-12
