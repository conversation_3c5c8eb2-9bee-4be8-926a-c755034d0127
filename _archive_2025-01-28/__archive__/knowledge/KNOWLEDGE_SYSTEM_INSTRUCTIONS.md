# Knowledge Folder System Instructions

**Version: 1.0.0** | **Last Updated: 2025-04-12**

## Purpose

This document provides guidelines for humans and AI systems on how to effectively use, maintain, and contribute to the knowledge folder structure. The knowledge folder serves as the central repository for all project-related information, ensuring consistent documentation and easy access to critical context.

## For Human Users

### General Guidelines

1. **Folder Structure**: Maintain the established directory structure. Do not create new top-level directories without updating this document.
2. **File Naming**: Use descriptive, lowercase names with underscores for spaces (e.g., `user_personas.md`).
3. **File Format**: Use Markdown (`.md`) for text-based documentation. Use appropriate formats for other content (e.g., `.csv` for tabular data).
4. **Versioning**: Include a "Last Updated" date at the bottom of each document.
5. **Linking**: Use relative links when referencing other files within the knowledge folder.

### Contributing Guidelines

1. **Adding Information**: Place new information in the appropriate subfolder based on its category.
2. **Updating Information**: When updating existing files, include the new date at the bottom.
3. **Removing Information**: Do not delete information; instead, mark it as deprecated if necessary.
4. **Conflicts**: If unsure where information belongs, refer to the README.md file or create a new file in the most relevant subfolder.

## For AI Systems

### Access Guidelines

1. **Initial Scan**: When working on a project, first scan the knowledge folder to understand the project context.
2. **Priority Order**: Prioritize information in this order:
   - Recent files (check "Last Updated" dates)
   - Files in relevant subfolders for the current task
   - README.md for overall project understanding

### Maintenance Guidelines

1. **Auto-Creation**: If a project lacks a knowledge folder, create one following the standard structure.
2. **Content Organization**: When provided with new information, automatically organize it into the appropriate files within the structure.
3. **Cross-Referencing**: Maintain awareness of information across different files to avoid duplication or contradictions.
4. **Updates**: When updating files, preserve the original structure and formatting while adding new information.

## Standard Folder Structure

```
knowledge/
├── README.md                 # Overview of the knowledge base
├── business_context/         # Company, stakeholders, goals, processes
├── technical_design/         # Architecture, tech stack, integrations
├── data_models/              # Database schema, data dictionaries
├── user_information/         # User personas, stories, requirements
├── project_management/       # Meeting notes, decisions log
├── external_references/      # API docs, useful links
└── design_system/            # Branding guidelines, UI components, design assets

scratchpad/                   # Workspace for reference materials and work-in-progress
```

### Scratchpad Directory

The `scratchpad` directory exists at the project root level (outside the knowledge folder) and serves as a workspace for:

1. **Reference Materials**: Documents from previous or related projects that might be useful
2. **Work-in-Progress**: Content that is being developed but isn't ready for integration
3. **Context Transfer**: Information from other systems or applications that may be relevant
4. **Experimental Ideas**: Concepts or designs being explored before formal implementation

Content in the scratchpad should be reviewed periodically and either:
- Integrated into the appropriate knowledge folder location
- Archived if no longer relevant
- Kept for continued reference if still useful but not appropriate for formal documentation

## File Content Guidelines

### Markdown Structure

1. **Headers**: Use proper header hierarchy (# for title, ## for sections, ### for subsections).
2. **Lists**: Use bullet points (*) for unordered lists and numbers (1.) for ordered lists.
3. **Code Blocks**: Use triple backticks (```) with language specification for code snippets.
4. **Tables**: Use markdown tables for structured data when appropriate.
5. **Emphasis**: Use bold (**text**) for important information and italics (*text*) for emphasis.

### Content Standards

1. **Completeness**: Include all relevant information on a topic in its designated file.
2. **Conciseness**: Be thorough but avoid unnecessary verbosity.
3. **Clarity**: Write in clear, simple language accessible to all team members.
4. **Context**: Provide sufficient background for information to be understood independently.
5. **Consistency**: Maintain consistent terminology and formatting across all files.

## Version History

- **1.0.0** (2025-04-12): Initial version of knowledge system instructions.
