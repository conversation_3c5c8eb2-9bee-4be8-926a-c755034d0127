[{"id": "task-toilet-clean-fixtures", "area_id": "area-toilets", "name": "Clean and disinfect fixtures", "description": "Clean and disinfect all fixtures and fittings including toilet bowls, urinals, hand basins, taps, showers, chrome fittings and unit surfaces.", "task_type": "Detail Clean", "standard_duration_minutes": 20, "equipment_required": ["toilet brush", "scrubber", "microfiber cloths"], "materials_required": ["toilet cleaner", "disinfectant", "glass cleaner"], "safety_requirements": "Wear gloves and appropriate PPE", "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-toilet-mirrors-walls", "area_id": "area-toilets", "name": "Spot clean walls and mirrors", "description": "Spot clean and wash free of marks; walls, mirrors, tiles, doors, benches, furniture, partitions, washable paintwork and hand dryers.", "task_type": "Spot Clean", "standard_duration_minutes": 15, "equipment_required": ["microfiber cloths", "spray bottle"], "materials_required": ["glass cleaner", "wall cleaner"], "safety_requirements": "Use caution on wet floors", "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-toilet-replenish", "area_id": "area-toilets", "name": "Replenish consumables", "description": "Replenish consumables including soap, paper towelling, toilet paper and disposable cup dispensers.", "task_type": "Replenishment", "standard_duration_minutes": 10, "equipment_required": null, "materials_required": ["hand soap", "paper towels", "toilet paper"], "safety_requirements": null, "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-toilet-floor", "area_id": "area-toilets", "name": "Mop floors", "description": "Floor surfaces to be swept and damp mopped with a suitable disinfectant", "task_type": "Floor Cleaning", "standard_duration_minutes": 15, "equipment_required": ["mop", "bucket", "broom"], "materials_required": ["disinfectant", "floor cleaner"], "safety_requirements": "Use wet floor signs", "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-kitchen-sink-benches", "area_id": "area-kitchen", "name": "Clean sinks and benches", "description": "Clean, disinfect and wipe dry all sinks, unit tops, draining boards, drinking fountains and chrome fittings.", "task_type": "Detail Clean", "standard_duration_minutes": 20, "equipment_required": ["scrubber", "sponge", "cloths"], "materials_required": ["kitchen cleaner", "disinfectant"], "safety_requirements": "Ensure electrical safety", "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-kitchen-appliances", "area_id": "area-kitchen", "name": "Clean appliances", "description": "Clean and wipe over stainless steel sinks and external surfaces of refrigerators, cupboards, walls, microwave ovens and related furniture", "task_type": "Detail Clean", "standard_duration_minutes": 20, "equipment_required": ["microfiber cloths", "soft scrubber"], "materials_required": ["stainless steel cleaner", "multi-purpose cleaner"], "safety_requirements": "Unplug appliances before cleaning", "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-kitchen-tables-chairs", "area_id": "area-kitchen", "name": "Clean tables and chairs", "description": "<PERSON>p clean all tables and chairs", "task_type": "General Clean", "standard_duration_minutes": 15, "equipment_required": ["microfiber cloths", "spray bottle"], "materials_required": ["multi-purpose cleaner"], "safety_requirements": null, "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-kitchen-fridges", "area_id": "area-kitchen", "name": "Clean inside fridges", "description": "Clean inside fridges", "task_type": "Detail Clean", "standard_duration_minutes": 30, "equipment_required": ["sponge", "cloths"], "materials_required": ["fridge cleaner", "disinfectant"], "safety_requirements": "Wear gloves, handle food items carefully", "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-kitchen-microwave", "area_id": "area-kitchen", "name": "Clean microwave", "description": "Damp wipe and clean inside and outside of microwave ovens", "task_type": "Detail Clean", "standard_duration_minutes": 10, "equipment_required": ["microfiber cloths", "soft scrubber"], "materials_required": ["multi-purpose cleaner"], "safety_requirements": "Unplug before cleaning inside", "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-office-desks", "area_id": "area-office", "name": "Clean desks and surfaces", "description": "Clean benches and desks", "task_type": "General Clean", "standard_duration_minutes": 10, "equipment_required": ["microfiber cloths", "spray bottle"], "materials_required": ["multi-purpose cleaner"], "safety_requirements": null, "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-office-computers", "area_id": "area-office", "name": "Wipe telephones and computers", "description": "Wipe telephones and computers", "task_type": "Spot Clean", "standard_duration_minutes": 10, "equipment_required": ["microfiber cloths"], "materials_required": ["electronics cleaner"], "safety_requirements": "Use dry or slightly damp cloth only", "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-office-dust", "area_id": "area-office", "name": "Dust workstations", "description": "Dust and wipe down all work stations and general dust throughout", "task_type": "Dusting", "standard_duration_minutes": 15, "equipment_required": ["duster", "microfiber cloths"], "materials_required": null, "safety_requirements": null, "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-office-bins", "area_id": "area-office", "name": "Empty bins", "description": "Remove centralised bins from the office area and place in the designated area for collection", "task_type": "Waste Management", "standard_duration_minutes": 10, "equipment_required": ["trolley"], "materials_required": ["bin liners"], "safety_requirements": null, "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-glass-mirrors", "area_id": "area-glass", "name": "Clean mirrors", "description": "Clean all glass mirrors", "task_type": "Glass Cleaning", "standard_duration_minutes": 10, "equipment_required": ["squeegee", "microfiber cloths"], "materials_required": ["glass cleaner"], "safety_requirements": "Use appropriate ladders", "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-glass-entrance", "area_id": "area-glass", "name": "Clean entrance glass", "description": "Front entry doors and frames to be cleaned", "task_type": "Glass Cleaning", "standard_duration_minutes": 15, "equipment_required": ["squeegee", "microfiber cloths", "extension pole"], "materials_required": ["glass cleaner"], "safety_requirements": "Use appropriate ladders", "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-glass-spot-clean", "area_id": "area-glass", "name": "Spot clean internal glass", "description": "Spot clean all glass desktops", "task_type": "Spot Clean", "standard_duration_minutes": 10, "equipment_required": ["microfiber cloths"], "materials_required": ["glass cleaner"], "safety_requirements": null, "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-glass-windows", "area_id": "area-glass", "name": "Clean windows", "description": "Clean all external windows and frames", "task_type": "Window Cleaning", "standard_duration_minutes": 30, "equipment_required": ["squeegee", "ladder", "extension pole"], "materials_required": ["window cleaning solution"], "safety_requirements": "Use fall protection", "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-floor-sweep-mop", "area_id": "area-hard-flooring", "name": "Sweep and mop", "description": "Sweep and/or mop vinyl, tiled and concrete flooring", "task_type": "Floor Cleaning", "standard_duration_minutes": 20, "equipment_required": ["broom", "mop", "bucket"], "materials_required": ["floor cleaner"], "safety_requirements": "Use wet floor signs", "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-floor-machine-buff", "area_id": "area-hard-flooring", "name": "Machine buff vinyl", "description": "Vinyl floor machine buffed and dust controlled and maintained to a perfect shine", "task_type": "Floor Maintenance", "standard_duration_minutes": 30, "equipment_required": ["floor buffer", "pads"], "materials_required": ["buffing solution"], "safety_requirements": "Use PPE and caution signage", "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-floor-scuff-marks", "area_id": "area-hard-flooring", "name": "Remove scuff marks", "description": "Remove scuff marks", "task_type": "Spot Clean", "standard_duration_minutes": 10, "equipment_required": ["scrubber", "cloths"], "materials_required": ["scuff remover"], "safety_requirements": null, "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-carpet-spot-vacuum", "area_id": "area-carpets", "name": "Spot vacuum", "description": "Spot vacuum and de-litter", "task_type": "Vacuuming", "standard_duration_minutes": 15, "equipment_required": ["vacuum cleaner"], "materials_required": null, "safety_requirements": null, "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-carpet-full-vacuum", "area_id": "area-carpets", "name": "Full vacuum", "description": "Full vacuum to all other floor areas", "task_type": "Vacuuming", "standard_duration_minutes": 30, "equipment_required": ["vacuum cleaner"], "materials_required": null, "safety_requirements": null, "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-carpet-spot-clean", "area_id": "area-carpets", "name": "Spot clean stains", "description": "Spot clean stains, marks and spills", "task_type": "Spot Clean", "standard_duration_minutes": 15, "equipment_required": ["carpet cleaner", "cloths"], "materials_required": ["carpet cleaning solution"], "safety_requirements": null, "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-carpet-steam-clean", "area_id": "area-carpets", "name": "Steam clean carpet", "description": "Steam clean all carpet area", "task_type": "Deep Clean", "standard_duration_minutes": 60, "equipment_required": ["steam cleaner"], "materials_required": ["carpet cleaning solution"], "safety_requirements": "Ensure proper ventilation", "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-rubbish-empty-bins", "area_id": "area-rubbish", "name": "Empty bins", "description": "Empty all waste and garbage containers and remove rubbish to designated area", "task_type": "Waste Management", "standard_duration_minutes": 15, "equipment_required": ["trolley"], "materials_required": ["bin liners"], "safety_requirements": "Proper manual handling", "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-rubbish-wipe-bins", "area_id": "area-rubbish", "name": "Wipe bins", "description": "Wipe over all bin surfaces & replace bin liner bags as necessary", "task_type": "General Clean", "standard_duration_minutes": 10, "equipment_required": ["cloths", "spray bottle"], "materials_required": ["disinfectant"], "safety_requirements": "Wear gloves", "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-rubbish-recycling", "area_id": "area-rubbish", "name": "Manage recycling", "description": "Ensure separation of recycled and non-recycled waste is maintained", "task_type": "Waste Management", "standard_duration_minutes": 10, "equipment_required": ["trolley"], "materials_required": null, "safety_requirements": null, "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-external-entrances", "area_id": "area-external", "name": "Clean entrances", "description": "Sweep building entrances, foyer footpaths and clean any door mats", "task_type": "General Clean", "standard_duration_minutes": 15, "equipment_required": ["broom", "dustpan"], "materials_required": null, "safety_requirements": null, "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-external-ashtrays", "area_id": "area-external", "name": "Empty ashtrays", "description": "Ashtrays to be emptied and external areas cleaned", "task_type": "Waste Management", "standard_duration_minutes": 10, "equipment_required": ["bin liners"], "materials_required": null, "safety_requirements": "Wear gloves", "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-external-collect-debris", "area_id": "area-external", "name": "Collect yard debris", "description": "Collect yard and car park debris", "task_type": "General Clean", "standard_duration_minutes": 20, "equipment_required": ["rake", "shovel", "wheelbarrow"], "materials_required": null, "safety_requirements": null, "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-external-sweeper", "area_id": "area-external", "name": "Use ride on sweeper", "description": "Use ride on sweeper to clean floors", "task_type": "Machine Cleaning", "standard_duration_minutes": 60, "equipment_required": ["ride-on sweeper"], "materials_required": null, "safety_requirements": "Proper training required", "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-dock-sweep", "area_id": "area-dock", "name": "Sweep floor", "description": "Sweep floor of all dirt and dust", "task_type": "Floor Cleaning", "standard_duration_minutes": 20, "equipment_required": ["broom", "dustpan"], "materials_required": null, "safety_requirements": "Wear safety vest", "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-dock-degrease", "area_id": "area-dock", "name": "Degrease floors", "description": "Spot degrease floors/oil spills", "task_type": "Spot Clean", "standard_duration_minutes": 15, "equipment_required": ["degreaser", "scrubber"], "materials_required": ["degreasing solution"], "safety_requirements": "Use appropriate PPE", "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-bike-empty-bins", "area_id": "area-bike-sheds", "name": "Empty waste bins", "description": "Empty waste bins", "task_type": "Waste Management", "standard_duration_minutes": 5, "equipment_required": ["bin liners"], "materials_required": null, "safety_requirements": null, "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-bike-clean-toilets", "area_id": "area-bike-sheds", "name": "Clean toilets", "description": "Clean toilets (where present)", "task_type": "Detail Clean", "standard_duration_minutes": 15, "equipment_required": ["toilet brush", "cloths"], "materials_required": ["toilet cleaner", "disinfectant"], "safety_requirements": "Wear gloves", "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-bike-sweep-mop", "area_id": "area-bike-sheds", "name": "Sweep and mop", "description": "Sweep, mop and spot degrease floors/oil spills", "task_type": "Floor Cleaning", "standard_duration_minutes": 20, "equipment_required": ["broom", "mop", "bucket"], "materials_required": ["degreaser", "floor cleaner"], "safety_requirements": "Use caution with oil spills", "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-production-sweep", "area_id": "area-production", "name": "Sweep walkways", "description": "Sweep and de-litter pedestrian walkways, vehicle traffic lanes, stairs, under the mezzanine / platforms", "task_type": "Floor Cleaning", "standard_duration_minutes": 30, "equipment_required": ["broom", "dustpan"], "materials_required": null, "safety_requirements": "Wear high visibility vest", "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-production-dust-rails", "area_id": "area-production", "name": "Dust handrails", "description": "Dust and/or wipe handrails, column protection and bollards including on balconies", "task_type": "Dusting", "standard_duration_minutes": 15, "equipment_required": ["duster", "cloths"], "materials_required": null, "safety_requirements": "Use fall protection on balconies", "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-retail-counter", "area_id": "area-retail-counter", "name": "Clean counter surfaces", "description": "Clean counters, bench tops and surfaces free of dust, dirt, litter or stains", "task_type": "Detail Clean", "standard_duration_minutes": 15, "equipment_required": ["microfiber cloths", "spray bottle"], "materials_required": ["surface cleaner"], "safety_requirements": null, "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-po-box-clean", "area_id": "area-po-box", "name": "Clean PO Box exteriors", "description": "Clean exterior of PO Boxes free of dust, dirt, grit, litter, graffiti, marks and spots", "task_type": "Detail Clean", "standard_duration_minutes": 30, "equipment_required": ["cloths", "duster", "ladder"], "materials_required": ["multi-purpose cleaner"], "safety_requirements": "Use appropriate ladders", "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "task-parcel-locker-screens", "area_id": "area-parcel-lockers", "name": "Clean locker screens", "description": "Clean screens free of dust, dirt, grit, litter, graffiti, marks and spots", "task_type": "Detail Clean", "standard_duration_minutes": 15, "equipment_required": ["microfiber cloths"], "materials_required": ["screen cleaner"], "safety_requirements": "Use electronic-safe products", "created_at": "2025-01-01T00:00:00Z", "updated_at": null}]