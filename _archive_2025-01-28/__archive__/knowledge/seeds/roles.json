[{"id": "admin-role", "name": "Administrator", "permissions": {"read": "*", "write": "*", "delete": "*", "manage_users": true, "manage_sites": true, "run_reports": true, "configure_system": true}, "created_at": "2025-01-01T00:00:00Z"}, {"id": "manager-role", "name": "Manager", "permissions": {"read": "*", "write": ["tasks", "reports", "inspections"], "delete": ["tasks"], "manage_users": false, "manage_sites": true, "run_reports": true, "configure_system": false}, "created_at": "2025-01-01T00:00:00Z"}, {"id": "inspector-role", "name": "Inspector", "permissions": {"read": ["sites", "tasks", "inspections"], "write": ["inspections", "action_items"], "delete": [], "manage_users": false, "manage_sites": false, "run_reports": false, "configure_system": false}, "created_at": "2025-01-01T00:00:00Z"}, {"id": "staff-role", "name": "Cleaning Staff", "permissions": {"read": ["tasks", "schedules"], "write": ["task_status"], "delete": [], "manage_users": false, "manage_sites": false, "run_reports": false, "configure_system": false}, "created_at": "2025-01-01T00:00:00Z"}, {"id": "client-role", "name": "Client", "permissions": {"read": ["reports", "inspections"], "write": ["feedback"], "delete": [], "manage_users": false, "manage_sites": false, "run_reports": false, "configure_system": false}, "created_at": "2025-01-01T00:00:00Z"}]