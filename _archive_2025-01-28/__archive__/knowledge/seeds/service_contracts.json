[{"id": "contract-prop-sydney-office-tier1", "client_id": "jll-client", "site_id": "site-jll-sydney-office", "start_date": "2025-01-01", "end_date": "2027-12-31", "contract_value": 250000.0, "billing_frequency": "monthly", "service_type": "Commercial Office", "service_frequency": "Daily (Multiple)", "special_terms": null, "status": "active", "contract_file_url": null, "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "contract-prop-melbourne-depot-tier2", "client_id": "australia-post-client", "site_id": "site-auspost-melbourne", "start_date": "2025-01-01", "end_date": "2027-12-31", "contract_value": 150000.0, "billing_frequency": "monthly", "service_type": "Industrial Depot", "service_frequency": "Daily", "special_terms": null, "status": "active", "contract_file_url": null, "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "contract-prop-lismore-retail-tier3", "client_id": "big-w-client", "site_id": "site-bigw-lismore", "start_date": "2025-01-01", "end_date": "2027-12-31", "contract_value": 75000.0, "billing_frequency": "monthly", "service_type": "Retail Store", "service_frequency": "Daily (3/5)", "special_terms": null, "status": "active", "contract_file_url": null, "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "contract-prop-regional-office-tier4", "client_id": "jll-client", "site_id": null, "start_date": "2025-01-01", "end_date": "2027-12-31", "contract_value": 50000.0, "billing_frequency": "monthly", "service_type": "Commercial Office", "service_frequency": "Weekly", "special_terms": null, "status": "active", "contract_file_url": null, "created_at": "2025-01-01T00:00:00Z", "updated_at": null}, {"id": "contract-prop-remote-site-tier5", "client_id": "australia-post-client", "site_id": null, "start_date": "2025-01-01", "end_date": "2027-12-31", "contract_value": 25000.0, "billing_frequency": "monthly", "service_type": "Logistics Centre", "service_frequency": "Fortnightly", "special_terms": null, "status": "active", "contract_file_url": null, "created_at": "2025-01-01T00:00:00Z", "updated_at": null}]