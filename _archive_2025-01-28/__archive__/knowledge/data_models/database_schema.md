# ARA Property Services: Database Schema

## Overview

This document describes the database schema for the ARA Property Services Internal App. The database is hosted on Supabase with the following details:

- **Project ID**: heixcxsdnmnqaindwfsz
- **URL**: https://heixcxsdnmnqaindwfsz.supabase.co
- **Region**: ap-southeast-2
- **Database Host**: db.heixcxsdnmnqaindwfsz.supabase.co
- **Postgres Version**: **********

## Core Tables

### user_profiles

Extends Supabase auth.users for additional user information.

```sql
CREATE TABLE user_profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL,
  email TEXT NOT NULL UNIQUE,
  phone TEXT,
  role TEXT NOT NULL,
  department TEXT,
  position TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  avatar_url TEXT,
  status TEXT DEFAULT 'active'
);
```

### clients

Stores information about client organizations.

```sql
CREATE TABLE clients (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  primary_contact_name TEXT,
  primary_contact_email TEXT,
  primary_contact_phone TEXT,
  address TEXT,
  suburb TEXT,
  state TEXT,
  postcode TEXT,
  sector TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  account_manager_id UUID REFERENCES user_profiles(id),
  status TEXT DEFAULT 'active',
  notes TEXT
);
```

### sites

Stores information about client service locations.

```sql
CREATE TABLE sites (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  address TEXT,
  suburb TEXT,
  state TEXT,
  postcode TEXT,
  site_contact_name TEXT,
  site_contact_phone TEXT,
  site_contact_email TEXT,
  site_type TEXT,
  floor_area_sqm NUMERIC,
  access_instructions TEXT,
  special_requirements TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  status TEXT DEFAULT 'active'
);
```

### service_contracts

Stores information about service agreements with clients.

```sql
CREATE TABLE service_contracts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
  site_id UUID REFERENCES sites(id) ON DELETE CASCADE,
  start_date DATE NOT NULL,
  end_date DATE,
  contract_value NUMERIC,
  billing_frequency TEXT,
  service_type TEXT NOT NULL,
  service_frequency TEXT,
  special_terms TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  status TEXT DEFAULT 'active',
  contract_file_url TEXT
);
```

### service_schedules

Stores scheduled service visits.

```sql
CREATE TABLE service_schedules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  contract_id UUID REFERENCES service_contracts(id) ON DELETE CASCADE,
  site_id UUID REFERENCES sites(id) ON DELETE CASCADE,
  service_date DATE NOT NULL,
  start_time TIME,
  end_time TIME,
  service_type TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  status TEXT DEFAULT 'scheduled',
  notes TEXT
);
```

### job_assignments

Links staff members to scheduled services.

```sql
CREATE TABLE job_assignments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  schedule_id UUID REFERENCES service_schedules(id) ON DELETE CASCADE,
  staff_id UUID REFERENCES user_profiles(id),
  assigned_by UUID REFERENCES user_profiles(id),
  assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  role_on_job TEXT,
  status TEXT DEFAULT 'assigned'
);
```

## Operational Tables

### service_reports

Stores information about completed services.

```sql
CREATE TABLE service_reports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  schedule_id UUID REFERENCES service_schedules(id) ON DELETE CASCADE,
  completed_by UUID REFERENCES user_profiles(id),
  completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  duration_minutes INTEGER,
  checklist_results JSONB,
  issues_identified TEXT,
  actions_taken TEXT,
  client_signature BOOLEAN DEFAULT FALSE,
  photos JSONB,
  quality_rating INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### inventory

Stores information about cleaning supplies and equipment.

```sql
CREATE TABLE inventory (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  item_name TEXT NOT NULL,
  item_category TEXT,
  current_stock INTEGER DEFAULT 0,
  unit_of_measure TEXT,
  reorder_threshold INTEGER,
  cost_per_unit NUMERIC,
  storage_location TEXT,
  last_restocked_date DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### equipment

Stores information about cleaning and maintenance equipment.

```sql
CREATE TABLE equipment (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  type TEXT,
  serial_number TEXT,
  purchase_date DATE,
  cost NUMERIC,
  current_location TEXT,
  status TEXT DEFAULT 'operational',
  last_serviced_date DATE,
  service_interval_days INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### maintenance_requests

Stores information about equipment maintenance requests.

```sql
CREATE TABLE maintenance_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  equipment_id UUID REFERENCES equipment(id) ON DELETE SET NULL,
  site_id UUID REFERENCES sites(id) ON DELETE SET NULL,
  requested_by UUID REFERENCES user_profiles(id),
  request_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  issue_description TEXT,
  priority TEXT,
  status TEXT DEFAULT 'new',
  assigned_to UUID REFERENCES user_profiles(id),
  completion_date TIMESTAMP WITH TIME ZONE,
  resolution_notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### client_feedback

Stores client feedback and complaints.

```sql
CREATE TABLE client_feedback (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
  site_id UUID REFERENCES sites(id) ON DELETE CASCADE,
  service_date DATE,
  feedback_type TEXT,
  description TEXT,
  received_by UUID REFERENCES user_profiles(id),
  received_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  severity TEXT,
  status TEXT DEFAULT 'open',
  resolution TEXT,
  follow_up_required BOOLEAN DEFAULT FALSE,
  follow_up_date DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Supporting Tables

### documents

Stores links to documents and files.

```sql
CREATE TABLE documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  document_type TEXT,
  related_to_type TEXT,
  related_to_id UUID,
  upload_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  uploaded_by UUID REFERENCES user_profiles(id),
  file_url TEXT,
  expiry_date DATE,
  tags JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### notifications

Stores system notifications for users.

```sql
CREATE TABLE notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  read_at TIMESTAMP WITH TIME ZONE,
  notification_type TEXT,
  related_entity_type TEXT,
  related_entity_id UUID,
  action_url TEXT
);
```

### audit_log

Stores audit trail of database changes.

```sql
CREATE TABLE audit_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  table_name TEXT NOT NULL,
  record_id UUID NOT NULL,
  action TEXT NOT NULL,
  changed_by UUID REFERENCES user_profiles(id),
  changed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  old_values JSONB,
  new_values JSONB
);
```

### contacts

Stores general contact information.

```sql
CREATE TABLE contacts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  first_name TEXT,
  last_name TEXT,
  email TEXT UNIQUE,
  phone TEXT,
  company_name TEXT,
  role_title TEXT,
  relationship_to_ara TEXT,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### draft_invites

Stores draft user invitations.

```sql
CREATE TABLE draft_invites (
  id SERIAL PRIMARY KEY,
  email TEXT NOT NULL UNIQUE,
  intended_role TEXT DEFAULT 'staff',
  status TEXT DEFAULT 'draft',
  logged_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  logged_by UUID REFERENCES user_profiles(id)
);
```

### seed_staff_emails

Stores reference data for staff emails.

```sql
CREATE TABLE seed_staff_emails (
  id SERIAL PRIMARY KEY,
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL,
  email TEXT NOT NULL UNIQUE,
  role TEXT NOT NULL DEFAULT 'staff'
);
```

## Database Functions and Triggers

### handle_new_user()

Automatically creates a profile when a user signs up.

### match_user_profile_to_seed()

Matches newly created profiles with seed data.

### log_draft_invite()

Logs a draft invite for a user.

## Last Updated

2025-04-12