# ARA Property Services Mobile App Database Design

**Document Date:** April 13, 2025  
**Version:** 1.0  
**Author:** Senior Data Architect  

## Executive Summary

This document details the database architecture for the ARA Property Services Mobile App, designed to address the comprehensive cleaning specifications required by Australia Post and Star Track facilities. The database schema is optimized to handle the complex cleaning requirements across different property tiers, regions, and facility types, while providing a robust foundation for inspection management, quality assurance, and service scheduling.

## Database Design Principles

The database architecture adheres to the following key principles:

1. **Contractual Compliance:** Captures all cleaning specifications and frequencies stipulated in the Australia Post and Star Track contracts.
2. **Operational Efficiency:** Supports efficient task scheduling, assignment, and monitoring.
3. **Quality Assurance:** Facilitates rigorous inspection processes and follow-up actions.
4. **Data Integrity:** Implements referential integrity constraints and audit trails.
5. **Security:** Leverages row-level security for fine-grained access control.
6. **Extensibility:** Designed to accommodate future contract requirements with minimal schema changes.

## Core Schema Design

### Entity Relationship Model

The database implements a comprehensive entity-relationship model that encompasses:

1. **Facilities Management:**
   - Properties categorized by type, tier, and region
   - Cleaning areas (toilets, kitchens, offices, production areas, etc.)
   - Cleaning tasks with detailed specifications
   - Frequency mappings customized by facility tier

2. **Quality Assurance Framework:**
   - Inspections with overall scoring
   - Area-specific inspection items
   - Action items for remediation
   - Historical performance tracking

3. **Operational Management:**
   - Periodical services scheduling
   - Daily task assignment and tracking
   - Consumables inventory management
   - Staff allocation and workload distribution

4. **Monitoring and Reporting:**
   - Notifications system
   - Audit trail for all data changes
   - Performance analytics and dashboards

### Key Tables and Relationships

```
facilities ──────┐
                 ├── facility_cleaning_specifications ── cleaning_tasks ── cleaning_areas
user_facilities ─┘               │
                                 │
                    ┌────────────┴─────────────┐
                    │                          │
              scheduled_tasks           periodical_services
                    │                          │
                    │                          │
                 users ── notifications        │
                    │                          │
                    │                          │
            inspections ─────┐                 │
                  │         │                 │
                  │         │                 │
        inspection_items ───┴── action_items  │
                                              │
                                              │
        consumables_inventory ────────────────┘
```

## Tier-Based Cleaning Specifications

A key innovation in this database design is the implementation of tier-based cleaning specifications. The Australia Post contract defines 5 different facility tiers, each with specific cleaning requirements:

### Tiered Frequency System

- **Tier 1 Sites:** Large industrial facilities operating 24/7 with multiple daily cleaning requirements (3x daily for high-traffic areas)
- **Tier 2 Sites:** Smaller industrial centers with daily cleaning schedules
- **Tier 3-5 Sites:** Smaller delivery centers with reduced frequency (3 out of 5 operational days)

The database implements this tiered approach through:

1. A mapping table (`australia_post_tier_specs`) that defines standard requirements per tier
2. A dynamic function (`generate_tier_based_cleaning_specs`) that generates appropriate specifications when new facilities are added
3. Intelligent scheduling logic that accounts for tier-specific frequencies when generating tasks

## Specialized Cleaning Requirements

The database schema supports specialized cleaning requirements unique to Australia Post and Star Track facilities:

### Production Areas

- Walkway cleaning
- Under-mezzanine cleaning
- Machine sweep operations
- Technical area boundaries

### Retail Spaces

- PO Box cleaning
- Parcel locker maintenance
- Counter sanitization
- Front-of-house vs. back-of-house scheduling

### Periodical Services

- Vinyl floor strip and seal (biannual)
- Carpet deep cleaning (biannual)
- Window cleaning (monthly for retail, quarterly for industrial)
- High dusting (biannual)
- Loading dock degreasing (quarterly)

## Automated Task Generation

The system implements intelligent task generation based on cleaning specifications:

1. `generate_scheduled_tasks` function creates tasks automatically based on facility tier, frequency, and area specifications
2. Multiple daily cleanings are scheduled at appropriate intervals
3. Reduced frequency schedules (3 out of 5 days) are implemented for Tier 3-5 sites
4. Staff are automatically assigned based on facility assignments

## Quality Assurance Workflow

The database supports a comprehensive quality assurance workflow:

1. Regular inspections with overall and area-specific scoring
2. Action item tracking with assignment and resolution
3. Trend analysis for identifying recurring issues
4. Performance comparisons across facility types, tiers, and regions

## Analytics and Reporting

The schema includes pre-built analytical views for common reporting needs:

1. `facility_performance_view`: Overall cleaning performance by facility
2. `upcoming_periodicals_view`: Scheduled and overdue periodical services
3. `action_items_summary_view`: Outstanding issues by facility

Custom analytical functions provide additional insights:

1. `get_facility_performance`: Historical performance trends
2. `get_upcoming_periodicals`: Forward-looking service schedule
3. `get_facility_inspections`: Inspection history and trends

## Security Model

The database implements a comprehensive row-level security model:

1. Role-based access control (admin, manager, staff, client)
2. Location-based permissions via user-facility assignments
3. Task-specific visibility for operational staff
4. Client data segregation

## Implementation Recommendations

### Database Deployment

1. **Initial Schema Creation:** Execute the core schema creation script first to establish the table structure
2. **Specification Migration:** Run the cleaning specification migration script to populate cleaning areas, tasks, and tier-based specifications
3. **Data Population:** Import existing facility data and execute the `initialize_new_facility` function for each site

### Integration with Mobile App

1. **Authentication Flow:** Implement JWT-based authentication with role-based permissions
2. **API Layer:** Create RESTful endpoints that map to the database functions
3. **Caching Strategy:** Implement Redis caching for frequently accessed data (specifications, facility details)
4. **Offline Capabilities:** Design sync mechanism for field staff operating in areas with intermittent connectivity

### Performance Considerations

1. **Indexing Strategy:** The schema includes optimized indexes for common query patterns
2. **Query Optimization:** Use the pre-built analytical views and functions for complex reporting needs
3. **Connection Pooling:** Implement pgBouncer for connection management in high-concurrency scenarios
4. **Partitioning:** Consider time-based partitioning for historical inspection and task data as the dataset grows

## Data Migration Strategy

For migrating from existing systems:

1. **Facility Mapping:** Create a concordance table mapping old facility IDs to new UUIDs
2. **Specification Transformation:** Convert existing cleaning schedules to the new tier-based specification model
3. **Historical Data:** Import historical inspection data with appropriate scoring normalization
4. **Incremental Transition:** Consider a phased rollout by facility tier or geographic region

## Technical Implementation Details

The database leverages several PostgreSQL-specific features:

1. **Row-Level Security:** For granular access control
2. **JSON Functions:** For storing complex configuration data
3. **Stored Procedures:** For encapsulating business logic
4. **Triggers:** For maintaining data integrity and audit trails
5. **PostGIS Extension:** For location-based features and proximity calculations
6. **pg_cron:** For scheduling automated task generation (in production environments)

## Maintenance Recommendations

1. **Regular Auditing:** Schedule quarterly audits of cleaning specifications against contract requirements
2. **Performance Monitoring:** Implement query performance monitoring with pg_stat_statements
3. **Backup Strategy:** Configure point-in-time recovery with WAL archiving
4. **Schema Evolution:** Use transactional DDL for schema updates to maintain data integrity

## Conclusion

This database design provides a comprehensive foundation for the ARA Property Services Mobile App, enabling efficient management of cleaning operations across Australia Post and Star Track facilities. By encoding the complex tier-based cleaning specifications directly into the data model, the system ensures contractual compliance while optimizing operational efficiency.

The flexible architecture allows for future expansion as cleaning requirements evolve, with minimal impact on existing functionality. The robust security model ensures appropriate data access control, while the comprehensive analytics capabilities provide valuable insights for continuous improvement.

## Appendix A: Entity Dictionary

| Entity | Description |
|--------|-------------|
| `facilities` | Australia Post and Star Track properties, categorized by type, tier, and region |
| `cleaning_areas` | Defined areas within facilities requiring specific cleaning (toilets, kitchens, etc.) |
| `cleaning_tasks` | Specific cleaning activities performed within areas |
| `facility_cleaning_specifications` | Mapping of tasks to facilities with frequencies based on tier |
| `inspections` | Quality checks performed at facilities |
| `inspection_items` | Individual area scores within inspections |
| `action_items` | Follow-up tasks created from inspection findings |
| `periodical_services` | Scheduled specialized cleaning services (e.g., carpet cleaning) |
| `scheduled_tasks` | Regular cleaning activities assigned to staff |
| `users` | Staff and management personnel |
| `notifications` | System alerts for users |
| `audit_trail` | Change tracking for security and compliance |
| `australia_post_tier_specs` | Mapping of tier-specific cleaning requirements |

## Appendix B: Function Reference

| Function | Purpose |
|----------|---------|
| `generate_tier_based_cleaning_specs` | Creates cleaning specifications based on facility tier |
| `setup_facility_periodicals` | Creates standard periodical services for a facility |
| `initialize_new_facility` | Onboards a new facility with appropriate cleaning specs |
| `get_facility_cleaning_frequency` | Retrieves cleaning frequency for a specific task |
| `get_day_scheduled_tasks` | Lists all tasks scheduled for a specific day |
| `get_pending_periodicals` | Lists upcoming and overdue periodical services |
| `generate_scheduled_tasks` | Creates daily task assignments based on specifications |
| `calculate_next_service_date` | Determines the next service date based on frequency |