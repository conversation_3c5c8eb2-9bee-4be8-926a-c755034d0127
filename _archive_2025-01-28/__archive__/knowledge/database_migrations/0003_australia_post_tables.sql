-- Australia Post and Star Track specific tables

-- Australia Post sites table
CREATE TABLE IF NOT EXISTS "australia_post_sites" (
  "id" TEXT PRIMARY KEY,
  "site_id" TEXT REFERENCES "sites"("id") ON DELETE CASCADE,
  "facility_code" TEXT,
  "facility_type" TEXT,
  "tier" INTEGER NOT NULL,
  "region" TEXT,
  "operational_hours" JSONB,
  "has_retail" BOOLEAN DEFAULT FALSE,
  "has_production" BOOLEAN DEFAULT FALSE,
  "has_loading_dock" BOOLEAN DEFAULT FALSE,
  "has_mezzanine" BOOLEAN DEFAULT FALSE,
  "special_requirements" TEXT,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

CREATE INDEX IF NOT EXISTS "idx_ap_sites_site" ON "australia_post_sites" ("site_id");
CREATE INDEX IF NOT EXISTS "idx_ap_sites_tier" ON "australia_post_sites" ("tier");
CREATE INDEX IF NOT EXISTS "idx_ap_sites_region" ON "australia_post_sites" ("region");
CREATE INDEX IF NOT EXISTS "idx_ap_sites_type" ON "australia_post_sites" ("facility_type");

-- Australia Post site areas table
CREATE TABLE IF NOT EXISTS "australia_post_site_areas" (
  "id" TEXT PRIMARY KEY,
  "ap_site_id" TEXT REFERENCES "australia_post_sites"("id") ON DELETE CASCADE,
  "area_name" TEXT NOT NULL,
  "area_type" TEXT NOT NULL,
  "floor_level" TEXT,
  "floor_area_sqm" DECIMAL,
  "special_requirements" TEXT,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

ALTER TABLE "australia_post_site_areas" ADD COLUMN IF NOT EXISTS "ap_site_id" TEXT;
CREATE INDEX IF NOT EXISTS "idx_ap_areas_site" ON "australia_post_site_areas" ("ap_site_id");
CREATE INDEX IF NOT EXISTS "idx_ap_areas_type" ON "australia_post_site_areas" ("area_type");

-- Australia Post tier specifications table
CREATE TABLE IF NOT EXISTS "australia_post_tier_specs" (
  "id" TEXT PRIMARY KEY,
  "tier" INTEGER NOT NULL,
  "description" TEXT,
  "cleaning_frequency_multiplier" DECIMAL DEFAULT 1.0,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

CREATE UNIQUE INDEX IF NOT EXISTS "idx_ap_tier_specs_tier" ON "australia_post_tier_specs" ("tier");

-- Apply changes to cleaning_areas from 0001 to match intended state in 0003
ALTER TABLE "cleaning_areas" RENAME COLUMN "category" TO "area_category";
ALTER TABLE "cleaning_areas" DROP COLUMN IF EXISTS "description";
DROP INDEX IF EXISTS "idx_area_category"; -- Drop index on old column name from 0001
CREATE INDEX IF NOT EXISTS "idx_cleaning_areas_category" ON "cleaning_areas" ("area_category"); -- Create index on new column name

-- Apply changes to cleaning_tasks from 0001 to match intended state in 0003
-- Drop the category column (implicitly drops associated index idx_task_category from 0001)
ALTER TABLE "cleaning_tasks" DROP COLUMN IF EXISTS "category";
-- Note: ON DELETE CASCADE from the original CREATE TABLE definition in this file is NOT added here. Handle separately if required.
-- Indexes idx_task_area and idx_task_type from 0001 should remain untouched by this column drop.

-- Cleaning frequencies table
CREATE TABLE IF NOT EXISTS "cleaning_frequencies" (
  "id" TEXT PRIMARY KEY,
  "name" TEXT NOT NULL,
  "description" TEXT,
  "times_per_year" INTEGER,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

CREATE UNIQUE INDEX IF NOT EXISTS "idx_cleaning_frequencies_name" ON "cleaning_frequencies" ("name");

-- Australia Post cleaning specifications table
CREATE TABLE IF NOT EXISTS "australia_post_cleaning_specs" (
  "id" TEXT PRIMARY KEY,
  "ap_site_id" TEXT REFERENCES "australia_post_sites"("id") ON DELETE CASCADE,
  "area_id" TEXT REFERENCES "cleaning_areas"("id"),
  "task_id" TEXT REFERENCES "cleaning_tasks"("id"),
  "tier_1_frequency_id" TEXT REFERENCES "cleaning_frequencies"("id"),
  "tier_2_frequency_id" TEXT REFERENCES "cleaning_frequencies"("id"),
  "tier_3_5_frequency_id" TEXT REFERENCES "cleaning_frequencies"("id"),
  "special_instructions" TEXT,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

ALTER TABLE "australia_post_cleaning_specs" ADD COLUMN IF NOT EXISTS "ap_site_id" TEXT;
CREATE INDEX IF NOT EXISTS "idx_ap_specs_site" ON "australia_post_cleaning_specs" ("ap_site_id");
CREATE INDEX IF NOT EXISTS "idx_ap_specs_area" ON "australia_post_cleaning_specs" ("area_id");
CREATE INDEX IF NOT EXISTS "idx_ap_specs_task" ON "australia_post_cleaning_specs" ("task_id");

-- Scheduled cleaning tasks table
CREATE TABLE IF NOT EXISTS "scheduled_cleaning_tasks" (
  "id" TEXT PRIMARY KEY,
  "ap_site_id" TEXT REFERENCES "australia_post_sites"("id") ON DELETE CASCADE,
  "task_id" TEXT REFERENCES "cleaning_tasks"("id"),
  "scheduled_date" DATE NOT NULL,
  "scheduled_time" TIME,
  "assigned_to" TEXT REFERENCES "users"("id"),
  "status" TEXT DEFAULT 'scheduled',
  "completion_time" TIMESTAMP WITH TIME ZONE,
  "completion_notes" TEXT,
  "quality_check_required" BOOLEAN DEFAULT FALSE,
  "quality_check_completed" BOOLEAN DEFAULT FALSE,
  "quality_check_by" TEXT REFERENCES "users"("id"),
  "quality_check_notes" TEXT,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

ALTER TABLE "scheduled_cleaning_tasks" ADD COLUMN IF NOT EXISTS "ap_site_id" TEXT;
CREATE INDEX IF NOT EXISTS "idx_scheduled_tasks_site" ON "scheduled_cleaning_tasks" ("ap_site_id");
CREATE INDEX IF NOT EXISTS "idx_scheduled_tasks_date" ON "scheduled_cleaning_tasks" ("scheduled_date");
CREATE INDEX IF NOT EXISTS "idx_scheduled_tasks_assigned" ON "scheduled_cleaning_tasks" ("assigned_to");
CREATE INDEX IF NOT EXISTS "idx_scheduled_tasks_status" ON "scheduled_cleaning_tasks" ("status");

-- Periodical services table
CREATE TABLE IF NOT EXISTS "periodical_services" (
  "id" TEXT PRIMARY KEY,
  "ap_site_id" TEXT REFERENCES "australia_post_sites"("id") ON DELETE CASCADE,
  "service_name" TEXT NOT NULL,
  "service_description" TEXT,
  "frequency_id" TEXT REFERENCES "cleaning_frequencies"("id"),
  "last_service_date" DATE,
  "next_service_date" DATE,
  "assigned_to" TEXT REFERENCES "users"("id"),
  "status" TEXT DEFAULT 'scheduled',
  "special_requirements" TEXT,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

ALTER TABLE "periodical_services" ADD COLUMN IF NOT EXISTS "ap_site_id" TEXT;
CREATE INDEX IF NOT EXISTS "idx_periodical_site" ON "periodical_services" ("ap_site_id");
CREATE INDEX IF NOT EXISTS "idx_periodical_next_date" ON "periodical_services" ("next_service_date");
CREATE INDEX IF NOT EXISTS "idx_periodical_status" ON "periodical_services" ("status");

-- Retail cleaning scope table
CREATE TABLE IF NOT EXISTS "retail_cleaning_scope" (
  "id" TEXT PRIMARY KEY,
  "area" TEXT NOT NULL,
  "element" TEXT NOT NULL,
  "requirement" TEXT NOT NULL,
  "frequency_id" TEXT REFERENCES "cleaning_frequencies"("id"),
  "category" TEXT NOT NULL,
  "notes" TEXT,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

CREATE INDEX IF NOT EXISTS "idx_retail_scope_area" ON "retail_cleaning_scope" ("area");
CREATE INDEX IF NOT EXISTS "idx_retail_scope_category" ON "retail_cleaning_scope" ("category");

-- Inspections table
CREATE TABLE IF NOT EXISTS "inspections" (
  "id" TEXT PRIMARY KEY,
  "ap_site_id" TEXT REFERENCES "australia_post_sites"("id") ON DELETE CASCADE,
  "inspection_date" DATE NOT NULL,
  "inspector_id" TEXT REFERENCES "users"("id"),
  "overall_score" DECIMAL,
  "pass_threshold" DECIMAL DEFAULT 85.0,
  "status" TEXT DEFAULT 'scheduled',
  "notes" TEXT,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

ALTER TABLE "inspections" ADD COLUMN IF NOT EXISTS "ap_site_id" TEXT;
CREATE INDEX IF NOT EXISTS "idx_inspections_site" ON "inspections" ("ap_site_id");
CREATE INDEX IF NOT EXISTS "idx_inspections_date" ON "inspections" ("inspection_date");
CREATE INDEX IF NOT EXISTS "idx_inspections_inspector" ON "inspections" ("inspector_id");
CREATE INDEX IF NOT EXISTS "idx_inspections_status" ON "inspections" ("status");

-- Inspection items table
CREATE TABLE IF NOT EXISTS "inspection_items" (
  "id" TEXT PRIMARY KEY,
  "inspection_id" TEXT REFERENCES "inspections"("id") ON DELETE CASCADE,
  "area_id" TEXT REFERENCES "cleaning_areas"("id"),
  "item_name" TEXT NOT NULL,
  "score" DECIMAL,
  "max_score" DECIMAL DEFAULT 10.0,
  "comments" TEXT,
  "photo_url" TEXT,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

CREATE INDEX IF NOT EXISTS "idx_inspection_items_inspection" ON "inspection_items" ("inspection_id");
CREATE INDEX IF NOT EXISTS "idx_inspection_items_area" ON "inspection_items" ("area_id");

-- Action items table
CREATE TABLE IF NOT EXISTS "action_items" (
  "id" TEXT PRIMARY KEY,
  "inspection_id" TEXT REFERENCES "inspections"("id") ON DELETE CASCADE,
  "inspection_item_id" TEXT REFERENCES "inspection_items"("id") ON DELETE CASCADE,
  "description" TEXT NOT NULL,
  "priority" TEXT DEFAULT 'medium',
  "due_date" DATE,
  "assigned_to" TEXT REFERENCES "users"("id"),
  "status" TEXT DEFAULT 'open',
  "resolution" TEXT,
  "resolved_date" DATE,
  "resolved_by" TEXT REFERENCES "users"("id"),
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

CREATE INDEX IF NOT EXISTS "idx_action_items_inspection" ON "action_items" ("inspection_id");
CREATE INDEX IF NOT EXISTS "idx_action_items_item" ON "action_items" ("inspection_item_id");
CREATE INDEX IF NOT EXISTS "idx_action_items_assigned" ON "action_items" ("assigned_to");
CREATE INDEX IF NOT EXISTS "idx_action_items_status" ON "action_items" ("status");
CREATE INDEX IF NOT EXISTS "idx_action_items_due" ON "action_items" ("due_date");
