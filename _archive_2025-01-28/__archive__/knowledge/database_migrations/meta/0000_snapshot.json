{"id": "d52831a2-258b-4150-a152-6770e9ade990", "prevId": "00000000-0000-0000-0000-000000000000", "version": "5", "dialect": "pg", "tables": {"chat_participants": {"name": "chat_participants", "schema": "", "columns": {"session_id": {"name": "session_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "joined_at": {"name": "joined_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "last_read_at": {"name": "last_read_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"chat_participants_session_id_chat_sessions_id_fk": {"name": "chat_participants_session_id_chat_sessions_id_fk", "tableFrom": "chat_participants", "tableTo": "chat_sessions", "columnsFrom": ["session_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "chat_participants_user_id_users_id_fk": {"name": "chat_participants_user_id_users_id_fk", "tableFrom": "chat_participants", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"chat_participants_session_id_user_id_pk": {"name": "chat_participants_session_id_user_id_pk", "columns": ["session_id", "user_id"]}}, "uniqueConstraints": {}}, "chat_sessions": {"name": "chat_sessions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "preview": {"name": "preview", "type": "text", "primaryKey": false, "notNull": false}, "date": {"name": "date", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "is_starred": {"name": "is_starred", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"chat_sessions_created_by_users_id_fk": {"name": "chat_sessions_created_by_users_id_fk", "tableFrom": "chat_sessions", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "companies": {"name": "companies", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "primary_contact_id": {"name": "primary_contact_id", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"companies_primary_contact_id_contacts_id_fk": {"name": "companies_primary_contact_id_contacts_id_fk", "tableFrom": "companies", "tableTo": "contacts", "columnsFrom": ["primary_contact_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "contacts": {"name": "contacts", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "company": {"name": "company", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": false}, "is_favorite": {"name": "is_favorite", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "avatar": {"name": "avatar", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "dashboard_metrics": {"name": "dashboard_metrics", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "metric_type": {"name": "metric_type", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "period": {"name": "period", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "property_id": {"name": "property_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_metric_type_date": {"name": "idx_metric_type_date", "columns": ["metric_type", "created_at"], "isUnique": false}, "idx_metric_property": {"name": "idx_metric_property", "columns": ["property_id"], "isUnique": false}}, "foreignKeys": {"dashboard_metrics_property_id_properties_id_fk": {"name": "dashboard_metrics_property_id_properties_id_fk", "tableFrom": "dashboard_metrics", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "inspection_actions": {"name": "inspection_actions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "report_id": {"name": "report_id", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "priority": {"name": "priority", "type": "text", "primaryKey": false, "notNull": true}, "assignee": {"name": "assignee", "type": "text", "primaryKey": false, "notNull": true}, "due_date": {"name": "due_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"idx_action_report": {"name": "idx_action_report", "columns": ["report_id"], "isUnique": false}, "idx_action_assignee": {"name": "idx_action_assignee", "columns": ["assignee"], "isUnique": false}}, "foreignKeys": {"inspection_actions_report_id_inspection_reports_id_fk": {"name": "inspection_actions_report_id_inspection_reports_id_fk", "tableFrom": "inspection_actions", "tableTo": "inspection_reports", "columnsFrom": ["report_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "inspection_actions_assignee_users_id_fk": {"name": "inspection_actions_assignee_users_id_fk", "tableFrom": "inspection_actions", "tableTo": "users", "columnsFrom": ["assignee"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "inspection_reports": {"name": "inspection_reports", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "property_id": {"name": "property_id", "type": "text", "primaryKey": false, "notNull": true}, "template_id": {"name": "template_id", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "inspector": {"name": "inspector", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "score": {"name": "score", "type": "integer", "primaryKey": false, "notNull": false}, "summary": {"name": "summary", "type": "text", "primaryKey": false, "notNull": false}, "sections": {"name": "sections", "type": "jsonb", "primaryKey": false, "notNull": false}, "weather_conditions": {"name": "weather_conditions", "type": "text", "primaryKey": false, "notNull": false}, "temperature": {"name": "temperature", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "photos": {"name": "photos", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "voice_notes": {"name": "voice_notes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "submitted_at": {"name": "submitted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "reviewed_by": {"name": "reviewed_by", "type": "text", "primaryKey": false, "notNull": false}, "reviewed_at": {"name": "reviewed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"idx_report_property": {"name": "idx_report_property", "columns": ["property_id"], "isUnique": false}, "idx_inspection_date": {"name": "idx_inspection_date", "columns": ["date"], "isUnique": false}, "idx_inspection_inspector": {"name": "idx_inspection_inspector", "columns": ["inspector"], "isUnique": false}}, "foreignKeys": {"inspection_reports_property_id_properties_id_fk": {"name": "inspection_reports_property_id_properties_id_fk", "tableFrom": "inspection_reports", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "inspection_reports_template_id_inspection_templates_id_fk": {"name": "inspection_reports_template_id_inspection_templates_id_fk", "tableFrom": "inspection_reports", "tableTo": "inspection_templates", "columnsFrom": ["template_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "inspection_reports_inspector_users_id_fk": {"name": "inspection_reports_inspector_users_id_fk", "tableFrom": "inspection_reports", "tableTo": "users", "columnsFrom": ["inspector"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "inspection_reports_reviewed_by_users_id_fk": {"name": "inspection_reports_reviewed_by_users_id_fk", "tableFrom": "inspection_reports", "tableTo": "users", "columnsFrom": ["reviewed_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "inspection_templates": {"name": "inspection_templates", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "version": {"name": "version", "type": "integer", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "sections": {"name": "sections", "type": "jsonb", "primaryKey": false, "notNull": true}}, "indexes": {"idx_template_category": {"name": "idx_template_category", "columns": ["category"], "isUnique": false}}, "foreignKeys": {"inspection_templates_created_by_users_id_fk": {"name": "inspection_templates_created_by_users_id_fk", "tableFrom": "inspection_templates", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "kpi_targets": {"name": "kpi_targets", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "metric_type": {"name": "metric_type", "type": "text", "primaryKey": false, "notNull": true}, "target_value": {"name": "target_value", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "period": {"name": "period", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "property_type": {"name": "property_type", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_kpi_type": {"name": "idx_kpi_type", "columns": ["metric_type"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "messages": {"name": "messages", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "session_id": {"name": "session_id", "type": "text", "primaryKey": false, "notNull": false}, "content_type": {"name": "content_type", "type": "text", "primaryKey": false, "notNull": false}, "content_transcript": {"name": "content_transcript", "type": "text", "primaryKey": false, "notNull": false}, "object": {"name": "object", "type": "text", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": false}, "sender_id": {"name": "sender_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_session_created_at": {"name": "idx_session_created_at", "columns": ["session_id", "created_at"], "isUnique": false}}, "foreignKeys": {"messages_session_id_chat_sessions_id_fk": {"name": "messages_session_id_chat_sessions_id_fk", "tableFrom": "messages", "tableTo": "chat_sessions", "columnsFrom": ["session_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "messages_sender_id_users_id_fk": {"name": "messages_sender_id_users_id_fk", "tableFrom": "messages", "tableTo": "users", "columnsFrom": ["sender_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "properties": {"name": "properties", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": true}, "suburb": {"name": "suburb", "type": "text", "primaryKey": false, "notNull": true}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": true}, "postcode": {"name": "postcode", "type": "<PERSON><PERSON><PERSON>(4)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "tier": {"name": "tier", "type": "integer", "primaryKey": false, "notNull": true}, "region": {"name": "region", "type": "text", "primaryKey": false, "notNull": true}, "size_sqm": {"name": "size_sqm", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'active'"}, "manager_id": {"name": "manager_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"idx_property_manager": {"name": "idx_property_manager", "columns": ["manager_id"], "isUnique": false}, "idx_property_status": {"name": "idx_property_status", "columns": ["status"], "isUnique": false}, "idx_property_region": {"name": "idx_property_region", "columns": ["region"], "isUnique": false}}, "foreignKeys": {"properties_manager_id_users_id_fk": {"name": "properties_manager_id_users_id_fk", "tableFrom": "properties", "tableTo": "users", "columnsFrom": ["manager_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "property_areas": {"name": "property_areas", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "property_id": {"name": "property_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "size_sqm": {"name": "size_sqm", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "floor_level": {"name": "floor_level", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"property_areas_property_id_properties_id_fk": {"name": "property_areas_property_id_properties_id_fk", "tableFrom": "property_areas", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "report_attachments": {"name": "report_attachments", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "report_id": {"name": "report_id", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true}, "thumbnail_url": {"name": "thumbnail_url", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"report_attachments_report_id_inspection_reports_id_fk": {"name": "report_attachments_report_id_inspection_reports_id_fk", "tableFrom": "report_attachments", "tableTo": "inspection_reports", "columnsFrom": ["report_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "roles": {"name": "roles", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "permissions": {"name": "permissions", "type": "jsonb", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "user_sessions": {"name": "user_sessions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_sessions_user_id_users_id_fk": {"name": "user_sessions_user_id_users_id_fk", "tableFrom": "user_sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "department": {"name": "department", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "avatar": {"name": "avatar", "type": "text", "primaryKey": false, "notNull": false}, "preferences": {"name": "preferences", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "last_login": {"name": "last_login", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}}}, "enums": {}, "schemas": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}