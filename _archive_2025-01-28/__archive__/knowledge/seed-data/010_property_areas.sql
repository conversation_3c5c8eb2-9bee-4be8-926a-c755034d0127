-- Seed data for property_areas table
-- Detailed area breakdown for each property based on cleaning specifications

-- Star Track Minchinbury (Tier 1 site)
INSERT INTO "property_areas" ("id", "property_id", "name", "type", "size_sqm", "floor_level", "notes")
VALUES
  ('area-st-minch-admin', 'st-ap02258', 'Administration Area', 'Office', 1200, 'Ground', 'Multiple office spaces, includes break rooms'),
  ('area-st-minch-depot', 'st-ap02258', 'Main Depot', 'Warehouse', 20000, 'Ground', 'Primary sorting and storage area'),
  ('area-st-minch-dock', 'st-ap02258', 'Loading Dock', 'Loading Area', 5000, 'Ground', 'Loading and unloading zone'),
  ('area-st-minch-mezzanine', 'st-ap02258', 'Mezzanine Level', 'Warehouse', 1500, 'Level 1', 'Elevated sorting area'),
  ('area-st-minch-toilets-admin', 'st-ap02258', 'Administration Toilets', 'Amenities', 80, 'Ground', 'Adjacent to admin offices'),
  ('area-st-minch-toilets-depot', 'st-ap02258', 'Depot Toilets', 'Amenities', 120, 'Ground', 'High usage area'),
  ('area-st-minch-kitchen', 'st-ap02258', 'Main Kitchen', 'Amenities', 60, 'Ground', 'Central kitchen facilities'),
  ('area-st-minch-external', 'st-ap02258', 'External Areas', 'External', 250, 'Ground', 'Entry, smoking area, pathways'),

-- Star Track Coffs Harbour (Tier 2 site)
  ('area-st-coffs-office', 'st-ap02405', 'Main Office', 'Office', 400, 'Ground', 'Admin and customer service area'),
  ('area-st-coffs-warehouse', 'st-ap02405', 'Warehouse', 'Warehouse', 3000, 'Ground', 'Processing and storage'),
  ('area-st-coffs-dock', 'st-ap02405', 'Loading Dock', 'Loading Area', 300, 'Ground', 'Vehicle access point'),
  ('area-st-coffs-amenities', 'st-ap02405', 'Amenities', 'Amenities', 60, 'Ground', 'Combined toilet and kitchen'),

-- Star Track Tullamarine (Tier 1 site)
  ('area-st-tull-admin', 'st-ap02327', 'Administration Building', 'Office', 900, 'Ground', 'Head office functions'),
  ('area-st-tull-warehouse1', 'st-ap02327', 'Warehouse Block 1', 'Warehouse', 7000, 'Ground', 'Primary distribution'),
  ('area-st-tull-warehouse2', 'st-ap02327', 'Warehouse Block 2', 'Warehouse', 6500, 'Ground', 'Secondary distribution'),
  ('area-st-tull-mezzanine', 'st-ap02327', 'Mezzanine Area', 'Warehouse', 1000, 'Level 1', 'Administration and viewing area'),
  ('area-st-tull-amenities', 'st-ap02327', 'Staff Amenities', 'Amenities', 85, 'Ground', 'Multiple toilet blocks and break areas'),

-- Star Track Perth Airport (Tier 5 site)
  ('area-st-perth-office', 'st-ap02482', 'Terminal Office', 'Office', 250, 'Ground', 'Small admin facility'),
  ('area-st-perth-warehouse', 'st-ap02482', 'Processing Area', 'Warehouse', 20000, 'Ground', 'Freight processing'),
  ('area-st-perth-dock', 'st-ap02482', 'Aircraft Dock', 'Loading Area', 3000, 'Ground', 'Aircraft to truck transfer area'),
  ('area-st-perth-amenities', 'st-ap02482', 'Basic Amenities', 'Amenities', 40, 'Ground', 'Toilet and break area'),

-- Australia Post Bankstown (Retail)
  ('area-ap-bank-retail', 'ap-ap00121', 'Retail Area', 'Retail', 200, 'Ground', 'Customer service area'),
  ('area-ap-bank-counter', 'ap-ap00121', 'Service Counter', 'Retail', 40, 'Ground', 'Transaction processing'),
  ('area-ap-bank-backoffice', 'ap-ap00121', 'Back Office', 'Office', 60, 'Ground', 'Staff workspace'),
  ('area-ap-bank-pobox', 'ap-ap00121', 'PO Box Area', 'Retail', 30, 'Ground', '24/7 accessible'),
  ('area-ap-bank-amenities', 'ap-ap00121', 'Staff Amenities', 'Amenities', 20, 'Ground', 'Staff facilities'),

-- Australia Post Minto (Industrial/Equipment Depot)
  ('area-ap-minto-workshop', 'ap-ap02208', 'Equipment Workshop', 'Workshop', 400, 'Ground', 'Vehicle maintenance'),
  ('area-ap-minto-storage', 'ap-ap02208', 'Equipment Storage', 'Warehouse', 800, 'Ground', 'Tool and part storage'),
  ('area-ap-minto-office', 'ap-ap02208', 'Management Office', 'Office', 80, 'Ground', 'Workshop administration'),
  ('area-ap-minto-washbay', 'ap-ap02208', 'Vehicle Wash Bay', 'External', 120, 'Ground', 'Vehicle cleaning area'),
  ('area-ap-minto-amenities', 'ap-ap02208', 'Workshop Amenities', 'Amenities', 40, 'Ground', 'Heavy-duty cleaning facilities'),

-- Australia Post Newcastle (Letter Facility)
  ('area-ap-newc-sorting', 'ap-ap02055', 'Letter Sorting Area', 'Warehouse', 5000, 'Ground', 'Automated sorting equipment'),
  ('area-ap-newc-loading', 'ap-ap02055', 'Loading Bay', 'Loading Area', 800, 'Ground', 'Mail transfer point'),
  ('area-ap-newc-office', 'ap-ap02055', 'Facility Office', 'Office', 300, 'Ground', 'Operations management'),
  ('area-ap-newc-amenities', 'ap-ap02055', 'Staff Facilities', 'Amenities', 100, 'Ground', 'Shift worker amenities'),

-- Australia Post Bondi Junction (Mixed Use)
  ('area-ap-bondi-retail', 'ap-ap02378', 'Retail Front', 'Retail', 150, 'Ground', 'Shopping centre location'),
  ('area-ap-bondi-parcel', 'ap-ap02378', 'Parcel Area', 'Warehouse', 200, 'Ground', 'Parcel processing'),
  ('area-ap-bondi-pobox', 'ap-ap02378', 'PO Box Lobby', 'Retail', 50, 'Ground', 'External access 24/7'),
  ('area-ap-bondi-office', 'ap-ap02378', 'Back Office', 'Office', 30, 'Ground', 'Administration'),
  ('area-ap-bondi-amenities', 'ap-ap02378', 'Staff Area', 'Amenities', 20, 'Ground', 'Staff-only facilities')
ON CONFLICT (id) DO NOTHING;

-- Seed data for australia_post_site_areas table
INSERT INTO "australia_post_site_areas" ("id", "ap_site_id", "area_name", "area_type", "floor_level", "floor_area_sqm", "special_requirements", "created_at")
SELECT
  'ap-area-' || pa.id,
  p.id || '-ap-site',
  pa.name,
  pa.type,
  pa.floor_level,
  pa.size_sqm,
  pa.notes,
  NOW()
FROM property_areas pa
JOIN properties p ON pa.property_id = p.id
WHERE p.id LIKE 'ap-%'
ON CONFLICT (id) DO NOTHING;
