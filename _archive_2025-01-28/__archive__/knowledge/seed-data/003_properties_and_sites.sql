-- Insert Star Track properties based on the actual Star Track Site List from the documentation
INSERT INTO "properties" ("id", "name", "address", "suburb", "state", "postcode", "type", "tier", "region", "size_sqm", "category", "status", "manager_id", "created_at")
VALUES 
  -- Star Track Facilities (from document 1)
  ('st-ap02252', 'HUME-TRALEE ST-ST', '2 Tralee Street', 'Hume', 'ACT', '2620', 'Industrial and Logistics', 2, 'Metro', 4241, 'Tier 2', 'active', 'mark-brady', NOW()),
  ('st-ap02405', 'COFFS HARBOUR-WINGARA DR-ST', '14 Wingara Dr', 'Coffs Harbour', 'NSW', '2450', 'Industrial and Logistics', 2, 'Regional', 3801, 'Tier 2', 'active', 'mark-brady', NOW()),
  ('st-ap02445', 'LISMORE SOUTH-WILSON ST-ST', '20 Wilson St', 'South Lismore', 'NSW', '2480', 'Industrial and Logistics', 2, 'Regional', 1273, 'Tier 2', 'active', 'mark-brady', NOW()),
  ('st-ap02429', 'GREENACRE-ROBERTS RD-DEL', '77 - 79 Roberts Rd', 'Greenacre', 'NSW', '2190', 'Industrial and Logistics', 2, 'Metro', 16423, 'Tier 2', 'active', 'mark-brady', NOW()),
  ('st-ap02232', 'TUGGERAH-RELIANCE DR-ST', '25 Reliance Drive', 'Tuggerah', 'NSW', '2259', 'Industrial and Logistics', 2, 'Regional', 1613, 'Tier 2', 'active', 'mark-brady', NOW()),
  ('st-ap02233', 'EDGEWORTH-ALUMINIUM CL-ST', '9 & 11 Allumium Close', 'Edgeworth', 'NSW', '2285', 'Industrial and Logistics', 2, 'Regional', 3474, 'Tier 2', 'active', 'mark-brady', NOW()),
  ('st-ap02240', 'TAMWORTH-ARMSTRONG ST-ST', '29-35 Armsrong Street', 'Tamworth', 'NSW', '2340', 'Industrial and Logistics', 3, 'Regional', 1395, 'Tier 3', 'active', 'mark-brady', NOW()),
  ('st-ap02246', 'UNANDERRA-INDUSTRIAL RD-ST', '4 Industrial Rd', 'Unandarra', 'NSW', '2526', 'Industrial and Logistics', 3, 'Regional', 2685, 'Tier 3', 'active', 'mark-brady', NOW()),
  ('st-ap02216', 'BOTANY-HALE ST-DEL', '1 Hale St', 'Botany', 'NSW', '2019', 'Industrial and Logistics', 2, 'Metro', 24123, 'Tier 2', 'active', 'mark-brady', NOW()),
  ('st-ap02254', 'ALBURY NORTH-BORONIA ST-ST', '80 Boronia Street', 'Albury', 'NSW', '2640', 'Industrial and Logistics', 3, 'Regional', 2049, 'Tier 3', 'active', 'mark-brady', NOW()),
  ('st-ap02255', 'WAGGA WAGGA-BALL PL-ST', '5 Ball Place', 'Wagga Wagga', 'NSW', '2650', 'Industrial and Logistics', 3, 'Regional', 2349, 'Tier 3', 'active', 'mark-brady', NOW()),
  ('st-ap02488', 'PORT MACQUARIE-GEEBUNG DR-ST', '7/14 Geebug Dr', 'Port Macquarie', 'NSW', '2444', 'Industrial and Logistics', 2, 'Remote', 1977, 'Tier 2', 'active', 'mark-brady', NOW()),
  ('st-ap02258', 'MINCHINBURYRE-51 SARGENTSRD-ST', '51 Sargents Road', 'Minchinbury', 'NSW', '2770', 'Industrial and Logistics', 1, 'Metro', 27792, 'Tier 1', 'active', 'mark-brady', NOW()),
  ('st-ap02257', 'MINCHINBURY-32 SARGENTS RD-ST', '32 Sargent Rd', 'Minchinbury', 'NSW', '2770', 'Industrial and Logistics', 2, 'Metro', 19993, 'Tier 2', 'active', 'mark-brady', NOW()),
  ('st-ap02260', 'MINCHINBURY BULK-90SARGENT-ST', '80-90 Sargents Rd', 'Minchinbury', 'NSW', '2770', 'Industrial and Logistics', 2, 'Metro', 8606, 'Tier 2', 'active', 'mark-brady', NOW()),
  ('st-ap02399', 'CHULLORA-RE 20 WORTH ST-ST', '20 Worth St', 'Chullora', 'NSW', '2190', 'Industrial and Logistics', 2, 'Metro', 8482, 'Tier 2', 'active', 'mark-brady', NOW()),
  ('st-ap03210', 'MT KURING-GAI-WOODLAND WAY-MX', '14 Woodland Way', 'Mount Kuring-Gai', 'NSW', '2080', 'Industrial and Logistics', 3, 'Metro', 4591, 'Tier 3', 'active', 'mark-brady', NOW()),
  ('st-ap02432', 'HELENSVALE-MILLAROO DR-ST', '83 Milaroo Drive', 'Helensvale', 'QLD', '4212', 'Industrial and Logistics', 2, 'Regional', 3324, 'Tier 2', 'active', 'mark-brady', NOW()),
  ('st-ap02433', 'HELENSVALE-88-92 MILLAROODR-ST', '88-92 Milaroo Drive', 'Helensvale', 'QLD', '4212', 'Industrial and Logistics', 2, 'Regional', 2950, 'Tier 2', 'active', 'mark-brady', NOW()),
  ('st-ap02320', 'TOWNSVILLE-WEBB DR-ST', '139-147 Webb Drive', 'Bohle', 'QLD', '4818', 'Industrial and Logistics', 2, 'Regional', 2889, 'Tier 2', 'active', 'mark-brady', NOW()),
  ('st-ap02312', 'NORMAN GARDENS-MACARTNEY ST-ST', '2-16 Macartney St', 'Norman Gardens', 'QLD', '4701', 'Industrial and Logistics', 3, 'Regional', 1726, 'Tier 3', 'active', 'mark-brady', NOW()),
  ('st-ap02315', 'PAGET-COMMERCIAL AVE-ST', '3 Commercial Ave', 'Paget', 'QLD', '4740', 'Industrial and Logistics', 3, 'Regional', 1680, 'Tier 3', 'active', 'mark-brady', NOW()),
  ('st-ap02302', 'TOOWOOMBA-5 TAIT ST-ST', '5 Tait Street', 'Toowoomba', 'QLD', '4350', 'Industrial and Logistics', 3, 'Regional', 1300, 'Tier 3', 'active', 'mark-brady', NOW()),
  ('st-ap02307', 'YANDINA-CENTRAL PARK DR-ST', '51 Central Park Drive', 'Yandina', 'QLD', '4561', 'Industrial and Logistics', 3, 'Regional', 2599, 'Tier 3', 'active', 'mark-brady', NOW()),
  ('st-ap02291', 'BRISBANEAIRPRT-WESTRINGIARD-ST', '11 Westringia Road', 'Brisbane Airport', 'QLD', '4009', 'Industrial and Logistics', 2, 'Metro', 8168, 'Tier 2', 'active', 'mark-brady', NOW()),
  ('st-ap02295', 'DARRA 3PL-EBBERN ST-ST', 'Lot 12 Ebbern St', 'Darra', 'QLD', '4076', 'Industrial and Logistics', 2, 'Metro', 6161, 'Tier 2', 'active', 'mark-brady', NOW()),
  ('st-ap02294', 'DARRA RE-GRAVEL PIT RD-ST', '39-43 Gravel Pit Road', 'Darra', 'QLD', '4076', 'Industrial and Logistics', 1, 'Metro', 23637, 'Tier 1', 'active', 'mark-brady', NOW()),
  ('st-ap02973', 'PINKENBA-231 HOLT ST-ST', '231 Holt St', 'Pinkenba', 'QLD', '4008', 'Industrial and Logistics', 3, 'Metro', 5471, 'Tier 3', 'active', 'mark-brady', NOW()),
  ('st-ap02523', 'WINGFIELD-GRAND JUNCTION RD-MX', '519-523 Grand Junction Road', 'Wingfield', 'SA', '5013', 'Industrial and Logistics', 2, 'Metro', 17738, 'Tier 2', 'active', 'mark-brady', NOW()),
  ('st-ap02470', 'MSE-SOUTH PARK DR-ST', '16-32 South Park Dr', 'Dandenong', 'VIC', '3175', 'Industrial and Logistics', 2, 'Metro', 12729, 'Tier 2', 'active', 'mark-brady', NOW()),
  ('st-ap02351', 'EAST BENDIGO-ALSTONVALE DR-ST', '6 Alstonvale drive', 'Bendigo East', 'VIC', '3550', 'Industrial and Logistics', 3, 'Regional', 935, 'Tier 3', 'active', 'mark-brady', NOW()),
  ('st-ap03280', 'LEMNOS-50 LEMNOS NORTH RD-DEL', '50 Lemnos North Road', 'Lemnos', 'VIC', '3631', 'Industrial and Logistics', 5, 'Regional', 2453, 'Tier 5', 'active', 'mark-brady', NOW()),
  ('st-ap02365', 'TRARALGON EAST-EASTERN RD-ST', '23 Eastern Road', 'Traralgon', 'VIC', '3844', 'Industrial and Logistics', 3, 'Regional', 1094, 'Tier 3', 'active', 'mark-brady', NOW()),
  ('st-ap02327', 'TULLAMARINE-140 SHARPS RD-ST', '140 Sharps Road', 'Tullamarine', 'VIC', '3045', 'Industrial and Logistics', 1, 'Metro', 14571, 'Tier 1', 'active', 'mark-brady', NOW()),
  ('st-ap02330', 'TULLAMARINE-38-40 ANNANDALE-ST', '38 Annandale Road', 'Tullamarine', 'VIC', '3043', 'Industrial and Logistics', 1, 'Metro', 44424, 'Tier 1', 'active', 'mark-brady', NOW()),
  ('st-ap02331', 'TULLAMARINE-45-55 ANNANDALE-ST', '45-55 Annandale Road', 'Tullamarine', 'VIC', '3054', 'Industrial and Logistics', 1, 'Metro', 21115, 'Tier 1', 'active', 'mark-brady', NOW()),
  ('st-ap03183', 'MELBOURNE APT-SKY RD-ST', '55 Sky Road', 'Tullamarine', 'VIC', '3045', 'Industrial and Logistics', 1, 'Metro', 18397, 'Tier 1', 'active', 'mark-brady', NOW()),
  ('st-ap02842', 'TRUGANINA-2 MAKER PL-ST', '2 Maker Pl', 'Truganina', 'VIC', '3029', 'Industrial and Logistics', 1, 'Metro', NULL, 'Tier 1', 'active', 'mark-brady', NOW()),
  ('st-ap02482', 'PERTH AIRPORT-BOUD AVE-ST', '32 Boud Ave, Perth Airport', 'Perth Airport', 'WA', '6105', 'Industrial and Logistics', 5, 'Metro', 23886, 'Tier 5', 'active', 'mark-brady', NOW()),
  ('st-ap02966', 'PERTH AIRPORT-TERMINAL 3-ST', 'Terminal 3, Boud Ave, Qantas shed, Perth Airport, WA', 'Perth Airport', 'WA', '6105', 'Industrial and Logistics', 5, 'Metro', 10423, 'Tier 5', 'active', 'mark-brady', NOW())
ON CONFLICT (id) DO NOTHING;

-- Now add some sample Australia Post properties including mixed-use sites (from document 2)
INSERT INTO "properties" ("id", "name", "address", "suburb", "state", "postcode", "type", "tier", "region", "size_sqm", "category", "status", "manager_id", "created_at")
VALUES 
  -- Mixed use sites
  ('ap-ap00121', 'BANKSTOWN-RESTWELL ST-PO', 'Restwell St', 'Bankstown', 'NSW', '2200', 'Retail', NULL, 'Metro', NULL, 'Post Office', 'active', 'mark-brady', NOW()),
  ('ap-ap02378', 'BONDI JUNCTION-WESTFIELD SC-MX', 'Westfield SC', 'Bondi Junction', 'NSW', '2022', 'Retail', NULL, 'Metro', NULL, 'Parcel Facility', 'active', 'mark-brady', NOW()),
  ('ap-ap00365', 'CAMPBELLTOWN-DUMARESQUE ST-PO', 'Dumaresque St', 'Campbelltown', 'NSW', '2560', 'Retail', NULL, 'Metro', NULL, 'Post Office', 'active', 'mark-brady', NOW()),
  ('ap-ap00520', 'COONABARABRAN-JOHN ST-PO', 'John St', 'Coonabarabran', 'NSW', '2357', 'Retail', NULL, 'Regional', NULL, 'Post Office', 'active', 'mark-brady', NOW()),
  ('ap-ap00525', 'COOTAMUNDRA-WALLENDOON ST-PO', 'Wallendoon St', 'Cootamundra', 'NSW', '2590', 'Retail', NULL, 'Regional', NULL, 'Post Office', 'active', 'mark-brady', NOW()),
  ('ap-ap00551', 'CRONULLA-CRONULLA ST-PO', 'Cronulla St', 'Cronulla', 'NSW', '2230', 'Retail', NULL, 'Metro', NULL, 'Post Office', 'active', 'mark-brady', NOW()),
  -- Some industrial Australia Post sites
  ('ap-ap02208', 'MINTO-LINCOLN ST-OTH', 'Lincoln St', 'Minto', 'NSW', '2566', 'Industrial and Logistics', 2, 'Metro', NULL, 'Equipment Depot', 'active', 'mark-brady', NOW()),
  ('ap-ap02055', 'NEWCASTLE DEL', 'Industrial Dr', 'Newcastle', 'NSW', '2300', 'Industrial and Logistics', 2, 'Regional', NULL, 'Letter Facility', 'active', 'mark-brady', NOW()),
  ('ap-ap02122', 'WETHERILL PARK-VICTORIA ST-DEL', 'Victoria St', 'Wetherill Park', 'NSW', '2164', 'Industrial and Logistics', 2, 'Metro', NULL, 'Letter Facility', 'active', 'mark-brady', NOW()),
  ('ap-ap00059', 'ARCHERFIELD-BOUNDARY RD-DEL', 'Boundary Rd', 'Archerfield', 'QLD', '4108', 'Industrial and Logistics', 2, 'Metro', NULL, 'Letter Facility', 'active', 'mark-brady', NOW()),
  ('ap-ap02316', 'MACKAY-BRIDGE RD-DEL', 'Bridge Rd', 'Mackay', 'QLD', '4740', 'Industrial and Logistics', 3, 'Regional', NULL, 'Letter Facility', 'active', 'mark-brady', NOW()),
  ('ap-ap02465', 'MORETON BAY-SIZER CT-MX', 'Sizer Ct', 'Moreton Bay', 'QLD', '4500', 'Industrial and Logistics', 2, 'Metro', NULL, 'Parcel Facility', 'active', 'mark-brady', NOW()),
  ('ap-ap02631', 'PINKENBA-HOLT ST-DEL', 'Holt St', 'Pinkenba', 'QLD', '4008', 'Industrial and Logistics', 2, 'Metro', NULL, 'Parcel Facility', 'active', 'mark-brady', NOW())
ON CONFLICT (id) DO NOTHING;

-- Insert sites table data for all properties
INSERT INTO "sites" ("id", "client_id", "name", "address", "suburb", "state", "postcode", "site_contact_name", "site_contact_phone", "site_contact_email", "site_type", "tier", "floor_area_sqm", "access_instructions", "special_requirements", "status", "created_at")
SELECT 
  p.id || '-site',
  CASE WHEN p.id LIKE 'st-%' THEN 'star-track-client' ELSE 'aus-post-client' END,
  p.name,
  p.address,
  p.suburb,
  p.state,
  p.postcode,
  'Site Contact',
  '+61 2 5555 5555',
  '<EMAIL>',
  p.type,
  p.tier,
  p.size_sqm,
  'Standard business hours access',
  NULL,
  'active',
  NOW()
FROM properties p
ON CONFLICT (id) DO NOTHING;

-- Insert australia_post_sites data for Australia Post properties
INSERT INTO "australia_post_sites" ("id", "site_id", "facility_code", "facility_type", "tier", "region", "operational_hours", "has_retail", "has_production", "has_loading_dock", "has_mezzanine", "special_requirements", "created_at")
SELECT
  p.id || '-ap-site',
  p.id || '-site',
  p.id,
  p.type,
  COALESCE(p.tier, 1),
  p.region,
  '{"monday": "6:00 AM - 10:00 PM", "tuesday": "6:00 AM - 10:00 PM", "wednesday": "6:00 AM - 10:00 PM", "thursday": "6:00 AM - 10:00 PM", "friday": "6:00 AM - 10:00 PM", "saturday": "8:00 AM - 2:00 PM", "sunday": "closed"}',
  CASE WHEN p.type = 'Retail' THEN true ELSE false END,
  CASE WHEN p.type = 'Industrial and Logistics' THEN true ELSE false END,
  CASE WHEN p.type = 'Industrial and Logistics' THEN true ELSE false END,
  CASE WHEN p.tier IN (1, 2) THEN true ELSE false END,
  NULL,
  NOW()
FROM properties p
WHERE p.id LIKE 'ap-%'
ON CONFLICT (id) DO NOTHING;
