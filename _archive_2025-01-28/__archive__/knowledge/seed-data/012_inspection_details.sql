-- Seed data for inspection_items table (detailed scoring)
-- Detailed inspection results for completed inspections

-- Minchinbury inspection details (ir-001-minchinbury)
INSERT INTO "inspection_items" ("id", "inspection_id", "area_id", "item_name", "score", "max_score", "comments", "photo_url", "created_at")
VALUES
  -- Toilets & Shower Room
  ('ii-001-minch-toilet-fixtures', 'ir-001-minchinbury', 'area-toilets', 'Fixtures and fittings', 8.5, 10.0, 'Some calcium buildup on taps, urinals need deep clean', '/photos/ir-001/toilet-fixtures-1.jpg', NOW()),
  ('ii-002-minch-toilet-walls', 'ir-001-minchinbury', 'area-toilets', 'Walls and mirrors', 9.0, 10.0, 'Minor water spots on mirrors', NULL, NOW()),
  ('ii-003-minch-toilet-floor', 'ir-001-minchinbury', 'area-toilets', 'Floor condition', 9.5, 10.0, 'Excellent condition, well maintained', NULL, NOW()),
  ('ii-004-minch-toilet-consumables', 'ir-001-minchinbury', 'area-toilets', 'Consumables stocked', 7.0, 10.0, 'Paper towels running low in depot toilets', '/photos/ir-001/consumables-1.jpg', NOW()),
  
  -- Kitchen & Tea Room
  ('ii-005-minch-kitchen-benches', 'ir-001-minchinbury', 'area-kitchen', 'Benches and sinks', 9.5, 10.0, 'Very clean, well maintained', NULL, NOW()),
  ('ii-006-minch-kitchen-appliances', 'ir-001-minchinbury', 'area-kitchen', 'Appliances condition', 8.0, 10.0, 'Microwave needs internal cleaning', '/photos/ir-001/microwave-1.jpg', NOW()),
  ('ii-007-minch-kitchen-floor', 'ir-001-minchinbury', 'area-kitchen', 'Floor cleanliness', 9.0, 10.0, 'Good condition, minor scuff marks', NULL, NOW()),
  
  -- Office Areas
  ('ii-008-minch-office-desks', 'ir-001-minchinbury', 'area-office', 'Desks and surfaces', 9.5, 10.0, 'Well maintained, no issues', NULL, NOW()),
  ('ii-009-minch-office-equipment', 'ir-001-minchinbury', 'area-office', 'Equipment cleanliness', 9.0, 10.0, 'Some dust on keyboards', NULL, NOW()),
  ('ii-010-minch-office-floor', 'ir-001-minchinbury', 'area-office', 'Floor condition', 10.0, 10.0, 'Excellent condition', NULL, NOW()),
  
  -- Production Area
  ('ii-011-minch-production-walkways', 'ir-001-minchinbury', 'area-production', 'Walkways and clear paths', 9.0, 10.0, 'Well maintained, some debris in corners', '/photos/ir-001/warehouse-1.jpg', NOW()),
  ('ii-012-minch-production-equipment', 'ir-001-minchinbury', 'area-production', 'Equipment area cleanliness', 8.5, 10.0, 'Dust accumulation under conveyors', '/photos/ir-001/conveyor-1.jpg', NOW()),
  ('ii-013-minch-production-safety', 'ir-001-minchinbury', 'area-production', 'Safety compliance', 10.0, 10.0, 'All areas clear, excellent safety standards', NULL, NOW()),

-- Coffs Harbour inspection details (ir-002-coffs)
  -- Toilets & Shower Room
  ('ii-014-coffs-toilet-fixtures', 'ir-002-coffs', 'area-toilets', 'Fixtures and fittings', 9.0, 10.0, 'Generally clean, minor water marks', NULL, NOW()),
  ('ii-015-coffs-toilet-walls', 'ir-002-coffs', 'area-toilets', 'Walls and mirrors', 8.5, 10.0, 'Some grout discoloration', NULL, NOW()),
  ('ii-016-coffs-toilet-floor', 'ir-002-coffs', 'area-toilets', 'Floor condition', 9.0, 10.0, 'Good condition', NULL, NOW()),
  ('ii-017-coffs-toilet-consumables', 'ir-002-coffs', 'area-toilets', 'Consumables stocked', 10.0, 10.0, 'Fully stocked', NULL, NOW()),
  
  -- Kitchen & Tea Room
  ('ii-018-coffs-kitchen-benches', 'ir-002-coffs', 'area-kitchen', 'Benches and sinks', 8.0, 10.0, 'Coffee stains on counter', '/photos/ir-002/kitchen-1.jpg', NOW()),
  ('ii-019-coffs-kitchen-appliances', 'ir-002-coffs', 'area-kitchen', 'Appliances condition', 7.5, 10.0, 'Fridge needs internal cleaning, microwave dirty', '/photos/ir-002/fridge-1.jpg', NOW()),
  ('ii-020-coffs-kitchen-floor', 'ir-002-coffs', 'area-kitchen', 'Floor cleanliness', 8.5, 10.0, 'Minor scuff marks and stains', NULL, NOW()),
  
  -- Office Areas
  ('ii-021-coffs-office-desks', 'ir-002-coffs', 'area-office', 'Desks and surfaces', 8.0, 10.0, 'Dust accumulation on surfaces', '/photos/ir-002/office-1.jpg', NOW()),
  ('ii-022-coffs-office-equipment', 'ir-002-coffs', 'area-office', 'Equipment cleanliness', 8.5, 10.0, 'Some fingerprints on screens', NULL, NOW()),
  ('ii-023-coffs-office-floor', 'ir-002-coffs', 'area-office', 'Floor condition', 7.5, 10.0, 'Carpet needs vacuum, visible dirt', '/photos/ir-002/carpet-1.jpg', NOW()),
  
  -- Production Area
  ('ii-024-coffs-production-walkways', 'ir-002-coffs', 'area-production', 'Walkways and clear paths', 9.0, 10.0, 'Generally clean, minor debris', NULL, NOW()),
  ('ii-025-coffs-production-equipment', 'ir-002-coffs', 'area-production', 'Equipment area cleanliness', 9.0, 10.0, 'Well maintained', NULL, NOW()),
  ('ii-026-coffs-production-safety', 'ir-002-coffs', 'area-production', 'Safety compliance', 9.5, 10.0, 'All areas clear, good standards', NULL, NOW())
ON CONFLICT (id) DO NOTHING;

-- Seed data for inspections table (Australia Post specific)
INSERT INTO "inspections" ("id", "ap_site_id", "inspection_date", "inspector_id", "overall_score", "pass_threshold", "status", "notes", "created_at")
VALUES
  ('insp-ap-bankstown-001', 'ap-ap00121-ap-site', '2025-01-18', 'sarah-johnson', 94.0, 85.0, 'completed', 'Excellent retail presentation standards', NOW()),
  ('insp-ap-minto-001', 'ap-ap02208-ap-site', '2025-01-20', 'sarah-johnson', 87.0, 85.0, 'completed', 'Workshop areas need additional attention', NOW()),
  ('insp-ap-newcastle-001', 'ap-ap02055-ap-site', '2025-01-22', 'sarah-johnson', 91.0, 85.0, 'completed', 'Good overall performance', NOW())
ON CONFLICT (id) DO NOTHING;

-- Seed data for inspection_items table (Australia Post specific)
INSERT INTO "inspection_items" ("id", "inspection_id", "area_id", "item_name", "score", "max_score", "comments", "photo_url", "created_at")
VALUES
  -- Bankstown Post Office
  ('ii-ap-bank-001', 'insp-ap-bankstown-001', 'area-retail-counter', 'Retail counter cleanliness', 9.5, 10.0, 'Immaculate condition', NULL, NOW()),
  ('ii-ap-bank-002', 'insp-ap-bankstown-001', 'area-hard-flooring', 'Floor condition', 9.0, 10.0, 'Very clean, minor scuffs near entry', NULL, NOW()),
  ('ii-ap-bank-003', 'insp-ap-bankstown-001', 'area-glass', 'Windows and glass', 9.5, 10.0, 'Crystal clear, well maintained', NULL, NOW()),
  ('ii-ap-bank-004', 'insp-ap-bankstown-001', 'area-displays', 'Displays and furnishings', 9.0, 10.0, 'All areas dust-free, well organized', NULL, NOW()),
  ('ii-ap-bank-005', 'insp-ap-bankstown-001', 'area-po-box', 'PO Box exterior condition', 10.0, 10.0, 'Perfect condition', NULL, NOW()),
  
  -- Minto Equipment Depot
  ('ii-ap-minto-001', 'insp-ap-minto-001', 'area-production', 'Workshop floor cleanliness', 7.5, 10.0, 'Oil stains require deep cleaning', '/photos/ap-minto/workshop-1.jpg', NOW()),
  ('ii-ap-minto-002', 'insp-ap-minto-001', 'area-equipment', 'Equipment maintenance area', 8.0, 10.0, 'Tools need reorganization', '/photos/ap-minto/tools-1.jpg', NOW()),
  ('ii-ap-minto-003', 'insp-ap-minto-001', 'area-external', 'Wash bay condition', 8.5, 10.0, 'Drainage needs attention', NULL, NOW()),
  ('ii-ap-minto-004', 'insp-ap-minto-001', 'area-toilets', 'Workshop amenities', 9.0, 10.0, 'Well maintained', NULL, NOW()),
  
  -- Newcastle Letter Facility
  ('ii-ap-newc-001', 'insp-ap-newcastle-001', 'area-production', 'Sorting area cleanliness', 9.0, 10.0, 'Good condition, well maintained', NULL, NOW()),
  ('ii-ap-newc-002', 'insp-ap-newcastle-001', 'area-dock', 'Loading bay condition', 8.5, 10.0, 'Minor debris accumulation', NULL, NOW()),
  ('ii-ap-newc-003', 'insp-ap-newcastle-001', 'area-office', 'Office area standards', 9.5, 10.0, 'Very clean and organized', NULL, NOW()),
  ('ii-ap-newc-004', 'insp-ap-newcastle-001', 'area-toilets', 'Staff amenities', 9.0, 10.0, 'All areas clean, well stocked', NULL, NOW())
ON CONFLICT (id) DO NOTHING;
