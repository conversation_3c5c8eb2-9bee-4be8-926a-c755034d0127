-- Seed data for service_contracts table
INSERT INTO "service_contracts" ("id", "client_id", "site_id", "start_date", "end_date", "contract_value", "billing_frequency", "service_type", "service_frequency", "special_terms", "status", "contract_file_url", "created_at")
SELECT
  'contract-' || id,
  client_id,
  id || '-site',
  '2025-01-01',
  '2027-12-31',
  CASE 
    WHEN tier = 1 THEN 250000.00
    WHEN tier = 2 THEN 150000.00
    WHEN tier = 3 THEN 75000.00
    WHEN tier = 4 THEN 50000.00
    WHEN tier = 5 THEN 25000.00
    ELSE 100000.00
  END,
  'monthly',
  type,
  CASE 
    WHEN tier = 1 THEN 'Daily (Multiple)'
    WHEN tier = 2 THEN 'Daily'
    WHEN tier = 3 THEN 'Daily (3/5)'
    WHEN tier = 4 THEN 'Weekly'
    WHEN tier = 5 THEN 'Fortnightly'
    ELSE 'Daily'
  END,
  NULL,
  'active',
  NULL,
  NOW()
FROM properties
ON CONFLICT (id) DO NOTHING;

-- Seed data for service_schedules table (next week's schedule)
INSERT INTO "service_schedules" ("id", "contract_id", "site_id", "service_date", "start_time", "end_time", "service_type", "status", "notes", "created_at")
SELECT
  'schedule-' || p.id || '-2025-01-27',
  'contract-' || p.id,
  p.id || '-site',
  '2025-01-27',
  CASE 
    WHEN p.tier = 1 THEN '06:00:00'
    WHEN p.tier = 2 THEN '07:00:00'
    ELSE '08:00:00'
  END,
  CASE 
    WHEN p.tier = 1 THEN '22:00:00'
    WHEN p.tier = 2 THEN '15:00:00'
    ELSE '14:00:00'
  END,
  'Regular Cleaning',
  'scheduled',
  NULL,
  NOW()
FROM properties p
WHERE p.tier IN (1, 2) -- Daily cleaning for Tier 1 and 2
ON CONFLICT (id) DO NOTHING;

-- Add some schedules for Tier 3 (3 out of 5 days)
INSERT INTO "service_schedules" ("id", "contract_id", "site_id", "service_date", "start_time", "end_time", "service_type", "status", "notes", "created_at")
SELECT
  'schedule-' || p.id || '-2025-01-27',
  'contract-' || p.id,
  p.id || '-site',
  '2025-01-27',
  '08:00:00',
  '14:00:00',
  'Regular Cleaning',
  'scheduled',
  NULL,
  NOW()
FROM properties p
WHERE p.tier = 3
ON CONFLICT (id) DO NOTHING;

-- Seed data for job_assignments table
INSERT INTO "job_assignments" ("id", "schedule_id", "staff_id", "assigned_by", "assigned_at", "role_on_job", "status", "created_at")
SELECT
  'assignment-' || s.id,
  s.id,
  CASE
    WHEN p.region = 'Metro' AND p.state IN ('NSW', 'VIC') THEN 'james-wilson'
    ELSE 'emma-thompson'
  END,
  'mark-brady',
  NOW(),
  'Lead Cleaner',
  'assigned',
  NOW()
FROM service_schedules s
JOIN sites st ON s.site_id = st.id
JOIN properties p ON st.id = p.id || '-site'
ON CONFLICT (id) DO NOTHING;

-- Seed data for inventory table
INSERT INTO "inventory" ("id", "item_name", "item_category", "current_stock", "unit_of_measure", "reorder_threshold", "cost_per_unit", "storage_location", "last_restocked_date", "created_at")
VALUES
  ('inv-001', 'Disinfectant Cleaner', 'Cleaning Chemicals', 50, 'Bottles (5L)', 20, 29.95, 'Main Warehouse', '2025-01-01', NOW()),
  ('inv-002', 'Glass Cleaner', 'Cleaning Chemicals', 40, 'Bottles (1L)', 15, 8.95, 'Main Warehouse', '2025-01-01', NOW()),
  ('inv-003', 'Floor Cleaner', 'Cleaning Chemicals', 30, 'Bottles (5L)', 10, 32.50, 'Main Warehouse', '2025-01-01', NOW()),
  ('inv-004', 'Paper Towels', 'Consumables', 200, 'Rolls', 50, 2.50, 'Main Warehouse', '2025-01-01', NOW()),
  ('inv-005', 'Toilet Paper', 'Consumables', 300, 'Rolls', 100, 1.80, 'Main Warehouse', '2025-01-01', NOW()),
  ('inv-006', 'Bin Liners - Large', 'Consumables', 1000, 'Units', 200, 0.15, 'Main Warehouse', '2025-01-01', NOW()),
  ('inv-007', 'Microfiber Cloths', 'Equipment', 150, 'Units', 50, 3.95, 'Main Warehouse', '2025-01-01', NOW()),
  ('inv-008', 'Mop Heads', 'Equipment', 40, 'Units', 15, 9.95, 'Main Warehouse', '2025-01-01', NOW()),
  ('inv-009', 'Scrubber Pads', 'Equipment', 75, 'Units', 25, 6.50, 'Main Warehouse', '2025-01-01', NOW()),
  ('inv-010', 'Safety Gloves', 'PPE', 200, 'Pairs', 50, 2.95, 'Main Warehouse', '2025-01-01', NOW())
ON CONFLICT (id) DO NOTHING;

-- Seed data for equipment table
INSERT INTO "equipment" ("id", "name", "type", "serial_number", "purchase_date", "cost", "current_location", "status", "last_serviced_date", "service_interval_days", "created_at")
VALUES
  ('eq-001', 'Floor Buffer - Industrial', 'Floor Machine', 'FB2023-001', '2023-01-15', 2500.00, 'Main Warehouse', 'operational', '2025-01-10', 90, NOW()),
  ('eq-002', 'Carpet Extractor', 'Carpet Cleaner', 'CE2023-002', '2023-03-01', 3500.00, 'Main Warehouse', 'operational', '2025-01-05', 180, NOW()),
  ('eq-003', 'Pressure Washer', 'Outdoor Equipment', 'PW2023-003', '2023-04-15', 1500.00, 'Main Warehouse', 'operational', '2024-12-01', 90, NOW()),
  ('eq-004', 'Ride-on Sweeper', 'Floor Machine', 'RS2023-004', '2023-06-01', 15000.00, 'Minchinbury Depot', 'operational', '2025-01-15', 90, NOW()),
  ('eq-005', 'Commercial Vacuum', 'Vacuum Cleaner', 'CV2023-005', '2023-07-01', 500.00, 'Coffs Harbour', 'operational', '2024-12-15', 60, NOW()),
  ('eq-006', 'Window Cleaning Kit', 'Window Equipment', 'WK2023-006', '2023-09-01', 300.00, 'Main Warehouse', 'operational', '2024-11-01', 180, NOW())
ON CONFLICT (id) DO NOTHING;

-- Seed data for client_feedback table
INSERT INTO "client_feedback" ("id", "client_id", "site_id", "service_date", "feedback_type", "description", "received_by", "received_at", "severity", "status", "resolution", "follow_up_required", "follow_up_date", "created_at")
VALUES
  ('fb-001', 'star-track-client', 'st-ap02258-site', '2025-01-10', 'positive', 'Excellent work on the warehouse floor maintenance. Staff were professional and thorough.', 'mark-brady', '2025-01-11', 'low', 'closed', 'Team commended for their work', false, NULL, NOW()),
  ('fb-002', 'aus-post-client', 'ap-ap00121-site', '2025-01-15', 'complaint', 'PO Box area not cleaned properly yesterday. Several cobwebs still visible.', 'mark-brady', '2025-01-16', 'medium', 'open', NULL, true, '2025-01-21', NOW()),
  ('fb-003', 'star-track-client', 'st-ap02405-site', '2025-01-18', 'suggestion', 'Could we increase the frequency of dusting in the office areas? Dust accumulating between weekly cleans.', 'mark-brady', '2025-01-19', 'low', 'open', NULL, true, '2025-02-01', NOW())
ON CONFLICT (id) DO NOTHING;

-- Seed data for documents table
INSERT INTO "documents" ("id", "name", "document_type", "related_to_type", "related_to_id", "upload_date", "uploaded_by", "file_url", "expiry_date", "tags", "created_at")
VALUES
  ('doc-001', 'Star Track Master Service Agreement', 'Contract', 'Client', 'star-track-client', '2024-12-15', 'gaurav-majumdar', '/documents/star-track-msa-2025.pdf', '2027-12-31', '["contract", "star track", "master agreement"]', NOW()),
  ('doc-002', 'Australia Post JLL Contract', 'Contract', 'Client', 'aus-post-client', '2024-12-20', 'gaurav-majumdar', '/documents/australia-post-contract-2025.pdf', '2027-12-31', '["contract", "australia post", "jll"]', NOW()),
  ('doc-003', 'Public Liability Insurance Certificate', 'Insurance', 'Company', 'ara-property', '2025-01-01', 'gaurav-majumdar', '/documents/ara-insurance-2025.pdf', '2025-12-31', '["insurance", "liability", "certificate"]', NOW()),
  ('doc-004', 'Workers Compensation Certificate', 'Insurance', 'Company', 'ara-property', '2025-01-01', 'gaurav-majumdar', '/documents/ara-workers-comp-2025.pdf', '2025-12-31', '["insurance", "workers comp", "certificate"]', NOW()),
  ('doc-005', 'Minchinbury Site Safety Plan', 'Safety', 'Property', 'st-ap02258', '2025-01-05', 'mark-brady', '/documents/minchinbury-safety-plan.pdf', NULL, '["safety", "site plan", "minchinbury"]', NOW())
ON CONFLICT (id) DO NOTHING;

-- Seed data for notifications table
INSERT INTO "notifications" ("id", "user_id", "title", "message", "read_at", "notification_type", "related_entity_type", "related_entity_id", "action_url", "created_at")
VALUES
  ('notif-001', 'james-wilson', 'Action Item Assigned', 'You have been assigned a new action item: Deep clean toilet fixtures at Minchinbury site', NULL, 'task', 'Action', 'action-001-minchinbury', '/actions/action-001-minchinbury', NOW()),
  ('notif-002', 'emma-thompson', 'Action Item Assigned', 'You have been assigned a new action item: Re-stock consumables at Minchinbury site', NULL, 'task', 'Action', 'action-002-minchinbury', '/actions/action-002-minchinbury', NOW()),
  ('notif-003', 'mark-brady', 'Inspection Completed', 'Minchinbury Monthly Inspection completed with score 92%', NULL, 'report', 'Inspection', 'ir-001-minchinbury', '/inspections/ir-001-minchinbury', NOW()),
  ('notif-004', 'sarah-johnson', 'Inspection Due', 'Bankstown Post Office Inspection is due today', NULL, 'reminder', 'Inspection', 'ir-003-bankstown', '/inspections/ir-003-bankstown', NOW()),
  ('notif-005', 'mark-brady', 'Client Feedback', 'New complaint received for Bankstown Post Office', NULL, 'alert', 'Feedback', 'fb-002', '/feedback/fb-002', NOW())
ON CONFLICT (id) DO NOTHING;

-- Seed data for chat_sessions table
INSERT INTO "chat_sessions" ("id", "title", "preview", "date", "is_starred", "created_by")
VALUES
  ('chat-001', 'Minchinbury Inspection Discussion', 'Discussion about recent inspection results...', '2025-01-15 16:00:00+11', false, 'mark-brady'),
  ('chat-002', 'Periodical Service Planning', 'Planning next round of periodical services...', '2025-01-18 10:00:00+11', true, 'james-wilson')
ON CONFLICT (id) DO NOTHING;

-- Seed data for chat_participants table
INSERT INTO "chat_participants" ("session_id", "user_id", "joined_at", "last_read_at")
VALUES
  ('chat-001', 'mark-brady', '2025-01-15 16:00:00+11', '2025-01-15 16:30:00+11'),
  ('chat-001', 'sarah-johnson', '2025-01-15 16:05:00+11', '2025-01-15 16:25:00+11'),
  ('chat-002', 'james-wilson', '2025-01-18 10:00:00+11', '2025-01-18 10:45:00+11'),
  ('chat-002', 'emma-thompson', '2025-01-18 10:10:00+11', '2025-01-18 10:45:00+11'),
  ('chat-002', 'mark-brady', '2025-01-18 10:15:00+11', '2025-01-18 10:40:00+11')
ON CONFLICT (session_id, user_id) DO NOTHING;

-- Seed data for messages table
INSERT INTO "messages" ("id", "session_id", "content_type", "content_transcript", "object", "role", "status", "type", "sender_id", "created_at")
VALUES
  ('msg-001', 'chat-001', 'text', 'Sarah, can you review the Minchinbury inspection results?', 'message', 'human', 'delivered', 'text', 'mark-brady', '2025-01-15 16:00:00+11'),
  ('msg-002', 'chat-001', 'text', 'Yes, I reviewed them. Overall score is good at 92% but we need to address the toilet fixture issue urgently.', 'message', 'human', 'delivered', 'text', 'sarah-johnson', '2025-01-15 16:05:00+11'),
  ('msg-003', 'chat-001', 'text', 'Agreed. I have created action items for James and Emma to handle this week.', 'message', 'human', 'delivered', 'text', 'mark-brady', '2025-01-15 16:10:00+11'),
  ('msg-004', 'chat-002', 'text', 'Team, we need to plan the periodical services for Q1. Emma, what is the status of the Minchinbury vinyl floor restoration?', 'message', 'human', 'delivered', 'text', 'james-wilson', '2025-01-18 10:00:00+11'),
  ('msg-005', 'chat-002', 'text', 'Minchinbury vinyl strip and seal is scheduled for next week. I have all equipment ready.', 'message', 'human', 'delivered', 'text', 'emma-thompson', '2025-01-18 10:10:00+11')
ON CONFLICT (id) DO NOTHING;

-- Seed data for user_sessions table
INSERT INTO "user_sessions" ("id", "user_id", "token", "expires_at", "created_at")
VALUES
  ('sess-001-mark', 'mark-brady', 'token-mark-brady-001', NOW() + INTERVAL '24 hours', NOW()),
  ('sess-002-sarah', 'sarah-johnson', 'token-sarah-johnson-001', NOW() + INTERVAL '24 hours', NOW()),
  ('sess-003-james', 'james-wilson', 'token-james-wilson-001', NOW() + INTERVAL '24 hours', NOW()),
  ('sess-004-emma', 'emma-thompson', 'token-emma-thompson-001', NOW() + INTERVAL '24 hours', NOW())
ON CONFLICT (id) DO NOTHING;
