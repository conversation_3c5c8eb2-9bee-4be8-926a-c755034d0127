-- Seed data for companies table
INSERT INTO "companies" ("id", "name", "type", "primary_contact_id", "address", "phone", "email", "created_at")
VALUES 
  ('aus-post-jll', 'Australia Post/JLL', 'Client', NULL, '111 Bourke Street', '+61 3 9106 8800', '<EMAIL>', NOW()),
  ('star-track', 'Star Track', 'Client', NULL, '40 Garden Street', '+61 13 13 45', '<EMAIL>', NOW()),
  ('ara-property', 'ARA Property Services', 'Service Provider', NULL, 'Sydney Office', '+61 2 9876 5432', '<EMAIL>', NOW())
ON CONFLICT (id) DO NOTHING;

-- Seed data for contacts table  
INSERT INTO "contacts" ("id", "name", "role", "company", "phone", "email", "location", "is_favorite", "created_at")
VALUES 
  ('john-smith', '<PERSON>', 'Facility Manager', 'Australia Post/JLL', '+61 3 9827 1234', '<EMAIL>', 'Melbourne', true, NOW()),
  ('alice-chen-contact', '<PERSON> <PERSON>', 'Operations Manager', '<PERSON> Track', '+61 4 7890 1234', '<EMAIL>', 'Sydney', false, NOW()),
  ('gaurav-majumdar-contact', 'Gaurav Majumdar', 'CEO', 'ARA Property Services', '+61 4 1234 5678', '<EMAIL>', 'Sydney', true, NOW())
ON CONFLICT (id) DO NOTHING;

-- Update primary contact IDs for companies
UPDATE "companies" SET "primary_contact_id" = 'john-smith' WHERE "id" = 'aus-post-jll';
UPDATE "companies" SET "primary_contact_id" = 'alice-chen-contact' WHERE "id" = 'star-track';
UPDATE "companies" SET "primary_contact_id" = 'gaurav-majumdar-contact' WHERE "id" = 'ara-property';

-- Seed data for clients table
INSERT INTO "clients" ("id", "name", "primary_contact_name", "primary_contact_email", "primary_contact_phone", "address", "suburb", "state", "postcode", "sector", "account_manager_id", "status", "notes", "created_at")
VALUES 
  ('aus-post-client', 'Australia Post/JLL', 'John Smith', '<EMAIL>', '+61 3 9827 1234', '111 Bourke Street', 'Melbourne', 'VIC', '3000', 'Postal Services', 'gaurav-majumdar', 'active', 'Major contract for postal facilities cleaning services', NOW()),
  ('star-track-client', 'Star Track', 'Alice Chen', '<EMAIL>', '+61 4 7890 1234', '40 Garden Street', 'South Yarra', 'VIC', '3141', 'Logistics', 'mark-brady', 'active', 'Contract covering 40 facilities nationwide', NOW())
ON CONFLICT (id) DO NOTHING;
