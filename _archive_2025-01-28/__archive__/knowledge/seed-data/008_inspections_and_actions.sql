-- Seed data for inspection_templates table
INSERT INTO "inspection_templates" ("id", "name", "category", "type", "version", "is_active", "created_by", "created_at", "sections")
VALUES
  ('template-star-track', 'Star Track Facility Inspection', 'Industrial', 'Standard Industrial', 1, true, 'sarah-johnson', NOW(), 
   '[
     {
       "id": "section-toilets",
       "name": "Toilets & Shower Room",
       "description": "All bathroom and shower facilities",
       "items": [
         {"id": "toilet-fixtures", "name": "Fixtures and fittings", "max_score": 10},
         {"id": "toilet-walls", "name": "Walls and mirrors", "max_score": 10},
         {"id": "toilet-floor", "name": "Floor condition", "max_score": 10},
         {"id": "toilet-consumables", "name": "Consumables stocked", "max_score": 10}
       ]
     },
     {
       "id": "section-kitchen",
       "name": "Kitchen & Tea Room",
       "description": "Kitchen and food preparation areas",
       "items": [
         {"id": "kitchen-benches", "name": "Benches and sinks", "max_score": 10},
         {"id": "kitchen-appliances", "name": "Appliances condition", "max_score": 10},
         {"id": "kitchen-floor", "name": "Floor cleanliness", "max_score": 10}
       ]
     },
     {
       "id": "section-office",
       "name": "Office Areas",
       "description": "Office spaces and workstations",
       "items": [
         {"id": "office-desks", "name": "Desks and surfaces", "max_score": 10},
         {"id": "office-equipment", "name": "Equipment cleanliness", "max_score": 10},
         {"id": "office-floor", "name": "Floor condition", "max_score": 10}
       ]
     },
     {
       "id": "section-production",
       "name": "Production Area",
       "description": "Warehouse and production floor",
       "items": [
         {"id": "production-walkways", "name": "Walkways and clear paths", "max_score": 10},
         {"id": "production-equipment", "name": "Equipment area cleanliness", "max_score": 10},
         {"id": "production-safety", "name": "Safety compliance", "max_score": 10}
       ]
     }
   ]'),
  ('template-aus-post-retail', 'Australia Post Retail Inspection', 'Retail', 'Post Office Standard', 1, true, 'sarah-johnson', NOW(),
   '[
     {
       "id": "section-front-house",
       "name": "Front of House",
       "description": "Customer-facing areas",
       "items": [
         {"id": "retail-counter", "name": "Retail counter cleanliness", "max_score": 10},
         {"id": "retail-floor", "name": "Floor condition", "max_score": 10},
         {"id": "retail-glass", "name": "Windows and glass", "max_score": 10},
         {"id": "retail-display", "name": "Displays and furnishings", "max_score": 10}
       ]
     },
     {
       "id": "section-po-boxes",
       "name": "PO Box Area",
       "description": "Post office box area",
       "items": [
         {"id": "pobox-exterior", "name": "PO Box exterior condition", "max_score": 10},
         {"id": "pobox-floor", "name": "Floor cleanliness", "max_score": 10},
         {"id": "pobox-lighting", "name": "Lighting and fixtures", "max_score": 10}
       ]
     }
   ]'),
  ('template-aus-post-industrial', 'Australia Post Industrial Inspection', 'Industrial', 'Standard Industrial', 1, true, 'sarah-johnson', NOW(),
   '[
     {
       "id": "section-mail-processing",
       "name": "Mail Processing Area",
       "description": "Sorting and processing zones",
       "items": [
         {"id": "processing-floor", "name": "Processing floor cleanliness", "max_score": 10},
         {"id": "processing-equipment", "name": "Equipment area cleanliness", "max_score": 10},
         {"id": "processing-safety", "name": "Safety signage and compliance", "max_score": 10}
       ]
     },
     {
       "id": "section-loading-dock",
       "name": "Loading Dock",
       "description": "Loading and unloading areas",
       "items": [
         {"id": "dock-floor", "name": "Dock floor condition", "max_score": 10},
         {"id": "dock-equipment", "name": "Equipment area cleanliness", "max_score": 10},
         {"id": "dock-waste", "name": "Waste management", "max_score": 10}
       ]
     }
   ]')
ON CONFLICT (id) DO NOTHING;

-- Seed data for inspection_reports table
INSERT INTO "inspection_reports" ("id", "property_id", "template_id", "title", "location", "date", "inspector", "status", "score", "summary", "sections", "weather_conditions", "temperature", "photos", "voice_notes", "created_at", "submitted_at", "reviewed_by", "reviewed_at")
VALUES
  ('ir-001-minchinbury', 'st-ap02258', 'template-star-track', 'Minchinbury Monthly Inspection', 'Minchinbury NSW', '2025-01-15 10:00:00+11', 'sarah-johnson', 'completed', 92, 'Overall facility cleanliness is excellent. Minor issues noted in toilet areas requiring additional attention.', '[]', 'Clear', 28.5, 5, 0, NOW(), '2025-01-15 14:30:00+11', 'mark-brady', '2025-01-16 09:00:00+11'),
  ('ir-002-coffs', 'st-ap02405', 'template-star-track', 'Coffs Harbour Regular Inspection', 'Coffs Harbour NSW', '2025-01-17 14:00:00+11', 'sarah-johnson', 'completed', 88, 'Good overall standards maintained. Office area requires additional dusting. Kitchen requires deep clean of appliances.', '[]', 'Partly Cloudy', 26.0, 3, 2, NOW(), '2025-01-17 16:30:00+11', NULL, NULL),
  ('ir-003-bankstown', 'ap-ap00121', 'template-aus-post-retail', 'Bankstown Post Office Inspection', 'Bankstown NSW', '2025-01-18 09:00:00+11', 'sarah-johnson', 'pending', NULL, NULL, '[]', 'Sunny', 29.0, 0, 0, NOW(), NULL, NULL, NULL),
  ('ir-004-minto', 'ap-ap02208', 'template-aus-post-industrial', 'Minto Equipment Depot Inspection', 'Minto NSW', '2025-01-20 11:00:00+11', 'sarah-johnson', 'scheduled', NULL, NULL, '[]', NULL, NULL, 0, 0, NOW(), NULL, NULL, NULL)
ON CONFLICT (id) DO NOTHING;

-- Seed data for inspection_actions table
INSERT INTO "inspection_actions" ("id", "report_id", "title", "priority", "assignee", "due_date", "status", "created_at", "updated_at")
VALUES
  ('action-001-minchinbury', 'ir-001-minchinbury', 'Deep clean toilet fixtures', 'high', 'james-wilson', '2025-01-22', 'open', NOW(), NOW()),
  ('action-002-minchinbury', 'ir-001-minchinbury', 'Re-stock consumables in all bathrooms', 'medium', 'emma-thompson', '2025-01-20', 'open', NOW(), NOW()),
  ('action-003-coffs', 'ir-002-coffs', 'Deep clean all kitchen appliances', 'high', 'james-wilson', '2025-01-24', 'open', NOW(), NOW()),
  ('action-004-coffs', 'ir-002-coffs', 'Comprehensive dusting of office areas', 'medium', 'emma-thompson', '2025-01-25', 'open', NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Seed data for kpi_targets table
INSERT INTO "kpi_targets" ("id", "metric_type", "target_value", "period", "category", "property_type", "created_at")
VALUES
  ('kpi-inspection-score', 'inspection_score', 90.0, 'monthly', 'Quality', NULL, NOW()),
  ('kpi-completion-rate', 'task_completion_rate', 98.0, 'weekly', 'Operations', NULL, NOW()),
  ('kpi-response-time', 'issue_response_time', 4.0, 'daily', 'Service', NULL, NOW()),
  ('kpi-client-satisfaction', 'client_satisfaction', 95.0, 'quarterly', 'Client', NULL, NOW()),
  ('kpi-industrial-score', 'inspection_score', 88.0, 'monthly', 'Quality', 'Industrial and Logistics', NOW()),
  ('kpi-retail-score', 'inspection_score', 92.0, 'monthly', 'Quality', 'Retail', NOW())
ON CONFLICT (id) DO NOTHING;

-- Seed data for dashboard_metrics table (last 3 months)
INSERT INTO "dashboard_metrics" ("id", "metric_type", "value", "period", "category", "property_id", "created_at")
VALUES
  -- Inspection scores for various properties
  ('metric-001', 'inspection_score', 92.0, 'monthly', 'Quality', 'st-ap02258', '2025-01-01'),
  ('metric-002', 'inspection_score', 88.0, 'monthly', 'Quality', 'st-ap02405', '2025-01-01'),
  ('metric-003', 'inspection_score', 94.0, 'monthly', 'Quality', 'ap-ap00121', '2025-01-01'),
  ('metric-004', 'inspection_score', 89.0, 'monthly', 'Quality', 'st-ap02258', '2024-12-01'),
  ('metric-005', 'inspection_score', 85.0, 'monthly', 'Quality', 'st-ap02405', '2024-12-01'),
  ('metric-006', 'inspection_score', 95.0, 'monthly', 'Quality', 'ap-ap00121', '2024-12-01'),
  
  -- Task completion rates
  ('metric-007', 'task_completion_rate', 98.5, 'weekly', 'Operations', 'st-ap02258', '2025-01-15'),
  ('metric-008', 'task_completion_rate', 97.0, 'weekly', 'Operations', 'st-ap02405', '2025-01-15'),
  ('metric-009', 'task_completion_rate', 99.0, 'weekly', 'Operations', 'ap-ap00121', '2025-01-15'),
  
  -- Issue response times (in hours)
  ('metric-010', 'issue_response_time', 3.5, 'daily', 'Service', 'st-ap02258', '2025-01-15'),
  ('metric-011', 'issue_response_time', 4.2, 'daily', 'Service', 'st-ap02405', '2025-01-15'),
  ('metric-012', 'issue_response_time', 2.8, 'daily', 'Service', 'ap-ap00121', '2025-01-15'),
  
  -- Client satisfaction scores
  ('metric-013', 'client_satisfaction', 95.0, 'quarterly', 'Client', 'st-ap02258', '2024-12-31'),
  ('metric-014', 'client_satisfaction', 93.0, 'quarterly', 'Client', 'st-ap02405', '2024-12-31'),
  ('metric-015', 'client_satisfaction', 97.0, 'quarterly', 'Client', 'ap-ap00121', '2024-12-31')
ON CONFLICT (id) DO NOTHING;
