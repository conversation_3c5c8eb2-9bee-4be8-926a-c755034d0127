-- Seed data for periodical_services table
-- These are specialist cleaning tasks that happen less frequently

-- Star Track Periodical Services
INSERT INTO "periodical_services" ("id", "property_id", "service_name", "service_description", "frequency_id", "last_service_date", "next_service_date", "assigned_to", "status", "special_requirements", "created_at")
VALUES
  -- Minchinbury facility periodicals
  ('ps-st-minch-vinyl-strip', 'st-ap02258', 'Vinyl Floor Strip and Seal', 'Strip and reseal all vinyl floors in administration area', 'freq-quarterly', '2024-10-01', '2025-01-01', 'james-wilson', 'scheduled', 'Book outside normal operation hours', NOW()),
  ('ps-st-minch-depot-strip', 'st-ap02258', 'Depot Floor Strip and Seal', 'Strip and reseal all vinyl floors in depot area', 'freq-biannually', '2024-07-01', '2025-01-15', 'james-wilson', 'scheduled', 'Requires weekend access', NOW()),
  ('ps-st-minch-carpet-deep', 'st-ap02258', 'Deep Carpet Clean', 'Deep clean all carpeted areas', 'freq-biannually', '2024-09-01', '2025-03-01', 'emma-thompson', 'scheduled', 'Section by section approach to minimize disruption', NOW()),
  
  -- Coffs Harbour periodicals
  ('ps-st-coffs-carpet-steam', 'st-ap02405', 'Carpet Steam Clean', 'Steam clean all carpet areas', 'freq-biannually', '2024-08-01', '2025-02-01', 'james-wilson', 'scheduled', NULL, NOW()),
  ('ps-st-coffs-window-ext', 'st-ap02405', 'External Window Clean', 'Clean all external windows and frames', 'freq-monthly', '2024-12-15', '2025-01-15', 'james-wilson', 'scheduled', 'Requires ladder and safety equipment', NOW()),
  
  -- Tullamarine periodicals
  ('ps-st-tull-strip-seal', 'st-ap02327', 'Vinyl Floor Strip and Seal', 'Strip and reseal all vinyl flooring', 'freq-quarterly', '2024-11-01', '2025-02-01', 'emma-thompson', 'scheduled', 'Coordinate with operations for 24/7 facility', NOW()),
  ('ps-st-tull-high-dust', 'st-ap02327', 'High Level Dusting', 'Dust all areas above 2 meters including ledges, rails, and vents', 'freq-monthly', '2024-12-20', '2025-01-20', 'james-wilson', 'scheduled', 'Requires elevated work platform', NOW()),
  
  -- Perth Airport periodicals
  ('ps-st-perth-deep-clean', 'st-ap02482', 'Deep Clean Service', 'Comprehensive facility deep clean', 'freq-biannually', '2024-07-01', '2025-01-15', 'james-wilson', 'scheduled', 'Weekend work preferred', NOW()),
  
-- Australia Post Periodical Services
  -- Industrial facility periodicals
  ('ps-ap-minto-workshop', 'ap-ap02208', 'Workshop Deep Clean', 'Deep clean workshop area including equipment', 'freq-quarterly', '2024-10-15', '2025-01-15', 'emma-thompson', 'scheduled', 'Book during maintenance shutdown', NOW()),
  ('ps-ap-minto-graffiti', 'ap-ap02208', 'Graffiti Removal', 'Remove graffiti from external walls', 'freq-adhoc', NULL, NULL, NULL, 'ad-hoc', 'Perform within 48 hours of report', NOW()),
  
  -- Retail facility periodicals
  ('ps-ap-bankstown-vinyl', 'ap-ap00121', 'Vinyl Floor Strip and Seal', 'Strip, seal and buff vinyl flooring', 'freq-biannually', '2024-11-01', '2025-05-01', 'james-wilson', 'scheduled', 'After hours work required', NOW()),
  ('ps-ap-bankstown-carpet', 'ap-ap00121', 'Carpet Deep Clean', 'Deep clean extraction of all carpets', 'freq-biannually', '2024-10-01', '2025-04-01', 'emma-thompson', 'scheduled', 'Section by section during business hours', NOW()),
  ('ps-ap-bankstown-window', 'ap-ap00121', 'Shop Front Window Clean', 'Wash and blade dry all shop front windows', 'freq-monthly', '2024-12-01', '2025-01-01', 'james-wilson', 'scheduled', 'Includes branded/vinyl application windows', NOW()),
  ('ps-ap-bankstown-high-dust', 'ap-ap00121', 'High Dusting Service', 'Clean all areas above 2 meters including light fittings', 'freq-biannually', '2024-09-01', '2025-03-01', 'james-wilson', 'scheduled', 'Requires specialist equipment', NOW()),
  
  -- Mixed use facility periodicals
  ('ps-ap-bondi-window', 'ap-ap02378', 'All Windows Clean', 'Clean all windows and glass including shop front', 'freq-biannually', '2024-08-01', '2025-02-01', 'james-wilson', 'scheduled', 'Extension pole required, possibly elevated platform', NOW()),
  ('ps-ap-bondi-floor', 'ap-ap02378', 'Floor Restoration', 'Strip, seal and buff all hard flooring', 'freq-annually', '2024-06-01', '2025-06-01', 'emma-thompson', 'scheduled', 'Major disruption - coordinate with management', NOW()),
  
  -- Equipment depot specific
  ('ps-ap-minto-degrease', 'ap-ap02208', 'Workshop Degrease', 'Degrease floors in equipment maintenance areas', 'freq-quarterly', '2024-11-15', '2025-02-15', 'james-wilson', 'scheduled', 'Use industrial degreaser, ensure proper ventilation', NOW()),
  
  -- PO Box specific
  ('ps-ap-cootamundra-po', 'ap-ap00525', 'PO Box Deep Clean', 'Deep clean of all PO Box external surfaces', 'freq-quarterly', '2024-10-01', '2025-01-01', 'emma-thompson', 'scheduled', 'Coordinate with postal operations', NOW()),
  
  -- Parcel Locker specific
  ('ps-ap-cronulla-locker', 'ap-ap00551', 'Parcel Locker Maintenance', 'Full clean and maintenance of all parcel lockers', 'freq-monthly', '2024-12-10', '2025-01-10', 'james-wilson', 'scheduled', 'Need access codes from facility manager', NOW())
ON CONFLICT (id) DO NOTHING;

-- Seed data for Australia Post periodical_services table (for AP site areas)
INSERT INTO "periodical_services" ("id", "ap_site_id", "service_name", "service_description", "frequency_id", "last_service_date", "next_service_date", "assigned_to", "status", "special_requirements", "created_at")
VALUES
  ('ap-ps-minto-vinyl', 'ap-ap02208-ap-site', 'Vinyl Floor Strip and Seal', 'Strip, seal and buff vinyl flooring', 'clean-freq-biannually', '2024-11-01', '2025-05-01', 'james-wilson', 'scheduled', 'After hours work required', NOW()),
  ('ap-ps-minto-carpet', 'ap-ap02208-ap-site', 'Carpet Deep Clean', 'Deep clean extraction of all carpets', 'clean-freq-biannually', '2024-10-01', '2025-04-01', 'emma-thompson', 'scheduled', 'Section by section during business hours', NOW()),
  ('ap-ps-bankstown-window', 'ap-ap00121-ap-site', 'Shop Front Window Clean', 'Wash and blade dry all shop front windows', 'clean-freq-monthly', '2024-12-01', '2025-01-01', 'james-wilson', 'scheduled', 'Includes branded/vinyl application windows', NOW())
ON CONFLICT (id) DO NOTHING;
