-- Seed data for scheduled_tasks table (regular cleaning schedule)
-- Creating a 2-week schedule for representative sites

-- Minchinbury (Tier 1) - 3x daily cleaning schedule
WITH date_series AS (
  SELECT generate_series('2025-01-27'::date, '2025-02-07'::date, '1 day'::interval) AS scheduled_date
)
INSERT INTO "scheduled_tasks" ("id", "property_id", "task_id", "scheduled_date", "scheduled_start_time", "scheduled_end_time", "assigned_to", "status", "completion_date", "completion_notes", "created_at")
SELECT
  'task-st-minch-' || ds.scheduled_date || '-shift' || shift_num || '-' || task_id,
  'st-ap02258',
  ct.id,
  ds.scheduled_date,
  CASE 
    WHEN shift_num = 1 THEN ds.scheduled_date + '06:00:00'::time
    WHEN shift_num = 2 THEN ds.scheduled_date + '14:00:00'::time
    ELSE ds.scheduled_date + '22:00:00'::time
  END,
  CASE 
    WHEN shift_num = 1 THEN ds.scheduled_date + '08:00:00'::time
    WHEN shift_num = 2 THEN ds.scheduled_date + '16:00:00'::time
    ELSE ds.scheduled_date + '23:59:00'::time
  END,
  CASE 
    WHEN shift_num IN (1, 2) THEN 'james-wilson'
    ELSE 'emma-thompson'
  END,
  'scheduled',
  NULL,
  NULL,
  NOW()
FROM date_series ds
CROSS JOIN (VALUES (1), (2), (3)) AS shifts(shift_num)
CROSS JOIN (VALUES 
  ('task-toilet-clean-fixtures'),
  ('task-toilet-replenish'),
  ('task-kitchen-sink-benches'),
  ('task-kitchen-tables-chairs'),
  ('task-rubbish-empty-bins')
) AS tasks(task_id)
JOIN cleaning_tasks ct ON ct.id = tasks.task_id
ON CONFLICT (id) DO NOTHING;

-- Coffs Harbour (Tier 2) - Daily cleaning schedule
WITH date_series AS (
  SELECT generate_series('2025-01-27'::date, '2025-02-07'::date, '1 day'::interval) AS scheduled_date
)
INSERT INTO "scheduled_tasks" ("id", "property_id", "task_id", "scheduled_date", "scheduled_start_time", "scheduled_end_time", "assigned_to", "status", "completion_date", "completion_notes", "created_at")
SELECT
  'task-st-coffs-' || ds.scheduled_date || '-' || task_id,
  'st-ap02405',
  ct.id,
  ds.scheduled_date,
  ds.scheduled_date + '08:00:00'::time,
  ds.scheduled_date + '14:00:00'::time,
  'emma-thompson',
  'scheduled',
  NULL,
  NULL,
  NOW()
FROM date_series ds
CROSS JOIN (VALUES 
  ('task-toilet-clean-fixtures'),
  ('task-toilet-floor'),
  ('task-kitchen-sink-benches'),
  ('task-office-computers'),
  ('task-carpet-full-vacuum'),
  ('task-glass-mirrors'),
  ('task-rubbish-empty-bins')
) AS tasks(task_id)
JOIN cleaning_tasks ct ON ct.id = tasks.task_id
ON CONFLICT (id) DO NOTHING;

-- Bankstown Post Office (Retail) - Daily cleaning schedule
WITH date_series AS (
  SELECT generate_series('2025-01-27'::date, '2025-02-07'::date, '1 day'::interval) AS scheduled_date
)
INSERT INTO "scheduled_tasks" ("id", "property_id", "task_id", "scheduled_date", "scheduled_start_time", "scheduled_end_time", "assigned_to", "status", "completion_date", "completion_notes", "created_at")
SELECT
  'task-ap-bank-' || ds.scheduled_date || '-' || task_id,
  'ap-ap00121',
  ct.id,
  ds.scheduled_date,
  ds.scheduled_date + '05:00:00'::time,  -- Early morning before store opens
  ds.scheduled_date + '08:00:00'::time,
  'james-wilson',
  'scheduled',
  NULL,
  NULL,
  NOW()
FROM date_series ds
CROSS JOIN (VALUES 
  ('task-retail-counter'),
  ('task-glass-entrance'),
  ('task-floor-sweep-mop'),
  ('task-carpet-spot-vacuum'),
  ('task-toilet-clean-fixtures'),
  ('task-rubbish-empty-bins')
) AS tasks(task_id)
JOIN cleaning_tasks ct ON ct.id = tasks.task_id
ON CONFLICT (id) DO NOTHING;

-- Weekly tasks for all sites
WITH date_series AS (
  SELECT generate_series('2025-02-03'::date, '2025-02-03'::date, '1 week'::interval) AS scheduled_date
)
INSERT INTO "scheduled_tasks" ("id", "property_id", "task_id", "scheduled_date", "scheduled_start_time", "scheduled_end_time", "assigned_to", "status", "completion_date", "completion_notes", "created_at")
SELECT
  'task-weekly-' || p.id || '-' || ds.scheduled_date || '-' || task_id,
  p.id,
  ct.id,
  ds.scheduled_date,
  ds.scheduled_date + '09:00:00'::time,
  ds.scheduled_date + '16:00:00'::time,
  CASE WHEN p.type = 'Retail' THEN 'james-wilson' ELSE 'emma-thompson' END,
  'scheduled',
  NULL,
  NULL,
  NOW()
FROM date_series ds
CROSS JOIN properties p
CROSS JOIN (VALUES 
  ('task-glass-windows'),
  ('task-floor-machine-buff'),
  ('task-kitchen-fridges'),
  ('task-po-box-clean'),
  ('task-parcel-locker-screens')
) AS tasks(task_id)
JOIN cleaning_tasks ct ON ct.id = tasks.task_id
WHERE p.tier IS NOT NULL 
  AND ct.id IN (
    CASE 
      WHEN p.type = 'Retail' THEN 'task-po-box-clean'
      WHEN p.type = 'Industrial and Logistics' THEN 'task-floor-machine-buff'
      ELSE ct.id
    END
  )
ON CONFLICT (id) DO NOTHING;

-- Australia Post scheduled_cleaning_tasks table
WITH date_series AS (
  SELECT generate_series('2025-01-27'::date, '2025-02-07'::date, '1 day'::interval) AS scheduled_date
)
INSERT INTO "scheduled_cleaning_tasks" ("id", "ap_site_id", "task_id", "scheduled_date", "scheduled_time", "assigned_to", "status", "completion_time", "completion_notes", "quality_check_required", "quality_check_completed", "quality_check_by", "quality_check_notes", "created_at")
SELECT
  'ap-task-' || aps.id || '-' || ds.scheduled_date || '-' || task_id,
  aps.id,
  ct.id,
  ds.scheduled_date,
  CASE 
    WHEN aps.facility_type = 'Retail' THEN '05:00:00'
    WHEN aps.tier = 1 THEN '06:00:00'
    ELSE '08:00:00'
  END,
  CASE 
    WHEN aps.region = 'Metro' THEN 'james-wilson'
    ELSE 'emma-thompson'
  END,
  'scheduled',
  NULL,
  NULL,
  CASE WHEN aps.tier = 1 THEN true ELSE false END,
  false,
  NULL,
  NULL,
  NOW()
FROM date_series ds
CROSS JOIN australia_post_sites aps
CROSS JOIN (VALUES 
  ('task-toilet-clean-fixtures'),
  ('task-floor-sweep-mop'),
  ('task-rubbish-empty-bins')
) AS tasks(task_id)
JOIN cleaning_tasks ct ON ct.id = tasks.task_id
WHERE ds.scheduled_date NOT IN ('2025-01-29', '2025-02-02', '2025-02-06')  -- Skip some days for Tier 3-5
  OR aps.tier <= 2
ON CONFLICT (id) DO NOTHING;
