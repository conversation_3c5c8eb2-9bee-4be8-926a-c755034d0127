-- Seed data for cleaning_areas table based on the Star Track and Australia Post specifications
INSERT INTO "cleaning_areas" ("id", "name", "area_category", "created_at")
VALUES 
  -- Common areas across both specifications
  ('area-toilets', 'Toilets & Shower Room', 'Amenities', NOW()),
  ('area-kitchen', 'Kitchen', 'Amenities', NOW()),
  ('area-office', 'General Office', 'Office Areas', NOW()),
  ('area-tea-room', 'Tea Rooms and Kitchenette / Lunch Areas', 'Amenities', NOW()),
  ('area-glass', 'Glass Cleaning', 'General Areas', NOW()),
  ('area-hard-flooring', 'Hard Flooring', 'General Areas', NOW()),
  ('area-carpets', 'Carpeted Floors', 'General Areas', NOW()),
  ('area-rubbish', 'Rubbish Cleaning', 'General Areas', NOW()),
  ('area-dry-dusting', 'Dry Dusting', 'General Areas', NOW()),
  ('area-damp-cleaning', 'Damp Cleaning', 'General Areas', NOW()),
  
  -- Star Track specific areas
  ('area-reception', 'Reception', 'Public Areas', NOW()),
  ('area-board-rooms', 'Board Rooms and Training rooms', 'Office Areas', NOW()),
  ('area-leather-furniture', 'Leather / Vinyl / Fabric Furniture', 'General Areas', NOW()),
  ('area-miscellaneous', 'Miscellaneous', 'General Areas', NOW()),
  ('area-stairways', 'Stairways', 'General Areas', NOW()),
  ('area-external', 'External Areas', 'External Areas', NOW()),
  ('area-dock', 'Dock / Loading', 'Industrial Areas', NOW()),
  ('area-bike-sheds', 'Bike Sheds', 'External Areas', NOW()),
  ('area-warehouse', 'Warehouse', 'Industrial Areas', NOW()),
  
  -- Australia Post specific areas  
  ('area-production', 'Production', 'Industrial Areas', NOW()),
  ('area-retail-counter', 'Retail Counter', 'Retail Areas', NOW()),
  ('area-po-box', 'PO Box Area', 'Retail Areas', NOW()),
  ('area-parcel-lockers', 'Parcel Lockers / Smart Lockers', 'Retail Areas', NOW()),
  ('area-displays', 'Displays', 'Retail Areas', NOW()),
  ('area-ducts-vents', 'Ducts, grills and vents', 'General Areas', NOW()),
  ('area-electrical', 'Electrical fixtures and appliances', 'General Areas', NOW()),
  ('area-furnishings', 'Furnishings and fixtures', 'General Areas', NOW()),
  ('area-walls-ceilings', 'Walls, skirtings and ceilings', 'General Areas', NOW()),
  ('area-doors', 'Doors', 'General Areas', NOW())
ON CONFLICT (id) DO NOTHING;

-- Seed data for cleaning_tasks table based on specifications
INSERT INTO "cleaning_tasks" ("id", "area_id", "name", "description", "task_type", "standard_duration_minutes", "equipment_required", "materials_required", "safety_requirements", "created_at")
VALUES
  -- Toilet tasks
  ('task-toilet-clean-fixtures', 'area-toilets', 'Clean and disinfect fixtures', 'Clean and disinfect all fixtures and fittings including toilet bowls, urinals, hand basins, taps, showers, chrome fittings and unit surfaces.', 'Detail Clean', 20, '["toilet brush", "scrubber", "microfiber cloths"]', '["toilet cleaner", "disinfectant", "glass cleaner"]', 'Wear gloves and appropriate PPE', NOW()),
  ('task-toilet-mirrors-walls', 'area-toilets', 'Spot clean walls and mirrors', 'Spot clean and wash free of marks; walls, mirrors, tiles, doors, benches, furniture, partitions, washable paintwork and hand dryers.', 'Spot Clean', 15, '["microfiber cloths", "spray bottle"]', '["glass cleaner", "wall cleaner"]', 'Use caution on wet floors', NOW()),
  ('task-toilet-replenish', 'area-toilets', 'Replenish consumables', 'Replenish consumables including soap, paper towelling, toilet paper and disposable cup dispensers.', 'Replenishment', 10, NULL, '["hand soap", "paper towels", "toilet paper"]', NULL, NOW()),
  ('task-toilet-floor', 'area-toilets', 'Mop floors', 'Floor surfaces to be swept and damp mopped with a suitable disinfectant', 'Floor Cleaning', 15, '["mop", "bucket", "broom"]', '["disinfectant", "floor cleaner"]', 'Use wet floor signs', NOW()),

  -- Kitchen tasks  
  ('task-kitchen-sink-benches', 'area-kitchen', 'Clean sinks and benches', 'Clean, disinfect and wipe dry all sinks, unit tops, draining boards, drinking fountains and chrome fittings.', 'Detail Clean', 20, '["scrubber", "sponge", "cloths"]', '["kitchen cleaner", "disinfectant"]', 'Ensure electrical safety', NOW()),
  ('task-kitchen-appliances', 'area-kitchen', 'Clean appliances', 'Clean and wipe over stainless steel sinks and external surfaces of refrigerators, cupboards, walls, microwave ovens and related furniture', 'Detail Clean', 20, '["microfiber cloths", "soft scrubber"]', '["stainless steel cleaner", "multi-purpose cleaner"]', 'Unplug appliances before cleaning', NOW()),
  ('task-kitchen-tables-chairs', 'area-kitchen', 'Clean tables and chairs', 'Damp clean all tables and chairs', 'General Clean', 15, '["microfiber cloths", "spray bottle"]', '["multi-purpose cleaner"]', NULL, NOW()),
  ('task-kitchen-fridges', 'area-kitchen', 'Clean inside fridges', 'Clean inside fridges', 'Detail Clean', 30, '["sponge", "cloths"]', '["fridge cleaner", "disinfectant"]', 'Wear gloves, handle food items carefully', NOW()),
  ('task-kitchen-microwave', 'area-kitchen', 'Clean microwave', 'Damp wipe and clean inside and outside of microwave ovens', 'Detail Clean', 10, '["microfiber cloths", "soft scrubber"]', '["multi-purpose cleaner"]', 'Unplug before cleaning inside', NOW()),

  -- Office tasks
  ('task-office-desks', 'area-office', 'Clean desks and surfaces', 'Clean benches and desks', 'General Clean', 10, '["microfiber cloths", "spray bottle"]', '["multi-purpose cleaner"]', NULL, NOW()),
  ('task-office-computers', 'area-office', 'Wipe telephones and computers', 'Wipe telephones and computers', 'Spot Clean', 10, '["microfiber cloths"]', '["electronics cleaner"]', 'Use dry or slightly damp cloth only', NOW()),
  ('task-office-dust', 'area-office', 'Dust workstations', 'Dust and wipe down all work stations and general dust throughout', 'Dusting', 15, '["duster", "microfiber cloths"]', NULL, NULL, NOW()),
  ('task-office-bins', 'area-office', 'Empty bins', 'Remove centralised bins from the office area and place in the designated area for collection', 'Waste Management', 10, '["trolley"]', '["bin liners"]', NULL, NOW()),

  -- Glass cleaning tasks
  ('task-glass-mirrors', 'area-glass', 'Clean mirrors', 'Clean all glass mirrors', 'Glass Cleaning', 10, '["squeegee", "microfiber cloths"]', '["glass cleaner"]', 'Use appropriate ladders', NOW()),
  ('task-glass-entrance', 'area-glass', 'Clean entrance glass', 'Front entry doors and frames to be cleaned', 'Glass Cleaning', 15, '["squeegee", "microfiber cloths", "extension pole"]', '["glass cleaner"]', 'Use appropriate ladders', NOW()),
  ('task-glass-spot-clean', 'area-glass', 'Spot clean internal glass', 'Spot clean all glass desktops', 'Spot Clean', 10, '["microfiber cloths"]', '["glass cleaner"]', NULL, NOW()),
  ('task-glass-windows', 'area-glass', 'Clean windows', 'Clean all external windows and frames', 'Window Cleaning', 30, '["squeegee", "ladder", "extension pole"]', '["window cleaning solution"]', 'Use fall protection', NOW()),

  -- Hard flooring tasks
  ('task-floor-sweep-mop', 'area-hard-flooring', 'Sweep and mop', 'Sweep and/or mop vinyl, tiled and concrete flooring', 'Floor Cleaning', 20, '["broom", "mop", "bucket"]', '["floor cleaner"]', 'Use wet floor signs', NOW()),
  ('task-floor-machine-buff', 'area-hard-flooring', 'Machine buff vinyl', 'Vinyl floor machine buffed and dust controlled and maintained to a perfect shine', 'Floor Maintenance', 30, '["floor buffer", "pads"]', '["buffing solution"]', 'Use PPE and caution signage', NOW()),
  ('task-floor-scuff-marks', 'area-hard-flooring', 'Remove scuff marks', 'Remove scuff marks', 'Spot Clean', 10, '["scrubber", "cloths"]', '["scuff remover"]', NULL, NOW()),

  -- Carpet tasks
  ('task-carpet-spot-vacuum', 'area-carpets', 'Spot vacuum', 'Spot vacuum and de-litter', 'Vacuuming', 15, '["vacuum cleaner"]', NULL, NULL, NOW()),
  ('task-carpet-full-vacuum', 'area-carpets', 'Full vacuum', 'Full vacuum to all other floor areas', 'Vacuuming', 30, '["vacuum cleaner"]', NULL, NULL, NOW()),
  ('task-carpet-spot-clean', 'area-carpets', 'Spot clean stains', 'Spot clean stains, marks and spills', 'Spot Clean', 15, '["carpet cleaner", "cloths"]', '["carpet cleaning solution"]', NULL, NOW()),
  ('task-carpet-steam-clean', 'area-carpets', 'Steam clean carpet', 'Steam clean all carpet area', 'Deep Clean', 60, '["steam cleaner"]', '["carpet cleaning solution"]', 'Ensure proper ventilation', NOW()),

  -- Rubbish tasks
  ('task-rubbish-empty-bins', 'area-rubbish', 'Empty bins', 'Empty all waste and garbage containers and remove rubbish to designated area', 'Waste Management', 15, '["trolley"]', '["bin liners"]', 'Proper manual handling', NOW()),
  ('task-rubbish-wipe-bins', 'area-rubbish', 'Wipe bins', 'Wipe over all bin surfaces & replace bin liner bags as necessary', 'General Clean', 10, '["cloths", "spray bottle"]', '["disinfectant"]', 'Wear gloves', NOW()),
  ('task-rubbish-recycling', 'area-rubbish', 'Manage recycling', 'Ensure separation of recycled and non-recycled waste is maintained', 'Waste Management', 10, '["trolley"]', NULL, NULL, NOW()),

  -- External tasks
  ('task-external-entrances', 'area-external', 'Clean entrances', 'Sweep building entrances, foyer footpaths and clean any door mats', 'General Clean', 15, '["broom", "dustpan"]', NULL, NULL, NOW()),
  ('task-external-ashtrays', 'area-external', 'Empty ashtrays', 'Ashtrays to be emptied and external areas cleaned', 'Waste Management', 10, '["bin liners"]', NULL, 'Wear gloves', NOW()),
  ('task-external-collect-debris', 'area-external', 'Collect yard debris', 'Collect yard and car park debris', 'General Clean', 20, '["rake", "shovel", "wheelbarrow"]', NULL, NULL, NOW()),
  ('task-external-sweeper', 'area-external', 'Use ride on sweeper', 'Use ride on sweeper to clean floors', 'Machine Cleaning', 60, '["ride-on sweeper"]', NULL, 'Proper training required', NOW()),

  -- Dock/Loading tasks
  ('task-dock-sweep', 'area-dock', 'Sweep floor', 'Sweep floor of all dirt and dust', 'Floor Cleaning', 20, '["broom", "dustpan"]', NULL, 'Wear safety vest', NOW()),
  ('task-dock-degrease', 'area-dock', 'Degrease floors', 'Spot degrease floors/oil spills', 'Spot Clean', 15, '["degreaser", "scrubber"]', '["degreasing solution"]', 'Use appropriate PPE', NOW()),

  -- Bike shed tasks
  ('task-bike-empty-bins', 'area-bike-sheds', 'Empty waste bins', 'Empty waste bins', 'Waste Management', 5, '["bin liners"]', NULL, NULL, NOW()),
  ('task-bike-clean-toilets', 'area-bike-sheds', 'Clean toilets', 'Clean toilets (where present)', 'Detail Clean', 15, '["toilet brush", "cloths"]', '["toilet cleaner", "disinfectant"]', 'Wear gloves', NOW()),
  ('task-bike-sweep-mop', 'area-bike-sheds', 'Sweep and mop', 'Sweep, mop and spot degrease floors/oil spills', 'Floor Cleaning', 20, '["broom", "mop", "bucket"]', '["degreaser", "floor cleaner"]', 'Use caution with oil spills', NOW()),

  -- Production area tasks
  ('task-production-sweep', 'area-production', 'Sweep walkways', 'Sweep and de-litter pedestrian walkways, vehicle traffic lanes, stairs, under the mezzanine / platforms', 'Floor Cleaning', 30, '["broom", "dustpan"]', NULL, 'Wear high visibility vest', NOW()),
  ('task-production-dust-rails', 'area-production', 'Dust handrails', 'Dust and/or wipe handrails, column protection and bollards including on balconies', 'Dusting', 15, '["duster", "cloths"]', NULL, 'Use fall protection on balconies', NOW()),

  -- Retail specific tasks
  ('task-retail-counter', 'area-retail-counter', 'Clean counter surfaces', 'Clean counters, bench tops and surfaces free of dust, dirt, litter or stains', 'Detail Clean', 15, '["microfiber cloths", "spray bottle"]', '["surface cleaner"]', NULL, NOW()),
  ('task-po-box-clean', 'area-po-box', 'Clean PO Box exteriors', 'Clean exterior of PO Boxes free of dust, dirt, grit, litter, graffiti, marks and spots', 'Detail Clean', 30, '["cloths", "duster", "ladder"]', '["multi-purpose cleaner"]', 'Use appropriate ladders', NOW()),
  ('task-parcel-locker-screens', 'area-parcel-lockers', 'Clean locker screens', 'Clean screens free of dust, dirt, grit, litter, graffiti, marks and spots', 'Detail Clean', 15, '["microfiber cloths"]', '["screen cleaner"]', 'Use electronic-safe products', NOW())
ON CONFLICT (id) DO NOTHING;
