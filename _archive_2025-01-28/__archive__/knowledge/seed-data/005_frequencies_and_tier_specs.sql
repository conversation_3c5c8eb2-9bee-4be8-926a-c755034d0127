-- Seed data for frequency_types table
INSERT INTO "frequency_types" ("id", "name", "description", "times_per_year", "created_at")
VALUES
  ('freq-daily', 'Daily', 'Every weekday', 260, NOW()),
  ('freq-3x-daily', '3x Daily', 'Three times daily', 780, NOW()),
  ('freq-2x-daily', '2x Daily', 'Twice daily', 520, NOW()),
  ('freq-weekly', 'Weekly', 'Once per week', 52, NOW()),
  ('freq-fortnightly', 'Fortnightly', 'Every two weeks', 26, NOW()),
  ('freq-monthly', 'Monthly', 'Once per month', 12, NOW()),
  ('freq-quarterly', 'Quarterly', 'Once per quarter', 4, NOW()),
  ('freq-biannually', 'Biannually', 'Twice per year', 2, NOW()),
  ('freq-annually', 'Annually', 'Once per year', 1, NOW()),
  ('freq-daily-3of5', 'Daily (3 of 5)', 'Three days out of five operational days', 156, NOW()),
  ('freq-as-required', 'As Required', 'Performed on demand', NULL, NOW()),
  ('freq-adhoc', 'Ad-hoc', 'Performed on reactive basis', NULL, NOW())
ON CONFLICT (id) DO NOTHING;

-- Seed data for cleaning_frequencies table (for Australia Post specs)
INSERT INTO "cleaning_frequencies" ("id", "name", "description", "times_per_year", "created_at")
VALUES
  ('clean-freq-daily', 'Daily', 'Every weekday', 260, NOW()),
  ('clean-freq-3x-daily', '3x Daily', 'Three times daily', 780, NOW()),
  ('clean-freq-2x-daily', '2x Daily', 'Twice daily', 520, NOW()),
  ('clean-freq-weekly', 'Weekly', 'Once per week', 52, NOW()),
  ('clean-freq-fortnightly', 'Fortnightly', 'Every two weeks', 26, NOW()),
  ('clean-freq-monthly', 'Monthly', 'Once per month', 12, NOW()),
  ('clean-freq-quarterly', 'Quarterly', 'Once per quarter', 4, NOW()),
  ('clean-freq-biannually', 'Biannually', 'Twice per year', 2, NOW()),
  ('clean-freq-annually', 'Annually', 'Once per year', 1, NOW()),
  ('clean-freq-daily-3of5', 'Daily (3 of 5)', 'Three days out of five operational days', 156, NOW()),
  ('clean-freq-as-required', 'As Required', 'Performed on demand', NULL, NOW()),
  ('clean-freq-adhoc', 'Ad-hoc', 'Performed on reactive basis', NULL, NOW())
ON CONFLICT (id) DO NOTHING;

-- Seed data for tier_specifications table
INSERT INTO "tier_specifications" ("id", "name", "description", "tier_level", "created_at")
VALUES
  ('tier-1', 'Tier 1 - Large Industrial', 'Large industrial delivery centres or commercial office spaces, typically 24 hour operation with intensive cleaning requirements', 1, NOW()),
  ('tier-2', 'Tier 2 - Standard Industrial', 'Smaller scale delivery and sorting centres with standard daily cleaning', 2, NOW()),
  ('tier-3', 'Tier 3 - Regional', 'Smaller delivery centres with reduced attendance to reflect scale and usage', 3, NOW()),
  ('tier-4', 'Tier 4 - Small Regional', 'Small regional facilities with limited cleaning frequency', 4, NOW()),
  ('tier-5', 'Tier 5 - Remote', 'Remote facilities with minimal cleaning requirements', 5, NOW())
ON CONFLICT (id) DO NOTHING;

-- Seed data for Australia Post tier specifications
INSERT INTO "australia_post_tier_specs" ("id", "tier", "description", "cleaning_frequency_multiplier", "created_at")
VALUES
  ('ap-tier-1', 1, 'Large complexed industrial processing environments with multiple shifts requiring intensive cleaning regime', 1.5, NOW()),
  ('ap-tier-2', 2, 'Standard industrial facilities with regular daily cleaning', 1.0, NOW()),
  ('ap-tier-3', 3, 'Regional facilities with reduced frequency cleaning (3 out of 5 days)', 0.6, NOW()),
  ('ap-tier-4', 4, 'Small regional facilities with weekly maintenance', 0.4, NOW()),
  ('ap-tier-5', 5, 'Remote facilities with fortnightly or monthly service', 0.25, NOW())
ON CONFLICT (id) DO NOTHING;

-- Seed data for contract_specifications table
INSERT INTO "contract_specifications" ("id", "client_id", "client_name", "contract_name", "contract_number", "start_date", "end_date", "contract_value", "contract_manager_id", "status", "special_requirements", "created_at")
VALUES
  ('contract-star-track', 'star-track-client', 'Star Track', 'Star Track National Cleaning Services', 'ST-2025-001', '2025-01-01', '2027-12-31', 5000000.00, 'mark-brady', 'active', 'National contract covering 40 facilities with tier-based cleaning specifications', NOW()),
  ('contract-aus-post', 'aus-post-client', 'Australia Post/JLL', 'Australia Post Cleaning and Maintenance Services', 'AP-2025-001', '2025-01-01', '2027-12-31', 10000000.00, 'gaurav-majumdar', 'active', 'Comprehensive cleaning services for postal facilities including industrial, retail, and mixed-use sites', NOW())
ON CONFLICT (id) DO NOTHING;

-- Seed data for retail_cleaning_scope table (from Australia Post retail specs)
INSERT INTO "retail_cleaning_scope" ("id", "area", "element", "requirement", "frequency_id", "category", "notes", "created_at")
VALUES
  ('retail-scope-floor-1', 'Front of House', 'Hard Flooring', 'Free of dust, dirt, grit, litter, chewing gum, marks and spots, water or other liquids', 'freq-daily', 'Floors', 'Housekeeping is site staff responsibility', NOW()),
  ('retail-scope-floor-2', 'Front of House', 'Hard Flooring', 'Free of polish or other residue build-up at edges, corners and walkways', 'freq-weekly', 'Floors', NULL, NOW()),
  ('retail-scope-floor-3', 'Front of House', 'Hard Flooring', 'Free of scuffs or scratches on walkways, around furniture and at pivot points', 'freq-weekly', 'Floors', NULL, NOW()),
  ('retail-scope-floor-4', 'Front of House', 'Hard Flooring', 'Polished or buffed floors are of a uniform finish', 'freq-monthly', 'Floors', NULL, NOW()),
  ('retail-scope-carpet-1', 'Front of House', 'Carpets', 'Free of dust, dirt, grit, litter, chewing gum, marks and spots, water or other liquids', 'freq-daily', 'Floors', NULL, NOW()),
  ('retail-scope-carpet-2', 'Front of House', 'Carpets', 'Free of debris on walkways, around furniture and at pivot points', 'freq-weekly', 'Floors', NULL, NOW()),
  ('retail-scope-carpet-3', 'Front of House', 'Carpets', 'Carpets are of an even appearance without flattened pile', 'freq-monthly', 'Floors', NULL, NOW()),
  ('retail-scope-walls-1', 'Front of House', 'Walls, skirtings and ceilings', 'Walls, skirtings and ceilings; free of dust, dirt, grit, lint, soil, film and cobwebs', 'freq-weekly', 'Surfaces', 'Use extension pole where possible', NOW()),
  ('retail-scope-walls-2', 'Front of House', 'Walls, skirtings and ceilings', 'Walls and skirtings; free of removable marks caused by furniture, equipment or persons', 'freq-weekly', 'Surfaces', NULL, NOW()),
  ('retail-scope-walls-3', 'Front of House', 'Walls, skirtings and ceilings', 'Light switches are free of fingerprints, scuffs and any other marks', 'freq-daily', 'Surfaces', NULL, NOW()),
  ('retail-scope-glass-1', 'Front of House', 'Windows & Glass', 'Entrance windows spot cleaned of streaks, spots and marks, including fingerprints and smudges', 'freq-daily', 'Glass', 'Entrance Glass = entrance door glass + all windows either side', NOW()),
  ('retail-scope-glass-2', 'Front of House', 'Windows & Glass', 'Internal glass spot cleaned of streaks, spots and marks, including fingerprints and smudges', 'freq-daily', 'Glass', NULL, NOW()),
  ('retail-scope-glass-3', 'Front of House', 'Windows & Glass', 'Window frames, tracks and ledges are clear and free of dust, dirt, grit, marks and spots', 'freq-weekly', 'Glass', NULL, NOW()),
  ('retail-scope-doors-1', 'Front of House', 'Doors', 'Internal and external doors and doorframes are free of dust, dirt, grit, lint, chewing gum, soil, film, fingerprints and cobwebs', 'freq-daily', 'Surfaces', 'Includes cleaning of glass window within doors', NOW()),
  ('retail-scope-doors-2', 'Front of House', 'Doors', 'Doors and doorframes are free of removable marks caused by furniture, equipment or people', 'freq-weekly', 'Surfaces', NULL, NOW()),
  ('retail-scope-external-1', 'Front of House', 'External Areas', 'Ramps, landings, steps, stairwells, entrances, porches, balconies, loading areas, patios, fire exits, eaves and external light fittings are free of rubbish, leaves, cobwebs, gum, cigarette butts and animal excrement', 'freq-daily', 'External', NULL, NOW()),
  ('retail-scope-external-2', 'Front of House', 'External Areas', 'Handrails are clean and free of stains', 'freq-weekly', 'External', NULL, NOW()),
  ('retail-scope-external-3', 'Front of House', 'External Areas', 'Outdoor furniture on patios, balconies or undercover is clean and fit for use', 'freq-weekly', 'External', 'Only furniture in close proximity to the outlet', NOW()),
  ('retail-scope-waste-1', 'Front of House', 'Waste', 'Waste/rubbish bins or containers are clean inside and out', 'freq-daily', 'Waste Management', NULL, NOW()),
  ('retail-scope-waste-2', 'Front of House', 'Waste', 'Waste is removed for collection', 'freq-daily', 'Waste Management', 'Staff to breakdown and flatten cardboard boxes', NOW()),
  ('retail-scope-furnishings-1', 'Front of House', 'Furnishings and fixtures', 'Hard surface furniture is free of marks, dust, dirt, fingerprints and spillages', 'freq-daily', 'Furniture', NULL, NOW()),
  ('retail-scope-furnishings-2', 'Front of House', 'Furnishings and fixtures', 'Soft furnishings are free from dirt, debris, dust and stains', 'freq-weekly', 'Furniture', 'Stain removal should be attempted', NOW()),
  ('retail-scope-furnishings-3', 'Front of House', 'Furnishings and fixtures', 'Furniture legs, wheels and castors are free from mop strings, soil, film, dust and cobwebs', 'freq-weekly', 'Furniture', NULL, NOW()),
  ('retail-scope-furnishings-4', 'Front of House', 'Furnishings and fixtures', 'Counters, bench tops and cupboards are clean and free of dust, dirt, litter or stains (including around and behind computer equipment)', 'freq-daily', 'Furniture', NULL, NOW()),
  ('retail-scope-toilets-1', 'Back of House', 'Toilets', 'Clean and disinfect toilet bowls, urinals, hand basins, taps and unit surfaces. Remove waste', 'freq-daily', 'Amenities', NULL, NOW()),
  ('retail-scope-toilets-2', 'Back of House', 'Toilets', 'Walls, mirrors, tiles, doors, benches and hand dryers', 'freq-daily', 'Amenities', 'Spot clean', NOW()),
  ('retail-scope-kitchen-1', 'Back of House', 'Kitchen', 'Sinks, unit tops, draining boards, drinking fountains, tables and chairs. Remove waste', 'freq-daily', 'Amenities', 'Notify staff minimum 3 days prior to fridge clean', NOW()),
  ('retail-scope-kitchen-2', 'Back of House', 'Kitchen', 'Wall tiles, cupboard doors, fridge, microwave (inside and out) and dishwasher doors', 'freq-weekly', 'Amenities', NULL, NOW()),
  ('retail-scope-electrical-1', 'Back of House', 'Electricals', 'Wipe keyboards, monitors and connected devices, electrical fixtures and ensure appliances are free of grease, dirt, dust, marks, stains and cobwebs', 'freq-weekly', 'Equipment', 'Use slightly damp cloth only', NOW()),
  ('retail-scope-general-1', 'Back of House', 'General', 'Desks, countertops, cupboards, cabinets, internal glass, chairs and table surface tops', 'freq-daily', 'General Areas', NULL, NOW())
ON CONFLICT (id) DO NOTHING;
