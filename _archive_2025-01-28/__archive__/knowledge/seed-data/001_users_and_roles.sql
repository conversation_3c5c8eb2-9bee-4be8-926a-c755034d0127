-- Seed data for roles table
INSERT INTO "roles" ("id", "name", "permissions", "created_at")
VALUES 
  ('admin-role', 'Administrator', '{"read": "*", "write": "*", "delete": "*", "manage_users": true, "manage_sites": true, "run_reports": true, "configure_system": true}', NOW()),
  ('manager-role', 'Manager', '{"read": "*", "write": ["tasks", "reports", "inspections"], "delete": ["tasks"], "manage_users": false, "manage_sites": true, "run_reports": true, "configure_system": false}', NOW()),
  ('inspector-role', 'Inspector', '{"read": ["sites", "tasks", "inspections"], "write": ["inspections", "action_items"], "delete": [], "manage_users": false, "manage_sites": false, "run_reports": false, "configure_system": false}', NOW()),
  ('staff-role', 'Cleaning Staff', '{"read": ["tasks", "schedules"], "write": ["task_status"], "delete": [], "manage_users": false, "manage_sites": false, "run_reports": false, "configure_system": false}', NOW()),
  ('client-role', 'Client', '{"read": ["reports", "inspections"], "write": ["feedback"], "delete": [], "manage_users": false, "manage_sites": false, "run_reports": false, "configure_system": false}', NOW())
ON CONFLICT (id) DO NOTHING;

-- Seed data for users table
INSERT INTO "users" ("id", "name", "role", "department", "email", "password", "phone", "preferences", "created_at")
VALUES 
  ('gaurav-majumdar', 'Gaurav Majumdar', 'admin-role', 'Management', '<EMAIL>', '$2a$10$KvC3tTvHJb5NVV3L5Rjo4eLKNLEH6zHOVKqFY7mJJOWKzkrQnewGm', '+61 4 1234 5678', '{"notifications": "all", "theme": "dark"}', NOW()),
  ('mark-brady', 'Mark Brady', 'manager-role', 'Operations', '<EMAIL>', '$2a$10$KvC3tTvHJb5NVV3L5Rjo4eLKNLEH6zHOVKqFY7mJJOWKzkrQnewGm', '+61 4 2345 6789', '{"notifications": "important", "theme": "light"}', NOW()),
  ('sarah-johnson', 'Sarah Johnson', 'inspector-role', 'Quality Assurance', '<EMAIL>', '$2a$10$KvC3tTvHJb5NVV3L5Rjo4eLKNLEH6zHOVKqFY7mJJOWKzkrQnewGm', '+61 4 3456 7890', '{"notifications": "tasks", "theme": "light"}', NOW()),
  ('james-wilson', 'James Wilson', 'staff-role', 'Cleaning Operations', '<EMAIL>', '$2a$10$KvC3tTvHJb5NVV3L5Rjo4eLKNLEH6zHOVKqFY7mJJOWKzkrQnewGm', '+61 4 4567 8901', '{"notifications": "assignments", "theme": "light"}', NOW()),
  ('emma-thompson', 'Emma Thompson', 'staff-role', 'Cleaning Operations', '<EMAIL>', '$2a$10$KvC3tTvHJb5NVV3L5Rjo4eLKNLEH6zHOVKqFY7mJJOWKzkrQnewGm', '+61 4 5678 9012', '{"notifications": "assignments", "theme": "light"}', NOW()),
  ('michael-lee', 'Michael Lee', 'client-role', 'Australia Post', '<EMAIL>', '$2a$10$KvC3tTvHJb5NVV3L5Rjo4eLKNLEH6zHOVKqFY7mJJOWKzkrQnewGm', '+61 4 6789 0123', '{"notifications": "reports", "theme": "light"}', NOW()),
  ('alice-chen', 'Alice Chen', 'client-role', 'Star Track', '<EMAIL>', '$2a$10$KvC3tTvHJb5NVV3L5Rjo4eLKNLEH6zHOVKqFY7mJJOWKzkrQnewGm', '+61 4 7890 1234', '{"notifications": "reports", "theme": "light"}', NOW())
ON CONFLICT (id) DO NOTHING;
