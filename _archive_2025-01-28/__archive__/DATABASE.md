# Database Documentation

This project uses Drizzle ORM with Neon PostgreSQL for database operations. This document provides information on how to work with the database.

## Database Schema

The database schema is defined in `app/db/schema.ts` and includes the following tables:

- `users`: User accounts and profiles
- `roles`: User roles and permissions
- `user_sessions`: User authentication sessions
- `messages`: Chat messages
- `contacts`: Contact information
- `companies`: Company information
- `chat_sessions`: Chat session metadata
- `chat_participants`: Chat session participants
- `properties`: Property information
- `property_areas`: Areas within properties
- `inspection_templates`: Templates for property inspections
- `inspection_reports`: Property inspection reports
- `inspection_actions`: Actions related to inspection reports
- `report_attachments`: Attachments for inspection reports
- `dashboard_metrics`: Metrics for dashboard displays
- `kpi_targets`: KPI targets for performance tracking

## Database Connection

The database connection is configured in `app/db/index.ts` using Neon PostgreSQL and Drizzle ORM:

```typescript
import { neon } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-http';
import * as schema from './schema';

// Initialize the Neon serverless driver
const sql = neon(process.env.DATABASE_URL);

// Initialize Drizzle ORM with the Neon driver and the schema
export const db = drizzle(sql, { schema });
```

## Environment Variables

The following environment variables are required:

- `DATABASE_URL`: The connection string for the Neon PostgreSQL database

## Data Access Layer

The data access layer is implemented using the repository pattern. Each repository provides methods for interacting with a specific table or set of related tables:

- `UserRepository`: Methods for user-related operations
- `ContactRepository`: Methods for contact-related operations
- `MessageRepository`: Methods for message-related operations
- `ChatSessionRepository`: Methods for chat session-related operations
- `InspectionRepository`: Methods for inspection-related operations
- `PropertyRepository`: Methods for property-related operations

## Database Scripts

The following npm scripts are available for database operations:

- `npm run db:push`: Push the schema to the database
- `npm run db:studio`: Open Drizzle Studio to view and manage data
- `npm run db:generate`: Generate migration files
- `npm run db:check`: Check the database connection
- `npm run db:verify`: Verify the database schema
- `npm run db:test`: Test the Drizzle ORM functionality
- `npm run db:migrate`: Run the complete migration process
- `npm run db:seed`: Seed the database with initial data

## Example Usage

Here's an example of how to use the repositories in your application:

```typescript
import { userRepository } from '@/app/repositories';

// Get a user by email
const user = await userRepository.findByEmail('<EMAIL>');

// Create a new user
const newUser = await userRepository.create({
  name: 'Jane Smith',
  email: '<EMAIL>',
  password: 'password123',
  role: 'User',
  department: 'General',
});

// Update a user
const updatedUser = await userRepository.update(user.id, {
  role: 'Admin',
});

// Delete a user
const deleted = await userRepository.delete(user.id);
```

## Migrations

Database migrations are handled using Drizzle Kit. The migration files are stored in the `drizzle` directory.

To create a new migration:

1. Update the schema in `app/db/schema.ts`
2. Run `npm run db:generate` to generate migration files
3. Run `npm run db:push` to apply the migration to the database

## Seeding the Database

To seed the database with initial data, run:

```bash
npm run db:seed
```

This will create sample users, properties, inspection templates, and contacts in the database.

## Drizzle Studio

Drizzle Studio provides a web interface for viewing and managing your database. To open Drizzle Studio, run:

```bash
npm run db:studio
```

This will start a local server and open Drizzle Studio in your browser.

## Troubleshooting

If you encounter issues with the database:

1. Check that your `DATABASE_URL` is correct and accessible
2. Ensure you have the necessary permissions to create and modify tables
3. Check for any error messages in the console output
4. Try running the individual database scripts to isolate the issue
