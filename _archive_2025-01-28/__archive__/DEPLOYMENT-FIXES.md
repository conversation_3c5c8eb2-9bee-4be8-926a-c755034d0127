# Deployment Fixes

This document outlines the fixes applied to resolve the deployment issues when deploying the application to Vercel.

## Issues Fixed

1. **Clerk Import Errors**
   - Fixed import of `authMiddleware` and `redirectToSignIn` in `middleware.ts` 
   - Updated imports to match Clerk NextJS v5.1.2 API

2. **Schema Import Errors**
   - Fixed import issues for `contract_specifications` in the organization service
   - Added `organization_id` field to both `properties` and `contract_specifications` tables

3. **SWC Dependencies**
   - Added SWC compiler options to `next.config.mjs`
   - Created a script to install required SWC dependencies

## Files Modified

1. `middleware.ts` - Fixed Clerk imports and authentication flow
2. `app/services/organization-service.ts` - Fixed schema imports
3. `app/db/schema.ts` - Added missing organization_id fields
4. `next.config.mjs` - Added SWC compiler configuration

## Deployment Process

To deploy the application to Vercel, follow these steps:

1. Run the fix script to ensure all dependencies are properly installed:
   ```bash
   ./fix-deployment-issues.sh
   ```

2. Deploy to Vercel:
   ```bash
   vercel --prod
   ```

3. Verify that the deployment completes without any import or dependency errors.

## Notes

- The application uses Clerk for authentication and organization management
- The Clerk version used is v5.1.2, which has a different API structure compared to previous versions
- The database schema includes organization-related tables and references
