#!/usr/bin/env bash
# Output all TSX/TS files under components/ to Markdown
output=COMPONENTS-CODE.md

# Initialize file
printf "# Components Code\n\n" > "$output"

# Loop through component files and append
find components -type f \( -name '*.tsx' -o -name '*.ts' \) | sort | while IFS= read -r file; do
  printf "## %s\n\n" "$file" >> "$output"
  printf '```tsx\n' >> "$output"
  cat "$file" >> "$output"
  printf '\n```\n\n' >> "$output"
done

printf "Generated %s with component source code.\n" "$output"
output=COMPONENTS-CODE.md

echo '# Components Code' > ""
echo >> ""

for file in   components/Auth08.tsx
components/Message.tsx
components/TextAnimation.tsx
components/assistant/Message.tsx
components/assistant/TextAnimation.tsx
components/assistant/assistant-chat.tsx
components/assistant/useTypingEffect.ts
components/chat/chat-window.tsx
components/client-providers.tsx
components/db-test.tsx
components/error-boundary.tsx
components/install-prompt.tsx
components/mobile-ai-interface.tsx
components/mobile-chat.tsx
components/notifications-panel.tsx
components/screens/auth-screen.tsx
components/screens/chat-screen.tsx
components/screens/contacts-screen.tsx
components/screens/corrective-actions-screen.tsx
components/screens/dashboard-screen.tsx
components/screens/forgot-password-screen.tsx
components/screens/help-screen.tsx
components/screens/history-screen.tsx
components/screens/home-screen.tsx
components/screens/inspection-dashboard.tsx
components/screens/inspection-form-screen.tsx
components/screens/inspection-report-screen.tsx
components/screens/knowledge-base-screen.tsx
components/screens/login-screen.tsx
components/screens/more-menu-screen.tsx
components/screens/new-chat-screen.tsx
components/screens/openai-screen.tsx
components/screens/profile-screen.tsx
components/screens/reports-screen.tsx
components/screens/schedule-screen.tsx
components/screens/settings-screen.tsx
components/screens/signup-screen.tsx
components/screens/tasks-screen.tsx
components/screens/team-screen.tsx
components/test-askara.tsx
components/theme-provider.tsx
components/theme-toggle.tsx
components/ui/accordion.tsx
components/ui/alert-dialog.tsx
components/ui/alert.tsx
components/ui/app-header.tsx
components/ui/aspect-ratio.tsx
components/ui/assistant-drawer-vaul.tsx
components/ui/assistant-drawer.tsx
components/ui/avatar.tsx
components/ui/badge.tsx
components/ui/breadcrumb.tsx
components/ui/button.tsx
components/ui/calendar.tsx
components/ui/card.tsx
components/ui/carousel.tsx
components/ui/chart.tsx
components/ui/checkbox.tsx
components/ui/collapsible.tsx
components/ui/command.tsx
components/ui/context-menu.tsx
components/ui/dialog.tsx
components/ui/drawer.tsx
components/ui/dropdown-menu.tsx
components/ui/form.tsx
components/ui/header-drawer.tsx
components/ui/home-indicator.tsx
components/ui/hover-card.tsx
components/ui/icons.tsx
components/ui/input-otp.tsx
components/ui/input.tsx
components/ui/label.tsx
components/ui/loading.tsx
components/ui/logo-button.tsx
components/ui/menubar.tsx
components/ui/navigation-bar.tsx
components/ui/navigation-menu.tsx
components/ui/openai-assistant-drawer.tsx
components/ui/pagination.tsx
components/ui/popover.tsx
components/ui/progress.tsx
components/ui/radio-group.tsx
components/ui/resizable.tsx
components/ui/screen-layout.tsx
components/ui/scroll-area.tsx
components/ui/select.tsx
components/ui/separator.tsx
components/ui/sheet.tsx
components/ui/sidebar.tsx
components/ui/skeleton.tsx
components/ui/slider.tsx
components/ui/sonner.tsx
components/ui/status-bar.tsx
components/ui/swipe-detector.tsx
components/ui/swipeable-container.tsx
components/ui/switch.tsx
components/ui/table.tsx
components/ui/tabs.tsx
components/ui/textarea.tsx
components/ui/toast-context.tsx
components/ui/toast.tsx
components/ui/toaster.tsx
components/ui/toggle-group.tsx
components/ui/toggle.tsx
components/ui/tooltip.tsx
components/ui/use-mobile.tsx
components/ui/use-toast.ts
components/useTypingEffect.ts
components/user-profile.tsx
components/voice-assistant/use-typing-effect.ts
components/voice-assistant/voice-assistant-provider.tsx; do
  echo ##
