import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Debug: Print the DATABASE_URL
console.log('DATABASE_URL:', process.env.DATABASE_URL ? 'Found' : 'Not found');

const sql = neon(process.env.DATABASE_URL);

async function verifyCredentials(email, password) {
  try {
    // Find user by email
    const users = await sql('SELECT * FROM users WHERE email = $1', [email]);

    if (!users || users.length === 0) {
      return null;
    }

    const user = users[0];

    // Compare passwords
    if (user.password !== password) {
      return null;
    }

    // Remove password before returning
    const { password: _, ...userWithoutPassword } = user;
    return userWithoutPassword;
  } catch (error) {
    console.error('Error verifying credentials:', error);
    return null;
  }
}

async function testLogin() {
  console.log('Testing login functionality with real users...');

  // Test valid credentials for <PERSON>
  const validEmail = '<EMAIL>';
  const validPassword = 'ARAPaulPS!';

  console.log(`Testing login with valid credentials: ${validEmail} / ${validPassword}`);
  const validUser = await verifyCredentials(validEmail, validPassword);

  if (validUser) {
    console.log('✅ Login successful!');
    console.log('User details:', validUser);
  } else {
    console.error('❌ Login failed with valid credentials');
  }

  // Test valid credentials for Shannon Laffey
  const validEmail2 = '<EMAIL>';
  const validPassword2 = 'ARAShannonPS!';

  console.log(`\nTesting login with valid credentials: ${validEmail2} / ${validPassword2}`);
  const validUser2 = await verifyCredentials(validEmail2, validPassword2);

  if (validUser2) {
    console.log('✅ Login successful!');
    console.log('User details:', validUser2);
  } else {
    console.error('❌ Login failed with valid credentials');
  }

  // Test invalid credentials
  const invalidPassword = 'wrongpassword';

  console.log(`\nTesting login with invalid credentials: ${validEmail} / ${invalidPassword}`);
  const invalidUser = await verifyCredentials(validEmail, invalidPassword);

  if (!invalidUser) {
    console.log('✅ Login correctly rejected with invalid credentials');
  } else {
    console.error('❌ Login incorrectly succeeded with invalid credentials');
  }

  // Test non-existent user
  const nonExistentEmail = '<EMAIL>';

  console.log(`\nTesting login with non-existent user: ${nonExistentEmail} / ${validPassword}`);
  const nonExistentUser = await verifyCredentials(nonExistentEmail, validPassword);

  if (!nonExistentUser) {
    console.log('✅ Login correctly rejected for non-existent user');
  } else {
    console.error('❌ Login incorrectly succeeded for non-existent user');
  }
}

testLogin();
