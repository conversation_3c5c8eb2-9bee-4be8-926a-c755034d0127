#!/bin/bash

# This script fixes the webpack error by updating the Next.js configuration

echo "Fixing webpack error in Next.js build..."

# Step 1: Clean build cache
echo "1. Cleaning build cache..."
rm -rf .next
rm -rf node_modules/.cache

# Step 2: Create a backup of the current next.config.mjs
echo "2. Creating backup of next.config.mjs..."
cp next.config.mjs next.config.mjs.bak

# Step 3: Update the next.config.mjs file
echo "3. Updating next.config.mjs..."
cat > next.config.mjs << 'EOL'
let userConfig = undefined
try {
  // try to import ESM first
  userConfig = await import('./v0-user-next.config.mjs')
} catch (e) {
  try {
    // fallback to CJS import
    userConfig = await import("./v0-user-next.config");
  } catch (innerError) {
    // ignore error
  }
}

/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  webpack: (config, { isServer }) => {
    // Fix for WebpackError constructor issue
    config.optimization = {
      ...config.optimization,
      minimize: false // Disable minification to avoid the minify-webpack-plugin error
    };
    
    // Ignore specific modules that might cause issues
    config.resolve = {
      ...config.resolve,
      fallback: {
        ...config.resolve?.fallback,
        fs: false,
        path: false,
      },
    };
    
    return config;
  },
  experimental: {
    // Disable experimental features that might cause issues
    webpackBuildWorker: false,
    parallelServerBuildTraces: false,
    parallelServerCompiles: false,
    turbo: {
      // Using default Turbopack rules; custom rules removed to match schema
      rules: {
        // Configure custom loaders for specific file extensions
        // https://turbo.build/pack/docs/features/rules
      },
      resolveAlias: {
        // Configure module resolution aliases
        // https://turbo.build/pack/docs/features/resolve-alias
      },
    },
  },
  // Add SWC options
  compiler: {
    // Enables the styled-components SWC transform
    styledComponents: true
  }
}

if (userConfig) {
  // ESM imports will have a "default" property
  const config = userConfig.default || userConfig

  for (const key in config) {
    if (
      typeof nextConfig[key] === 'object' &&
      !Array.isArray(nextConfig[key])
    ) {
      nextConfig[key] = {
        ...nextConfig[key],
        ...config[key],
      }
    } else {
      nextConfig[key] = config[key]
    }
  }
}

export default nextConfig
EOL

echo "Next.js configuration updated successfully!"
echo "You can now try running 'pnpm build' again."
echo "If you encounter any issues, you can restore the original configuration with:"
echo "cp next.config.mjs.bak next.config.mjs"
