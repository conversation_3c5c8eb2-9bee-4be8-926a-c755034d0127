// This script checks the database connection using the Neon PostgreSQL client
const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

async function checkConnection() {
  console.log('Checking database connection...');
  
  const databaseUrl = process.env.DATABASE_URL;
  
  if (!databaseUrl) {
    console.error('❌ DATABASE_URL not found in environment variables');
    return;
  }
  
  try {
    console.log('Connecting to database...');
    const sql = neon(databaseUrl);
    
    // Test the connection
    const result = await sql('SELECT 1 as test');
    if (result && result[0] && result[0].test === 1) {
      console.log('✅ Database connection successful');
    } else {
      console.error('❌ Database connection failed: Unexpected response');
    }
    
    // List tables in the database
    const tables = await sql(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `);
    
    console.log('\nTables in the database:');
    if (tables.length === 0) {
      console.log('No tables found');
    } else {
      tables.forEach(table => {
        console.log(`- ${table.table_name}`);
      });
    }
  } catch (error) {
    console.error('❌ Database error:', error);
  }
}

// Run the check
checkConnection();
