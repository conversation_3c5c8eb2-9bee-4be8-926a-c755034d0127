import { MC<PERSON>lient } from 'mcp-client';

async function testMcpServer() {
  console.log('Connecting to MCP server...');
  
  const client = new MCPClient({
    serverUrl: 'http://localhost:3005/sse'
  });
  
  try {
    await client.connect();
    console.log('Connected to MCP server');
    
    // Get available tools
    const tools = client.getTools();
    console.log('Available tools:', tools.map(tool => tool.name));
    
    // Test list_tables tool
    console.log('\nTesting list_tables tool...');
    const tables = await client.callTool('list_tables', {});
    console.log('Tables in the database:', tables);
    
    // Test run_query tool
    console.log('\nTesting run_query tool...');
    const queryResult = await client.callTool('run_query', {
      query: 'SELECT COUNT(*) FROM messages'
    });
    console.log('Query result:', queryResult);
    
    // Test get_messages tool
    console.log('\nTesting get_messages tool...');
    const messages = await client.callTool('get_messages', {
      session_id: 'test-session'
    });
    console.log('Messages:', messages);
    
    // Test create_message tool
    console.log('\nTesting create_message tool...');
    const newMessage = await client.callTool('create_message', {
      session_id: 'test-session',
      content: 'Hello from MCP client',
      role: 'user'
    });
    console.log('New message:', newMessage);
    
  } catch (error) {
    console.error('Error testing MCP server:', error);
  } finally {
    client.disconnect();
    console.log('Disconnected from MCP server');
  }
}

testMcpServer();
