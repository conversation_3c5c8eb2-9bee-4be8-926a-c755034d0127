import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const sql = neon(process.env.DATABASE_URL);

async function main() {
  try {
    console.log('Testing database connection...');
    const testResult = await sql('SELECT 1 as test');
    console.log('Database connection successful:', testResult);
    
    console.log('Clearing existing users...');
    await sql('DELETE FROM users');
    
    console.log('Resetting ID sequence...');
    await sql('ALTER SEQUENCE users_id_seq RESTART WITH 1');
    
    console.log('Adding new users...');
    
    // Add <PERSON>
    await sql(`
      INSERT INTO users (name, role, department, email, phone, password)
      VALUES ($1, $2, $3, $4, $5, $6)
    `, [
      '<PERSON>',
      'Chief Executive Officer',
      'VIC',
      '<EMAIL>',
      '0403 244 691',
      'ARAPaulPS!'
    ]);
    
    console.log('Added <PERSON>');
    
    // Add <PERSON>
    await sql(`
      INSERT INTO users (name, role, department, email, phone, password)
      VALUES ($1, $2, $3, $4, $5, $6)
    `, [
      'Shannon Laffey',
      'Executive General Manager',
      'VIC',
      '<EMAIL>',
      '0423 532 977',
      'ARAShannonPS!'
    ]);
    
    console.log('Added Shannon Laffey');
    
    // Add Morgan Chletsos
    await sql(`
      INSERT INTO users (name, role, department, email, phone, password)
      VALUES ($1, $2, $3, $4, $5, $6)
    `, [
      'Morgan Chletsos',
      'Executive Assistant',
      'VIC',
      '<EMAIL>',
      '0450 113 726',
      'ARAMorganPS!'
    ]);
    
    console.log('Added Morgan Chletsos');
    
    // Check the users
    const users = await sql('SELECT * FROM users');
    console.log('Users in database:', users);
  } catch (error) {
    console.error('Error:', error);
  }
}

main();
