#!/bin/bash

# This script copies the refactored ARA Property Services app to the parent directory with the name araps-refactored

# Ensure we're in the right directory
cd "$(dirname "$0")"

# Create the target directory in the parent folder
echo "Creating target directory in parent folder..."
mkdir -p ../araps-refactored

# Copy the refactored project to the parent directory
echo "Copying refactored project to ../araps-refactored/..."
rsync -av --exclude="node_modules" --exclude=".next" ara-property-services-refactored/ ../araps-refactored/

# Copy the .env file if it exists
if [ -f ".env" ]; then
  echo "Copying .env file..."
  cp .env ../araps-refactored/
fi

# Copy any other necessary files that might not be in the refactored project
echo "Copying additional files..."
cp copy-refactored-project.sh ../araps-refactored/

# Clean up (optional - comment out if you want to keep the original files)
# echo "Cleaning up..."
# rm -rf ara-property-services-refactored Crackit-AI

echo "Done! The refactored ARA Property Services app has been copied to ../araps-refactored/"
echo "You can now cd into ../araps-refactored/ to work with the refactored project."
