import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function testNeonConnection() {
  console.log('Testing Neon database connection...');
  
  const databaseUrl = process.env.DATABASE_URL;
  
  if (!databaseUrl) {
    console.error('❌ DATABASE_URL not found in environment variables');
    return;
  }
  
  try {
    const sql = neon(databaseUrl);
    
    // Test the connection by executing a simple query
    const result = await sql('SELECT 1 as test');
    
    if (result && result[0] && result[0].test === 1) {
      console.log('✅ Database connection successful');
      
      // List tables in the database
      const tables = await sql(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
      `);
      
      console.log('Tables in the database:');
      tables.forEach(table => {
        console.log(`- ${table.table_name}`);
      });
    } else {
      console.error('❌ Database connection failed: Unexpected response');
    }
  } catch (error) {
    console.error('❌ Database connection failed:', error);
  }
}

testNeonConnection();
