#!/bin/bash

# Database Migration to Drizzle ORM with Neon
# This script runs the complete migration process

# Set colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print section header
print_header() {
  echo -e "\n${BLUE}=== $1 ===${NC}"
}

# Function to print success message
print_success() {
  echo -e "${GREEN}✅ $1${NC}"
}

# Function to print error message
print_error() {
  echo -e "${RED}❌ $1${NC}"
}

# Check if DATABASE_URL is set
if [ -z "$DATABASE_URL" ]; then
  if [ -f .env.local ]; then
    export $(grep -v '^#' .env.local | xargs)
    print_success "Loaded environment from .env.local"
  elif [ -f .env ]; then
    export $(grep -v '^#' .env | xargs)
    print_success "Loaded environment from .env"
  else
    print_error "No .env or .env.local file found"
    exit 1
  fi
fi

if [ -z "$DATABASE_URL" ]; then
  print_error "DATABASE_URL is not set in environment variables"
  exit 1
fi

# Step 1: Check if drizzle-kit is installed
print_header "CHECKING DEPENDENCIES"
if ! npx drizzle-kit --version &> /dev/null; then
  print_error "drizzle-kit is not installed"
  echo "Installing drizzle-kit..."
  npm install -D drizzle-kit
else
  print_success "drizzle-kit is installed"
fi

if ! grep -q "drizzle-orm" package.json; then
  print_error "drizzle-orm is not installed"
  echo "Installing drizzle-orm..."
  npm install drizzle-orm
else
  print_success "drizzle-orm is installed"
fi

# Step 2: Create drizzle directory if it doesn't exist
print_header "PREPARING DIRECTORIES"
if [ ! -d "drizzle" ]; then
  mkdir -p drizzle
  print_success "Created drizzle directory"
else
  print_success "drizzle directory already exists"
fi

# Step 3: Generate migrations
print_header "GENERATING MIGRATIONS"
npx drizzle-kit generate:pg
if [ $? -ne 0 ]; then
  print_error "Failed to generate migrations"
  exit 1
fi
print_success "Migrations generated successfully"

# Step 4: Push schema to database
print_header "PUSHING SCHEMA TO DATABASE"
npx drizzle-kit push:pg
if [ $? -ne 0 ]; then
  print_error "Failed to push schema to database"
  exit 1
fi
print_success "Schema pushed to database successfully"

# Step 5: Verify schema
print_header "MIGRATION COMPLETED"
print_success "The database has been successfully migrated to Drizzle ORM with Neon"
echo -e "\nYou can now use the following npm scripts:"
echo "  npm run db:studio - Open Drizzle Studio to view and manage data"
echo "  npm run db:generate - Generate new migrations"
echo "  npm run db:push - Push schema changes to the database"

print_header "NEXT STEPS"
echo "1. Update your application code to use Drizzle ORM"
echo "2. Test your application with the new database setup"
echo "3. Read the DB-MIGRATION.md file for more information"

exit 0
