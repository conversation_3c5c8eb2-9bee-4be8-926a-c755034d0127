import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Debug: Print the DATABASE_URL
console.log('DATABASE_URL:', process.env.DATABASE_URL ? 'Found' : 'Not found');

// Check if we can connect to the database
async function testConnection() {
  try {
    const sql = neon(process.env.DATABASE_URL);
    const result = await sql('SELECT 1 as test');
    console.log('Database connection test:', result[0].test === 1 ? 'Success' : 'Failed');
  } catch (error) {
    console.error('Database connection test failed:', error);
  }
}

await testConnection();

async function updateRealUsers() {
  console.log('Updating users table with real ARA Property Services personnel...');

  const databaseUrl = process.env.DATABASE_URL;

  if (!databaseUrl) {
    console.error('❌ DATABASE_URL not found in environment variables');
    return;
  }

  try {
    const sql = neon(databaseUrl);

    // First, clear the existing users table
    console.log('Clearing existing users table...');
    await sql('DELETE FROM users');
    console.log('✅ Users table cleared');

    // Reset the sequence for the id column
    await sql('ALTER SEQUENCE users_id_seq RESTART WITH 1');
    console.log('✅ ID sequence reset');

    // Define the real ARA Property Services personnel
    const personnel = [
      {
        name: "Paul McCann",
        position: "Chief Executive Officer",
        email: "<EMAIL>",
        phone: "0403 244 691",
        state: "VIC"
      },
      {
        name: "Mark Brady",
        position: "Senior Estimator",
        email: "<EMAIL>",
        phone: "0477 806 648",
        state: "VIC"
      },
      {
        name: "Gaurav Majumdar",
        position: "Head of Optimisation & Performance",
        email: "<EMAIL>",
        phone: "0417 741 543",
        state: "VIC"
      },
      {
        name: "Charles McCann",
        position: "Unknown",
        email: "<EMAIL>",
        phone: "Unknown",
        state: "Unknown"
      },
      {
        name: "Charlie McCann",
        position: "Unknown",
        email: "<EMAIL>",
        phone: "Unknown",
        state: "Unknown"
      },
      {
        name: "Shannon Laffey",
        position: "Executive General Manager",
        email: "<EMAIL>",
        phone: "0423 532 977",
        state: "VIC"
      },
      {
        name: "Michael O'Loughlin",
        position: "Managing Director - IS",
        email: "<EMAIL>",
        phone: "Unknown",
        state: "NSW"
      },
      {
        name: "Morgan Chletsos",
        position: "Executive Assistant",
        email: "<EMAIL>",
        phone: "0450 113 726",
        state: "VIC"
      },
      {
        name: "Amanga Rajasinghe",
        position: "Financial Controller",
        email: "<EMAIL>",
        phone: "0413 207 317",
        state: "VIC"
      },
      {
        name: "Sophie Feng",
        position: "Finance Manager",
        email: "<EMAIL>",
        phone: "0415 577 720",
        state: "VIC"
      },
      {
        name: "Paul Weldone",
        position: "Accountant",
        email: "<EMAIL>",
        phone: "0437 040 191",
        state: "VIC"
      },
      {
        name: "Yasas Perera",
        position: "Purchasing Officer",
        email: "<EMAIL>",
        phone: "0436 426 263",
        state: "VIC"
      },
      {
        name: "Belinda Lew",
        position: "Collections Officer",
        email: "<EMAIL>",
        phone: "0457 636 494",
        state: "VIC"
      },
      {
        name: "Linh Vu",
        position: "Accountant- AR Lead",
        email: "<EMAIL>",
        phone: "0438 171 004",
        state: "VIC"
      },
      {
        name: "Vidusha Hettiarachchige",
        position: "Finance Officer - AR",
        email: "<EMAIL>",
        phone: "0427 118 055",
        state: "VIC"
      },
      {
        name: "Tharanya Pathirannehelage",
        position: "Accountant - Accounts Payable Lead",
        email: "<EMAIL>",
        phone: "0409 866 661",
        state: "VIC"
      },
      {
        name: "Ganesh Venkatachalapathy",
        position: "Finance Officer - AP",
        email: "<EMAIL>",
        phone: "0403 442 479",
        state: "VIC"
      },
      {
        name: "Kevin Sun",
        position: "Accountant/ Payroll Team Lead",
        email: "<EMAIL>",
        phone: "0466 025 857",
        state: "VIC"
      },
      {
        name: "Sherry Xiong",
        position: "Senior Payroll Officer",
        email: "<EMAIL>",
        phone: "0477 995 379",
        state: "VIC"
      },
      {
        name: "Cedrick Lui",
        position: "Payroll Officer",
        email: "<EMAIL>",
        phone: "0436 422 880",
        state: "VIC"
      }
    ];

    // Insert each person into the users table
    for (const person of personnel) {
      // Extract first name
      const firstName = person.name.split(' ')[0];

      // Create password in the format ARA{first_name}PS!
      const password = `ARA${firstName}PS!`;

      // Insert the user
      await sql(`
        INSERT INTO users (name, role, department, email, phone, password)
        VALUES ($1, $2, $3, $4, $5, $6)
      `, [
        person.name,
        person.position,
        person.state || 'Unknown',
        person.email,
        person.phone !== 'Unknown' ? person.phone : null,
        password
      ]);

      console.log(`✅ Added user: ${person.name} (${person.email}) with password ${password}`);
    }

    console.log('✅ All users added successfully');

    // Verify the data was inserted
    const updatedUsers = await sql('SELECT id, name, role, department, email, phone, password FROM users');
    console.log(`Total users in database: ${updatedUsers.length}`);
    console.log('First 5 users:');
    console.table(updatedUsers.slice(0, 5));

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

updateRealUsers();
