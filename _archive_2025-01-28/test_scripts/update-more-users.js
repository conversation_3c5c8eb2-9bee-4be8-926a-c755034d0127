import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const sql = neon(process.env.DATABASE_URL);

async function main() {
  try {
    console.log('Adding more users...');
    
    // Define more users to add
    const users = [
      {
        name: "<PERSON>",
        position: "Senior Estimator",
        email: "<EMAIL>",
        phone: "0477 806 648",
        state: "VIC"
      },
      {
        name: "<PERSON><PERSON><PERSON><PERSON>",
        position: "Head of Optimisation & Performance",
        email: "<EMAIL>",
        phone: "0417 741 543",
        state: "VIC"
      },
      {
        name: "<PERSON>",
        position: "Unknown",
        email: "<EMAIL>",
        phone: "Unknown",
        state: "Unknown"
      },
      {
        name: "<PERSON>",
        position: "Managing Director - IS",
        email: "mi<PERSON><PERSON>@araindigenous.com.au",
        phone: "Unknown",
        state: "NSW"
      },
      {
        name: "<PERSON><PERSON>",
        position: "Financial Controller",
        email: "a<PERSON><PERSON><PERSON>@arapropertyservices.com.au",
        phone: "0413 207 317",
        state: "VIC"
      },
      {
        name: "<PERSON>",
        position: "Finance Manager",
        email: "<EMAIL>",
        phone: "0415 577 720",
        state: "VIC"
      },
      {
        name: "Kaveesha Mahanama",
        position: "Head of People, Safety and Compliance",
        email: "<EMAIL>",
        phone: "0449 653 649",
        state: "VIC"
      }
    ];
    
    // Add each user
    for (const user of users) {
      // Extract first name
      const firstName = user.name.split(' ')[0];
      
      // Create password in the format ARA{first_name}PS!
      const password = `ARA${firstName}PS!`;
      
      // Insert the user
      await sql(`
        INSERT INTO users (name, role, department, email, phone, password)
        VALUES ($1, $2, $3, $4, $5, $6)
      `, [
        user.name,
        user.position,
        user.state,
        user.email,
        user.phone !== 'Unknown' ? user.phone : null,
        password
      ]);
      
      console.log(`Added ${user.name} with password ${password}`);
    }
    
    // Check all users
    const allUsers = await sql('SELECT id, name, email, password FROM users');
    console.log('All users in database:');
    console.table(allUsers);
  } catch (error) {
    console.error('Error:', error);
  }
}

main();
