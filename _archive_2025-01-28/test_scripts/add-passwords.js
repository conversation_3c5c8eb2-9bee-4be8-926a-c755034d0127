import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function addPasswordsToUsers() {
  console.log('Adding passwords to users...');
  
  const databaseUrl = process.env.DATABASE_URL;
  
  if (!databaseUrl) {
    console.error('❌ DATABASE_URL not found in environment variables');
    return;
  }
  
  try {
    const sql = neon(databaseUrl);
    
    // Check if the password column exists
    const checkColumn = await sql(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'users' AND column_name = 'password'
    `);
    
    // If the password column doesn't exist, add it
    if (!checkColumn || checkColumn.length === 0) {
      console.log('Adding password column to users table...');
      await sql('ALTER TABLE users ADD COLUMN password TEXT');
      console.log('✅ Password column added successfully');
    } else {
      console.log('Password column already exists');
    }
    
    // Get all users
    const users = await sql('SELECT * FROM users');
    console.log(`Found ${users.length} users`);
    
    // Add a default password to each user
    // In a real application, you would use a secure password hashing function
    const defaultPassword = 'password123';
    
    for (const user of users) {
      await sql('UPDATE users SET password = $1 WHERE id = $2', [defaultPassword, user.id]);
      console.log(`✅ Added password for user: ${user.name} (${user.email})`);
    }
    
    console.log('✅ All users now have passwords');
    
    // Verify the data was updated
    const updatedUsers = await sql('SELECT id, name, email, password FROM users');
    console.log('Users with passwords:');
    console.table(updatedUsers);
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

addPasswordsToUsers();
