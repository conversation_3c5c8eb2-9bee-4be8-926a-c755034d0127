import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const sql = neon(process.env.DATABASE_URL);

async function testConnection() {
  try {
    const result = await sql('SELECT 1 as test');
    console.log('Database connection test:', result[0].test === 1 ? 'Success' : 'Failed');
    return true;
  } catch (error) {
    console.error('Database connection test failed:', error);
    return false;
  }
}

async function addFinalUsers() {
  try {
    console.log('Adding final batch of ARA Property Services personnel...');
    
    // Define more users to add
    const users = [
      {
        name: "<PERSON><PERSON><PERSON>",
        position: "Customer Care Representative",
        email: "<EMAIL>",
        phone: "0461 488 336",
        state: "VIC"
      },
      {
        name: "<PERSON><PERSON><PERSON>",
        position: "Customer Care Representative",
        email: "<EMAIL>",
        phone: "0477 998 324",
        state: "VIC"
      },
      {
        name: "<PERSON>",
        position: "Customer Care Representative",
        email: "<EMAIL>",
        phone: "0457 382 319",
        state: "VIC"
      },
      {
        name: "Pav Rai",
        position: "Head of Strategic Partnerships and Growth",
        email: "<EMAIL>",
        phone: "439016732",
        state: "VIC"
      },
      {
        name: "Cassandra Donovan",
        position: "Head of Business Development",
        email: "<EMAIL>",
        phone: "0425 720 690",
        state: "VIC"
      },
      {
        name: "Sam Khalil",
        position: "General Manager - Operations",
        email: "<EMAIL>",
        phone: "0411 391 876",
        state: "VIC"
      },
      {
        name: "Illona Saliacos",
        position: "Regional Operations Manager VIC/TAS",
        email: "<EMAIL>",
        phone: "0436 961 261",
        state: "VIC"
      },
      {
        name: "Dani Anderson",
        position: "National Operations Manager",
        email: "<EMAIL>",
        phone: "0431 534 495",
        state: "VIC"
      },
      {
        name: "Frank Lombardo",
        position: "State Manager VIC / Account Manager ACU",
        email: "<EMAIL>",
        phone: "0461 545 808",
        state: "VIC"
      },
      {
        name: "Phillip Bailey",
        position: "Regional Operations Manager - NSW/ACT/QLD",
        email: "<EMAIL>",
        phone: "0466 519 308",
        state: "NSW"
      }
    ];
    
    // Add each user
    for (const user of users) {
      // Extract first name
      const firstName = user.name.split(' ')[0];
      
      // Create password in the format ARA{first_name}PS!
      const password = `ARA${firstName}PS!`;
      
      // Check if user already exists
      const existingUser = await sql('SELECT * FROM users WHERE email = $1', [user.email]);
      
      if (existingUser && existingUser.length > 0) {
        console.log(`User ${user.name} (${user.email}) already exists, skipping...`);
        continue;
      }
      
      // Insert the user
      await sql(`
        INSERT INTO users (name, role, department, email, phone, password)
        VALUES ($1, $2, $3, $4, $5, $6)
      `, [
        user.name,
        user.position,
        user.state,
        user.email,
        user.phone !== 'Unknown' ? user.phone : null,
        password
      ]);
      
      console.log(`Added ${user.name} with password ${password}`);
    }
    
    // Check all users
    const allUsers = await sql('SELECT COUNT(*) as count FROM users');
    console.log(`Total users in database: ${allUsers[0].count}`);
    
    // Count users by department/state
    const departments = {};
    const deptUsers = await sql('SELECT department, COUNT(*) as count FROM users GROUP BY department');
    
    console.log('\nUsers by department/state:');
    deptUsers.forEach(dept => {
      console.log(`${dept.department}: ${dept.count} users`);
    });
    
  } catch (error) {
    console.error('Error adding users:', error);
  }
}

async function main() {
  // Test connection first
  const connected = await testConnection();
  if (!connected) {
    console.error('Cannot connect to database. Exiting...');
    return;
  }
  
  // Add users
  await addFinalUsers();
}

main();
