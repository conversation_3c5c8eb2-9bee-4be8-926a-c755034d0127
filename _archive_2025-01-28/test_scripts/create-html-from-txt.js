const fs = require('fs');
const path = require('path');

// Function to read all TXT files and combine them into HTML
function createHtmlFromTxtFiles() {
  const txtDir = path.join(__dirname, 'data_txt');
  const outputHtmlPath = path.join(__dirname, 'public', 'askara-knowledge.html');
  
  // Create public directory if it doesn't exist
  if (!fs.existsSync(path.join(__dirname, 'public'))) {
    fs.mkdirSync(path.join(__dirname, 'public'));
  }
  
  // Get all TXT files
  const files = fs.readdirSync(txtDir).filter(file => file.endsWith('.txt'));
  
  // Start HTML content
  let htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AskARA Knowledge Base</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 20px;
      color: #333;
    }
    h1 {
      color: #0056b3;
      border-bottom: 2px solid #0056b3;
      padding-bottom: 10px;
    }
    h2 {
      color: #0056b3;
      margin-top: 30px;
    }
    .record {
      background-color: #f9f9f9;
      border: 1px solid #ddd;
      border-radius: 5px;
      padding: 15px;
      margin-bottom: 15px;
    }
    .record-header {
      font-weight: bold;
      color: #0056b3;
      margin-bottom: 10px;
    }
    .field {
      margin-bottom: 5px;
    }
    .field-name {
      font-weight: bold;
    }
  </style>
</head>
<body>
  <h1>AskARA Knowledge Base</h1>
`;

  // Process each file
  files.forEach(file => {
    const filePath = path.join(txtDir, file);
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const fileName = file.replace('.txt', '');
    
    htmlContent += `
  <h2>${fileName}</h2>
  <div class="file-content">
`;
    
    // Split content by records
    const records = fileContent.split(/Record \d+:/g).filter(Boolean);
    
    records.forEach((record, index) => {
      htmlContent += `
    <div class="record">
      <div class="record-header">Record ${index + 1}</div>
`;
      
      // Process each line in the record
      const lines = record.trim().split('\n');
      lines.forEach(line => {
        if (line.trim()) {
          const [fieldName, ...fieldValueParts] = line.split(':');
          const fieldValue = fieldValueParts.join(':').trim();
          
          if (fieldName && fieldValue) {
            htmlContent += `
      <div class="field">
        <span class="field-name">${fieldName}:</span> ${fieldValue}
      </div>
`;
          }
        }
      });
      
      htmlContent += `
    </div>
`;
    });
    
    htmlContent += `
  </div>
`;
  });
  
  // End HTML content
  htmlContent += `
</body>
</html>
`;
  
  // Write HTML file
  fs.writeFileSync(outputHtmlPath, htmlContent);
  
  console.log(`HTML file created at: ${outputHtmlPath}`);
  return outputHtmlPath;
}

// Create the HTML file
const htmlFilePath = createHtmlFromTxtFiles();
