import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Debug: Print the DATABASE_URL
console.log('DATABASE_URL:', process.env.DATABASE_URL ? 'Found' : 'Not found');

const sql = neon(process.env.DATABASE_URL);

// Test database connection
async function testConnection() {
  try {
    const result = await sql('SELECT 1 as test');
    console.log('Database connection test:', result[0].test === 1 ? 'Success' : 'Failed');
    return true;
  } catch (error) {
    console.error('Database connection test failed:', error);
    return false;
  }
}

async function addMoreUsers() {
  try {
    console.log('Adding more ARA Property Services personnel...');

    // Define more users to add
    const users = [
      {
        name: "<PERSON>",
        position: "Accountant",
        email: "<EMAIL>",
        phone: "0437 040 191",
        state: "VIC"
      },
      {
        name: "<PERSON><PERSON><PERSON>",
        position: "Purchasing Officer",
        email: "<EMAIL>",
        phone: "0436 426 263",
        state: "VIC"
      },
      {
        name: "<PERSON> <PERSON>w",
        position: "Collections Officer",
        email: "<EMAIL>",
        phone: "0457 636 494",
        state: "VIC"
      },
      {
        name: "Linh Vu",
        position: "Accountant- AR Lead",
        email: "<EMAIL>",
        phone: "0438 171 004",
        state: "VIC"
      },
      {
        name: "Vidusha Hettiarachchige",
        position: "Finance Officer - AR",
        email: "<EMAIL>",
        phone: "0427 118 055",
        state: "VIC"
      },
      {
        name: "Tharanya Pathirannehelage",
        position: "Accountant - Accounts Payable Lead",
        email: "<EMAIL>",
        phone: "0409 866 661",
        state: "VIC"
      },
      {
        name: "Ganesh Venkatachalapathy",
        position: "Finance Officer - AP",
        email: "<EMAIL>",
        phone: "0403 442 479",
        state: "VIC"
      },
      {
        name: "Kevin Sun",
        position: "Accountant/ Payroll Team Lead",
        email: "<EMAIL>",
        phone: "0466 025 857",
        state: "VIC"
      },
      {
        name: "Sherry Xiong",
        position: "Senior Payroll Officer",
        email: "<EMAIL>",
        phone: "0477 995 379",
        state: "VIC"
      },
      {
        name: "Cedrick Lui",
        position: "Payroll Officer",
        email: "<EMAIL>",
        phone: "0436 422 880",
        state: "VIC"
      },
      {
        name: "Shilpa Chowdhury",
        position: "Compliance and ESG Lead",
        email: "<EMAIL>",
        phone: "0422 078 134",
        state: "VIC"
      },
      {
        name: "Joshua Beckman",
        position: "Senior HSE Advisor",
        email: "<EMAIL>",
        phone: "0474 047 301",
        state: "VIC"
      },
      {
        name: "Mila Limskul",
        position: "People and Culture Administrator",
        email: "<EMAIL>",
        phone: "0415 887 974",
        state: "VIC"
      },
      {
        name: "Asanka Theadore",
        position: "People and Culture Administrator",
        email: "<EMAIL>",
        phone: "0428 973 959",
        state: "VIC"
      },
      {
        name: "Cath Pichut",
        position: "Centre of Excellence Lead",
        email: "<EMAIL>",
        phone: "0417 035 435",
        state: "VIC"
      }
    ];

    // Add each user
    for (const user of users) {
      // Extract first name
      const firstName = user.name.split(' ')[0];

      // Create password in the format ARA{first_name}PS!
      const password = `ARA${firstName}PS!`;

      // Check if user already exists
      const existingUser = await sql('SELECT * FROM users WHERE email = $1', [user.email]);

      if (existingUser && existingUser.length > 0) {
        console.log(`User ${user.name} (${user.email}) already exists, skipping...`);
        continue;
      }

      // Insert the user
      await sql(`
        INSERT INTO users (name, role, department, email, phone, password)
        VALUES ($1, $2, $3, $4, $5, $6)
      `, [
        user.name,
        user.position,
        user.state,
        user.email,
        user.phone !== 'Unknown' ? user.phone : null,
        password
      ]);

      console.log(`Added ${user.name} with password ${password}`);
    }

    // Check all users
    const allUsers = await sql('SELECT COUNT(*) as count FROM users');
    console.log(`Total users in database: ${allUsers[0].count}`);

  } catch (error) {
    console.error('Error adding users:', error);
  }
}

async function main() {
  // Test connection first
  const connected = await testConnection();
  if (!connected) {
    console.error('Cannot connect to database. Exiting...');
    return;
  }

  // Add users
  await addMoreUsers();
}

main();
