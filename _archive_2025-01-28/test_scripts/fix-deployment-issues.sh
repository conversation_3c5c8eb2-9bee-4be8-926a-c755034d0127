#!/bin/bash
# <PERSON><PERSON>t to fix deployment issues

echo "Fixing deployment issues for Ask-Ara project..."

# Step 1: Update SWC dependencies
echo "1. Updating SWC dependencies..."
pnpm add -D @swc/core @swc/helpers

# Step 2: Clean build cache
echo "2. Cleaning build cache..."
rm -rf .next
rm -rf node_modules/.cache

# Step 3: Rebuild the project
echo "3. Rebuilding project..."
pnpm install
pnpm build

echo "All fixes applied! The project should now be ready for deployment to Vercel."
echo "Run 'vercel --prod' to deploy to production."
