import fetch from 'node-fetch';

async function testSseConnection() {
  console.log('Connecting to SSE endpoint...');
  
  try {
    const response = await fetch('http://localhost:3005/sse');
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    if (response.headers.get('content-type') !== 'text/event-stream') {
      throw new Error('Not an SSE endpoint!');
    }
    
    console.log('Connected to SSE endpoint');
    console.log('Response headers:', response.headers);
    
    // Read the SSE stream
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    
    while (true) {
      const { value, done } = await reader.read();
      
      if (done) {
        console.log('Stream closed');
        break;
      }
      
      const chunk = decoder.decode(value);
      console.log('Received chunk:', chunk);
      
      // Parse SSE events
      const events = chunk.split('\\n\\n').filter(Boolean);
      
      for (const event of events) {
        if (event.startsWith('data: ')) {
          const data = event.slice(6);
          try {
            const parsedData = JSON.parse(data);
            console.log('Parsed SSE event:', parsedData);
            
            // If we received the tools list, we're good to go
            if (parsedData.type === 'tools') {
              console.log('Received tools list:', parsedData.tools.map(tool => tool.name));
              return;
            }
          } catch (e) {
            console.log('Could not parse SSE event data:', data);
          }
        }
      }
    }
  } catch (error) {
    console.error('Error testing SSE connection:', error);
  }
}

testSseConnection();
