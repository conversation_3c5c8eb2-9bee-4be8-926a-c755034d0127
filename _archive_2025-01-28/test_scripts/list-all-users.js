import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const sql = neon(process.env.DATABASE_URL);

async function listAllUsers() {
  try {
    console.log('Fetching all users from the database...');
    
    // Get all users
    const users = await sql('SELECT id, name, role, department, email, password FROM users ORDER BY id');
    
    console.log(`Found ${users.length} users in the database:`);
    console.table(users);
    
    // Count users by department/state
    const departments = {};
    users.forEach(user => {
      const dept = user.department || 'Unknown';
      departments[dept] = (departments[dept] || 0) + 1;
    });
    
    console.log('\nUsers by department/state:');
    for (const [dept, count] of Object.entries(departments)) {
      console.log(`${dept}: ${count} users`);
    }
    
  } catch (error) {
    console.error('Error fetching users:', error);
  }
}

listAllUsers();
