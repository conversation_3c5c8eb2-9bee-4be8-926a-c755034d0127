#!/bin/zsh

# This script fixes the webpack error in Next.js build for zsh users

echo "Fixing webpack error in Next.js build..."

# Step 1: Clean build cache
echo "1. Cleaning build cache..."
rm -rf .next
rm -rf node_modules/.cache

# Step 2: Create a backup of the current next.config.mjs
echo "2. Creating backup of next.config.mjs..."
cp next.config.mjs next.config.mjs.bak

# Step 3: Update the next.config.mjs file with a simplified configuration
echo "3. Updating next.config.mjs..."
cat > next.config.mjs << 'EOL'
/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  webpack: (config) => {
    // Fix for WebpackError constructor issue
    config.optimization = {
      ...config.optimization,
      minimize: false // Disable minification to avoid the minify-webpack-plugin error
    };
    
    return config;
  },
  // Disable experimental features that might cause issues
  experimental: {
    webpackBuildWorker: false,
    parallelServerBuildTraces: false,
    parallelServerCompiles: false,
  },
  // Add SWC options
  compiler: {
    styledComponents: true
  }
}

export default nextConfig
EOL

echo "Next.js configuration updated successfully!"
echo "You can now try running 'pnpm build' again."
echo "If you encounter any issues, you can restore the original configuration with:"
echo "cp next.config.mjs.bak next.config.mjs"
