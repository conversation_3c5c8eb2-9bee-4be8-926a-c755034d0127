#!/bin/bash

# This script provides a comprehensive fix for the webpack error in Next.js build
# It will try to use pnpm, npm, or yarn, whichever is available

echo "Comprehensive fix for webpack error in Next.js build..."

# Step 1: Clean build cache
echo "1. Cleaning build cache..."
rm -rf .next
rm -rf node_modules/.cache

# Step 2: Create a backup of the current next.config.mjs
echo "2. Creating backup of next.config.mjs..."
cp next.config.mjs next.config.mjs.bak

# Step 3: Update the next.config.mjs file
echo "3. Updating next.config.mjs..."
cat > next.config.mjs << 'EOL'
/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  webpack: (config) => {
    // Fix for WebpackError constructor issue
    config.optimization = {
      ...config.optimization,
      minimize: false // Disable minification to avoid the minify-webpack-plugin error
    };
    
    return config;
  },
  // Disable experimental features that might cause issues
  experimental: {
    webpackBuildWorker: false,
    parallelServerBuildTraces: false,
    parallelServerCompiles: false,
  },
  // Add SWC options
  compiler: {
    styledComponents: true
  }
}

export default nextConfig
EOL

echo "Next.js configuration updated successfully!"

# Step 4: Determine which package manager is available
echo "4. Checking available package managers..."

if command -v pnpm &> /dev/null; then
    echo "Using pnpm..."
    PACKAGE_MANAGER="pnpm"
elif command -v npm &> /dev/null; then
    echo "Using npm..."
    PACKAGE_MANAGER="npm"
elif command -v yarn &> /dev/null; then
    echo "Using yarn..."
    PACKAGE_MANAGER="yarn"
else
    echo "No package manager found. Please install pnpm, npm, or yarn."
    exit 1
fi

# Step 5: Install SWC dependencies
echo "5. Installing SWC dependencies..."
if [ "$PACKAGE_MANAGER" = "pnpm" ]; then
    pnpm add -D @swc/core@1.3.100 @swc/helpers@0.5.3
elif [ "$PACKAGE_MANAGER" = "npm" ]; then
    npm install --save-dev @swc/core@1.3.100 @swc/helpers@0.5.3
else
    yarn add --dev @swc/core@1.3.100 @swc/helpers@0.5.3
fi

# Step 6: Try to build the project
echo "6. Building the project..."
if [ "$PACKAGE_MANAGER" = "pnpm" ]; then
    pnpm build
elif [ "$PACKAGE_MANAGER" = "npm" ]; then
    npm run build
else
    yarn build
fi

echo "Build process completed. If you encounter any issues, you can restore the original configuration with:"
echo "cp next.config.mjs.bak next.config.mjs"
