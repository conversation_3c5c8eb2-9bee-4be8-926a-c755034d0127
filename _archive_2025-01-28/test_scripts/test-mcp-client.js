import fetch from 'node-fetch';

async function testMcpServer() {
  const url = 'http://localhost:3005/mcp';
  
  try {
    // Test listing tables
    console.log('Testing list_tables action...');
    const listTablesResponse = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'list_tables',
        params: {}
      }),
    });
    
    const listTablesResult = await listTablesResponse.json();
    console.log('Tables in the database:', listTablesResult.result);
    
    // Test running a query
    console.log('\nTesting run_query action...');
    const runQueryResponse = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'run_query',
        params: {
          query: 'SELECT COUNT(*) FROM messages'
        }
      }),
    });
    
    const runQueryResult = await runQueryResponse.json();
    console.log('Query result:', runQueryResult.result);
    
  } catch (error) {
    console.error('Error testing MCP server:', error);
  }
}

testMcpServer();
