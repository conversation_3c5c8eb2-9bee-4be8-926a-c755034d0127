import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Debug: Print the DATABASE_URL
console.log('DATABASE_URL:', process.env.DATABASE_URL ? 'Found' : 'Not found');

async function updateUserPasswords() {
  console.log('Updating user passwords...');

  const databaseUrl = process.env.DATABASE_URL;

  if (!databaseUrl) {
    console.error('❌ DATABASE_URL not found in environment variables');
    return;
  }

  try {
    const sql = neon(databaseUrl);

    // Get all users
    const users = await sql('SELECT * FROM users');
    console.log(`Found ${users.length} users`);

    // Update password for each user following the format ARA{first_name}PS!
    for (const user of users) {
      // Extract first name from the full name
      const firstName = user.name.split(' ')[0];

      // Create the password in the format ARA{first_name}PS!
      const password = `ARA${firstName}PS!`;

      // Update the user's password
      await sql('UPDATE users SET password = $1 WHERE id = $2', [password, user.id]);
      console.log(`✅ Updated password for user: ${user.name} (${user.email}) to ${password}`);
    }

    console.log('✅ All user passwords updated successfully');

    // Verify the data was updated
    const updatedUsers = await sql('SELECT id, name, email, password FROM users');
    console.log('Users with updated passwords:');
    console.table(updatedUsers);

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

updateUserPasswords();
