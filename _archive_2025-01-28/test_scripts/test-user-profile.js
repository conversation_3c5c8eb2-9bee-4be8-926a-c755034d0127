import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Debug: Print the DATABASE_URL
console.log('DATABASE_URL:', process.env.DATABASE_URL ? 'Found' : 'Not found');

const sql = neon(process.env.DATABASE_URL);

async function testUserProfile() {
  try {
    console.log('Testing user profile functionality...');

    // Get a sample user from the database
    const users = await sql('SELECT * FROM users LIMIT 1');

    if (!users || users.length === 0) {
      console.error('No users found in the database');
      return;
    }

    const user = users[0];
    console.log('Sample user from database:');
    console.log({
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      department: user.department,
      phone: user.phone
    });

    // Simulate fetching user by ID (like our API endpoint)
    console.log(`\nFetching user with ID ${user.id}...`);
    const fetchedUser = await sql('SELECT id, name, role, department, email, phone FROM users WHERE id = $1', [user.id]);

    if (fetchedUser && fetchedUser.length > 0) {
      console.log('Successfully fetched user:');
      console.log(fetchedUser[0]);
    } else {
      console.error('Failed to fetch user by ID');
    }

    // Test user authentication
    console.log('\nTesting user authentication...');
    const authUser = await sql('SELECT * FROM users WHERE email = $1 AND password = $2', [user.email, user.password]);

    if (authUser && authUser.length > 0) {
      console.log('Authentication successful!');
      console.log('User data that would be returned by login API:');
      const { password, ...userWithoutPassword } = authUser[0];
      console.log(userWithoutPassword);
    } else {
      console.error('Authentication failed');
    }

  } catch (error) {
    console.error('Error testing user profile:', error);
  }
}

testUserProfile();
