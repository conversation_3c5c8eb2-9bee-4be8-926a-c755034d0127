import fetch from 'node-fetch';

async function testMcpServer() {
  const url = 'http://localhost:3005/sse';
  
  try {
    console.log('Testing MCP server...');
    
    // Just test if the server is responding
    const response = await fetch(url);
    
    if (response.ok) {
      console.log('MCP server is running and responding');
      console.log('Status:', response.status);
      console.log('Headers:', response.headers);
      
      // Read a bit of the stream to confirm it's SSE
      const reader = response.body.getReader();
      const { value, done } = await reader.read();
      
      if (!done) {
        const text = new TextDecoder().decode(value);
        console.log('Initial SSE response:', text);
      }
    } else {
      console.error('MCP server responded with error:', response.status, response.statusText);
    }
    
  } catch (error) {
    console.error('Error testing MCP server:', error);
  }
}

testMcpServer();
