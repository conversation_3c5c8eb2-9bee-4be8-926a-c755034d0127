import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function createUsersTable() {
  console.log('Creating users table...');
  
  const databaseUrl = process.env.DATABASE_URL;
  
  if (!databaseUrl) {
    console.error('❌ DATABASE_URL not found in environment variables');
    return;
  }
  
  try {
    const sql = neon(databaseUrl);
    
    // Create users table
    await sql(`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        name TEXT NOT NULL,
        role TEXT NOT NULL,
        department TEXT NOT NULL,
        email TEXT UNIQUE,
        phone TEXT,
        avatar TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
    `);
    
    console.log('✅ Users table created successfully');
    
    // Insert personnel data
    const users = [
      {
        name: '<PERSON>',
        role: 'Property Manager',
        department: 'Management',
        email: '<EMAIL>',
        phone: '(*************'
      },
      {
        name: '<PERSON>',
        role: 'Maintenance Supervisor',
        department: 'Maintenance',
        email: 'micha<PERSON>.<EMAIL>',
        phone: '(*************'
      },
      {
        name: '<PERSON>',
        role: 'Leasing Agent',
        department: 'Leasing',
        email: '<EMAIL>',
        phone: '(*************'
      },
      {
        name: '<PERSON>',
        role: 'Maintenance Technician',
        department: 'Maintenance',
        email: '<EMAIL>',
        phone: '(*************'
      },
      {
        name: 'Emily Patel',
        role: 'Administrative Assistant',
        department: 'Administration',
        email: '<EMAIL>',
        phone: '(*************'
      },
      {
        name: 'James Wilson',
        role: 'Regional Manager',
        department: 'Management',
        email: '<EMAIL>',
        phone: '(*************'
      },
      {
        name: 'Sophia Martinez',
        role: 'Accountant',
        department: 'Finance',
        email: '<EMAIL>',
        phone: '(*************'
      },
      {
        name: 'Robert Taylor',
        role: 'Maintenance Technician',
        department: 'Maintenance',
        email: '<EMAIL>',
        phone: '(*************'
      },
      {
        name: 'Olivia Brown',
        role: 'Leasing Manager',
        department: 'Leasing',
        email: '<EMAIL>',
        phone: '(*************'
      },
      {
        name: 'William Lee',
        role: 'Property Inspector',
        department: 'Operations',
        email: '<EMAIL>',
        phone: '(*************'
      }
    ];
    
    // Insert each user
    for (const user of users) {
      await sql(`
        INSERT INTO users (name, role, department, email, phone)
        VALUES ($1, $2, $3, $4, $5)
        ON CONFLICT (email) DO UPDATE
        SET name = $1, role = $2, department = $3, phone = $5
      `, [user.name, user.role, user.department, user.email, user.phone]);
    }
    
    console.log('✅ Personnel data inserted successfully');
    
    // Verify the data was inserted
    const result = await sql('SELECT * FROM users');
    console.log('Users in the database:');
    console.table(result);
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

createUsersTable();
