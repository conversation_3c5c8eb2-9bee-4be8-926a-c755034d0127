import fetch from 'node-fetch';
import * as eventsource from 'eventsource';
const EventSource = eventsource.default || eventsource;

async function testMcpServer() {
  const url = 'http://localhost:3005/sse';
  
  try {
    console.log('Connecting to MCP server...');
    
    // Create SSE connection
    const eventSource = new EventSource(url);
    
    eventSource.onopen = () => {
      console.log('SSE connection established');
    };
    
    eventSource.onerror = (error) => {
      console.error('SSE connection error:', error);
      eventSource.close();
    };
    
    // Listen for tools message
    eventSource.addEventListener('message', (event) => {
      try {
        const data = JSON.parse(event.data);
        
        if (data.type === 'tools') {
          console.log('Received tools:', data.tools);
          
          // Send tool call for list_tables
          sendToolCall(eventSource.url, {
            type: 'tool_call',
            tool_call: {
              id: 'call_1',
              name: 'list_tables',
              parameters: {}
            }
          });
        }
        
        if (data.type === 'tool_result') {
          console.log('Tool result:', data.result);
          
          // Once we get results, close the connection
          setTimeout(() => {
            eventSource.close();
            console.log('Test completed');
          }, 1000);
        }
        
        if (data.type === 'tool_error') {
          console.error('Tool error:', data.error);
          eventSource.close();
        }
      } catch (error) {
        console.error('Error parsing SSE message:', error);
      }
    });
    
    // Keep the script running for a while
    await new Promise(resolve => setTimeout(resolve, 10000));
    
  } catch (error) {
    console.error('Error testing MCP server:', error);
  }
}

async function sendToolCall(url, message) {
  try {
    console.log('Sending tool call:', message);
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(message),
    });
    
    if (!response.ok) {
      console.error('Failed to send tool call:', response.status, response.statusText);
    }
  } catch (error) {
    console.error('Error sending tool call:', error);
  }
}

testMcpServer();
