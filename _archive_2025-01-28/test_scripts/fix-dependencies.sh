#!/bin/bash

# This script fixes dependencies and build issues for the ARA Property Services app

echo "Fixing dependencies and build issues..."

# Remove node_modules and package-lock.json
echo "Removing node_modules and package-lock.json..."
rm -rf node_modules package-lock.json .next

# Install dependencies with specific versions
echo "Installing dependencies with specific versions..."
pnpm install

# Fix SWC dependencies
echo "Fixing SWC dependencies..."
pnpm add -D @swc/core@1.3.100 @swc/helpers@0.5.3

# Fix React and Next.js versions
echo "Fixing React and Next.js versions..."
pnpm add next@14.1.0 react@18.2.0 react-dom@18.2.0

# Fix Clerk imports
echo "Fixing Clerk imports..."
pnpm add @clerk/nextjs@5.1.2

# Fix Drizzle ORM
echo "Fixing Drizzle ORM..."
pnpm add drizzle-orm@0.29.5 drizzle-kit@0.20.14

echo "Dependencies fixed successfully!"
echo "You can now run 'pnpm dev' to start the development server."
