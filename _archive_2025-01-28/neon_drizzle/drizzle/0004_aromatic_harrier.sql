CREATE TABLE "cleaning_areas" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"area_category" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "cleaning_tasks" (
	"id" text PRIMARY KEY NOT NULL,
	"area_id" text NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"task_type" text NOT NULL,
	"standard_duration_minutes" integer,
	"equipment_required" jsonb,
	"materials_required" jsonb,
	"safety_requirements" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "contract_specifications" (
	"id" text PRIMARY KEY NOT NULL,
	"client_id" text NOT NULL,
	"client_name" text NOT NULL,
	"contract_name" text NOT NULL,
	"contract_number" text,
	"start_date" timestamp with time zone NOT NULL,
	"end_date" timestamp with time zone,
	"contract_value" numeric(10, 2),
	"contract_manager_id" text,
	"status" text DEFAULT 'active' NOT NULL,
	"special_requirements" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "facility_cleaning_specifications" (
	"id" text PRIMARY KEY NOT NULL,
	"contract_id" text NOT NULL,
	"property_id" text NOT NULL,
	"task_id" text NOT NULL,
	"tier_id" text NOT NULL,
	"frequency_id" text NOT NULL,
	"custom_frequency" text,
	"special_instructions" text,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "frequency_types" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"times_per_year" integer,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone,
	CONSTRAINT "frequency_types_name_unique" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "periodical_services" (
	"id" text PRIMARY KEY NOT NULL,
	"property_id" text NOT NULL,
	"service_name" text NOT NULL,
	"service_description" text,
	"frequency_id" text NOT NULL,
	"last_service_date" timestamp with time zone,
	"next_service_date" timestamp with time zone,
	"assigned_to" text,
	"status" text DEFAULT 'scheduled' NOT NULL,
	"special_requirements" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "retail_cleaning_scope" (
	"id" text PRIMARY KEY NOT NULL,
	"area" text NOT NULL,
	"element" text NOT NULL,
	"requirement" text NOT NULL,
	"frequency" text NOT NULL,
	"category" text NOT NULL,
	"notes" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "scheduled_tasks" (
	"id" text PRIMARY KEY NOT NULL,
	"property_id" text NOT NULL,
	"task_id" text NOT NULL,
	"scheduled_date" timestamp with time zone NOT NULL,
	"scheduled_start_time" timestamp with time zone,
	"scheduled_end_time" timestamp with time zone,
	"assigned_to" text,
	"status" text DEFAULT 'scheduled' NOT NULL,
	"completion_date" timestamp with time zone,
	"completion_notes" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "tier_specifications" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"tier_level" integer NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
ALTER TABLE "cleaning_tasks" ADD CONSTRAINT "cleaning_tasks_area_id_cleaning_areas_id_fk" FOREIGN KEY ("area_id") REFERENCES "public"."cleaning_areas"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "contract_specifications" ADD CONSTRAINT "contract_specifications_contract_manager_id_users_id_fk" FOREIGN KEY ("contract_manager_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "facility_cleaning_specifications" ADD CONSTRAINT "facility_cleaning_specifications_contract_id_contract_specifications_id_fk" FOREIGN KEY ("contract_id") REFERENCES "public"."contract_specifications"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "facility_cleaning_specifications" ADD CONSTRAINT "facility_cleaning_specifications_property_id_properties_id_fk" FOREIGN KEY ("property_id") REFERENCES "public"."properties"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "facility_cleaning_specifications" ADD CONSTRAINT "facility_cleaning_specifications_task_id_cleaning_tasks_id_fk" FOREIGN KEY ("task_id") REFERENCES "public"."cleaning_tasks"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "facility_cleaning_specifications" ADD CONSTRAINT "facility_cleaning_specifications_tier_id_tier_specifications_id_fk" FOREIGN KEY ("tier_id") REFERENCES "public"."tier_specifications"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "facility_cleaning_specifications" ADD CONSTRAINT "facility_cleaning_specifications_frequency_id_frequency_types_id_fk" FOREIGN KEY ("frequency_id") REFERENCES "public"."frequency_types"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "periodical_services" ADD CONSTRAINT "periodical_services_property_id_properties_id_fk" FOREIGN KEY ("property_id") REFERENCES "public"."properties"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "periodical_services" ADD CONSTRAINT "periodical_services_frequency_id_frequency_types_id_fk" FOREIGN KEY ("frequency_id") REFERENCES "public"."frequency_types"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "periodical_services" ADD CONSTRAINT "periodical_services_assigned_to_users_id_fk" FOREIGN KEY ("assigned_to") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "scheduled_tasks" ADD CONSTRAINT "scheduled_tasks_property_id_properties_id_fk" FOREIGN KEY ("property_id") REFERENCES "public"."properties"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "scheduled_tasks" ADD CONSTRAINT "scheduled_tasks_task_id_cleaning_tasks_id_fk" FOREIGN KEY ("task_id") REFERENCES "public"."cleaning_tasks"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "scheduled_tasks" ADD CONSTRAINT "scheduled_tasks_assigned_to_users_id_fk" FOREIGN KEY ("assigned_to") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "idx_cleaning_areas_category" ON "cleaning_areas" USING btree ("area_category");--> statement-breakpoint
CREATE INDEX "idx_task_area" ON "cleaning_tasks" USING btree ("area_id");--> statement-breakpoint
CREATE INDEX "idx_task_type" ON "cleaning_tasks" USING btree ("task_type");--> statement-breakpoint
CREATE INDEX "idx_contract_client" ON "contract_specifications" USING btree ("client_id");--> statement-breakpoint
CREATE INDEX "idx_contract_status" ON "contract_specifications" USING btree ("status");--> statement-breakpoint
CREATE INDEX "idx_contract_manager" ON "contract_specifications" USING btree ("contract_manager_id");--> statement-breakpoint
CREATE INDEX "idx_spec_contract" ON "facility_cleaning_specifications" USING btree ("contract_id");--> statement-breakpoint
CREATE INDEX "idx_spec_property" ON "facility_cleaning_specifications" USING btree ("property_id");--> statement-breakpoint
CREATE INDEX "idx_spec_task" ON "facility_cleaning_specifications" USING btree ("task_id");--> statement-breakpoint
CREATE INDEX "idx_spec_tier" ON "facility_cleaning_specifications" USING btree ("tier_id");--> statement-breakpoint
CREATE INDEX "idx_periodical_property" ON "periodical_services" USING btree ("property_id");--> statement-breakpoint
CREATE INDEX "idx_periodical_next_service" ON "periodical_services" USING btree ("next_service_date");--> statement-breakpoint
CREATE INDEX "idx_periodical_status" ON "periodical_services" USING btree ("status");--> statement-breakpoint
CREATE INDEX "idx_retail_area" ON "retail_cleaning_scope" USING btree ("area");--> statement-breakpoint
CREATE INDEX "idx_retail_category" ON "retail_cleaning_scope" USING btree ("category");--> statement-breakpoint
CREATE INDEX "idx_scheduled_property" ON "scheduled_tasks" USING btree ("property_id");--> statement-breakpoint
CREATE INDEX "idx_scheduled_task" ON "scheduled_tasks" USING btree ("task_id");--> statement-breakpoint
CREATE INDEX "idx_scheduled_date" ON "scheduled_tasks" USING btree ("scheduled_date");--> statement-breakpoint
CREATE INDEX "idx_scheduled_status" ON "scheduled_tasks" USING btree ("status");--> statement-breakpoint
CREATE INDEX "idx_tier_level" ON "tier_specifications" USING btree ("tier_level");