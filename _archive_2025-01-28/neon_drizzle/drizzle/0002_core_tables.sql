-- Core tables for ARA Property Services

-- Clients table
CREATE TABLE IF NOT EXISTS "clients" (
  "id" TEXT PRIMARY KEY,
  "name" TEXT NOT NULL,
  "primary_contact_name" TEXT,
  "primary_contact_email" TEXT,
  "primary_contact_phone" TEXT,
  "address" TEXT,
  "suburb" TEXT,
  "state" TEXT,
  "postcode" TEXT,
  "sector" TEXT,
  "account_manager_id" TEXT REFERENCES "users"("id"),
  "status" TEXT DEFAULT 'active',
  "notes" TEXT,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

CREATE INDEX IF NOT EXISTS "idx_clients_account_manager" ON "clients" ("account_manager_id");
CREATE INDEX IF NOT EXISTS "idx_clients_status" ON "clients" ("status");

-- Sites table
CREATE TABLE IF NOT EXISTS "sites" (
  "id" TEXT PRIMARY KEY,
  "client_id" TEXT REFERENCES "clients"("id") ON DELETE CASCADE,
  "name" TEXT NOT NULL,
  "address" TEXT,
  "suburb" TEXT,
  "state" TEXT,
  "postcode" TEXT,
  "site_contact_name" TEXT,
  "site_contact_phone" TEXT,
  "site_contact_email" TEXT,
  "site_type" TEXT,
  "tier" INTEGER,
  "floor_area_sqm" DECIMAL,
  "access_instructions" TEXT,
  "special_requirements" TEXT,
  "status" TEXT DEFAULT 'active',
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

CREATE INDEX IF NOT EXISTS "idx_sites_client" ON "sites" ("client_id");
CREATE INDEX IF NOT EXISTS "idx_sites_status" ON "sites" ("status");
CREATE INDEX IF NOT EXISTS "idx_sites_tier" ON "sites" ("tier");

-- Service contracts table
CREATE TABLE IF NOT EXISTS "service_contracts" (
  "id" TEXT PRIMARY KEY,
  "client_id" TEXT REFERENCES "clients"("id") ON DELETE CASCADE,
  "site_id" TEXT REFERENCES "sites"("id") ON DELETE CASCADE,
  "start_date" DATE NOT NULL,
  "end_date" DATE,
  "contract_value" DECIMAL,
  "billing_frequency" TEXT,
  "service_type" TEXT NOT NULL,
  "service_frequency" TEXT,
  "special_terms" TEXT,
  "status" TEXT DEFAULT 'active',
  "contract_file_url" TEXT,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

CREATE INDEX IF NOT EXISTS "idx_contracts_client" ON "service_contracts" ("client_id");
CREATE INDEX IF NOT EXISTS "idx_contracts_site" ON "service_contracts" ("site_id");
CREATE INDEX IF NOT EXISTS "idx_contracts_status" ON "service_contracts" ("status");

-- Service schedules table
CREATE TABLE IF NOT EXISTS "service_schedules" (
  "id" TEXT PRIMARY KEY,
  "contract_id" TEXT REFERENCES "service_contracts"("id") ON DELETE CASCADE,
  "site_id" TEXT REFERENCES "sites"("id") ON DELETE CASCADE,
  "service_date" DATE NOT NULL,
  "start_time" TIME,
  "end_time" TIME,
  "service_type" TEXT,
  "status" TEXT DEFAULT 'scheduled',
  "notes" TEXT,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

CREATE INDEX IF NOT EXISTS "idx_schedules_contract" ON "service_schedules" ("contract_id");
CREATE INDEX IF NOT EXISTS "idx_schedules_site" ON "service_schedules" ("site_id");
CREATE INDEX IF NOT EXISTS "idx_schedules_date" ON "service_schedules" ("service_date");
CREATE INDEX IF NOT EXISTS "idx_schedules_status" ON "service_schedules" ("status");

-- Job assignments table
CREATE TABLE IF NOT EXISTS "job_assignments" (
  "id" TEXT PRIMARY KEY,
  "schedule_id" TEXT REFERENCES "service_schedules"("id") ON DELETE CASCADE,
  "staff_id" TEXT REFERENCES "users"("id"),
  "assigned_by" TEXT REFERENCES "users"("id"),
  "assigned_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "role_on_job" TEXT,
  "status" TEXT DEFAULT 'assigned',
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

CREATE INDEX IF NOT EXISTS "idx_assignments_schedule" ON "job_assignments" ("schedule_id");
CREATE INDEX IF NOT EXISTS "idx_assignments_staff" ON "job_assignments" ("staff_id");
CREATE INDEX IF NOT EXISTS "idx_assignments_status" ON "job_assignments" ("status");

-- Service reports table
CREATE TABLE IF NOT EXISTS "service_reports" (
  "id" TEXT PRIMARY KEY,
  "schedule_id" TEXT REFERENCES "service_schedules"("id") ON DELETE CASCADE,
  "completed_by" TEXT REFERENCES "users"("id"),
  "completed_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "duration_minutes" INTEGER,
  "checklist_results" JSONB,
  "issues_identified" TEXT,
  "actions_taken" TEXT,
  "client_signature" BOOLEAN DEFAULT FALSE,
  "photos" JSONB,
  "quality_rating" INTEGER,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

CREATE INDEX IF NOT EXISTS "idx_reports_schedule" ON "service_reports" ("schedule_id");
CREATE INDEX IF NOT EXISTS "idx_reports_completed_by" ON "service_reports" ("completed_by");

-- Inventory table
CREATE TABLE IF NOT EXISTS "inventory" (
  "id" TEXT PRIMARY KEY,
  "item_name" TEXT NOT NULL,
  "item_category" TEXT,
  "current_stock" INTEGER DEFAULT 0,
  "unit_of_measure" TEXT,
  "reorder_threshold" INTEGER,
  "cost_per_unit" DECIMAL,
  "storage_location" TEXT,
  "last_restocked_date" DATE,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

CREATE INDEX IF NOT EXISTS "idx_inventory_category" ON "inventory" ("item_category");

-- Equipment table
CREATE TABLE IF NOT EXISTS "equipment" (
  "id" TEXT PRIMARY KEY,
  "name" TEXT NOT NULL,
  "type" TEXT,
  "serial_number" TEXT,
  "purchase_date" DATE,
  "cost" DECIMAL,
  "current_location" TEXT,
  "status" TEXT DEFAULT 'operational',
  "last_serviced_date" DATE,
  "service_interval_days" INTEGER,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

CREATE INDEX IF NOT EXISTS "idx_equipment_type" ON "equipment" ("type");
CREATE INDEX IF NOT EXISTS "idx_equipment_status" ON "equipment" ("status");

-- Maintenance requests table
CREATE TABLE IF NOT EXISTS "maintenance_requests" (
  "id" TEXT PRIMARY KEY,
  "equipment_id" TEXT REFERENCES "equipment"("id") ON DELETE SET NULL,
  "site_id" TEXT REFERENCES "sites"("id") ON DELETE SET NULL,
  "requested_by" TEXT REFERENCES "users"("id"),
  "request_date" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "issue_description" TEXT,
  "priority" TEXT,
  "status" TEXT DEFAULT 'new',
  "assigned_to" TEXT REFERENCES "users"("id"),
  "completion_date" TIMESTAMP WITH TIME ZONE,
  "resolution_notes" TEXT,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

CREATE INDEX IF NOT EXISTS "idx_maintenance_equipment" ON "maintenance_requests" ("equipment_id");
CREATE INDEX IF NOT EXISTS "idx_maintenance_site" ON "maintenance_requests" ("site_id");
CREATE INDEX IF NOT EXISTS "idx_maintenance_status" ON "maintenance_requests" ("status");

-- Client feedback table
CREATE TABLE IF NOT EXISTS "client_feedback" (
  "id" TEXT PRIMARY KEY,
  "client_id" TEXT REFERENCES "clients"("id") ON DELETE CASCADE,
  "site_id" TEXT REFERENCES "sites"("id") ON DELETE CASCADE,
  "service_date" DATE,
  "feedback_type" TEXT,
  "description" TEXT,
  "received_by" TEXT REFERENCES "users"("id"),
  "received_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "severity" TEXT,
  "status" TEXT DEFAULT 'open',
  "resolution" TEXT,
  "follow_up_required" BOOLEAN DEFAULT FALSE,
  "follow_up_date" DATE,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

CREATE INDEX IF NOT EXISTS "idx_feedback_client" ON "client_feedback" ("client_id");
CREATE INDEX IF NOT EXISTS "idx_feedback_site" ON "client_feedback" ("site_id");
CREATE INDEX IF NOT EXISTS "idx_feedback_status" ON "client_feedback" ("status");

-- Documents table
CREATE TABLE IF NOT EXISTS "documents" (
  "id" TEXT PRIMARY KEY,
  "name" TEXT NOT NULL,
  "document_type" TEXT,
  "related_to_type" TEXT,
  "related_to_id" TEXT,
  "upload_date" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "uploaded_by" TEXT REFERENCES "users"("id"),
  "file_url" TEXT,
  "expiry_date" DATE,
  "tags" JSONB,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

CREATE INDEX IF NOT EXISTS "idx_documents_type" ON "documents" ("document_type");
CREATE INDEX IF NOT EXISTS "idx_documents_related" ON "documents" ("related_to_type", "related_to_id");

-- Notifications table
CREATE TABLE IF NOT EXISTS "notifications" (
  "id" TEXT PRIMARY KEY,
  "user_id" TEXT REFERENCES "users"("id") ON DELETE CASCADE,
  "title" TEXT NOT NULL,
  "message" TEXT,
  "read_at" TIMESTAMP WITH TIME ZONE,
  "notification_type" TEXT,
  "related_entity_type" TEXT,
  "related_entity_id" TEXT,
  "action_url" TEXT,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

CREATE INDEX IF NOT EXISTS "idx_notifications_user" ON "notifications" ("user_id");
CREATE INDEX IF NOT EXISTS "idx_notifications_read" ON "notifications" ("read_at");

-- Audit log table
CREATE TABLE IF NOT EXISTS "audit_log" (
  "id" TEXT PRIMARY KEY,
  "table_name" TEXT NOT NULL,
  "record_id" TEXT NOT NULL,
  "action" TEXT NOT NULL,
  "changed_by" TEXT REFERENCES "users"("id"),
  "changed_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "old_values" JSONB,
  "new_values" JSONB,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

CREATE INDEX IF NOT EXISTS "idx_audit_table" ON "audit_log" ("table_name");
CREATE INDEX IF NOT EXISTS "idx_audit_record" ON "audit_log" ("record_id");
CREATE INDEX IF NOT EXISTS "idx_audit_action" ON "audit_log" ("action");
