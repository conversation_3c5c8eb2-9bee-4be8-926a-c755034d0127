{"id": "bdd7f070-354c-426b-900d-c11286101d0b", "prevId": "d52831a2-258b-4150-a152-6770e9ade990", "version": "7", "dialect": "postgresql", "tables": {"public.chat_participants": {"name": "chat_participants", "schema": "", "columns": {"session_id": {"name": "session_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "joined_at": {"name": "joined_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "last_read_at": {"name": "last_read_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"chat_participants_session_id_chat_sessions_id_fk": {"name": "chat_participants_session_id_chat_sessions_id_fk", "tableFrom": "chat_participants", "tableTo": "chat_sessions", "columnsFrom": ["session_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "chat_participants_user_id_users_id_fk": {"name": "chat_participants_user_id_users_id_fk", "tableFrom": "chat_participants", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"chat_participants_session_id_user_id_pk": {"name": "chat_participants_session_id_user_id_pk", "columns": ["session_id", "user_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chat_sessions": {"name": "chat_sessions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "preview": {"name": "preview", "type": "text", "primaryKey": false, "notNull": false}, "date": {"name": "date", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "is_starred": {"name": "is_starred", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"chat_sessions_created_by_users_id_fk": {"name": "chat_sessions_created_by_users_id_fk", "tableFrom": "chat_sessions", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.companies": {"name": "companies", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "primary_contact_id": {"name": "primary_contact_id", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"companies_primary_contact_id_contacts_id_fk": {"name": "companies_primary_contact_id_contacts_id_fk", "tableFrom": "companies", "tableTo": "contacts", "columnsFrom": ["primary_contact_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.contacts": {"name": "contacts", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "company": {"name": "company", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": false}, "is_favorite": {"name": "is_favorite", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "avatar": {"name": "avatar", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.dashboard_metrics": {"name": "dashboard_metrics", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "metric_type": {"name": "metric_type", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "period": {"name": "period", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "property_id": {"name": "property_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_metric_type_date": {"name": "idx_metric_type_date", "columns": [{"expression": "metric_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_metric_property": {"name": "idx_metric_property", "columns": [{"expression": "property_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"dashboard_metrics_property_id_properties_id_fk": {"name": "dashboard_metrics_property_id_properties_id_fk", "tableFrom": "dashboard_metrics", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.inspection_actions": {"name": "inspection_actions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "report_id": {"name": "report_id", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "priority": {"name": "priority", "type": "text", "primaryKey": false, "notNull": true}, "assignee": {"name": "assignee", "type": "text", "primaryKey": false, "notNull": true}, "due_date": {"name": "due_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"idx_action_report": {"name": "idx_action_report", "columns": [{"expression": "report_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_action_assignee": {"name": "idx_action_assignee", "columns": [{"expression": "assignee", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"inspection_actions_report_id_inspection_reports_id_fk": {"name": "inspection_actions_report_id_inspection_reports_id_fk", "tableFrom": "inspection_actions", "tableTo": "inspection_reports", "columnsFrom": ["report_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "inspection_actions_assignee_users_id_fk": {"name": "inspection_actions_assignee_users_id_fk", "tableFrom": "inspection_actions", "tableTo": "users", "columnsFrom": ["assignee"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.inspection_reports": {"name": "inspection_reports", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "property_id": {"name": "property_id", "type": "text", "primaryKey": false, "notNull": true}, "template_id": {"name": "template_id", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "inspector": {"name": "inspector", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "score": {"name": "score", "type": "integer", "primaryKey": false, "notNull": false}, "summary": {"name": "summary", "type": "text", "primaryKey": false, "notNull": false}, "sections": {"name": "sections", "type": "jsonb", "primaryKey": false, "notNull": false}, "weather_conditions": {"name": "weather_conditions", "type": "text", "primaryKey": false, "notNull": false}, "temperature": {"name": "temperature", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "photos": {"name": "photos", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "voice_notes": {"name": "voice_notes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "submitted_at": {"name": "submitted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "reviewed_by": {"name": "reviewed_by", "type": "text", "primaryKey": false, "notNull": false}, "reviewed_at": {"name": "reviewed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"idx_report_property": {"name": "idx_report_property", "columns": [{"expression": "property_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_inspection_date": {"name": "idx_inspection_date", "columns": [{"expression": "date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_inspection_inspector": {"name": "idx_inspection_inspector", "columns": [{"expression": "inspector", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"inspection_reports_property_id_properties_id_fk": {"name": "inspection_reports_property_id_properties_id_fk", "tableFrom": "inspection_reports", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "inspection_reports_template_id_inspection_templates_id_fk": {"name": "inspection_reports_template_id_inspection_templates_id_fk", "tableFrom": "inspection_reports", "tableTo": "inspection_templates", "columnsFrom": ["template_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "inspection_reports_inspector_users_id_fk": {"name": "inspection_reports_inspector_users_id_fk", "tableFrom": "inspection_reports", "tableTo": "users", "columnsFrom": ["inspector"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "inspection_reports_reviewed_by_users_id_fk": {"name": "inspection_reports_reviewed_by_users_id_fk", "tableFrom": "inspection_reports", "tableTo": "users", "columnsFrom": ["reviewed_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.inspection_templates": {"name": "inspection_templates", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "version": {"name": "version", "type": "integer", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "sections": {"name": "sections", "type": "jsonb", "primaryKey": false, "notNull": true}}, "indexes": {"idx_template_category": {"name": "idx_template_category", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"inspection_templates_created_by_users_id_fk": {"name": "inspection_templates_created_by_users_id_fk", "tableFrom": "inspection_templates", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.kpi_targets": {"name": "kpi_targets", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "metric_type": {"name": "metric_type", "type": "text", "primaryKey": false, "notNull": true}, "target_value": {"name": "target_value", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "period": {"name": "period", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "property_type": {"name": "property_type", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_kpi_type": {"name": "idx_kpi_type", "columns": [{"expression": "metric_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.messages": {"name": "messages", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "session_id": {"name": "session_id", "type": "text", "primaryKey": false, "notNull": false}, "content_type": {"name": "content_type", "type": "text", "primaryKey": false, "notNull": false}, "content_transcript": {"name": "content_transcript", "type": "text", "primaryKey": false, "notNull": false}, "object": {"name": "object", "type": "text", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": false}, "sender_id": {"name": "sender_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_session_created_at": {"name": "idx_session_created_at", "columns": [{"expression": "session_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"messages_session_id_chat_sessions_id_fk": {"name": "messages_session_id_chat_sessions_id_fk", "tableFrom": "messages", "tableTo": "chat_sessions", "columnsFrom": ["session_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "messages_sender_id_users_id_fk": {"name": "messages_sender_id_users_id_fk", "tableFrom": "messages", "tableTo": "users", "columnsFrom": ["sender_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.properties": {"name": "properties", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": true}, "suburb": {"name": "suburb", "type": "text", "primaryKey": false, "notNull": true}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": true}, "postcode": {"name": "postcode", "type": "<PERSON><PERSON><PERSON>(4)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "tier": {"name": "tier", "type": "integer", "primaryKey": false, "notNull": true}, "region": {"name": "region", "type": "text", "primaryKey": false, "notNull": true}, "size_sqm": {"name": "size_sqm", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'active'"}, "manager_id": {"name": "manager_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"idx_property_manager": {"name": "idx_property_manager", "columns": [{"expression": "manager_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_property_status": {"name": "idx_property_status", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_property_region": {"name": "idx_property_region", "columns": [{"expression": "region", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"properties_manager_id_users_id_fk": {"name": "properties_manager_id_users_id_fk", "tableFrom": "properties", "tableTo": "users", "columnsFrom": ["manager_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.property_areas": {"name": "property_areas", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "property_id": {"name": "property_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "size_sqm": {"name": "size_sqm", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "floor_level": {"name": "floor_level", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"property_areas_property_id_properties_id_fk": {"name": "property_areas_property_id_properties_id_fk", "tableFrom": "property_areas", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.report_attachments": {"name": "report_attachments", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "report_id": {"name": "report_id", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true}, "thumbnail_url": {"name": "thumbnail_url", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"report_attachments_report_id_inspection_reports_id_fk": {"name": "report_attachments_report_id_inspection_reports_id_fk", "tableFrom": "report_attachments", "tableTo": "inspection_reports", "columnsFrom": ["report_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.roles": {"name": "roles", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "permissions": {"name": "permissions", "type": "jsonb", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_sessions": {"name": "user_sessions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_sessions_user_id_users_id_fk": {"name": "user_sessions_user_id_users_id_fk", "tableFrom": "user_sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "department": {"name": "department", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "avatar": {"name": "avatar", "type": "text", "primaryKey": false, "notNull": false}, "preferences": {"name": "preferences", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "last_login": {"name": "last_login", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.cleaning_areas": {"name": "cleaning_areas", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "area_category": {"name": "area_category", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"idx_cleaning_areas_category": {"name": "idx_cleaning_areas_category", "columns": [{"expression": "area_category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.cleaning_tasks": {"name": "cleaning_tasks", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "area_id": {"name": "area_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "task_type": {"name": "task_type", "type": "text", "primaryKey": false, "notNull": true}, "standard_duration_minutes": {"name": "standard_duration_minutes", "type": "integer", "primaryKey": false, "notNull": false}, "equipment_required": {"name": "equipment_required", "type": "jsonb", "primaryKey": false, "notNull": false}, "materials_required": {"name": "materials_required", "type": "jsonb", "primaryKey": false, "notNull": false}, "safety_requirements": {"name": "safety_requirements", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"idx_task_area": {"name": "idx_task_area", "columns": [{"expression": "area_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_task_type": {"name": "idx_task_type", "columns": [{"expression": "task_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"cleaning_tasks_area_id_cleaning_areas_id_fk": {"name": "cleaning_tasks_area_id_cleaning_areas_id_fk", "tableFrom": "cleaning_tasks", "tableTo": "cleaning_areas", "columnsFrom": ["area_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.contract_specifications": {"name": "contract_specifications", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "client_id": {"name": "client_id", "type": "text", "primaryKey": false, "notNull": true}, "client_name": {"name": "client_name", "type": "text", "primaryKey": false, "notNull": true}, "contract_name": {"name": "contract_name", "type": "text", "primaryKey": false, "notNull": true}, "contract_number": {"name": "contract_number", "type": "text", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "contract_value": {"name": "contract_value", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "contract_manager_id": {"name": "contract_manager_id", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'active'"}, "special_requirements": {"name": "special_requirements", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"idx_contract_client": {"name": "idx_contract_client", "columns": [{"expression": "client_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_contract_status": {"name": "idx_contract_status", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_contract_manager": {"name": "idx_contract_manager", "columns": [{"expression": "contract_manager_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"contract_specifications_contract_manager_id_users_id_fk": {"name": "contract_specifications_contract_manager_id_users_id_fk", "tableFrom": "contract_specifications", "tableTo": "users", "columnsFrom": ["contract_manager_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.facility_cleaning_specifications": {"name": "facility_cleaning_specifications", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "contract_id": {"name": "contract_id", "type": "text", "primaryKey": false, "notNull": true}, "property_id": {"name": "property_id", "type": "text", "primaryKey": false, "notNull": true}, "task_id": {"name": "task_id", "type": "text", "primaryKey": false, "notNull": true}, "tier_id": {"name": "tier_id", "type": "text", "primaryKey": false, "notNull": true}, "frequency_id": {"name": "frequency_id", "type": "text", "primaryKey": false, "notNull": true}, "custom_frequency": {"name": "custom_frequency", "type": "text", "primaryKey": false, "notNull": false}, "special_instructions": {"name": "special_instructions", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"idx_spec_contract": {"name": "idx_spec_contract", "columns": [{"expression": "contract_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_spec_property": {"name": "idx_spec_property", "columns": [{"expression": "property_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_spec_task": {"name": "idx_spec_task", "columns": [{"expression": "task_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_spec_tier": {"name": "idx_spec_tier", "columns": [{"expression": "tier_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"facility_cleaning_specifications_contract_id_contract_specifications_id_fk": {"name": "facility_cleaning_specifications_contract_id_contract_specifications_id_fk", "tableFrom": "facility_cleaning_specifications", "tableTo": "contract_specifications", "columnsFrom": ["contract_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "facility_cleaning_specifications_property_id_properties_id_fk": {"name": "facility_cleaning_specifications_property_id_properties_id_fk", "tableFrom": "facility_cleaning_specifications", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "facility_cleaning_specifications_task_id_cleaning_tasks_id_fk": {"name": "facility_cleaning_specifications_task_id_cleaning_tasks_id_fk", "tableFrom": "facility_cleaning_specifications", "tableTo": "cleaning_tasks", "columnsFrom": ["task_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "facility_cleaning_specifications_tier_id_tier_specifications_id_fk": {"name": "facility_cleaning_specifications_tier_id_tier_specifications_id_fk", "tableFrom": "facility_cleaning_specifications", "tableTo": "tier_specifications", "columnsFrom": ["tier_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "facility_cleaning_specifications_frequency_id_frequency_types_id_fk": {"name": "facility_cleaning_specifications_frequency_id_frequency_types_id_fk", "tableFrom": "facility_cleaning_specifications", "tableTo": "frequency_types", "columnsFrom": ["frequency_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.frequency_types": {"name": "frequency_types", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "times_per_year": {"name": "times_per_year", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"frequency_types_name_unique": {"name": "frequency_types_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.periodical_services": {"name": "periodical_services", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "property_id": {"name": "property_id", "type": "text", "primaryKey": false, "notNull": true}, "service_name": {"name": "service_name", "type": "text", "primaryKey": false, "notNull": true}, "service_description": {"name": "service_description", "type": "text", "primaryKey": false, "notNull": false}, "frequency_id": {"name": "frequency_id", "type": "text", "primaryKey": false, "notNull": true}, "last_service_date": {"name": "last_service_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "next_service_date": {"name": "next_service_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "assigned_to": {"name": "assigned_to", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'scheduled'"}, "special_requirements": {"name": "special_requirements", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"idx_periodical_property": {"name": "idx_periodical_property", "columns": [{"expression": "property_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_periodical_next_service": {"name": "idx_periodical_next_service", "columns": [{"expression": "next_service_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_periodical_status": {"name": "idx_periodical_status", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"periodical_services_property_id_properties_id_fk": {"name": "periodical_services_property_id_properties_id_fk", "tableFrom": "periodical_services", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "periodical_services_frequency_id_frequency_types_id_fk": {"name": "periodical_services_frequency_id_frequency_types_id_fk", "tableFrom": "periodical_services", "tableTo": "frequency_types", "columnsFrom": ["frequency_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "periodical_services_assigned_to_users_id_fk": {"name": "periodical_services_assigned_to_users_id_fk", "tableFrom": "periodical_services", "tableTo": "users", "columnsFrom": ["assigned_to"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.retail_cleaning_scope": {"name": "retail_cleaning_scope", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "area": {"name": "area", "type": "text", "primaryKey": false, "notNull": true}, "element": {"name": "element", "type": "text", "primaryKey": false, "notNull": true}, "requirement": {"name": "requirement", "type": "text", "primaryKey": false, "notNull": true}, "frequency": {"name": "frequency", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"idx_retail_area": {"name": "idx_retail_area", "columns": [{"expression": "area", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_retail_category": {"name": "idx_retail_category", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.scheduled_tasks": {"name": "scheduled_tasks", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "property_id": {"name": "property_id", "type": "text", "primaryKey": false, "notNull": true}, "task_id": {"name": "task_id", "type": "text", "primaryKey": false, "notNull": true}, "scheduled_date": {"name": "scheduled_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "scheduled_start_time": {"name": "scheduled_start_time", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "scheduled_end_time": {"name": "scheduled_end_time", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "assigned_to": {"name": "assigned_to", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'scheduled'"}, "completion_date": {"name": "completion_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "completion_notes": {"name": "completion_notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"idx_scheduled_property": {"name": "idx_scheduled_property", "columns": [{"expression": "property_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_scheduled_task": {"name": "idx_scheduled_task", "columns": [{"expression": "task_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_scheduled_date": {"name": "idx_scheduled_date", "columns": [{"expression": "scheduled_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_scheduled_status": {"name": "idx_scheduled_status", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"scheduled_tasks_property_id_properties_id_fk": {"name": "scheduled_tasks_property_id_properties_id_fk", "tableFrom": "scheduled_tasks", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "scheduled_tasks_task_id_cleaning_tasks_id_fk": {"name": "scheduled_tasks_task_id_cleaning_tasks_id_fk", "tableFrom": "scheduled_tasks", "tableTo": "cleaning_tasks", "columnsFrom": ["task_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "scheduled_tasks_assigned_to_users_id_fk": {"name": "scheduled_tasks_assigned_to_users_id_fk", "tableFrom": "scheduled_tasks", "tableTo": "users", "columnsFrom": ["assigned_to"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tier_specifications": {"name": "tier_specifications", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "tier_level": {"name": "tier_level", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"idx_tier_level": {"name": "idx_tier_level", "columns": [{"expression": "tier_level", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}