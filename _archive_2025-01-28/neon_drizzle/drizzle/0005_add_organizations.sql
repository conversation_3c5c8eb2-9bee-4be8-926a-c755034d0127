-- Add organizations table
CREATE TABLE IF NOT EXISTS "organizations" (
  "id" TEXT PRIMARY KEY,
  "name" TEXT NOT NULL,
  "slug" TEXT NOT NULL UNIQUE,
  "image_url" TEXT,
  "max_memberships" INTEGER,
  "admin_delete_enabled" BOOLEAN DEFAULT true,
  "public_metadata" JSONB DEFAULT '{}',
  "private_metadata" JSONB DEFAULT '{}',
  "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMP WITH TIME ZONE
);

-- Add index on slug
CREATE INDEX IF NOT EXISTS "idx_organization_slug" ON "organizations" ("slug");

-- Add organization members table
CREATE TABLE IF NOT EXISTS "organization_members" (
  "id" TEXT PRIMARY KEY,
  "organization_id" TEXT NOT NULL REFERENCES "organizations" ("id") ON DELETE CASCADE,
  "user_id" TEXT NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE,
  "role" TEXT NOT NULL,
  "joined_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Add index on organization_id and user_id
CREATE INDEX IF NOT EXISTS "idx_org_user" ON "organization_members" ("organization_id", "user_id");

-- Add initial ARA Property Services organization
INSERT INTO "organizations" ("id", "name", "slug", "image_url", "created_at", "updated_at")
VALUES (
  'org_2vgJJLPin926eyZLua1fijgiXFJ',
  'ARA Property Services',
  'araps',
  'https://img.clerk.com/eyJ0eXBlIjoiZGVmYXVsdCIsImlpZCI6Imluc18ydXhxaVMxbnpvQlpWSFhEVVBuT1hvM2dTckwiLCJyaWQiOiJvcmdfMnZnSkpMUGluOTI2ZXlaTHVhMWZpamdpWEZKIiwiaW5pdGlhbHMiOiJBIn0',
  NOW(),
  NOW()
)
ON CONFLICT (id) DO UPDATE
SET 
  name = EXCLUDED.name,
  slug = EXCLUDED.slug,
  image_url = EXCLUDED.image_url,
  updated_at = NOW();

-- Add organization relation to properties
ALTER TABLE "properties" 
ADD COLUMN IF NOT EXISTS "organization_id" TEXT REFERENCES "organizations" ("id");

-- Add index on organization_id in properties
CREATE INDEX IF NOT EXISTS "idx_property_organization" ON "properties" ("organization_id");

-- Add organization relation to contracts
ALTER TABLE "contract_specifications" 
ADD COLUMN IF NOT EXISTS "organization_id" TEXT REFERENCES "organizations" ("id");

-- Add index on organization_id in contracts
CREATE INDEX IF NOT EXISTS "idx_contract_organization" ON "contract_specifications" ("organization_id");
