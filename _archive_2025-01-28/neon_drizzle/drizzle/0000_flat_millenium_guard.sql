CREATE TABLE IF NOT EXISTS "chat_participants" (
	"session_id" text NOT NULL,
	"user_id" text NOT NULL,
	"joined_at" timestamp with time zone DEFAULT now() NOT NULL,
	"last_read_at" timestamp with time zone,
	CONSTRAINT "chat_participants_session_id_user_id_pk" PRIMARY KEY("session_id","user_id")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "chat_sessions" (
	"id" text PRIMARY KEY NOT NULL,
	"title" text NOT NULL,
	"preview" text,
	"date" timestamp with time zone DEFAULT now() NOT NULL,
	"is_starred" boolean DEFAULT false NOT NULL,
	"created_by" text
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "companies" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"type" text NOT NULL,
	"primary_contact_id" text,
	"address" text,
	"phone" text,
	"email" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "contacts" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"role" text NOT NULL,
	"company" text NOT NULL,
	"phone" text,
	"email" text,
	"location" text,
	"is_favorite" boolean DEFAULT false NOT NULL,
	"avatar" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "dashboard_metrics" (
	"id" text PRIMARY KEY NOT NULL,
	"metric_type" text NOT NULL,
	"value" numeric(10, 2) NOT NULL,
	"period" text NOT NULL,
	"category" text NOT NULL,
	"property_id" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "inspection_actions" (
	"id" text PRIMARY KEY NOT NULL,
	"report_id" text NOT NULL,
	"title" text NOT NULL,
	"priority" text NOT NULL,
	"assignee" text NOT NULL,
	"due_date" timestamp with time zone,
	"status" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "inspection_reports" (
	"id" text PRIMARY KEY NOT NULL,
	"property_id" text NOT NULL,
	"template_id" text NOT NULL,
	"title" text NOT NULL,
	"location" text NOT NULL,
	"date" timestamp with time zone DEFAULT now() NOT NULL,
	"inspector" text NOT NULL,
	"status" text NOT NULL,
	"score" integer,
	"summary" text,
	"sections" jsonb,
	"weather_conditions" text,
	"temperature" numeric(5, 2),
	"photos" integer DEFAULT 0 NOT NULL,
	"voice_notes" integer DEFAULT 0 NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"submitted_at" timestamp with time zone,
	"reviewed_by" text,
	"reviewed_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "inspection_templates" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"category" text NOT NULL,
	"type" text NOT NULL,
	"version" integer NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_by" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"sections" jsonb NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "kpi_targets" (
	"id" text PRIMARY KEY NOT NULL,
	"metric_type" text NOT NULL,
	"target_value" numeric(10, 2) NOT NULL,
	"period" text NOT NULL,
	"category" text NOT NULL,
	"property_type" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "messages" (
	"id" text PRIMARY KEY NOT NULL,
	"session_id" text,
	"content_type" text,
	"content_transcript" text,
	"object" text,
	"role" text,
	"status" text,
	"type" text,
	"sender_id" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "properties" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"address" text NOT NULL,
	"suburb" text NOT NULL,
	"state" varchar(3) NOT NULL,
	"postcode" varchar(4) NOT NULL,
	"type" text NOT NULL,
	"tier" integer NOT NULL,
	"region" text NOT NULL,
	"size_sqm" numeric(10, 2),
	"category" text NOT NULL,
	"status" text DEFAULT 'active' NOT NULL,
	"manager_id" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "property_areas" (
	"id" text PRIMARY KEY NOT NULL,
	"property_id" text NOT NULL,
	"name" text NOT NULL,
	"type" text NOT NULL,
	"size_sqm" numeric(10, 2),
	"floor_level" text,
	"notes" text
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "report_attachments" (
	"id" text PRIMARY KEY NOT NULL,
	"report_id" text NOT NULL,
	"type" text NOT NULL,
	"url" text NOT NULL,
	"thumbnail_url" text,
	"description" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "roles" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"permissions" jsonb NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "user_sessions" (
	"id" text PRIMARY KEY NOT NULL,
	"user_id" text NOT NULL,
	"token" text NOT NULL,
	"expires_at" timestamp with time zone,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "users" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"role" text NOT NULL,
	"department" text NOT NULL,
	"email" text NOT NULL,
	"password" text NOT NULL,
	"phone" text,
	"avatar" text,
	"preferences" jsonb,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"last_login" timestamp with time zone,
	CONSTRAINT "users_email_unique" UNIQUE("email")
);
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_metric_type_date" ON "dashboard_metrics" ("metric_type","created_at");
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_metric_property" ON "dashboard_metrics" ("property_id");
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_action_report" ON "inspection_actions" ("report_id");
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_action_assignee" ON "inspection_actions" ("assignee");
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_report_property" ON "inspection_reports" ("property_id");
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_inspection_date" ON "inspection_reports" ("date");
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_inspection_inspector" ON "inspection_reports" ("inspector");
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_template_category" ON "inspection_templates" ("category");
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_kpi_type" ON "kpi_targets" ("metric_type");
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_session_created_at" ON "messages" ("session_id","created_at");
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_property_manager" ON "properties" ("manager_id");
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_property_status" ON "properties" ("status");
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_property_region" ON "properties" ("region");
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "chat_participants" ADD CONSTRAINT "chat_participants_session_id_chat_sessions_id_fk" FOREIGN KEY ("session_id") REFERENCES "chat_sessions"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "chat_participants" ADD CONSTRAINT "chat_participants_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "chat_sessions" ADD CONSTRAINT "chat_sessions_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "companies" ADD CONSTRAINT "companies_primary_contact_id_contacts_id_fk" FOREIGN KEY ("primary_contact_id") REFERENCES "contacts"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "dashboard_metrics" ADD CONSTRAINT "dashboard_metrics_property_id_properties_id_fk" FOREIGN KEY ("property_id") REFERENCES "properties"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "inspection_actions" ADD CONSTRAINT "inspection_actions_report_id_inspection_reports_id_fk" FOREIGN KEY ("report_id") REFERENCES "inspection_reports"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "inspection_actions" ADD CONSTRAINT "inspection_actions_assignee_users_id_fk" FOREIGN KEY ("assignee") REFERENCES "users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "inspection_reports" ADD CONSTRAINT "inspection_reports_property_id_properties_id_fk" FOREIGN KEY ("property_id") REFERENCES "properties"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "inspection_reports" ADD CONSTRAINT "inspection_reports_template_id_inspection_templates_id_fk" FOREIGN KEY ("template_id") REFERENCES "inspection_templates"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "inspection_reports" ADD CONSTRAINT "inspection_reports_inspector_users_id_fk" FOREIGN KEY ("inspector") REFERENCES "users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "inspection_reports" ADD CONSTRAINT "inspection_reports_reviewed_by_users_id_fk" FOREIGN KEY ("reviewed_by") REFERENCES "users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "inspection_templates" ADD CONSTRAINT "inspection_templates_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "messages" ADD CONSTRAINT "messages_session_id_chat_sessions_id_fk" FOREIGN KEY ("session_id") REFERENCES "chat_sessions"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "messages" ADD CONSTRAINT "messages_sender_id_users_id_fk" FOREIGN KEY ("sender_id") REFERENCES "users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "properties" ADD CONSTRAINT "properties_manager_id_users_id_fk" FOREIGN KEY ("manager_id") REFERENCES "users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "property_areas" ADD CONSTRAINT "property_areas_property_id_properties_id_fk" FOREIGN KEY ("property_id") REFERENCES "properties"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "report_attachments" ADD CONSTRAINT "report_attachments_report_id_inspection_reports_id_fk" FOREIGN KEY ("report_id") REFERENCES "inspection_reports"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "user_sessions" ADD CONSTRAINT "user_sessions_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
