-- Create contract specification tables

-- Cleaning areas table
CREATE TABLE IF NOT EXISTS "cleaning_areas" (
  "id" TEXT PRIMARY KEY,
  "name" TEXT NOT NULL,
  "description" TEXT,
  "category" TEXT NOT NULL,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

CREATE INDEX IF NOT EXISTS "idx_area_category" ON "cleaning_areas" ("category");

-- Cleaning tasks table
CREATE TABLE IF NOT EXISTS "cleaning_tasks" (
  "id" TEXT PRIMARY KEY,
  "area_id" TEXT NOT NULL REFERENCES "cleaning_areas" ("id"),
  "name" TEXT NOT NULL,
  "description" TEXT,
  "task_type" TEXT NOT NULL,
  "category" TEXT NOT NULL,
  "standard_duration_minutes" INTEGER,
  "equipment_required" JSONB,
  "materials_required" JSONB,
  "safety_requirements" TEXT,
  "created_at" TIMES<PERSON>MP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

CREATE INDEX IF NOT EXISTS "idx_task_area" ON "cleaning_tasks" ("area_id");
CREATE INDEX IF NOT EXISTS "idx_task_category" ON "cleaning_tasks" ("category");
CREATE INDEX IF NOT EXISTS "idx_task_type" ON "cleaning_tasks" ("task_type");

-- Frequency types table
CREATE TABLE IF NOT EXISTS "frequency_types" (
  "id" TEXT PRIMARY KEY,
  "name" TEXT NOT NULL UNIQUE,
  "description" TEXT,
  "times_per_year" INTEGER,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

-- Tier specifications table
CREATE TABLE IF NOT EXISTS "tier_specifications" (
  "id" TEXT PRIMARY KEY,
  "name" TEXT NOT NULL,
  "description" TEXT,
  "tier_level" INTEGER NOT NULL,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

CREATE INDEX IF NOT EXISTS "idx_tier_level" ON "tier_specifications" ("tier_level");

-- Contract specifications table
CREATE TABLE IF NOT EXISTS "contract_specifications" (
  "id" TEXT PRIMARY KEY,
  "client_id" TEXT NOT NULL,
  "client_name" TEXT NOT NULL,
  "contract_name" TEXT NOT NULL,
  "contract_number" TEXT,
  "start_date" TIMESTAMP WITH TIME ZONE NOT NULL,
  "end_date" TIMESTAMP WITH TIME ZONE,
  "contract_value" DECIMAL(10, 2),
  "contract_manager_id" TEXT REFERENCES "users" ("id"),
  "status" TEXT DEFAULT 'active' NOT NULL,
  "special_requirements" TEXT,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

CREATE INDEX IF NOT EXISTS "idx_contract_client" ON "contract_specifications" ("client_id");
CREATE INDEX IF NOT EXISTS "idx_contract_status" ON "contract_specifications" ("status");
CREATE INDEX IF NOT EXISTS "idx_contract_manager" ON "contract_specifications" ("contract_manager_id");

-- Facility cleaning specifications table
CREATE TABLE IF NOT EXISTS "facility_cleaning_specifications" (
  "id" TEXT PRIMARY KEY,
  "contract_id" TEXT NOT NULL REFERENCES "contract_specifications" ("id"),
  "property_id" TEXT NOT NULL REFERENCES "properties" ("id"),
  "task_id" TEXT NOT NULL REFERENCES "cleaning_tasks" ("id"),
  "tier_id" TEXT NOT NULL REFERENCES "tier_specifications" ("id"),
  "frequency_id" TEXT NOT NULL REFERENCES "frequency_types" ("id"),
  "custom_frequency" TEXT,
  "special_instructions" TEXT,
  "is_active" BOOLEAN DEFAULT TRUE NOT NULL,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

CREATE INDEX IF NOT EXISTS "idx_spec_contract" ON "facility_cleaning_specifications" ("contract_id");
CREATE INDEX IF NOT EXISTS "idx_spec_property" ON "facility_cleaning_specifications" ("property_id");
CREATE INDEX IF NOT EXISTS "idx_spec_task" ON "facility_cleaning_specifications" ("task_id");
CREATE INDEX IF NOT EXISTS "idx_spec_tier" ON "facility_cleaning_specifications" ("tier_id");

-- Periodical services table
CREATE TABLE IF NOT EXISTS "periodical_services" (
  "id" TEXT PRIMARY KEY,
  "property_id" TEXT NOT NULL REFERENCES "properties" ("id"),
  "service_name" TEXT NOT NULL,
  "service_description" TEXT,
  "frequency_id" TEXT NOT NULL REFERENCES "frequency_types" ("id"),
  "last_service_date" TIMESTAMP WITH TIME ZONE,
  "next_service_date" TIMESTAMP WITH TIME ZONE,
  "assigned_to" TEXT REFERENCES "users" ("id"),
  "status" TEXT DEFAULT 'scheduled' NOT NULL,
  "special_requirements" TEXT,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

CREATE INDEX IF NOT EXISTS "idx_periodical_property" ON "periodical_services" ("property_id");
CREATE INDEX IF NOT EXISTS "idx_periodical_next_service" ON "periodical_services" ("next_service_date");
CREATE INDEX IF NOT EXISTS "idx_periodical_status" ON "periodical_services" ("status");

-- Scheduled tasks table
CREATE TABLE IF NOT EXISTS "scheduled_tasks" (
  "id" TEXT PRIMARY KEY,
  "property_id" TEXT NOT NULL REFERENCES "properties" ("id"),
  "task_id" TEXT NOT NULL REFERENCES "cleaning_tasks" ("id"),
  "scheduled_date" TIMESTAMP WITH TIME ZONE NOT NULL,
  "scheduled_start_time" TIMESTAMP WITH TIME ZONE,
  "scheduled_end_time" TIMESTAMP WITH TIME ZONE,
  "assigned_to" TEXT REFERENCES "users" ("id"),
  "status" TEXT DEFAULT 'scheduled' NOT NULL,
  "completion_date" TIMESTAMP WITH TIME ZONE,
  "completion_notes" TEXT,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

CREATE INDEX IF NOT EXISTS "idx_scheduled_property" ON "scheduled_tasks" ("property_id");
CREATE INDEX IF NOT EXISTS "idx_scheduled_task" ON "scheduled_tasks" ("task_id");
CREATE INDEX IF NOT EXISTS "idx_scheduled_date" ON "scheduled_tasks" ("scheduled_date");
CREATE INDEX IF NOT EXISTS "idx_scheduled_status" ON "scheduled_tasks" ("status");

-- Retail cleaning scope table
CREATE TABLE IF NOT EXISTS "retail_cleaning_scope" (
  "id" TEXT PRIMARY KEY,
  "area" TEXT NOT NULL,
  "element" TEXT NOT NULL,
  "requirement" TEXT NOT NULL,
  "frequency" TEXT NOT NULL,
  "category" TEXT NOT NULL,
  "notes" TEXT,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE
);

CREATE INDEX IF NOT EXISTS "idx_retail_area" ON "retail_cleaning_scope" ("area");
CREATE INDEX IF NOT EXISTS "idx_retail_category" ON "retail_cleaning_scope" ("category");
