import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';
import { createServer } from 'http';
import { parse } from 'url';

// Load environment variables
dotenv.config();

// Initialize Neon database connection
const sql = neon(process.env.DATABASE_URL);

// Define MCP tools
const tools = [
  {
    name: 'list_projects',
    description: 'Lists all Neon projects associated with your account',
    parameters: {}
  },
  {
    name: 'list_tables',
    description: 'Lists all tables in the current Neon database',
    parameters: {}
  },
  {
    name: 'run_query',
    description: 'Runs a SQL query against the Neon database',
    parameters: {
      query: {
        type: 'string',
        description: 'The SQL query to execute'
      }
    }
  },
  {
    name: 'get_messages',
    description: 'Gets all messages for a specific session',
    parameters: {
      session_id: {
        type: 'string',
        description: 'The session ID to get messages for'
      }
    }
  },
  {
    name: 'create_message',
    description: 'Creates a new message in the database',
    parameters: {
      session_id: {
        type: 'string',
        description: 'The session ID for the message'
      },
      content: {
        type: 'string',
        description: 'The content of the message'
      },
      role: {
        type: 'string',
        description: 'The role of the message sender (user or assistant)'
      }
    }
  }
];

// Create a simple HTTP server to handle MC<PERSON> requests
const server = createServer(async (req, res) => {
  const { pathname } = parse(req.url, true);

  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  // Handle SSE connection for MCP
  if (pathname === '/sse') {
    // Set headers for SSE
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive'
    });

    // Send initial message with tools
    const initialMessage = {
      type: 'tools',
      tools: tools
    };

    res.write(`data: ${JSON.stringify(initialMessage)}\n\n`);

    // Keep connection alive
    const keepAliveInterval = setInterval(() => {
      res.write(': keepalive\n\n');
    }, 30000);

    // Handle client disconnection
    req.on('close', () => {
      clearInterval(keepAliveInterval);
      console.log('Client disconnected');
    });

    // Handle tool calls
    let body = '';

    req.on('data', chunk => {
      body += chunk.toString();

      // Check if we have a complete JSON object
      try {
        const message = JSON.parse(body);
        body = '';

        if (message.type === 'tool_call') {
          handleToolCall(message, res);
        }
      } catch (e) {
        // Not a complete JSON object yet, continue collecting data
      }
    });
  } else {
    res.writeHead(404);
    res.end('Not Found');
  }
});

// Handle tool calls
async function handleToolCall(message, res) {
  const { id, name, parameters } = message.tool_call;

  console.log(`Received tool call: ${name}`);
  console.log('Parameters:', parameters);

  try {
    let result;

    switch (name) {
      case 'list_projects':
        result = await listProjects();
        break;
      case 'list_tables':
        result = await listTables();
        break;
      case 'run_query':
        result = await runQuery(parameters.query);
        break;
      case 'get_messages':
        result = await getMessages(parameters.session_id);
        break;
      case 'create_message':
        result = await createMessage(
          parameters.session_id,
          parameters.content,
          parameters.role
        );
        break;
      default:
        throw new Error(`Unknown tool: ${name}`);
    }

    // Send tool result
    const toolResultMessage = {
      type: 'tool_result',
      id,
      result
    };

    res.write(`data: ${JSON.stringify(toolResultMessage)}\n\n`);
  } catch (error) {
    console.error(`Error handling tool call ${name}:`, error);

    // Send error result
    const errorResultMessage = {
      type: 'tool_error',
      id,
      error: error.message
    };

    res.write(`data: ${JSON.stringify(errorResultMessage)}\n\n`);
  }
}

// MCP action handlers
async function listProjects() {
  // In a real implementation, this would call the Neon API to list projects
  // For now, we'll return a mock response
  return [
    {
      id: 'project-1',
      name: 'Ask Ara',
      region: 'us-east-1',
      created_at: new Date().toISOString()
    }
  ];
}

async function listTables() {
  const tables = await sql(`
    SELECT table_name
    FROM information_schema.tables
    WHERE table_schema = 'public'
  `);

  return tables.map(table => table.table_name);
}

async function runQuery(query) {
  return await sql(query);
}

async function getMessages(sessionId) {
  if (!sessionId) {
    throw new Error('Session ID is required');
  }

  return await sql(`
    SELECT * FROM messages
    WHERE session_id = $1
    ORDER BY created_at ASC
  `, [sessionId]);
}

async function createMessage(sessionId, content, role) {
  if (!sessionId || !content || !role) {
    throw new Error('Session ID, content, and role are required');
  }

  const id = `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

  await sql(`
    INSERT INTO messages (id, session_id, content_transcript, role, status, type, object)
    VALUES ($1, $2, $3, $4, $5, $6, $7)
  `, [id, sessionId, content, role, 'completed', 'message', 'realtime.item']);

  return { id, session_id: sessionId, content, role };
}

// Start the server
const PORT = process.env.MCP_PORT || 3005;
server.listen(PORT, () => {
  console.log(`Neon MCP server running on port ${PORT}`);
});
