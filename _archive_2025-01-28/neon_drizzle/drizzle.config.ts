import { defineConfig } from 'drizzle-kit';
import { loadEnvConfig } from '@next/env';

// Load environment variables from .env.local, .env, etc.
loadEnvConfig(process.cwd());

if (!process.env.DATABASE_URL) {
  throw new Error('DATABASE_URL must be a Neon postgres connection string');
}

export default defineConfig({
  schema: './app/db/schema.ts', // Point to the schema file
  out: './drizzle', // Specify the output directory for migrations
  dialect: 'postgresql', // Use postgresql dialect (required in newer versions)
  dbCredentials: {
    url: process.env.DATABASE_URL, // Use the Neon connection string
  },
  // seedScript removed; handled separately by package.json script
});
