-- Dashboard metrics
CREATE TABLE dashboard_metrics (
    id TEXT PRIMARY KEY,
    metric_type VARCHAR(50) NOT NULL,
    value DECIMAL(10,2) NOT NULL,
    period VARCHAR(20) NOT NULL,
    category VARCHAR(50) NOT NULL,
    property_id VARCHAR(10) REFERENCES properties(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON> targets
CREATE TABLE kpi_targets (
    id TEXT PRIMARY KEY,
    metric_type VARCHAR(50) NOT NULL,
    target_value DECIMAL(10,2) NOT NULL,
    period VARCHAR(20) NOT NULL,
    category VARCHAR(50) NOT NULL,
    property_type VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Historical metrics
CREATE TABLE historical_metrics (
    id TEXT PRIMARY KEY,
    metric_type VARCHAR(50) NOT NULL,
    value DECIMAL(10,2) NOT NULL,
    date TIMESTAMP WITH TIME ZONE NOT NULL,
    property_id VARCHAR(10) REFERENCES properties(id),
    category VARCHAR(50) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);