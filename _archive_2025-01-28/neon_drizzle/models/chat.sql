-- Chat sessions
CREATE TABLE chat_sessions (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    preview TEXT,
    date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_starred BOOLEAN DEFAULT false,
    created_by TEXT REFERENCES users(id)
);

-- Chat participants
CREATE TABLE chat_participants (
    session_id TEXT REFERENCES chat_sessions(id),
    user_id TEXT REFERENCES users(id),
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_read_at TIMESTAMP WITH TIME ZONE,
    PRIMARY KEY (session_id, user_id)
);

-- Messages
CREATE TABLE messages (
    created_at SERIAL,
    id TEXT PRIMARY KEY,
    session_id TEXT REFERENCES chat_sessions(id),
    content_type TEXT,
    content_transcript TEXT,
    object TEXT,
    role TEXT,
    status TEXT,
    type TEXT,
    sender_id TEXT REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);