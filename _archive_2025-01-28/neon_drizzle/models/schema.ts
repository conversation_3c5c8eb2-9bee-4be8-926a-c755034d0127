// Core Types
interface User {
  id: string;
  name: string;
  role: string;
  department: string;
  email: string;
  avatar?: string;
  preferences?: {
    theme: 'light' | 'dark';
    notifications: boolean;
  };
}

interface Message {
  created_at: number;
  id: string;
  session_id: string;
  content_type: string;
  content_transcript: string;
  object: string;
  role: 'user' | 'assistant';
  status: string;
  type: string;
}

interface Contact {
  id: string;
  name: string;
  role: string;
  company: string;
  phone: string;
  email: string;
  location: string;
  isFavorite: boolean;
  avatar: string;
}

interface ChatSession {
  id: string;
  title: string;
  preview: string;
  date: string;
  isStarred: boolean;
  messages?: Message[];
  participants?: string[]; // User IDs
}

interface InspectionReport {
  id: string;
  title: string;
  location: string;
  date: string;
  inspector: string;
  status: 'draft' | 'submitted' | 'reviewed' | 'approved';
  score: number;
  summary: string;
  sections: {
    safety: InspectionSection;
    equipment: InspectionSection;
    compliance: InspectionSection;
  };
  actions: InspectionAction[];
  photos: number;
  voiceNotes: number;
}

interface InspectionSection {
  score: number;
  items: InspectionItem[];
}

interface InspectionItem {
  id: string;
  title: string;
  status: 'pass' | 'fail' | 'na';
  notes: string;
  photos?: string[];
}

interface InspectionAction {
  id: string;
  title: string;
  priority: 'low' | 'medium' | 'high';
  assignee: string; // User ID
  dueDate: string;
  status: 'pending' | 'in-progress' | 'completed';
}

interface Dashboard {
  currentJob: {
    title: string;
    client: string;
    progress: number;
    timeRemaining: string;
    tasksCompleted: number;
    tasksTotal: number;
  };
  kpis: Array<{
    title: string;
    value: string;
    icon: string;
  }>;
}