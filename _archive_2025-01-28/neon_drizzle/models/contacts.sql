-- Contacts
CREATE TABLE contacts (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    role TEXT NOT NULL,
    company TEXT NOT NULL,
    phone TEXT,
    email TEXT,
    location TEXT,
    is_favorite BOOLEAN DEFAULT false,
    avatar TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Companies
CREATE TABLE companies (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    type TEXT NOT NULL,
    primary_contact_id TEXT REFERENCES contacts(id),
    address TEXT,
    phone TEXT,
    email TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Contact relationships
CREATE TABLE contact_relationships (
    contact_id1 TEXT REFERENCES contacts(id),
    contact_id2 TEXT REFERENCES contacts(id),
    relationship_type TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    <PERSON>IMAR<PERSON> KEY (contact_id1, contact_id2)
);