-- Users and Authentication
CREATE TABLE users (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  role TEXT NOT NULL,
  department TEXT NOT NULL,
  email TEXT UNIQUE NOT NULL,
  avatar TEXT,
  preferences JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  last_login TIMESTAMP WITH TIME ZONE
);

-- User roles and permissions
CREATE TABLE roles (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  permissions JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- User sessions
CREATE TABLE user_sessions (
  id TEXT PRIMARY KEY,
  user_id TEXT REFERENCES users(id),
  token TEXT NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Messages table (existing)
CREATE TABLE messages (
  created_at SERIAL,
  id TEXT PRIMARY KEY,
  session_id TEXT,
  content_type TEXT,
  content_transcript TEXT,
  object TEXT,
  role TEXT,
  status TEXT,
  type TEXT
);

-- Contacts table
CREATE TABLE contacts (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  role TEXT NOT NULL,
  company TEXT NOT NULL,
  phone TEXT,
  email TEXT,
  location TEXT,
  is_favorite BOOLEAN DEFAULT false,
  avatar TEXT
);

-- Chat sessions table
CREATE TABLE chat_sessions (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  preview TEXT,
  date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  is_starred BOOLEAN DEFAULT false
);

-- Inspection reports table
CREATE TABLE inspection_reports (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  location TEXT NOT NULL,
  date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  inspector TEXT REFERENCES users(id),
  status TEXT NOT NULL,
  score INTEGER,
  summary TEXT,
  sections JSONB,
  photos INTEGER DEFAULT 0,
  voice_notes INTEGER DEFAULT 0
);

-- Inspection actions table
CREATE TABLE inspection_actions (
  id TEXT PRIMARY KEY,
  report_id TEXT REFERENCES inspection_reports(id),
  title TEXT NOT NULL,
  priority TEXT NOT NULL,
  assignee TEXT REFERENCES users(id),
  due_date TIMESTAMP WITH TIME ZONE,
  status TEXT NOT NULL
);

-- Create indexes
CREATE INDEX idx_session_created_at ON messages (session_id, created_at);
CREATE INDEX idx_inspection_date ON inspection_reports (date);
CREATE INDEX idx_inspection_inspector ON inspection_reports (inspector);
CREATE INDEX idx_action_assignee ON inspection_actions (assignee);
