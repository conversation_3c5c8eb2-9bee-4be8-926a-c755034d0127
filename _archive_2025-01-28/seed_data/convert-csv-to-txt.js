const fs = require('fs');
const path = require('path');

// Function to convert CSV to readable text
function convertCsvToText(csvContent) {
  // Split by lines
  const lines = csvContent.split('\n');
  
  // Get headers
  const headers = lines[0].split(',').map(header => header.trim());
  
  let textContent = '';
  
  // Process each line
  for (let i = 1; i < lines.length; i++) {
    if (!lines[i].trim()) continue; // Skip empty lines
    
    const values = lines[i].split(',').map(value => value.trim());
    
    // Create a readable format for each row
    textContent += `Record ${i}:\n`;
    for (let j = 0; j < headers.length; j++) {
      if (values[j]) {
        textContent += `${headers[j]}: ${values[j]}\n`;
      }
    }
    textContent += '\n';
  }
  
  return textContent;
}

// Get all CSV files in the data directory
const dataDir = path.join(__dirname, 'data');
const outputDir = path.join(__dirname, 'data_txt');

// Create output directory if it doesn't exist
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir);
}

// Process each CSV file
const files = fs.readdirSync(dataDir);
const txtFiles = [];

files.forEach(file => {
  if (file.endsWith('.csv')) {
    const filePath = path.join(dataDir, file);
    const outputFilePath = path.join(outputDir, file.replace('.csv', '.txt'));
    
    console.log(`Converting ${file} to TXT...`);
    
    // Read CSV content
    const csvContent = fs.readFileSync(filePath, 'utf8');
    
    // Convert to text
    const textContent = convertCsvToText(csvContent);
    
    // Write to TXT file
    fs.writeFileSync(outputFilePath, textContent);
    
    txtFiles.push(outputFilePath);
    
    console.log(`Created ${outputFilePath}`);
  }
});

console.log('Conversion complete!');
console.log('TXT files created:');
txtFiles.forEach(file => console.log(file));
