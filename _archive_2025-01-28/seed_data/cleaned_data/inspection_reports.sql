-- Create reports related tables
CREATE TABLE IF NOT EXISTS inspection_reports (
    id VARCHAR(20) PRIMARY KEY,
    property_id VARCHAR(10) REFERENCES properties(id),
    template_id VARCHAR(10) REFERENCES inspection_templates(id),
    inspector <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    inspection_date DATE NOT NULL,
    status VARCHAR(50) DEFAULT 'draft',
    overall_score DECIMAL(5,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS report_sections (
    id SERIAL PRIMARY KEY,
    report_id VARCHAR(20) REFERENCES inspection_reports(id),
    name VARCHAR(50) NOT NULL,
    score DECIMAL(5,2),
    notes TEXT
);

CREATE TABLE IF NOT EXISTS report_items (
    id SERIAL PRIMARY KEY,
    section_id INT REFERENCES report_sections(id),
    name VARCHAR(255) NOT NULL,
    value TEXT,
    score DECIMAL(5,2),
    notes TEXT
);

CREATE TABLE IF NOT EXISTS report_actions (
    id SERIAL PRIMARY KEY,
    report_id VARCHAR(20) REFERENCES inspection_reports(id),
    description TEXT NOT NULL,
    priority VARCHAR(20) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    due_date DATE,
    assigned_to VARCHAR(255)
);

-- Populate with sample data from Auspost inspections
INSERT INTO inspection_reports (id, property_id, template_id, inspector, inspection_date, status, overall_score)
SELECT 
    'INS-' || EXTRACT(YEAR FROM TO_DATE("Inspection Date", 'YYYY-MM-DD HH24:MI')) || 
    '-' || LPAD(ROW_NUMBER() OVER (ORDER BY "Inspection Date"), 4, '0'),
    'AP' || LPAD(ROW_NUMBER() OVER (ORDER BY "Inspection Date"), 5, '0'),
    'INSP001',
    "Inspector",
    TO_DATE("Inspection Date", 'YYYY-MM-DD HH24:MI'),
    'completed',
    "Overall Rating"::DECIMAL(5,2)
FROM auspost_inspection_report;

-- Add report sections
INSERT INTO report_sections (report_id, name, score)
SELECT 
    r.id,
    unnest(ARRAY['External Areas', 'Internal Public Areas', 'Staff Areas']) as section_name,
    unnest(ARRAY[a."External Areas"::DECIMAL(5,2), 
                 a."Internal Public Areas"::DECIMAL(5,2), 
                 a."Staff Areas"::DECIMAL(5,2)]) as section_score
FROM inspection_reports r
JOIN auspost_inspection_report a ON r.inspector = a."Inspector"
AND r.inspection_date = TO_DATE(a."Inspection Date", 'YYYY-MM-DD HH24:MI');

-- Add action items
INSERT INTO report_actions (report_id, description, priority, status, due_date)
SELECT 
    r.id,
    a."Action Items",
    CASE 
        WHEN a."Overall Rating"::DECIMAL(5,2) < 94 THEN 'high'
        WHEN a."Overall Rating"::DECIMAL(5,2) < 96 THEN 'medium'
        ELSE 'low'
    END,
    'pending',
    r.inspection_date + INTERVAL '14 days'
FROM inspection_reports r
JOIN auspost_inspection_report a ON r.inspector = a."Inspector"
AND r.inspection_date = TO_DATE(a."Inspection Date", 'YYYY-MM-DD HH24:MI')
WHERE a."Action Items" IS NOT NULL;