-- Create inspection related tables
CREATE TABLE IF NOT EXISTS inspection_templates (
    id VARCHAR(10) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    category VARCHAR(50) NOT NULL,
    type VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS inspection_sections (
    id SERIAL PRIMARY KEY,
    template_id VARCHAR(10) REFERENCES inspection_templates(id),
    name VARCHAR(50) NOT NULL,
    order_index INT NOT NULL,
    required BOOLEAN DEFAULT true
);

CREATE TABLE IF NOT EXISTS inspection_items (
    id SERIAL PRIMARY KEY,
    section_id INT REFERENCES inspection_sections(id),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL, -- checkbox, rating, text, etc.
    required BOOLEAN DEFAULT true,
    order_index INT NOT NULL
);

-- Populate with ARA-specific templates
INSERT INTO inspection_templates (id, name, category, type) VALUES
('INSP001', 'Industrial Property Inspection', 'Industrial and Logistics', 'Regular'),
('INSP002', 'Retail Space Inspection', 'Retail', 'Regular'),
('INSP003', 'Office Space Inspection', 'Office', 'Regular'),
('INSP004', 'Post-Cleaning Inspection', 'Quality Control', 'Cleaning'),
('INSP005', 'Safety Compliance Check', 'Compliance', 'Safety');

-- Add sections based on existing form structure
INSERT INTO inspection_sections (template_id, name, order_index) VALUES
('INSP001', 'general', 1),
('INSP001', 'safety', 2),
('INSP001', 'equipment', 3),
('INSP001', 'compliance', 4);

-- Add inspection items based on the form structure
INSERT INTO inspection_items (section_id, name, description, type, order_index) VALUES
(1, 'location', 'Property location', 'text', 1),
(1, 'date', 'Inspection date', 'date', 2),
(1, 'time', 'Inspection time', 'time', 3),
(1, 'inspector', 'Inspector name', 'text', 4),
(1, 'type', 'Inspection type', 'select', 5),
(2, 'emergencyExits', 'Emergency exit condition and accessibility', 'rating', 1),
(2, 'fireExtinguishers', 'Fire extinguisher status and expiry', 'rating', 2),
(2, 'firstAidKit', 'First aid kit completeness', 'rating', 3),
(2, 'evacuationPlan', 'Evacuation plan visibility and accuracy', 'rating', 4);