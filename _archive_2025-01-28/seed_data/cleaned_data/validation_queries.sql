-- Check property distribution by state
SELECT state, COUNT(*) as property_count
FROM properties
GROUP BY state
ORDER BY property_count DESC;

-- Analyze cleaning requirements by tier
SELECT tier, COUNT(*) as template_count
FROM cleaning_templates ct
JOIN properties p ON ct.tier_1_frequency IS NOT NULL
GROUP BY tier;

-- Validate schedule coverage
SELECT p.id, p.name, 
    CASE WHEN cs.property_id IS NULL THEN 'Missing' ELSE 'Present' END as has_schedule
FROM properties p
LEFT JOIN cleaning_schedules cs ON p.id = cs.property_id;

-- Check task distribution by category
SELECT category, COUNT(*) as task_count
FROM cleaning_templates
GROUP BY category
ORDER BY task_count DESC;

-- Analyze retail cleaning frequency patterns
SELECT frequency, COUNT(*) as requirement_count
FROM retail_cleaning_scope
GROUP BY frequency
ORDER BY requirement_count DESC;