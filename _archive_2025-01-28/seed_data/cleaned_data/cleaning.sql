-- Cleaning templates
CREATE TABLE cleaning_templates (
    id VARCHAR(10) PRIMARY KEY,
    area VARCHAR(50) NOT NULL,
    task VARCHAR(255) NOT NULL,
    tier_1_frequency VARCHAR(50),
    tier_2_frequency VARCHAR(50),
    tier_3_5_frequency VARCHAR(50),
    category VARCHAR(50) NOT NULL,
    task_type VARCHAR(50) NOT NULL
);

-- Cleaning schedules
CREATE TABLE cleaning_schedules (
    id SERIAL PRIMARY KEY,
    property_id VARCHAR(10) REFERENCES properties(id),
    area VARCHAR(50) NOT NULL,
    component TEXT NOT NULL,
    daily_tasks TEXT,
    weekly_tasks TEXT,
    monthly_tasks TEXT,
    special_requirements TEXT,
    assigned_staff TEXT REFERENCES users(id)
);

-- Maintenance records
CREATE TABLE maintenance_records (
    id TEXT PRIMARY KEY,
    property_id VARCHAR(10) REFERENCES properties(id),
    type VARCHAR(50) NOT NULL,
    description TEXT,
    reported_by TEXT REFERENCES users(id),
    assigned_to TEXT REFERENCES users(id),
    status VARCHAR(50),
    priority VARCHAR(20),
    due_date TIMESTAMP WITH TIME ZONE,
    completed_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);