-- Properties
CREATE TABLE properties (
    id VARCHAR(10) PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    address VARCHAR(255) NOT NULL,
    suburb VARCHAR(100) NOT NULL,
    state VARCHAR(3) NOT NULL,
    postcode VARCHAR(4) NOT NULL,
    type VARCHAR(50) NOT NULL,
    tier INT NOT NULL,
    region VARCHAR(50) NOT NULL,
    size_sqm DECIMAL(10,2),
    category VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    manager_id TEXT REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Property areas
CREATE TABLE property_areas (
    id SERIAL PRIMARY KEY,
    property_id VARCHAR(10) REFERENCES properties(id),
    name VA<PERSON>HAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL,
    size_sqm DECIMAL(10,2),
    floor_level VARCHAR(10),
    notes TEXT
);

-- Property contacts
CREATE TABLE property_contacts (
    property_id VARCHAR(10) REFERENCES properties(id),
    contact_id TEXT REFERENCES contacts(id),
    role VARCHAR(50) NOT NULL,
    is_primary BOOLEAN DEFAULT false,
    PRIMARY KEY (property_id, contact_id)
);