-- Inspection templates (extended)
CREATE TABLE inspection_templates (
    id VARCHAR(10) PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    category VARCHAR(50) NOT NULL,
    type VARCHAR(50) NOT NULL,
    version INT DEFAULT 1,
    is_active BOOLEAN DEFAULT true,
    created_by TEXT REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Inspection reports (extended)
CREATE TABLE inspection_reports (
    id VARCHAR(20) PRIMARY KEY,
    property_id VARCHAR(10) REFERENCES properties(id),
    template_id VARCHAR(10) REFERENCES inspection_templates(id),
    inspector TEXT REFERENCES users(id),
    inspection_date TIMESTAMP WITH TIME ZONE NOT NULL,
    status VARCHAR(50) DEFAULT 'draft',
    overall_score DECIMAL(5,2),
    summary TEXT,
    weather_conditions TEXT,
    temperature DECIMAL(4,1),
    photos_count INT DEFAULT 0,
    voice_notes_count INT DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    submitted_at TIMESTAMP WITH TIME ZONE,
    reviewed_by TEXT REFERENCES users(id),
    reviewed_at TIMESTAMP WITH TIME ZONE
);

-- Media attachments
CREATE TABLE report_attachments (
    id TEXT PRIMARY KEY,
    report_id VARCHAR(20) REFERENCES inspection_reports(id),
    type VARCHAR(20) NOT NULL, -- 'photo', 'voice', 'document'
    url TEXT NOT NULL,
    thumbnail_url TEXT,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Report comments
CREATE TABLE report_comments (
    id TEXT PRIMARY KEY,
    report_id VARCHAR(20) REFERENCES inspection_reports(id),
    user_id TEXT REFERENCES users(id),
    content TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);