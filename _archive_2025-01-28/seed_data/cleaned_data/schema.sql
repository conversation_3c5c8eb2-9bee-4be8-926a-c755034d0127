-- Create tables
CREATE TABLE IF NOT EXISTS properties (
    id VARCHAR(10) PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    address VARCHAR(255) NOT NULL,
    suburb VARCHAR(100) NOT NULL,
    state VARCHAR(3) NOT NULL,
    postcode VARCHAR(4) NOT NULL,
    type VARCHAR(50) NOT NULL,
    tier INT NOT NULL,
    region VARCHAR(50) NOT NULL,
    size_sqm DECIMAL(10,2),
    category VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'active'
);

CREATE TABLE IF NOT EXISTS cleaning_templates (
    id VARCHAR(10) PRIMARY KEY,
    area VARCHAR(50) NOT NULL,
    task VARCHAR(255) NOT NULL,
    tier_1_frequency VARCHAR(50),
    tier_2_frequency VARCHAR(50),
    tier_3_5_frequency VARCHAR(50),
    category VARCHAR(50) NOT NULL,
    task_type VARCHAR(50) NOT NULL
);

CREATE TABLE IF NOT EXISTS cleaning_schedules (
    id SERIAL PRIMARY KEY,
    property_id VARCHAR(10) REFERENCES properties(id),
    area VARCHAR(50) NOT NULL,
    component TEXT NOT NULL,
    daily_tasks TEXT,
    weekly_tasks TEXT,
    monthly_tasks TEXT,
    special_requirements TEXT
);

CREATE TABLE IF NOT EXISTS retail_cleaning_scope (
    id SERIAL PRIMARY KEY,
    area VARCHAR(50) NOT NULL,
    element VARCHAR(50) NOT NULL,
    requirement TEXT NOT NULL,
    frequency VARCHAR(50) NOT NULL,
    category VARCHAR(50) NOT NULL,
    notes TEXT
);

-- Create reference tables
CREATE TABLE IF NOT EXISTS frequency_types (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    times_per_year INT
);

CREATE TABLE IF NOT EXISTS categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT
);