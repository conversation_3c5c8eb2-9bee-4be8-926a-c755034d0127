// Imports (dotenv loading is now handled in lib/neon-db.js)
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { db, pool, users, roles, properties, property_areas, cleaning_areas, cleaning_tasks, frequency_types, tier_specifications, contract_specifications, facility_cleaning_specifications, periodical_services, scheduled_tasks, retail_cleaning_scope, inspection_templates, inspection_reports, inspection_actions, inspection_items, action_items, contacts, companies, chat_sessions, chat_participants, messages, dashboard_metrics, kpi_targets, user_sessions, 
// Added missing table schemas (Ensure these are exported from lib/neon-db.js)
clients, sites, australia_post_sites, australia_post_site_areas, australia_post_tier_specs, australia_post_cleaning_specs, inspections, scheduled_cleaning_tasks
// Add others like client_feedback, documents etc. if they have schema definitions and need seeding
 } from './lib/neon-db.js'; // Ensure this path is correct relative to seed.ts at root
// --- Configuration ---
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const seedsDir = path.join(__dirname, 'knowledge', 'seeds'); // Assuming seed.ts is at root
console.log(`🌱 Seeding data from: ${seedsDir}`);
string, any & gt;
;
// --- Helper Function to Seed Data ---
// No 'db' parameter needed as it's imported globally
async function seedTable(tableName, tableSchema, jsonDataPath) {
    console.log(`\nSeeding ${tableName}...`);
    const fullPath = path.join(seedsDir, jsonDataPath);
    if (!fs.existsSync(fullPath)) {
        console.warn(`⚠️ Seed file not found: ${fullPath}. Skipping ${tableName}.`);
        return true; // Continue seeding other tables
    }
    try {
        // Read and parse the JSON file
        const jsonString = fs.readFileSync(fullPath, 'utf-8');
        const data = JSON.parse(jsonString);
        if (!Array.isArray(data) || data.length === 0) {
            console.log(`⚪ No data found in ${jsonDataPath} for ${tableName}.`);
            return true;
        }
        console.log(`Inserting ${data.length} records into ${tableName}...`);
        // Determine the conflict target columns and filter data
        let conflictTarget;
        let validData = data; // Initialize with original data
        if (tableName === 'chat_participants') {
            // Specific handling for composite key table
            // @ts-ignore - Accessing composite keys dynamically
            const pk1 = tableSchema.session_id;
            // @ts-ignore - Accessing composite keys dynamically
            const pk2 = tableSchema.user_id;
            if (!pk1 || !pk2) {
                console.error(`❌ Cannot determine composite primary keys for table ${tableName}. Skipping.`);
                return false;
            }
            conflictTarget = [pk1, pk2];
            // Filter out records missing either part of the composite key
            validData = data.filter(record => record.session_id != null && record.user_id != null);
            if (validData.length !== data.length) {
                console.warn(`⚠️ Filtered out ${data.length - validData.length} records with null/undefined composite keys from ${jsonDataPath}`);
            }
        }
        else {
            // Default handling for single 'id' primary key
            // @ts-ignore - Accessing 'id' dynamically
            const primaryKeyColumn = tableSchema.id;
            if (!primaryKeyColumn) {
                console.error(`❌ Cannot determine primary key 'id' for table ${tableName}. Skipping.`);
                return false;
            }
            conflictTarget = primaryKeyColumn;
            // Filter out records with null/undefined primary keys
            validData = data.filter(record => record.id != null);
            if (validData.length !== data.length) {
                console.warn(`⚠️ Filtered out ${data.length - validData.length} records with null/undefined primary keys from ${jsonDataPath}`);
            }
        }
        if (validData.length === 0) {
            console.log(`⚪ No valid data (with primary keys) found in ${jsonDataPath} for ${tableName}.`);
            return true;
        }
        // Insert data using onConflictDoNothing
        await db.insert(tableSchema)
            .values(validData)
            // @ts-ignore - Using dynamic conflict target determined above
            .onConflictDoNothing({ target: conflictTarget });
        console.log(`✅ Finished seeding ${tableName}.`);
        return true;
    }
    catch (error) { // Changed variable name
        console.error(`❌ Error seeding ${tableName} from ${jsonDataPath}:`, error.message);
        // Optionally log the full error for debugging: console.error(error);
        return false; // Indicate failure
    }
}
// --- Main Seeding Function ---
async function main() {
    // No dynamic import block needed here
    console.log('🚀 Starting database seeding...');
    let overallSuccess = true;
    const startTime = Date.now();
    // Define the seeding order based on dependencies
    // Add more tables and adjust order as needed based on schema FKs and seed file content
    const seedOrder = [
        // Independent tables first
        ['roles', roles, 'roles.json'],
        ['properties', properties, 'properties.json'],
        ['frequency_types', frequency_types, 'frequency_types.json'],
        ['tier_specifications', tier_specifications, 'tier_specifications.json'],
        ['cleaning_areas', cleaning_areas, 'cleaning_areas.json'],
        ['contacts', contacts, 'contacts.json'],
        ['inspection_templates', inspection_templates, 'inspection_templates.json'],
        ['kpi_targets', kpi_targets, 'kpi_targets.json'],
        ['clients', clients, 'clients.json'], // Added
        ['sites', sites, 'sites.json'], // Added
        ['australia_post_tier_specs', australia_post_tier_specs, 'australia_post_tier_specs.json'], // Added
        // Dependent tables
        ['users', users, 'users.json'], // Depends on roles (conceptually)
        ['property_areas', property_areas, 'property_areas.json'], // Depends on properties
        ['companies', companies, 'companies.json'], // Depends on contacts
        ['cleaning_tasks', cleaning_tasks, 'cleaning_tasks.json'], // Depends on cleaning_areas
        ['australia_post_sites', australia_post_sites, 'australia_post_sites.json'], // Added (depends on sites)
        ['australia_post_site_areas', australia_post_site_areas, 'australia_post_site_areas.json'], // Added (depends on aus_post_sites)
        ['contract_specifications', contract_specifications, 'contract_specifications.json'], // Depends on users
        ['facility_cleaning_specifications', facility_cleaning_specifications, 'facility_cleaning_specifications.json'], // Depends on contracts, properties, tasks, tiers, frequencies
        ['periodical_services', periodical_services, 'periodical_services.json'], // Depends on properties, frequencies, users
        ['scheduled_tasks', scheduled_tasks, 'scheduled_tasks.json'], // Depends on properties, tasks, users
        ['inspection_reports', inspection_reports, 'inspection_reports.json'], // Depends on properties, templates, users
        ['inspection_actions', inspection_actions, 'inspection_actions.json'], // Depends on reports, users
        ['inspection_items', inspection_items, 'inspection_items.json'], // Depends on reports, cleaning_areas?
        ['action_items', action_items, 'action_items.json'], // Depends on reports, items?, users
        ['chat_sessions', chat_sessions, 'chat_sessions.json'], // Depends on users
        ['chat_participants', chat_participants, 'chat_participants.json'], // Depends on sessions, users
        ['messages', messages, 'messages.json'], // Depends on sessions, users
        ['dashboard_metrics', dashboard_metrics, 'dashboard_metrics.json'], // Depends on properties
        ['user_sessions', user_sessions, 'user_sessions.json'], // Depends on users
        ['retail_cleaning_scope', retail_cleaning_scope, 'retail_cleaning_scope.json'], // Depends on frequencies?
        ['australia_post_cleaning_specs', australia_post_cleaning_specs, 'australia_post_cleaning_specs.json'], // Added (depends on aus_post_sites, areas, tasks, freq)
        ['inspections', inspections, 'inspections.json'], // Added (depends on aus_post_sites, users)
        ['scheduled_cleaning_tasks', scheduled_cleaning_tasks, 'scheduled_cleaning_tasks.json'], // Added (depends on aus_post_sites, tasks, users)
        // Add other tables with seed files in the correct order
        // NOTE: Ensure schema for tables like client_feedback, documents, equipment etc. are imported if they need seeding
    ];
    for (const [tableName, tableSchema, jsonFile] of seedOrder) {
        // Check if tableSchema is defined before seeding
        if (!tableSchema) {
            console.warn(`⚠️ Schema definition for table '${tableName}' not found/imported. Skipping seeding.`);
            continue; // Skip to the next table
        }
        // Call seedTable without db argument
        const success = await seedTable(tableName, tableSchema, jsonFile);
        if (!success) {
            overallSuccess = false;
            // Decide if you want to stop on first error or continue seeding others
            // break; // Uncomment to stop on first error
        }
    }
    const endTime = Date.now();
    console.log(`\n⏱️ Seeding process took ${(endTime - startTime) / 1000} seconds.`);
    // drizzle-kit seed might handle connection closing, but we add it defensively if pool is imported
    if (pool & amp)
        ;
     & amp;
    typeof pool.end === 'function';
    {
        console.log('\n🔌 Closing database connection pool...');
        await pool.end();
        console.log('✅ Connection pool closed.');
    }
    {
        console.log('\nPool object not available or pool.end is not a function, skipping explicit pool closing.');
    }
    if (overallSuccess) {
        console.log('\n✅✅✅ Database seeding completed successfully!');
        // Drizzle Kit seed expects exit code 0 for success
        process.exit(0);
    }
    else {
        console.error('\n❌❌❌ Database seeding failed for one or more tables.');
        // Drizzle Kit seed expects non-zero exit code for failure
        process.exit(1);
    }
}
// --- Run Seeding ---
main().catch((error) => {
    console.error('❌ An unexpected error occurred:', error);
    // Ensure pool is closed even on unexpected errors if possible
    if (pool & amp)
        ;
     & amp;
    typeof pool.end === 'function';
});
{
    pool.end().finally(() => process.exit(1));
}
{
    process.exit(1);
}
;
