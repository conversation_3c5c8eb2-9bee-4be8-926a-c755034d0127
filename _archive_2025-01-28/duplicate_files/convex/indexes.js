// Additional indexes for specific query patterns
// Properties
export const propertyIndexes = {
    // For filtering properties by type and region
    by_type_region: ["type", "region"],
    // For filtering properties by manager and status
    by_manager_status: ["manager_id", "status"],
    // For searching properties by name
    by_name: ["name"],
};
// Inspection Reports
export const inspectionIndexes = {
    // For filtering reports by date range and status
    by_date_status: ["inspection_date", "status"],
    // For filtering reports by property and date
    by_property_date: ["property_id", "inspection_date"],
    // For filtering reports by inspector and date
    by_inspector_date: ["inspector", "inspection_date"],
};
// Messages
export const messageIndexes = {
    // For filtering messages by session and type
    by_session_type: ["session_id", "type"],
    // For filtering messages by sender and date
    by_sender_date: ["sender_id", "created_at"],
};
// Metrics
export const metricIndexes = {
    // For filtering metrics by type and property
    by_type_property: ["metric_type", "property_id"],
    // For filtering metrics by category and date
    by_category_date: ["category", "created_at"],
};
// Contacts
export const contactIndexes = {
    // For filtering contacts by role
    by_role: ["role"],
    // For searching contacts by name
    by_name: ["name"],
    // For filtering favorite contacts
    by_favorite: ["is_favorite"],
};
