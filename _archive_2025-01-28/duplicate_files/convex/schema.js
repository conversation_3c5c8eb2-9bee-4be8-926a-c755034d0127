import { defineSchema, defineTable } from "convex/schema";
export default defineSchema({
    // Users and Authentication
    users: defineTable({
        name: "string",
        role: "string",
        department: "string",
        email: "string",
        avatar: "optional<string>",
        preferences: "optional<object>",
        created_at: "number",
        last_login: "optional<number>",
    }).index("by_email", ["email"]),
    roles: defineTable({
        name: "string",
        permissions: "object",
        created_at: "number",
    }),
    // Properties
    properties: defineTable({
        name: "string",
        address: "string",
        suburb: "string",
        state: "string",
        postcode: "string",
        type: "string",
        tier: "number",
        region: "string",
        size_sqm: "optional<number>",
        category: "string",
        status: "string",
        manager_id: "string",
        created_at: "number",
        updated_at: "optional<number>",
    }).index("by_manager", ["manager_id"])
        .index("by_status", ["status"])
        .index("by_region", ["region"]),
    property_areas: defineTable({
        property_id: "string",
        name: "string",
        type: "string",
        size_sqm: "optional<number>",
        floor_level: "optional<string>",
        notes: "optional<string>",
    }).index("by_property", ["property_id"]),
    // Contacts
    contacts: defineTable({
        name: "string",
        role: "string",
        company: "string",
        phone: "optional<string>",
        email: "optional<string>",
        location: "optional<string>",
        is_favorite: "boolean",
        avatar: "optional<string>",
        created_at: "number",
        updated_at: "optional<number>",
    }).index("by_company", ["company"]),
    property_contacts: defineTable({
        property_id: "string",
        contact_id: "string",
        role: "string",
        is_primary: "boolean",
    }).index("by_property", ["property_id"]),
    // Inspections
    inspection_templates: defineTable({
        name: "string",
        category: "string",
        type: "string",
        version: "number",
        is_active: "boolean",
        created_by: "string",
        created_at: "number",
    }).index("by_category", ["category"]),
    inspection_reports: defineTable({
        property_id: "string",
        template_id: "string",
        inspector: "string",
        inspection_date: "number",
        status: "string",
        overall_score: "optional<number>",
        summary: "optional<string>",
        weather_conditions: "optional<string>",
        temperature: "optional<number>",
        photos_count: "number",
        voice_notes_count: "number",
        created_at: "number",
        submitted_at: "optional<number>",
        reviewed_by: "optional<string>",
        reviewed_at: "optional<number>",
    }).index("by_property", ["property_id"])
        .index("by_date", ["inspection_date"])
        .index("by_inspector", ["inspector"]),
    report_attachments: defineTable({
        report_id: "string",
        type: "string",
        url: "string",
        thumbnail_url: "optional<string>",
        description: "optional<string>",
        created_at: "number",
    }).index("by_report", ["report_id"]),
    // Chat and Messages
    chat_sessions: defineTable({
        title: "string",
        preview: "optional<string>",
        date: "number",
        is_starred: "boolean",
        created_by: "string",
    }).index("by_creator", ["created_by"]),
    chat_participants: defineTable({
        session_id: "string",
        user_id: "string",
        joined_at: "number",
        last_read_at: "optional<number>",
    }).index("by_session", ["session_id"])
        .index("by_user", ["user_id"]),
    messages: defineTable({
        session_id: "string",
        content_type: "string",
        content_transcript: "optional<string>",
        object: "optional<string>",
        role: "string",
        status: "string",
        type: "string",
        sender_id: "string",
        created_at: "number",
    }).index("by_session", ["session_id", "created_at"]),
    // Metrics
    dashboard_metrics: defineTable({
        metric_type: "string",
        value: "number",
        period: "string",
        category: "string",
        property_id: "string",
        created_at: "number",
    }).index("by_type_date", ["metric_type", "created_at"])
        .index("by_property", ["property_id"]),
    kpi_targets: defineTable({
        metric_type: "string",
        target_value: "number",
        period: "string",
        category: "string",
        property_type: "optional<string>",
        created_at: "number",
    }).index("by_type", ["metric_type"]),
});
