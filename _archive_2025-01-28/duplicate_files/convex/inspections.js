var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
export const getInspectionTemplate = query({
    args: { templateId: v.string() },
    handler: (ctx, args) => __awaiter(void 0, void 0, void 0, function* () {
        return yield ctx.db
            .query("inspection_templates")
            .filter((q) => q.eq(q.field("_id"), args.templateId))
            .first();
    }),
});
export const getInspectionReport = query({
    args: { reportId: v.string() },
    handler: (ctx, args) => __awaiter(void 0, void 0, void 0, function* () {
        return yield ctx.db
            .query("inspection_reports")
            .filter((q) => q.eq(q.field("_id"), args.reportId))
            .first();
    }),
});
export const getDashboardMetrics = query({
    args: { period: v.string() },
    handler: (ctx, args) => __awaiter(void 0, void 0, void 0, function* () {
        return yield ctx.db
            .query("dashboard_metrics")
            .filter((q) => q.eq(q.field("period"), args.period))
            .collect();
    }),
});
export const createInspectionReport = mutation({
    args: {
        propertyId: v.string(),
        templateId: v.string(),
        inspector: v.string(),
        sections: v.object({
            safety: v.object({
                score: v.number(),
                items: v.array(v.any()),
            }),
            equipment: v.object({
                score: v.number(),
                items: v.array(v.any()),
            }),
            compliance: v.object({
                score: v.number(),
                items: v.array(v.any()),
            }),
        }),
    },
    handler: (ctx, args) => __awaiter(void 0, void 0, void 0, function* () {
        const report = yield ctx.db.insert("inspection_reports", {
            property_id: args.propertyId,
            template_id: args.templateId,
            inspector: args.inspector,
            inspection_date: Date.now(),
            status: "draft",
            overall_score: 0,
            sections: args.sections,
            actions: [],
            photos: 0,
            voiceNotes: 0,
            created_at: Date.now(),
        });
        return report;
    }),
});
