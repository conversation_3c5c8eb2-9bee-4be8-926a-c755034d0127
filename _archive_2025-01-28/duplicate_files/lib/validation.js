export const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};
export const validatePassword = (password) => {
    return password.length >= 8;
};
export const validateRequired = (value) => {
    return value.trim() !== "";
};
export const validatePhone = (phone) => {
    // Basic phone validation - can be enhanced for specific formats
    const phoneRegex = /^\+?[0-9\s\-$$$$]{8,}$/;
    return phoneRegex.test(phone);
};
export const validateUrl = (url) => {
    try {
        new URL(url);
        return true;
    }
    catch (_a) {
        return false;
    }
};
export const validateField = (value, rules) => {
    for (const rule of rules) {
        if (!rule.validate(value)) {
            return { isValid: false, errorMessage: rule.message };
        }
    }
    return { isValid: true, errorMessage: null };
};
export const emailRules = [
    {
        validate: validateRequired,
        message: "Email is required",
    },
    {
        validate: validateEmail,
        message: "Please enter a valid email address",
    },
];
export const passwordRules = [
    {
        validate: validateRequired,
        message: "Password is required",
    },
    {
        validate: validatePassword,
        message: "Password must be at least 8 characters",
    },
];
export const phoneRules = [
    {
        validate: validateRequired,
        message: "Phone number is required",
    },
    {
        validate: validatePhone,
        message: "Please enter a valid phone number",
    },
];
