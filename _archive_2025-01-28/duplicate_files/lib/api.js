var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
export function fetchWithTimeout(url_1) {
    return __awaiter(this, arguments, void 0, function* (url, options = {}) {
        const { timeout = 8000 } = options, fetchOptions = __rest(options
        // Add default headers
        , ["timeout"]);
        // Add default headers
        const headers = Object.assign({ "Content-Type": "application/json" }, options.headers);
        // Prepare body if it's an object
        const body = options.body && typeof options.body === "object" ? JSON.stringify(options.body) : options.body;
        // Create abort controller for timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);
        try {
            const response = yield fetch(url, Object.assign({ method: options.method || "GET", headers,
                body, signal: controller.signal }, fetchOptions));
            clearTimeout(timeoutId);
            // Handle non-2xx responses
            if (!response.ok) {
                const errorData = yield response.json().catch(() => ({}));
                throw new Error(errorData.error || `Request failed with status ${response.status}`);
            }
            // Parse JSON response
            const data = yield response.json();
            return data;
        }
        catch (error) {
            clearTimeout(timeoutId);
            if (error.name === "AbortError") {
                throw new Error("Request timeout. Please try again.");
            }
            throw error;
        }
    });
}
export const api = {
    get: (url, options = {}) => fetchWithTimeout(url, options),
    post: (url, data, options = {}) => fetchWithTimeout(url, Object.assign(Object.assign({}, options), { method: 'POST', body: data })),
    put: (url, data, options = {}) => fetchWithTimeout(url, Object.assign(Object.assign({}, options), { method: 'PUT', body: data })),
    delete: (url, options = {}) => fetchWithTimeout(url, Object.assign(Object.assign({}, options), { method: 'DELETE' })),
};
