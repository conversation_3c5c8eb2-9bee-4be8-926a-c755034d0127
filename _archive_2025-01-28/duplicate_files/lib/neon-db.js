import { Pool } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-serverless';
import * as schema from '@/app/db/schema';
// Check if DATABASE_URL is defined
if (!process.env.DATABASE_URL) {
    throw new Error('DATABASE_URL environment variable is not defined');
}
// Create a Neon serverless client
const pool = new Pool({ connectionString: process.env.DATABASE_URL });
// Create a Drizzle client
export const db = drizzle(pool, { schema });
// Export schema for use in other files
export * from '@/app/db/schema';
// Export the pool for direct SQL queries
export { pool };
// Function to test the connection
export async function testConnection() {
    try {
        const result = await pool.query('SELECT 1 as test');
        if (result && result.rows && result.rows[0] && result.rows[0].test === 1) {
            console.log('✅ Database connection successful');
            return true;
        }
        else {
            console.error('❌ Database connection failed: Unexpected response');
            return false;
        }
    }
    catch (error) {
        console.error('❌ Database connection error:', error);
        return false;
    }
}
