var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { db } from '@/lib/neon-mcp'; // Import Drizzle client with Neon MCP
import { users } from '@/app/db/schema'; // Import users table schema (value and type)
import { eq } from 'drizzle-orm'; // Import Drizzle eq function
/**
 * Find a user by email in the database
 */
export const findUserByEmail = (email) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Use Drizzle query builder to find the user
        const user = yield db.query.users.findFirst({
            where: eq(users.email, email),
        });
        return user !== null && user !== void 0 ? user : null; // Return user if found, otherwise null
    }
    catch (error) {
        console.error('Error finding user by email:', error);
        return null;
    }
});
/**
 * Get all users from the database
 */
export const getAllUsers = () => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Use Drizzle query builder to get all users
        const allUsers = yield db.select().from(users);
        return allUsers;
    }
    catch (error) {
        console.error('Error getting all users:', error);
        return [];
    }
});
/**
 * Add a temporary password to a user for testing purposes
 */
export const addPasswordToUser = (email, password) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Find the user first
        const user = yield findUserByEmail(email);
        if (!user) {
            console.error(`User with email ${email} not found`);
            return false;
        }
        // Update the user's password
        yield db.update(users)
            .set({ password: password })
            .where(eq(users.email, email));
        // Check if the update was successful
        // Note: In PostgreSQL, the result doesn't have rowCount directly
        // We'll consider it successful if no error was thrown
        console.log(`Password updated for user ${email}`);
        return true;
    }
    catch (error) {
        console.error('Error adding password to user:', error);
        return false;
    }
});
/**
 * Verify a user's credentials
 */
export const verifyCredentials = (email, password) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = yield findUserByEmail(email);
        if (!user) {
            // User not found by email
            console.log(`User with email ${email} not found`);
            return null;
        }
        // Check if the user has a password
        if (!user.password) {
            console.log(`User ${email} has no password set`);
            return null;
        }
        // In a real application, you would use a secure password comparison (e.g., bcrypt.compare)
        // For this demo, we're comparing plain text passwords
        if (user.password !== password) {
            console.log(`Invalid password for user ${email}`);
            return null;
        }
        // Update last login timestamp
        yield db.update(users)
            .set({ last_login: new Date() })
            .where(eq(users.id, user.id));
        console.log(`User ${email} authenticated successfully`);
        // In a real application, we would remove the password before returning the user
        // For this demo, we'll just return the user as is
        return user;
    }
    catch (error) {
        console.error('Error verifying credentials:', error);
        return null;
    }
});
