var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
// Auth store using Drizzle ORM with Neon MCP
import { db } from '@/lib/neon-mcp';
import { users } from '@/app/db/schema';
import { eq } from 'drizzle-orm';
import crypto from 'crypto';
/**
 * Add a new user to the database
 * @param user User data without ID
 * @returns The created user or null if user already exists
 */
export const addUser = (userData) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Check if user already exists
        const existingUser = yield findUserByEmail(userData.email);
        if (existingUser) {
            console.log(`User with email ${userData.email} already exists`);
            return null; // User already exists
        }
        // In a real app, passwords should be hashed
        // For this demo, we'll store them as plain text, which is insecure
        const newUser = {
            id: crypto.randomUUID(),
            email: userData.email,
            password: userData.password,
            name: userData.name || userData.email.split('@')[0], // Use part of email as name if not provided
            role: userData.role || 'User',
            department: userData.department || 'General',
            created_at: new Date(),
            last_login: null,
            phone: null,
            avatar: null,
            preferences: null,
        };
        // Insert user into database
        const result = yield db.insert(users).values(newUser).returning();
        if (!result.length) {
            console.error('Failed to create user');
            return null;
        }
        console.log('User added:', result[0]); // Log for demo purposes
        return result[0];
    }
    catch (error) {
        console.error('Error adding user:', error);
        return null;
    }
});
/**
 * Find a user by email
 * @param email User email
 * @returns The user or undefined if not found
 */
export const findUserByEmail = (email) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const result = yield db.select().from(users).where(eq(users.email, email)).limit(1);
        return result[0];
    }
    catch (error) {
        console.error('Error finding user by email:', error);
        return undefined;
    }
});
/**
 * Get all users (for debugging/demo purposes)
 * @returns Array of all users
 */
export const getAllUsers = () => __awaiter(void 0, void 0, void 0, function* () {
    try {
        return yield db.select().from(users);
    }
    catch (error) {
        console.error('Error getting all users:', error);
        return [];
    }
});
/**
 * Verify user credentials
 * @param email User email
 * @param password User password
 * @returns The user if credentials are valid, null otherwise
 */
export const verifyCredentials = (email, password) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = yield findUserByEmail(email);
        if (!user) {
            return null; // User not found
        }
        // In a real app, compare hashed passwords
        // For this demo, we're comparing plain text, which is insecure
        if (user.password !== password) {
            return null; // Invalid password
        }
        // Update last login timestamp
        yield db.update(users)
            .set({ last_login: new Date() })
            .where(eq(users.id, user.id));
        return user;
    }
    catch (error) {
        console.error('Error verifying credentials:', error);
        return null;
    }
});
