var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { db } from '../app/db'; // Import Drizzle client
import { users } from '../app/db/schema'; // Import users schema
import { eq } from 'drizzle-orm'; // Import Drizzle functions
import crypto from 'crypto';
/**
 * Find a user by email in the database using Drizzle
 */
export const findUserByEmail = (email) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Explicitly select all columns to ensure password is included for login check
        const result = yield db.select({
            id: users.id,
            name: users.name,
            role: users.role,
            department: users.department,
            email: users.email,
            password: users.password, // Explicitly select password
            phone: users.phone, // Explicitly select phone
            avatar: users.avatar,
            preferences: users.preferences
        }).from(users).where(eq(users.email, email)).limit(1);
        if (result && result.length > 0) {
            return result[0];
        }
        return null;
    }
    catch (error) {
        console.error('Error finding user by email with Drizzle:', error);
        // Consider more specific error handling or re-throwing
        return null;
    }
});
/**
 * Add a new user to the database using Drizzle
 */
export const addUser = (user) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Prepare data conforming to Drizzle's inferred insert type
        const newUserInsertData = {
            id: crypto.randomUUID(), // Generate and add the user ID
            name: user.name,
            role: user.role || 'User', // Default role
            department: user.department || 'General', // Default department
            email: user.email,
            password: user.password, // Ensure password is appropriately handled (e.g., hashed) before this point
            phone: user.phone || null,
            avatar: user.avatar || null,
            // Add other fields from NewUserInsert if they exist and need defaults (e.g., createdAt, updatedAt)
            // Assuming Drizzle handles defaults like createdAt, updatedAt if defined in schema
        };
        // Insert the new user using Drizzle
        const result = yield db.insert(users).values(newUserInsertData).returning();
        if (result && result.length > 0) {
            return result[0]; // Return the newly created user record
        }
        return null; // Indicate insertion failure or no return value
    }
    catch (error) {
        console.error('Error adding user with Drizzle:', error);
        // Check for specific Drizzle errors, e.g., unique constraint violations
        // Add type check for error object
        if (error instanceof Error && error.message.includes('duplicate key value violates unique constraint')) {
            console.error(`Attempted to add user with existing email: ${user.email}`);
            // Optionally, return a specific error indicator or throw a custom error
            return { error: 'Email already exists' };
        }
        return null; // General error
    }
});
