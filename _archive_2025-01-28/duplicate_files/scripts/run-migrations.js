#!/usr/bin/env node

import { execSync } from 'child_process';
import pkg from '@next/env';
const { loadEnvConfig } = pkg;

// Load environment variables
loadEnvConfig(process.cwd());

console.log('🚀 Running database migrations...');

try {
  // Push schema changes directly to the database
  console.log('\n--- Pushing Schema to Database ---');
  execSync('npx drizzle-kit push:pg', { stdio: 'inherit' });

  console.log('\n✅ Database schema push completed successfully!');
} catch (error) {
  console.error('\n❌ Error pushing database schema:', error.message);
  process.exit(1);
}
