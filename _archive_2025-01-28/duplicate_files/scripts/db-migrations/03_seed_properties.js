var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { loadEnvConfig } from '@next/env';
import { neon } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-http';
import * as schema from '../../app/db/schema';
import crypto from 'crypto';
// Load environment variables
loadEnvConfig(process.cwd());
// Check if the database URL is set
if (!process.env.DATABASE_URL) {
    throw new Error('DATABASE_URL must be a Neon postgres connection string');
}
// Initialize the Neon serverless driver
const sql = neon(process.env.DATABASE_URL);
// Initialize Drizzle ORM with the Neon driver and the schema
const db = drizzle(sql, { schema });
function seedProperties() {
    return __awaiter(this, void 0, void 0, function* () {
        console.log('Seeding properties...');
        try {
            // First, get some user IDs to use as managers
            const managers = yield db.select({ id: schema.users.id, name: schema.users.name })
                .from(schema.users)
                .where(schema.users.role.in(['Regional Manager', 'CEO', 'Head of Innovation']));
            if (managers.length === 0) {
                throw new Error('No managers found in the database. Please seed users first.');
            }
            // Create properties
            const properties = [
                // Australia Post facilities
                {
                    id: crypto.randomUUID(),
                    name: 'Australia Post Sydney Mail Centre',
                    address: '219-241 Cleveland St',
                    suburb: 'Strawberry Hills',
                    state: 'NSW',
                    postcode: '2012',
                    type: 'Industrial',
                    tier: 1,
                    region: 'Sydney',
                    size_sqm: 25000,
                    category: 'Mail Processing',
                    status: 'active',
                    manager_id: managers[0].id,
                    created_at: new Date(),
                },
                {
                    id: crypto.randomUUID(),
                    name: 'Australia Post Melbourne Gateway Facility',
                    address: '45 Fulton Dr',
                    suburb: 'Derrimut',
                    state: 'VIC',
                    postcode: '3030',
                    type: 'Industrial',
                    tier: 1,
                    region: 'Melbourne',
                    size_sqm: 30000,
                    category: 'Mail Processing',
                    status: 'active',
                    manager_id: managers[1].id,
                    created_at: new Date(),
                },
                {
                    id: crypto.randomUUID(),
                    name: 'Australia Post Brisbane Mail Centre',
                    address: '33 Heathwood Dr',
                    suburb: 'Heathwood',
                    state: 'QLD',
                    postcode: '4110',
                    type: 'Industrial',
                    tier: 1,
                    region: 'Brisbane',
                    size_sqm: 22000,
                    category: 'Mail Processing',
                    status: 'active',
                    manager_id: managers[2].id,
                    created_at: new Date(),
                },
                // Australia Post retail locations
                {
                    id: crypto.randomUUID(),
                    name: 'Australia Post GPO Sydney',
                    address: '1 Martin Place',
                    suburb: 'Sydney',
                    state: 'NSW',
                    postcode: '2000',
                    type: 'Retail',
                    tier: 2,
                    region: 'Sydney',
                    size_sqm: 1200,
                    category: 'Post Office',
                    status: 'active',
                    manager_id: managers[0].id,
                    created_at: new Date(),
                },
                {
                    id: crypto.randomUUID(),
                    name: 'Australia Post Bondi Junction',
                    address: '15 Spring St',
                    suburb: 'Bondi Junction',
                    state: 'NSW',
                    postcode: '2022',
                    type: 'Retail',
                    tier: 3,
                    region: 'Sydney',
                    size_sqm: 450,
                    category: 'Post Office',
                    status: 'active',
                    manager_id: managers[0].id,
                    created_at: new Date(),
                },
                // Star Track facilities
                {
                    id: crypto.randomUUID(),
                    name: 'Star Track Express Sydney',
                    address: '10 Smith St',
                    suburb: 'Chullora',
                    state: 'NSW',
                    postcode: '2190',
                    type: 'Industrial',
                    tier: 2,
                    region: 'Sydney',
                    size_sqm: 15000,
                    category: 'Logistics',
                    status: 'active',
                    manager_id: managers[1].id,
                    created_at: new Date(),
                },
                {
                    id: crypto.randomUUID(),
                    name: 'Star Track Express Melbourne',
                    address: '45 Logistics Dr',
                    suburb: 'Tullamarine',
                    state: 'VIC',
                    postcode: '3043',
                    type: 'Industrial',
                    tier: 2,
                    region: 'Melbourne',
                    size_sqm: 18000,
                    category: 'Logistics',
                    status: 'active',
                    manager_id: managers[2].id,
                    created_at: new Date(),
                },
            ];
            // Insert properties
            for (const property of properties) {
                // Check if property already exists
                const existingProperty = yield db.select()
                    .from(schema.properties)
                    .where(schema.properties.name.equals(property.name))
                    .limit(1);
                if (existingProperty.length === 0) {
                    yield db.insert(schema.properties).values(property);
                    console.log(`✅ Created property: ${property.name}`);
                }
                else {
                    console.log(`⏭️ Property already exists: ${property.name}`);
                }
            }
            console.log('Properties seeded successfully!');
            // Verify the data was inserted
            const result = yield db.select().from(schema.properties);
            console.log(`Total properties in database: ${result.length}`);
        }
        catch (error) {
            console.error('Error seeding properties:', error);
        }
    });
}
// Run the function
seedProperties();
