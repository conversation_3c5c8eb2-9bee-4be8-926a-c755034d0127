var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { exec } from 'child_process';
import { promisify } from 'util';
const execAsync = promisify(exec);
function runMigrations() {
    return __awaiter(this, void 0, void 0, function* () {
        console.log('Running all database migrations...');
        try {
            // Run migrations in order
            console.log('\n1. Creating tables...');
            yield execAsync('npx tsx scripts/db-migrations/01_create_tables.ts');
            console.log('\n2. Seeding ARA users...');
            yield execAsync('npx tsx scripts/db-migrations/02_seed_ara_users.ts');
            console.log('\n3. Seeding properties...');
            yield execAsync('npx tsx scripts/db-migrations/03_seed_properties.ts');
            console.log('\nAll migrations completed successfully!');
        }
        catch (error) {
            console.error('Migration failed:', error);
        }
    });
}
runMigrations();
