var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { api } from "../../convex/_generated/api";
export function migrateInitialSetup(client) {
    return __awaiter(this, void 0, void 0, function* () {
        // Create default roles
        const roles = [
            {
                name: "Admin",
                permissions: {
                    users: ["create", "read", "update", "delete"],
                    properties: ["create", "read", "update", "delete"],
                    inspections: ["create", "read", "update", "delete"],
                },
            },
            {
                name: "Property Manager",
                permissions: {
                    properties: ["read", "update"],
                    inspections: ["create", "read", "update"],
                },
            },
            {
                name: "Inspector",
                permissions: {
                    properties: ["read"],
                    inspections: ["create", "read", "update"],
                },
            },
        ];
        for (const role of roles) {
            yield client.mutation(api.roles.create, { role });
        }
        // Create initial KPI targets
        const kpiTargets = [
            {
                metric_type: "inspection_score",
                target_value: 90,
                period: "monthly",
                category: "quality",
                property_type: "industrial",
            },
            {
                metric_type: "response_time",
                target_value: 24,
                period: "daily",
                category: "service",
                property_type: "all",
            },
        ];
        for (const target of kpiTargets) {
            yield client.mutation(api.kpi_targets.create, { target });
        }
    });
}
