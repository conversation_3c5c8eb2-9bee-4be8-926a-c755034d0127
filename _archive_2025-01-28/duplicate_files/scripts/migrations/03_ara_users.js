var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { api } from "../../convex/_generated/api";
import crypto from "crypto";
export function migrateARAUsers(client) {
    return __awaiter(this, void 0, void 0, function* () {
        // ARA Property Services leadership team and staff
        const araUsers = [
            // Leadership Team
            {
                name: "<PERSON>",
                role: "CEO",
                department: "Executive",
                email: "<EMAIL>",
                password: "ARApaulPS!",
                phone: "0403 244 691",
                preferences: { theme: "light", notifications: true },
            },
            {
                name: "Gaurav Majumdar",
                role: "Head of Innovation",
                department: "Innovation & Compliance",
                email: "<EMAIL>",
                password: "ARAgauravPS!",
                phone: "0417 741 543",
                preferences: { theme: "dark", notifications: true },
            },
            {
                name: "Mark Brady",
                role: "Senior Estimator",
                department: "Sales",
                email: "<EMAIL>",
                password: "ARAmarkPS!",
                phone: "0477 806 648",
                preferences: { theme: "system", notifications: true },
            },
            {
                name: "Charles McCann",
                role: "Content Creator",
                department: "Marketing",
                email: "<EMAIL>",
                password: "ARAcharlesPS!",
                phone: "0404 459 623",
                preferences: { theme: "light", notifications: false },
            },
            // Operations Staff - NSW
            {
                name: "Sarah Johnson",
                role: "Regional Manager",
                department: "Operations",
                email: "<EMAIL>",
                password: "ARAsarahPS!",
                phone: "0412 345 678",
                preferences: { theme: "light", notifications: true },
            },
            {
                name: "Michael Chen",
                role: "Site Supervisor",
                department: "Operations",
                email: "<EMAIL>",
                password: "ARAmichaelPS!",
                phone: "0423 456 789",
                preferences: { theme: "dark", notifications: true },
            },
            // Operations Staff - QLD
            {
                name: "Emma Wilson",
                role: "Regional Manager",
                department: "Operations",
                email: "<EMAIL>",
                password: "ARAemmaPS!",
                phone: "0434 567 890",
                preferences: { theme: "system", notifications: true },
            },
            {
                name: "David Thompson",
                role: "Site Supervisor",
                department: "Operations",
                email: "<EMAIL>",
                password: "ARAdavidPS!",
                phone: "0445 678 901",
                preferences: { theme: "light", notifications: true },
            },
            // Operations Staff - VIC
            {
                name: "Jessica Brown",
                role: "Regional Manager",
                department: "Operations",
                email: "<EMAIL>",
                password: "ARAjessicaPS!",
                phone: "0456 789 012",
                preferences: { theme: "dark", notifications: true },
            },
            {
                name: "Robert Lee",
                role: "Site Supervisor",
                department: "Operations",
                email: "<EMAIL>",
                password: "ARArobertPS!",
                phone: "0467 890 123",
                preferences: { theme: "system", notifications: true },
            },
            // Inspectors
            {
                name: "Amanda Garcia",
                role: "Inspector",
                department: "Quality Assurance",
                email: "<EMAIL>",
                password: "ARAamandaPS!",
                phone: "0478 901 234",
                preferences: { theme: "light", notifications: true },
            },
            {
                name: "James Wilson",
                role: "Inspector",
                department: "Quality Assurance",
                email: "<EMAIL>",
                password: "ARAjamesPS!",
                phone: "0489 012 345",
                preferences: { theme: "dark", notifications: true },
            },
            // Admin Staff
            {
                name: "Olivia Martinez",
                role: "Office Manager",
                department: "Administration",
                email: "<EMAIL>",
                password: "ARAoliviaPS!",
                phone: "0490 123 456",
                preferences: { theme: "light", notifications: true },
            },
            {
                name: "William Taylor",
                role: "HR Coordinator",
                department: "Human Resources",
                email: "<EMAIL>",
                password: "ARAwilliamPS!",
                phone: "0401 234 567",
                preferences: { theme: "system", notifications: true },
            },
        ];
        for (const userData of araUsers) {
            // Add a unique ID for each user
            const user = Object.assign(Object.assign({}, userData), { id: crypto.randomUUID(), created_at: new Date() });
            try {
                // Check if user already exists
                const existingUsers = yield client.query(api.users.getUserByEmail, { email: user.email });
                if (!existingUsers || existingUsers.length === 0) {
                    yield client.mutation(api.users.create, { user });
                    console.log(`Created user: ${user.name}`);
                }
                else {
                    console.log(`User already exists: ${user.name}`);
                }
            }
            catch (error) {
                console.error(`Error creating user ${user.name}:`, error);
            }
        }
    });
}
