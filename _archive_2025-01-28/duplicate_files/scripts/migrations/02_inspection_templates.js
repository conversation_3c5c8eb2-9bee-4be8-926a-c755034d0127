var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { api } from "../../convex/_generated/api";
export function migrateInspectionTemplates(client) {
    return __awaiter(this, void 0, void 0, function* () {
        const templates = [
            {
                name: "Industrial Property Inspection",
                category: "Industrial",
                type: "Regular",
                version: 1,
                is_active: true,
                sections: [
                    {
                        name: "Safety",
                        items: [
                            { name: "Emergency Exits", type: "rating" },
                            { name: "Fire Equipment", type: "rating" },
                            { name: "First Aid", type: "rating" },
                        ],
                    },
                    {
                        name: "Equipment",
                        items: [
                            { name: "HVAC Systems", type: "rating" },
                            { name: "Lighting", type: "rating" },
                            { name: "Security Systems", type: "rating" },
                        ],
                    },
                ],
            },
            // Add more templates...
        ];
        for (const template of templates) {
            yield client.mutation(api.inspection_templates.create, { template });
        }
    });
}
