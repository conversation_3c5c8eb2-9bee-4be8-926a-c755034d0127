import { mockUsers } from './users';
// Mock properties for UI testing
export const mockProperties = [
    {
        id: '1',
        name: 'Australia Post Sydney Mail Centre',
        address: '219-241 Cleveland St',
        suburb: 'Strawberry Hills',
        state: 'NSW',
        postcode: '2012',
        type: 'Industrial',
        tier: 1,
        region: 'Sydney',
        size_sqm: 25000,
        category: 'Mail Processing',
        status: 'active',
        manager_id: mockUsers[0].id, // <PERSON>
        created_at: new Date('2025-01-01T00:00:00Z'),
        updated_at: new Date('2025-04-01T00:00:00Z'),
    },
    {
        id: '2',
        name: 'Australia Post Melbourne Gateway Facility',
        address: '45 Fulton Dr',
        suburb: 'Derrimut',
        state: 'VIC',
        postcode: '3030',
        type: 'Industrial',
        tier: 1,
        region: 'Melbourne',
        size_sqm: 30000,
        category: 'Mail Processing',
        status: 'active',
        manager_id: mockUsers[1].id, // <PERSON><PERSON><PERSON><PERSON>
        created_at: new Date('2025-01-01T00:00:00Z'),
        updated_at: new Date('2025-03-15T00:00:00Z'),
    },
    {
        id: '3',
        name: 'Australia Post Brisbane Mail Centre',
        address: '33 Heathwood Dr',
        suburb: 'Heathwood',
        state: 'QLD',
        postcode: '4110',
        type: 'Industrial',
        tier: 1,
        region: 'Brisbane',
        size_sqm: 22000,
        category: 'Mail Processing',
        status: 'active',
        manager_id: mockUsers[4].id, // Sarah Johnson
        created_at: new Date('2025-01-01T00:00:00Z'),
        updated_at: new Date('2025-02-20T00:00:00Z'),
    },
    {
        id: '4',
        name: 'Australia Post GPO Sydney',
        address: '1 Martin Place',
        suburb: 'Sydney',
        state: 'NSW',
        postcode: '2000',
        type: 'Retail',
        tier: 2,
        region: 'Sydney',
        size_sqm: 1200,
        category: 'Post Office',
        status: 'active',
        manager_id: mockUsers[0].id, // Paul McCann
        created_at: new Date('2025-01-01T00:00:00Z'),
        updated_at: new Date('2025-04-05T00:00:00Z'),
    },
    {
        id: '5',
        name: 'Australia Post Bondi Junction',
        address: '15 Spring St',
        suburb: 'Bondi Junction',
        state: 'NSW',
        postcode: '2022',
        type: 'Retail',
        tier: 3,
        region: 'Sydney',
        size_sqm: 450,
        category: 'Post Office',
        status: 'active',
        manager_id: mockUsers[0].id, // Paul McCann
        created_at: new Date('2025-01-01T00:00:00Z'),
        updated_at: new Date('2025-03-10T00:00:00Z'),
    },
    {
        id: '6',
        name: 'Star Track Express Sydney',
        address: '10 Smith St',
        suburb: 'Chullora',
        state: 'NSW',
        postcode: '2190',
        type: 'Industrial',
        tier: 2,
        region: 'Sydney',
        size_sqm: 15000,
        category: 'Logistics',
        status: 'active',
        manager_id: mockUsers[1].id, // Gaurav Majumdar
        created_at: new Date('2025-01-01T00:00:00Z'),
        updated_at: new Date('2025-02-15T00:00:00Z'),
    },
    {
        id: '7',
        name: 'Star Track Express Melbourne',
        address: '45 Logistics Dr',
        suburb: 'Tullamarine',
        state: 'VIC',
        postcode: '3043',
        type: 'Industrial',
        tier: 2,
        region: 'Melbourne',
        size_sqm: 18000,
        category: 'Logistics',
        status: 'active',
        manager_id: mockUsers[4].id, // Sarah Johnson
        created_at: new Date('2025-01-01T00:00:00Z'),
        updated_at: new Date('2025-03-25T00:00:00Z'),
    },
];
// Function to get a property by ID
export function getPropertyById(id) {
    return mockProperties.find(property => property.id === id);
}
// Function to get properties by manager ID
export function getPropertiesByManagerId(managerId) {
    return mockProperties.filter(property => property.manager_id === managerId);
}
// Function to get properties by region
export function getPropertiesByRegion(region) {
    return mockProperties.filter(property => property.region === region);
}
// Export default for convenience
export default mockProperties;
