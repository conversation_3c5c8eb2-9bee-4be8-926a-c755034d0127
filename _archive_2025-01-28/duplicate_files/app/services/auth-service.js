var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { userRepository } from '@/app/repositories';
// Removed unused crypto import
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
// Simple token generation for demo purposes
// In production, use a proper JWT library
const generateToken = (userId) => {
    const payload = {
        userId,
        exp: Math.floor(Date.now() / 1000) + 60 * 60 * 24, // 24 hours
    };
    return Buffer.from(JSON.stringify(payload)).toString('base64');
};
// Simple token verification for demo purposes
const verifyToken = (token) => {
    try {
        const payload = JSON.parse(Buffer.from(token, 'base64').toString());
        if (payload.exp < Math.floor(Date.now() / 1000)) {
            return null; // Token expired
        }
        return { userId: payload.userId };
    }
    catch (_error) { // Prefix unused variable
        return null;
    }
};
/**
 * Authentication service
 */
export class AuthService {
    /**
     * Register a new user
     */
    register(userData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // Check if user already exists
                const existingUser = yield userRepository.findByEmail(userData.email);
                if (existingUser) {
                    throw new Error('User with this email already exists');
                }
                // In a real app, hash the password
                // For demo purposes, we're storing it as plain text
                const newUser = {
                    name: userData.name,
                    email: userData.email,
                    password: userData.password, // Should be hashed in production
                    role: userData.role || 'User',
                    department: userData.department || 'General',
                    phone: userData.phone || null,
                    avatar: null,
                    preferences: null,
                    created_at: new Date(),
                    last_login: null,
                };
                return yield userRepository.create(newUser);
            }
            catch (error) {
                console.error('Error registering user:', error);
                return null;
            }
        });
    }
    /**
     * Login a user
     */
    login(email, password) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const user = yield userRepository.findByEmail(email);
                if (!user) {
                    return null; // User not found
                }
                // In a real app, compare hashed passwords
                // For demo purposes, we're comparing plain text
                if (user.password !== password) {
                    return null; // Invalid password
                }
                // Update last login timestamp
                yield userRepository.updateLastLogin(user.id);
                // Generate token
                const token = generateToken(user.id);
                return { user, token };
            }
            catch (error) {
                console.error('Error logging in user:', error);
                return null;
            }
        });
    }
    /**
     * Get the current user from the session
     */
    getCurrentUser() {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            try {
                const cookieStore = cookies();
                const token = (_a = cookieStore.get('auth_token')) === null || _a === void 0 ? void 0 : _a.value;
                if (!token) {
                    return null;
                }
                const payload = verifyToken(token);
                if (!payload) {
                    return null;
                }
                return yield userRepository.findById(payload.userId);
            }
            catch (error) {
                console.error('Error getting current user:', error);
                return null;
            }
        });
    }
    /**
     * Set auth token cookie
     */
    setAuthCookie(token) {
        const cookieStore = cookies();
        cookieStore.set('auth_token', token, {
            httpOnly: true,
            path: '/',
            secure: process.env.NODE_ENV === 'production',
            maxAge: 60 * 60 * 24, // 24 hours
        });
    }
    /**
     * Logout the current user
     */
    logout() {
        const cookieStore = cookies();
        cookieStore.delete('auth_token');
    }
    /**
     * Check if the user is authenticated
     */
    isAuthenticated() {
        return __awaiter(this, void 0, void 0, function* () {
            const user = yield this.getCurrentUser();
            return !!user;
        });
    }
    /**
     * Require authentication for a route
     */
    requireAuth() {
        return __awaiter(this, void 0, void 0, function* () {
            const user = yield this.getCurrentUser();
            if (!user) {
                redirect('/login');
            }
            return user;
        });
    }
    /**
     * Check if the user has a specific role
     */
    hasRole(role) {
        return __awaiter(this, void 0, void 0, function* () {
            const user = yield this.getCurrentUser();
            if (!user) {
                return false;
            }
            return user.role === role;
        });
    }
    /**
     * Require a specific role for a route
     */
    requireRole(role) {
        return __awaiter(this, void 0, void 0, function* () {
            const user = yield this.requireAuth();
            if (user.role !== role) {
                redirect('/unauthorized');
            }
            return user;
        });
    }
}
// Export a singleton instance
export const authService = new AuthService();
