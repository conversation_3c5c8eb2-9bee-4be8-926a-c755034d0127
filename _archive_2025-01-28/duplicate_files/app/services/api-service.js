var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { NextResponse } from 'next/server';
import { authService } from './auth-service';
/**
 * API service for handling API requests
 */
export class ApiService {
    /**
     * Handle an authenticated API request
     */
    handleAuthenticatedRequest(req, handler // Change data type to unknown
    ) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // Check if user is authenticated
                const user = yield authService.getCurrentUser();
                if (!user) {
                    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
                }
                // Parse request body
                let data = {};
                if (req.method !== 'GET') {
                    try {
                        data = yield req.json();
                    }
                    catch (_error) { // Prefix unused variable
                        // If request body is empty or invalid JSON, use empty object
                    }
                }
                // Call handler with user ID and data
                const result = yield handler(user.id, data);
                return NextResponse.json(result);
            }
            catch (error) { // Type error as unknown
                console.error('Error handling authenticated request:', error);
                if (error instanceof Error) {
                    return NextResponse.json({ error: error.message }, { status: 400 });
                }
                return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
            }
        });
    }
    /**
     * Handle a role-based API request
     */
    handleRoleBasedRequest(req, role, handler // Change data type to unknown
    ) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // Check if user is authenticated
                const user = yield authService.getCurrentUser();
                if (!user) {
                    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
                }
                // Check if user has the required role
                if (user.role !== role) {
                    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
                }
                // Parse request body
                let data = {};
                if (req.method !== 'GET') {
                    try {
                        data = yield req.json();
                    }
                    catch (_error) { // Prefix unused variable
                        // If request body is empty or invalid JSON, use empty object
                    }
                }
                // Call handler with user ID and data
                const result = yield handler(user.id, data);
                return NextResponse.json(result);
            }
            catch (error) { // Type error as unknown
                console.error('Error handling role-based request:', error);
                if (error instanceof Error) {
                    return NextResponse.json({ error: error.message }, { status: 400 });
                }
                return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
            }
        });
    }
    /**
     * Handle a public API request
     */
    handlePublicRequest(req, handler // Change data type to unknown
    ) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // Parse request body
                let data = {};
                if (req.method !== 'GET') {
                    try {
                        data = yield req.json();
                    }
                    catch (_error) { // Prefix unused variable
                        // If request body is empty or invalid JSON, use empty object
                    }
                }
                // Call handler with data
                const result = yield handler(data);
                return NextResponse.json(result);
            }
            catch (error) { // Type error as unknown
                console.error('Error handling public request:', error);
                if (error instanceof Error) {
                    return NextResponse.json({ error: error.message }, { status: 400 });
                }
                return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
            }
        });
    }
}
// Export a singleton instance
export const apiService = new ApiService();
