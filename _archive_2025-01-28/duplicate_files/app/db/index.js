import { loadEnvConfig } from '@next/env';
import { neon } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-http';
// Import the schema definitions
import * as schema from './schema';
// Load environment variables only if not in Edge Runtime (where process.cwd is unavailable)
// and likely not needed as env vars are injected differently.
if (typeof process !== 'undefined' && process.versions && process.versions.node) {
    loadEnvConfig(process.cwd());
}
// Check if the database URL is set
if (!process.env.DATABASE_URL) {
    throw new Error('DATABASE_URL must be a Neon postgres connection string');
}
// Initialize the Neon serverless driver
const sql = neon(process.env.DATABASE_URL);
// Initialize Drizzle ORM with the Neon driver and the schema
// The schema object contains all the table definitions from schema.ts
export const db = drizzle(sql, { schema });
// Export schema for use in other files
export * from './schema';
