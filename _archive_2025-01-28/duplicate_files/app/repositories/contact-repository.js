var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { db } from '@/app/db';
import { contacts } from '@/app/db/schema';
import { eq, or, like } from 'drizzle-orm'; // Removed unused 'and', 'desc'
import crypto from 'crypto';
/**
 * Repository for contact-related database operations
 */
export class ContactRepository {
    /**
     * Find a contact by ID
     */
    findById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.query.contacts.findFirst({
                    where: eq(contacts.id, id),
                });
                return result || null;
            }
            catch (error) {
                console.error('Error finding contact by ID:', error);
                throw error;
            }
        });
    }
    /**
     * Get all contacts
     */
    getAll(options) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { limit = 100, offset = 0 } = options || {};
                const result = yield db.select().from(contacts).limit(limit).offset(offset);
                return result;
            }
            catch (error) {
                console.error('Error getting all contacts:', error);
                throw error;
            }
        });
    }
    /**
     * Search contacts by name, company, or role
     */
    search(query, options) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { limit = 100, offset = 0 } = options || {};
                const searchTerm = `%${query}%`;
                const result = yield db.select()
                    .from(contacts)
                    .where(or(like(contacts.name, searchTerm), like(contacts.company, searchTerm), like(contacts.role, searchTerm)))
                    .limit(limit)
                    .offset(offset);
                return result;
            }
            catch (error) {
                console.error('Error searching contacts:', error);
                throw error;
            }
        });
    }
    /**
     * Create a new contact
     */
    create(contactData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const newContact = Object.assign(Object.assign({}, contactData), { id: crypto.randomUUID(), created_at: new Date(), updated_at: null });
                const result = yield db.insert(contacts).values(newContact).returning();
                if (!result.length) {
                    throw new Error('Failed to create contact');
                }
                return result[0];
            }
            catch (error) {
                console.error('Error creating contact:', error);
                throw error;
            }
        });
    }
    /**
     * Update a contact
     */
    update(id, contactData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.update(contacts)
                    .set(Object.assign(Object.assign({}, contactData), { updated_at: new Date() }))
                    .where(eq(contacts.id, id))
                    .returning();
                if (!result.length) {
                    return null;
                }
                return result[0];
            }
            catch (error) {
                console.error('Error updating contact:', error);
                throw error;
            }
        });
    }
    /**
     * Delete a contact
     */
    delete(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.delete(contacts)
                    .where(eq(contacts.id, id))
                    .returning();
                return result.length > 0;
            }
            catch (error) {
                console.error('Error deleting contact:', error);
                throw error;
            }
        });
    }
    /**
     * Toggle favorite status
     */
    toggleFavorite(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // First get the current contact to check its favorite status
                const contact = yield this.findById(id);
                if (!contact) {
                    return null;
                }
                // Toggle the is_favorite status
                const result = yield db.update(contacts)
                    .set({
                    is_favorite: !contact.is_favorite,
                    updated_at: new Date(),
                })
                    .where(eq(contacts.id, id))
                    .returning();
                if (!result.length) {
                    return null;
                }
                return result[0];
            }
            catch (error) {
                console.error('Error toggling contact favorite status:', error);
                throw error;
            }
        });
    }
    /**
     * Find contacts by company
     */
    findByCompany(company, options) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { limit = 100, offset = 0 } = options || {};
                const result = yield db.select()
                    .from(contacts)
                    .where(eq(contacts.company, company))
                    .limit(limit)
                    .offset(offset);
                return result;
            }
            catch (error) {
                console.error('Error finding contacts by company:', error);
                throw error;
            }
        });
    }
    /**
     * Find contacts by role
     */
    findByRole(role, options) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { limit = 100, offset = 0 } = options || {};
                const result = yield db.select()
                    .from(contacts)
                    .where(eq(contacts.role, role))
                    .limit(limit)
                    .offset(offset);
                return result;
            }
            catch (error) {
                console.error('Error finding contacts by role:', error);
                throw error;
            }
        });
    }
    /**
     * Get favorite contacts
     */
    getFavorites(options) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { limit = 100, offset = 0 } = options || {};
                const result = yield db.select()
                    .from(contacts)
                    .where(eq(contacts.is_favorite, true))
                    .limit(limit)
                    .offset(offset);
                return result;
            }
            catch (error) {
                console.error('Error getting favorite contacts:', error);
                throw error;
            }
        });
    }
    /**
     * Count total contacts
     */
    count() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.select({ count: db.fn.count() }).from(contacts);
                return Number(result[0].count) || 0;
            }
            catch (error) {
                console.error('Error counting contacts:', error);
                throw error;
            }
        });
    }
}
// Export a singleton instance
export const contactRepository = new ContactRepository();
