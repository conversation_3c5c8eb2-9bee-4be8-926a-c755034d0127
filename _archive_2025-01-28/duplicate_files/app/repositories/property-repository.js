var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { db } from '@/app/db';
import { properties, property_areas } from '@/app/db/schema';
import { eq, or, like, asc } from 'drizzle-orm'; // Removed unused 'and', 'desc'
import crypto from 'crypto';
/**
 * Repository for property-related database operations
 */
export class PropertyRepository {
    /**
     * Find a property by ID
     */
    findById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.query.properties.findFirst({
                    where: eq(properties.id, id),
                });
                return result || null;
            }
            catch (error) {
                console.error('Error finding property by ID:', error);
                throw error;
            }
        });
    }
    /**
     * Get all properties
     */
    getAll(options) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { limit = 100, offset = 0 } = options || {};
                const result = yield db.select()
                    .from(properties)
                    .orderBy(asc(properties.name))
                    .limit(limit)
                    .offset(offset);
                return result;
            }
            catch (error) {
                console.error('Error getting all properties:', error);
                throw error;
            }
        });
    }
    /**
     * Create a new property
     */
    create(propertyData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const newProperty = Object.assign(Object.assign({}, propertyData), { id: crypto.randomUUID(), created_at: new Date(), updated_at: null });
                const result = yield db.insert(properties).values(newProperty).returning();
                if (!result.length) {
                    throw new Error('Failed to create property');
                }
                return result[0];
            }
            catch (error) {
                console.error('Error creating property:', error);
                throw error;
            }
        });
    }
    /**
     * Update a property
     */
    update(id, propertyData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.update(properties)
                    .set(Object.assign(Object.assign({}, propertyData), { updated_at: new Date() }))
                    .where(eq(properties.id, id))
                    .returning();
                if (!result.length) {
                    return null;
                }
                return result[0];
            }
            catch (error) {
                console.error('Error updating property:', error);
                throw error;
            }
        });
    }
    /**
     * Delete a property
     */
    delete(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // First delete all areas associated with the property
                yield db.delete(property_areas)
                    .where(eq(property_areas.property_id, id));
                // Then delete the property itself
                const result = yield db.delete(properties)
                    .where(eq(properties.id, id))
                    .returning();
                return result.length > 0;
            }
            catch (error) {
                console.error('Error deleting property:', error);
                throw error;
            }
        });
    }
    /**
     * Search properties by name, address, or suburb
     */
    search(query, options) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { limit = 100, offset = 0 } = options || {};
                const searchTerm = `%${query}%`;
                const result = yield db.select()
                    .from(properties)
                    .where(or(like(properties.name, searchTerm), like(properties.address, searchTerm), like(properties.suburb, searchTerm)))
                    .orderBy(asc(properties.name))
                    .limit(limit)
                    .offset(offset);
                return result;
            }
            catch (error) {
                console.error('Error searching properties:', error);
                throw error;
            }
        });
    }
    /**
     * Get properties by manager
     */
    getByManager(managerId, options) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { limit = 100, offset = 0 } = options || {};
                const result = yield db.select()
                    .from(properties)
                    .where(eq(properties.manager_id, managerId))
                    .orderBy(asc(properties.name))
                    .limit(limit)
                    .offset(offset);
                return result;
            }
            catch (error) {
                console.error('Error getting properties by manager:', error);
                throw error;
            }
        });
    }
    /**
     * Get properties by region
     */
    getByRegion(region, options) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { limit = 100, offset = 0 } = options || {};
                const result = yield db.select()
                    .from(properties)
                    .where(eq(properties.region, region))
                    .orderBy(asc(properties.name))
                    .limit(limit)
                    .offset(offset);
                return result;
            }
            catch (error) {
                console.error('Error getting properties by region:', error);
                throw error;
            }
        });
    }
    /**
     * Get properties by type
     */
    getByType(type, options) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { limit = 100, offset = 0 } = options || {};
                const result = yield db.select()
                    .from(properties)
                    .where(eq(properties.type, type))
                    .orderBy(asc(properties.name))
                    .limit(limit)
                    .offset(offset);
                return result;
            }
            catch (error) {
                console.error('Error getting properties by type:', error);
                throw error;
            }
        });
    }
    /**
     * Get properties by status
     */
    getByStatus(status, options) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { limit = 100, offset = 0 } = options || {};
                const result = yield db.select()
                    .from(properties)
                    .where(eq(properties.status, status))
                    .orderBy(asc(properties.name))
                    .limit(limit)
                    .offset(offset);
                return result;
            }
            catch (error) {
                console.error('Error getting properties by status:', error);
                throw error;
            }
        });
    }
    /**
     * Add an area to a property
     */
    addArea(area) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const newArea = Object.assign(Object.assign({}, area), { id: crypto.randomUUID() });
                const result = yield db.insert(property_areas).values(newArea).returning();
                if (!result.length) {
                    throw new Error('Failed to add area to property');
                }
                return result[0];
            }
            catch (error) {
                console.error('Error adding area to property:', error);
                throw error;
            }
        });
    }
    /**
     * Get areas for a property
     */
    getAreas(propertyId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.select()
                    .from(property_areas)
                    .where(eq(property_areas.property_id, propertyId))
                    .orderBy(asc(property_areas.name));
                return result;
            }
            catch (error) {
                console.error('Error getting areas for property:', error);
                throw error;
            }
        });
    }
    /**
     * Update a property area
     */
    updateArea(id, areaData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.update(property_areas)
                    .set(areaData)
                    .where(eq(property_areas.id, id))
                    .returning();
                if (!result.length) {
                    return null;
                }
                return result[0];
            }
            catch (error) {
                console.error('Error updating property area:', error);
                throw error;
            }
        });
    }
    /**
     * Delete a property area
     */
    deleteArea(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.delete(property_areas)
                    .where(eq(property_areas.id, id))
                    .returning();
                return result.length > 0;
            }
            catch (error) {
                console.error('Error deleting property area:', error);
                throw error;
            }
        });
    }
    /**
     * Count total properties
     */
    count() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.select({ count: db.fn.count() }).from(properties);
                return Number(result[0].count) || 0;
            }
            catch (error) {
                console.error('Error counting properties:', error);
                throw error;
            }
        });
    }
    /**
     * Get property statistics by region
     */
    getStatsByRegion() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.select({
                    region: properties.region,
                    count: db.fn.count(),
                })
                    .from(properties)
                    .groupBy(properties.region);
                return result.map(item => ({
                    region: item.region,
                    count: Number(item.count) || 0,
                }));
            }
            catch (error) {
                console.error('Error getting property statistics by region:', error);
                throw error;
            }
        });
    }
    /**
     * Get property statistics by type
     */
    getStatsByType() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.select({
                    type: properties.type,
                    count: db.fn.count(),
                })
                    .from(properties)
                    .groupBy(properties.type);
                return result.map(item => ({
                    type: item.type,
                    count: Number(item.count) || 0,
                }));
            }
            catch (error) {
                console.error('Error getting property statistics by type:', error);
                throw error;
            }
        });
    }
}
// Export a singleton instance
export const propertyRepository = new PropertyRepository();
