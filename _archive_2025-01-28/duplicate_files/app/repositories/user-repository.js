var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { db } from '@/app/db';
import { users } from '@/app/db/schema';
import { eq, or, like } from 'drizzle-orm'; // Removed unused 'and', 'desc'
import crypto from 'crypto';
/**
 * Repository for user-related database operations
 */
export class UserRepository {
    /**
     * Find a user by email
     */
    findByEmail(email) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.query.users.findFirst({
                    where: eq(users.email, email),
                });
                return result || null;
            }
            catch (error) {
                console.error('Error finding user by email:', error);
                throw error;
            }
        });
    }
    /**
     * Find a user by ID
     */
    findById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.query.users.findFirst({
                    where: eq(users.id, id),
                });
                return result || null;
            }
            catch (error) {
                console.error('Error finding user by ID:', error);
                throw error;
            }
        });
    }
    /**
     * Get all users
     */
    getAll(options) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { limit = 100, offset = 0 } = options || {};
                const result = yield db.select().from(users).limit(limit).offset(offset);
                return result;
            }
            catch (error) {
                console.error('Error getting all users:', error);
                throw error;
            }
        });
    }
    /**
     * Search users by name, email, or department
     */
    search(query, options) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { limit = 100, offset = 0 } = options || {};
                const searchTerm = `%${query}%`;
                const result = yield db.select()
                    .from(users)
                    .where(or(like(users.name, searchTerm), like(users.email, searchTerm), like(users.department, searchTerm)))
                    .limit(limit)
                    .offset(offset);
                return result;
            }
            catch (error) {
                console.error('Error searching users:', error);
                throw error;
            }
        });
    }
    /**
     * Create a new user
     */
    create(userData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const newUser = Object.assign(Object.assign({}, userData), { id: crypto.randomUUID() });
                const result = yield db.insert(users).values(newUser).returning();
                if (!result.length) {
                    throw new Error('Failed to create user');
                }
                return result[0];
            }
            catch (error) {
                console.error('Error creating user:', error);
                throw error;
            }
        });
    }
    /**
     * Update a user
     */
    update(id, userData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.update(users)
                    .set(Object.assign(Object.assign({}, userData), { 
                    // Don't update these fields if not provided
                    name: userData.name !== undefined ? userData.name : undefined, email: userData.email !== undefined ? userData.email : undefined, password: userData.password !== undefined ? userData.password : undefined }))
                    .where(eq(users.id, id))
                    .returning();
                if (!result.length) {
                    return null;
                }
                return result[0];
            }
            catch (error) {
                console.error('Error updating user:', error);
                throw error;
            }
        });
    }
    /**
     * Delete a user
     */
    delete(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.delete(users)
                    .where(eq(users.id, id))
                    .returning();
                return result.length > 0;
            }
            catch (error) {
                console.error('Error deleting user:', error);
                throw error;
            }
        });
    }
    /**
     * Update user's last login timestamp
     */
    updateLastLogin(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.update(users)
                    .set({
                    last_login: new Date(),
                })
                    .where(eq(users.id, id))
                    .returning();
                return result.length > 0;
            }
            catch (error) {
                console.error('Error updating user last login:', error);
                throw error;
            }
        });
    }
    /**
     * Find users by role
     */
    findByRole(role, options) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { limit = 100, offset = 0 } = options || {};
                const result = yield db.select()
                    .from(users)
                    .where(eq(users.role, role))
                    .limit(limit)
                    .offset(offset);
                return result;
            }
            catch (error) {
                console.error('Error finding users by role:', error);
                throw error;
            }
        });
    }
    /**
     * Find users by department
     */
    findByDepartment(department, options) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { limit = 100, offset = 0 } = options || {};
                const result = yield db.select()
                    .from(users)
                    .where(eq(users.department, department))
                    .limit(limit)
                    .offset(offset);
                return result;
            }
            catch (error) {
                console.error('Error finding users by department:', error);
                throw error;
            }
        });
    }
    /**
     * Count total users
     */
    count() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.select({ count: db.fn.count() }).from(users);
                return Number(result[0].count) || 0;
            }
            catch (error) {
                console.error('Error counting users:', error);
                throw error;
            }
        });
    }
}
// Export a singleton instance
export const userRepository = new UserRepository();
