var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { db } from '@/app/db';
import { inspection_reports, inspection_actions, report_attachments, inspection_templates } from '@/app/db/schema';
import { eq, and, or, desc, asc, gte, lte } from 'drizzle-orm'; // Removed unused 'like', 'isNull', 'isNotNull'
import crypto from 'crypto';
/**
 * Repository for inspection report-related database operations
 */
export class InspectionRepository {
    /**
     * Find an inspection report by ID
     */
    findById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.query.inspection_reports.findFirst({
                    where: eq(inspection_reports.id, id),
                });
                return result || null;
            }
            catch (error) {
                console.error('Error finding inspection report by ID:', error);
                throw error;
            }
        });
    }
    /**
     * Get all inspection reports
     */
    getAll(options) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { limit = 100, offset = 0 } = options || {};
                const result = yield db.select()
                    .from(inspection_reports)
                    .orderBy(desc(inspection_reports.date))
                    .limit(limit)
                    .offset(offset);
                return result;
            }
            catch (error) {
                console.error('Error getting all inspection reports:', error);
                throw error;
            }
        });
    }
    /**
     * Create a new inspection report
     */
    create(reportData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const newReport = Object.assign(Object.assign({}, reportData), { id: crypto.randomUUID(), date: new Date(), created_at: new Date(), submitted_at: null, reviewed_at: null });
                const result = yield db.insert(inspection_reports).values(newReport).returning();
                if (!result.length) {
                    throw new Error('Failed to create inspection report');
                }
                return result[0];
            }
            catch (error) {
                console.error('Error creating inspection report:', error);
                throw error;
            }
        });
    }
    /**
     * Update an inspection report
     */
    update(id, reportData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.update(inspection_reports)
                    .set(reportData)
                    .where(eq(inspection_reports.id, id))
                    .returning();
                if (!result.length) {
                    return null;
                }
                return result[0];
            }
            catch (error) {
                console.error('Error updating inspection report:', error);
                throw error;
            }
        });
    }
    /**
     * Delete an inspection report
     */
    delete(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // First delete all actions associated with the report
                yield db.delete(inspection_actions)
                    .where(eq(inspection_actions.report_id, id));
                // Then delete all attachments
                yield db.delete(report_attachments)
                    .where(eq(report_attachments.report_id, id));
                // Finally delete the report itself
                const result = yield db.delete(inspection_reports)
                    .where(eq(inspection_reports.id, id))
                    .returning();
                return result.length > 0;
            }
            catch (error) {
                console.error('Error deleting inspection report:', error);
                throw error;
            }
        });
    }
    /**
     * Get inspection reports by property
     */
    getByProperty(propertyId, options) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { limit = 100, offset = 0 } = options || {};
                const result = yield db.select()
                    .from(inspection_reports)
                    .where(eq(inspection_reports.property_id, propertyId))
                    .orderBy(desc(inspection_reports.date))
                    .limit(limit)
                    .offset(offset);
                return result;
            }
            catch (error) {
                console.error('Error getting inspection reports by property:', error);
                throw error;
            }
        });
    }
    /**
     * Get inspection reports by inspector
     */
    getByInspector(inspectorId, options) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { limit = 100, offset = 0 } = options || {};
                const result = yield db.select()
                    .from(inspection_reports)
                    .where(eq(inspection_reports.inspector, inspectorId))
                    .orderBy(desc(inspection_reports.date))
                    .limit(limit)
                    .offset(offset);
                return result;
            }
            catch (error) {
                console.error('Error getting inspection reports by inspector:', error);
                throw error;
            }
        });
    }
    /**
     * Get inspection reports by status
     */
    getByStatus(status, options) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { limit = 100, offset = 0 } = options || {};
                const result = yield db.select()
                    .from(inspection_reports)
                    .where(eq(inspection_reports.status, status))
                    .orderBy(desc(inspection_reports.date))
                    .limit(limit)
                    .offset(offset);
                return result;
            }
            catch (error) {
                console.error('Error getting inspection reports by status:', error);
                throw error;
            }
        });
    }
    /**
     * Get inspection reports by date range
     */
    getByDateRange(startDate, endDate, options) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { limit = 100, offset = 0 } = options || {};
                const result = yield db.select()
                    .from(inspection_reports)
                    .where(and(gte(inspection_reports.date, startDate), lte(inspection_reports.date, endDate)))
                    .orderBy(desc(inspection_reports.date))
                    .limit(limit)
                    .offset(offset);
                return result;
            }
            catch (error) {
                console.error('Error getting inspection reports by date range:', error);
                throw error;
            }
        });
    }
    /**
     * Submit an inspection report
     */
    submit(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.update(inspection_reports)
                    .set({
                    status: 'submitted',
                    submitted_at: new Date(),
                })
                    .where(eq(inspection_reports.id, id))
                    .returning();
                if (!result.length) {
                    return null;
                }
                return result[0];
            }
            catch (error) {
                console.error('Error submitting inspection report:', error);
                throw error;
            }
        });
    }
    /**
     * Review an inspection report
     */
    review(id, reviewerId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.update(inspection_reports)
                    .set({
                    status: 'reviewed',
                    reviewed_by: reviewerId,
                    reviewed_at: new Date(),
                })
                    .where(eq(inspection_reports.id, id))
                    .returning();
                if (!result.length) {
                    return null;
                }
                return result[0];
            }
            catch (error) {
                console.error('Error reviewing inspection report:', error);
                throw error;
            }
        });
    }
    /**
     * Approve an inspection report
     */
    approve(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.update(inspection_reports)
                    .set({
                    status: 'approved',
                })
                    .where(eq(inspection_reports.id, id))
                    .returning();
                if (!result.length) {
                    return null;
                }
                return result[0];
            }
            catch (error) {
                console.error('Error approving inspection report:', error);
                throw error;
            }
        });
    }
    /**
     * Get pending inspection reports
     */
    getPending(options) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { limit = 100, offset = 0 } = options || {};
                const result = yield db.select()
                    .from(inspection_reports)
                    .where(or(eq(inspection_reports.status, 'draft'), eq(inspection_reports.status, 'submitted')))
                    .orderBy(desc(inspection_reports.date))
                    .limit(limit)
                    .offset(offset);
                return result;
            }
            catch (error) {
                console.error('Error getting pending inspection reports:', error);
                throw error;
            }
        });
    }
    /**
     * Add an action to an inspection report
     */
    addAction(action) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const newAction = Object.assign(Object.assign({}, action), { id: crypto.randomUUID(), created_at: new Date(), updated_at: null });
                const result = yield db.insert(inspection_actions).values(newAction).returning();
                if (!result.length) {
                    throw new Error('Failed to add action to inspection report');
                }
                return result[0];
            }
            catch (error) {
                console.error('Error adding action to inspection report:', error);
                throw error;
            }
        });
    }
    /**
     * Get actions for an inspection report
     */
    getActions(reportId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.select()
                    .from(inspection_actions)
                    .where(eq(inspection_actions.report_id, reportId))
                    .orderBy(asc(inspection_actions.due_date));
                return result;
            }
            catch (error) {
                console.error('Error getting actions for inspection report:', error);
                throw error;
            }
        });
    }
    /**
     * Update an action
     */
    updateAction(id, actionData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.update(inspection_actions)
                    .set(Object.assign(Object.assign({}, actionData), { updated_at: new Date() }))
                    .where(eq(inspection_actions.id, id))
                    .returning();
                if (!result.length) {
                    return null;
                }
                return result[0];
            }
            catch (error) {
                console.error('Error updating inspection action:', error);
                throw error;
            }
        });
    }
    /**
     * Delete an action
     */
    deleteAction(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.delete(inspection_actions)
                    .where(eq(inspection_actions.id, id))
                    .returning();
                return result.length > 0;
            }
            catch (error) {
                console.error('Error deleting inspection action:', error);
                throw error;
            }
        });
    }
    /**
     * Add an attachment to an inspection report
     */
    addAttachment(attachment) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const newAttachment = Object.assign(Object.assign({}, attachment), { id: crypto.randomUUID(), created_at: new Date() });
                const result = yield db.insert(report_attachments).values(newAttachment).returning();
                if (!result.length) {
                    throw new Error('Failed to add attachment to inspection report');
                }
                return result[0];
            }
            catch (error) {
                console.error('Error adding attachment to inspection report:', error);
                throw error;
            }
        });
    }
    /**
     * Get attachments for an inspection report
     */
    getAttachments(reportId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.select()
                    .from(report_attachments)
                    .where(eq(report_attachments.report_id, reportId))
                    .orderBy(desc(report_attachments.created_at));
                return result;
            }
            catch (error) {
                console.error('Error getting attachments for inspection report:', error);
                throw error;
            }
        });
    }
    /**
     * Delete an attachment
     */
    deleteAttachment(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.delete(report_attachments)
                    .where(eq(report_attachments.id, id))
                    .returning();
                return result.length > 0;
            }
            catch (error) {
                console.error('Error deleting inspection attachment:', error);
                throw error;
            }
        });
    }
    /**
     * Create a new inspection template
     */
    createTemplate(templateData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const newTemplate = Object.assign(Object.assign({}, templateData), { id: crypto.randomUUID(), created_at: new Date() });
                const result = yield db.insert(inspection_templates).values(newTemplate).returning();
                if (!result.length) {
                    throw new Error('Failed to create inspection template');
                }
                return result[0];
            }
            catch (error) {
                console.error('Error creating inspection template:', error);
                throw error;
            }
        });
    }
    /**
     * Get all inspection templates
     */
    getAllTemplates(options) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { limit = 100, offset = 0 } = options || {};
                const result = yield db.select()
                    .from(inspection_templates)
                    .orderBy(asc(inspection_templates.name))
                    .limit(limit)
                    .offset(offset);
                return result;
            }
            catch (error) {
                console.error('Error getting all inspection templates:', error);
                throw error;
            }
        });
    }
    /**
     * Get active inspection templates
     */
    getActiveTemplates() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.select()
                    .from(inspection_templates)
                    .where(eq(inspection_templates.is_active, true))
                    .orderBy(asc(inspection_templates.name));
                return result;
            }
            catch (error) {
                console.error('Error getting active inspection templates:', error);
                throw error;
            }
        });
    }
    /**
     * Find an inspection template by ID
     */
    findTemplateById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.query.inspection_templates.findFirst({
                    where: eq(inspection_templates.id, id),
                });
                return result || null;
            }
            catch (error) {
                console.error('Error finding inspection template by ID:', error);
                throw error;
            }
        });
    }
    /**
     * Update an inspection template
     */
    updateTemplate(id, templateData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.update(inspection_templates)
                    .set(templateData)
                    .where(eq(inspection_templates.id, id))
                    .returning();
                if (!result.length) {
                    return null;
                }
                return result[0];
            }
            catch (error) {
                console.error('Error updating inspection template:', error);
                throw error;
            }
        });
    }
    /**
     * Delete an inspection template
     */
    deleteTemplate(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.delete(inspection_templates)
                    .where(eq(inspection_templates.id, id))
                    .returning();
                return result.length > 0;
            }
            catch (error) {
                console.error('Error deleting inspection template:', error);
                throw error;
            }
        });
    }
}
// Export a singleton instance
export const inspectionRepository = new InspectionRepository();
