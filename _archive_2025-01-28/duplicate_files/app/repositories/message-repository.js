var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { db } from '@/app/db';
import { messages } from '@/app/db/schema';
import { eq, like, desc, asc } from 'drizzle-orm'; // Removed unused 'and', 'or'
import crypto from 'crypto';
/**
 * Repository for message-related database operations
 */
export class MessageRepository {
    /**
     * Find a message by ID
     */
    findById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.query.messages.findFirst({
                    where: eq(messages.id, id),
                });
                return result || null;
            }
            catch (error) {
                console.error('Error finding message by ID:', error);
                throw error;
            }
        });
    }
    /**
     * Get messages by session ID
     */
    getBySessionId(sessionId, options) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { limit = 100, offset = 0 } = options || {};
                const result = yield db.select()
                    .from(messages)
                    .where(eq(messages.session_id, sessionId))
                    .orderBy(asc(messages.created_at))
                    .limit(limit)
                    .offset(offset);
                return result;
            }
            catch (error) {
                console.error('Error getting messages by session ID:', error);
                throw error;
            }
        });
    }
    /**
     * Create a new message
     */
    create(messageData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const newMessage = Object.assign(Object.assign({}, messageData), { id: crypto.randomUUID(), created_at: new Date() });
                const result = yield db.insert(messages).values(newMessage).returning();
                if (!result.length) {
                    throw new Error('Failed to create message');
                }
                return result[0];
            }
            catch (error) {
                console.error('Error creating message:', error);
                throw error;
            }
        });
    }
    /**
     * Update a message
     */
    update(id, messageData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.update(messages)
                    .set(messageData)
                    .where(eq(messages.id, id))
                    .returning();
                if (!result.length) {
                    return null;
                }
                return result[0];
            }
            catch (error) {
                console.error('Error updating message:', error);
                throw error;
            }
        });
    }
    /**
     * Delete a message
     */
    delete(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.delete(messages)
                    .where(eq(messages.id, id))
                    .returning();
                return result.length > 0;
            }
            catch (error) {
                console.error('Error deleting message:', error);
                throw error;
            }
        });
    }
    /**
     * Delete all messages in a session
     */
    deleteBySessionId(sessionId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.delete(messages)
                    .where(eq(messages.session_id, sessionId))
                    .returning();
                return result.length;
            }
            catch (error) {
                console.error('Error deleting messages by session ID:', error);
                throw error;
            }
        });
    }
    /**
     * Search messages by content
     */
    searchByContent(query, options) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { limit = 100, offset = 0 } = options || {};
                const searchTerm = `%${query}%`;
                const result = yield db.select()
                    .from(messages)
                    .where(like(messages.content_transcript, searchTerm))
                    .orderBy(desc(messages.created_at))
                    .limit(limit)
                    .offset(offset);
                return result;
            }
            catch (error) {
                console.error('Error searching messages by content:', error);
                throw error;
            }
        });
    }
    /**
     * Get messages by sender
     */
    getBySender(senderId, options) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { limit = 100, offset = 0 } = options || {};
                const result = yield db.select()
                    .from(messages)
                    .where(eq(messages.sender_id, senderId))
                    .orderBy(desc(messages.created_at))
                    .limit(limit)
                    .offset(offset);
                return result;
            }
            catch (error) {
                console.error('Error getting messages by sender:', error);
                throw error;
            }
        });
    }
    /**
     * Count messages in a session
     */
    countBySessionId(sessionId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.select({ count: db.fn.count() })
                    .from(messages)
                    .where(eq(messages.session_id, sessionId));
                return Number(result[0].count) || 0;
            }
            catch (error) {
                console.error('Error counting messages by session ID:', error);
                throw error;
            }
        });
    }
    /**
     * Get latest message in a session
     */
    getLatestBySessionId(sessionId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.select()
                    .from(messages)
                    .where(eq(messages.session_id, sessionId))
                    .orderBy(desc(messages.created_at))
                    .limit(1);
                if (!result.length) {
                    return null;
                }
                return result[0];
            }
            catch (error) {
                console.error('Error getting latest message by session ID:', error);
                throw error;
            }
        });
    }
}
// Export a singleton instance
export const messageRepository = new MessageRepository();
