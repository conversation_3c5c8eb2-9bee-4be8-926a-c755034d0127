var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { db } from '@/app/db';
import { chat_sessions, chat_participants, messages } from '@/app/db/schema';
import { eq, and, or, like, desc } from 'drizzle-orm'; // Removed unused 'asc'
import crypto from 'crypto';
/**
 * Repository for chat session-related database operations
 */
export class ChatSessionRepository {
    /**
     * Find a chat session by ID
     */
    findById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.query.chat_sessions.findFirst({
                    where: eq(chat_sessions.id, id),
                });
                return result || null;
            }
            catch (error) {
                console.error('Error finding chat session by ID:', error);
                throw error;
            }
        });
    }
    /**
     * Get all chat sessions
     */
    getAll(options) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { limit = 100, offset = 0 } = options || {};
                const result = yield db.select()
                    .from(chat_sessions)
                    .orderBy(desc(chat_sessions.date))
                    .limit(limit)
                    .offset(offset);
                return result;
            }
            catch (error) {
                console.error('Error getting all chat sessions:', error);
                throw error;
            }
        });
    }
    /**
     * Create a new chat session
     */
    create(sessionData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const newSession = Object.assign(Object.assign({}, sessionData), { id: crypto.randomUUID(), date: new Date() });
                const result = yield db.insert(chat_sessions).values(newSession).returning();
                if (!result.length) {
                    throw new Error('Failed to create chat session');
                }
                return result[0];
            }
            catch (error) {
                console.error('Error creating chat session:', error);
                throw error;
            }
        });
    }
    /**
     * Update a chat session
     */
    update(id, sessionData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.update(chat_sessions)
                    .set(sessionData)
                    .where(eq(chat_sessions.id, id))
                    .returning();
                if (!result.length) {
                    return null;
                }
                return result[0];
            }
            catch (error) {
                console.error('Error updating chat session:', error);
                throw error;
            }
        });
    }
    /**
     * Delete a chat session
     */
    delete(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // First delete all messages in the session
                yield db.delete(messages)
                    .where(eq(messages.session_id, id));
                // Then delete all participants
                yield db.delete(chat_participants)
                    .where(eq(chat_participants.session_id, id));
                // Finally delete the session itself
                const result = yield db.delete(chat_sessions)
                    .where(eq(chat_sessions.id, id))
                    .returning();
                return result.length > 0;
            }
            catch (error) {
                console.error('Error deleting chat session:', error);
                throw error;
            }
        });
    }
    /**
     * Toggle starred status
     */
    toggleStarred(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // First get the current session to check its starred status
                const session = yield this.findById(id);
                if (!session) {
                    return null;
                }
                // Toggle the is_starred status
                const result = yield db.update(chat_sessions)
                    .set({
                    is_starred: !session.is_starred,
                })
                    .where(eq(chat_sessions.id, id))
                    .returning();
                if (!result.length) {
                    return null;
                }
                return result[0];
            }
            catch (error) {
                console.error('Error toggling chat session starred status:', error);
                throw error;
            }
        });
    }
    /**
     * Get chat sessions by creator
     */
    getByCreator(creatorId, options) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { limit = 100, offset = 0 } = options || {};
                const result = yield db.select()
                    .from(chat_sessions)
                    .where(eq(chat_sessions.created_by, creatorId))
                    .orderBy(desc(chat_sessions.date))
                    .limit(limit)
                    .offset(offset);
                return result;
            }
            catch (error) {
                console.error('Error getting chat sessions by creator:', error);
                throw error;
            }
        });
    }
    /**
     * Get starred chat sessions
     */
    getStarred(options) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { limit = 100, offset = 0 } = options || {};
                const result = yield db.select()
                    .from(chat_sessions)
                    .where(eq(chat_sessions.is_starred, true))
                    .orderBy(desc(chat_sessions.date))
                    .limit(limit)
                    .offset(offset);
                return result;
            }
            catch (error) {
                console.error('Error getting starred chat sessions:', error);
                throw error;
            }
        });
    }
    /**
     * Search chat sessions by title
     */
    search(query, options) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { limit = 100, offset = 0 } = options || {};
                const searchTerm = `%${query}%`;
                const result = yield db.select()
                    .from(chat_sessions)
                    .where(or(like(chat_sessions.title, searchTerm), like(chat_sessions.preview, searchTerm)))
                    .orderBy(desc(chat_sessions.date))
                    .limit(limit)
                    .offset(offset);
                return result;
            }
            catch (error) {
                console.error('Error searching chat sessions:', error);
                throw error;
            }
        });
    }
    /**
     * Add a participant to a chat session
     */
    addParticipant(sessionId, userId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // Check if the participant already exists
                const existingParticipant = yield db.select()
                    .from(chat_participants)
                    .where(and(eq(chat_participants.session_id, sessionId), eq(chat_participants.user_id, userId)))
                    .limit(1);
                if (existingParticipant.length > 0) {
                    return existingParticipant[0];
                }
                // Add the new participant
                const newParticipant = {
                    session_id: sessionId,
                    user_id: userId,
                    joined_at: new Date(),
                    last_read_at: null,
                };
                const result = yield db.insert(chat_participants)
                    .values(newParticipant)
                    .returning();
                if (!result.length) {
                    throw new Error('Failed to add participant to chat session');
                }
                return result[0];
            }
            catch (error) {
                console.error('Error adding participant to chat session:', error);
                throw error;
            }
        });
    }
    /**
     * Remove a participant from a chat session
     */
    removeParticipant(sessionId, userId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.delete(chat_participants)
                    .where(and(eq(chat_participants.session_id, sessionId), eq(chat_participants.user_id, userId)))
                    .returning();
                return result.length > 0;
            }
            catch (error) {
                console.error('Error removing participant from chat session:', error);
                throw error;
            }
        });
    }
    /**
     * Get participants of a chat session
     */
    getParticipants(sessionId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.select()
                    .from(chat_participants)
                    .where(eq(chat_participants.session_id, sessionId));
                return result;
            }
            catch (error) {
                console.error('Error getting chat session participants:', error);
                throw error;
            }
        });
    }
    /**
     * Update participant's last read timestamp
     */
    updateLastRead(sessionId, userId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield db.update(chat_participants)
                    .set({
                    last_read_at: new Date(),
                })
                    .where(and(eq(chat_participants.session_id, sessionId), eq(chat_participants.user_id, userId)))
                    .returning();
                return result.length > 0;
            }
            catch (error) {
                console.error('Error updating participant last read timestamp:', error);
                throw error;
            }
        });
    }
    /**
     * Get chat sessions for a participant
     */
    getByParticipant(userId, options) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { limit = 100, offset = 0 } = options || {};
                // Get session IDs where the user is a participant
                const participantSessions = yield db.select({ session_id: chat_participants.session_id })
                    .from(chat_participants)
                    .where(eq(chat_participants.user_id, userId));
                const sessionIds = participantSessions.map(p => p.session_id);
                if (sessionIds.length === 0) {
                    return [];
                }
                // Get the actual sessions
                const result = yield db.select()
                    .from(chat_sessions)
                    .where(sessionIds.map(id => eq(chat_sessions.id, id)).reduce((acc, curr) => or(acc, curr)))
                    .orderBy(desc(chat_sessions.date))
                    .limit(limit)
                    .offset(offset);
                return result;
            }
            catch (error) {
                console.error('Error getting chat sessions by participant:', error);
                throw error;
            }
        });
    }
}
// Export a singleton instance
export const chatSessionRepository = new ChatSessionRepository();
